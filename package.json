{"name": "platemotion", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "start": "turbo run start", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,md}\"", "prepare": "husky || true"}, "devDependencies": {"prettier": "^3.2.5", "turbo": "^1.13.3", "callsites": "^3.1.0", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "husky": "^9.0.11"}, "packageManager": "pnpm@9.1.0"}
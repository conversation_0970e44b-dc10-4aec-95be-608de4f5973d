# Tamagui to React Native Paper Migration Inventory

## Overview

This document serves as a comprehensive inventory and migration guide for transitioning the PlateMotion mobile app's UI components from Tamagui to React Native Paper.

For a detailed migration plan with step-by-step instructions, see [MIGRATION_PLAN.md](./MIGRATION_PLAN.md).

For a progress summary, see [MIGRATION_PROGRESS_SUMMARY.md](./MIGRATION_PROGRESS_SUMMARY.md).

## Files Using Tamagui

### Progress Feature

- **File**: `src/features/progress/screens/ProgressScreen.tsx`
  - Components: YStack, XStack, Text, But<PERSON>, <PERSON>rollView, Spinner, Card, Input, TextArea
  - Icons: None

### Nutrition Feature

- **File**: `src/features/nutrition/screens/GroceryListScreen.tsx`
  - Components: YStack, XStack, Text, But<PERSON>, ScrollView, Card, Input
  - Icons: Plus, Trash2
- **File**: `src/features/nutrition/screens/NutritionScreen.tsx`
  - Components: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card
  - Icons: ChevronLeft, ChevronRight, Calendar, ShoppingCart
- **File**: `src/features/nutrition/screens/RecipeDetailScreen.tsx`
  - Components: YStack, XStack, Text, Button, ScrollView, Card
  - Icons: None

### Exercise Feature

- **File**: `src/features/exercise/screens/ExercisePlayerScreen.tsx`
  - Components: YStack, XStack, Text, Button, Card, Progress
  - Icons: None
- **File**: `src/features/exercise/screens/ExerciseScreen.tsx`
  - Components: YStack, XStack, Text, Button, ScrollView, Spinner, Card
  - Icons: ChevronLeft, ChevronRight, Calendar
- **File**: `src/features/exercise/screens/WorkoutDetailScreen.tsx`
  - Components: YStack, XStack, Text, Button, ScrollView, Card
  - Icons: None

### Profile Feature

- **File**: `src/features/profile/screens/ProfileScreen.tsx`
  - Components: YStack, XStack, Text, Button, ScrollView, Card, Spinner
  - Icons: None

### AI Coach Feature

- **File**: `src/features/ai-coach/screens/ChatScreen.tsx`
  - Components: None (uses React Native components directly)
  - Icons: None

## Component Mapping

| Tamagui Component | React Native Paper Equivalent     | Props Mapping                                                            | Notes                   |
| ----------------- | --------------------------------- | ------------------------------------------------------------------------ | ----------------------- |
| YStack            | View                              | padding, margin, flex, alignItems, justifyContent                        | Vertical stack layout   |
| XStack            | View                              | padding, margin, flex, flexDirection: 'row', alignItems, justifyContent  | Horizontal stack layout |
| Text              | Text                              | fontSize, fontWeight, color, textTransform                               | Text component          |
| Button            | Button                            | mode, onPress, disabled, loading, icon                                   | Button component        |
| Card              | Card                              | onPress, mode, style                                                     | Card component          |
| ScrollView        | ScrollView                        | horizontal, showsHorizontalScrollIndicator, showsVerticalScrollIndicator | Scrollable container    |
| Spinner           | ActivityIndicator                 | size, color, animating                                                   | Loading indicator       |
| Progress          | ProgressBar                       | progress, color, style                                                   | Progress indicator      |
| Input             | TextInput                         | value, onChangeText, placeholder, secureTextEntry, keyboardType          | Input component         |
| TextArea          | TextInput                         | multiline, numberOfLines                                                 | Multi-line input        |
| Checkbox          | Checkbox.Item or Checkbox.Android | status, onPress, label                                                   | Checkbox component      |

## Icon Mapping

| Tamagui Icon | React Native Paper Equivalent | Icon Library                                     | Notes                |
| ------------ | ----------------------------- | ------------------------------------------------ | -------------------- |
| ChevronLeft  | IconButton                    | react-native-vector-icons/MaterialCommunityIcons | name="chevron-left"  |
| ChevronRight | IconButton                    | react-native-vector-icons/MaterialCommunityIcons | name="chevron-right" |
| Calendar     | IconButton                    | react-native-vector-icons/MaterialCommunityIcons | name="calendar"      |
| ShoppingCart | IconButton                    | react-native-vector-icons/MaterialCommunityIcons | name="cart"          |
| Plus         | IconButton                    | react-native-vector-icons/MaterialCommunityIcons | name="plus"          |
| Trash2       | IconButton                    | react-native-vector-icons/MaterialCommunityIcons | name="trash-can"     |

## Migration Status

| Feature   | Status      | Notes                   |
| --------- | ----------- | ----------------------- |
| Progress  | Complete    | ProgressScreen migrated |
| Nutrition | Complete    | All screens migrated    |
| Exercise  | Complete    | All screens migrated    |
| Profile   | Complete    | ProfileScreen migrated  |
| AI Coach  | Not started |                         |
| AI Coach  | Not started |                         |

## Migration Checklist

- [x] Phase 1: Preparation and Setup
- [x] Phase 2: Dependency Management
- [x] Phase 3: Configuration Updates
- [x] Phase 4: Component-by-Component Migration
- [ ] Phase 5: Styling and Theme Updates
- [ ] Phase 6: Testing and Validation
- [ ] Phase 7: Performance Optimization
- [ ] Phase 8: Documentation

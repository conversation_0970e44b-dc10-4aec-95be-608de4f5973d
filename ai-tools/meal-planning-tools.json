{"meal_planner": {"name": "meal_planner", "description": "Generate personalized weekly meal plans based on user preferences, dietary restrictions, and nutritional goals", "parameters": {"type": "object", "properties": {"week_start_date": {"type": "string", "format": "date", "description": "Start date for the meal plan (YYYY-MM-DD)"}, "dietary_preferences": {"type": "array", "items": {"type": "string", "enum": ["vegetarian", "vegan", "gluten-free", "dairy-free", "keto", "paleo", "mediterranean", "low-carb", "high-protein"]}, "description": "User's dietary preferences and restrictions"}, "target_calories_per_day": {"type": "integer", "minimum": 1200, "maximum": 4000, "description": "Daily calorie target"}, "target_macros": {"type": "object", "properties": {"protein_grams": {"type": "integer"}, "carbs_grams": {"type": "integer"}, "fat_grams": {"type": "integer"}}, "description": "Daily macro targets"}, "excluded_ingredients": {"type": "array", "items": {"type": "string"}, "description": "Ingredients to avoid"}, "preferred_cuisines": {"type": "array", "items": {"type": "string"}, "description": "Preferred cuisine types"}, "max_prep_time_minutes": {"type": "integer", "description": "Maximum prep time per meal"}, "meals_per_day": {"type": "integer", "minimum": 2, "maximum": 6, "default": 3, "description": "Number of meals per day"}, "cooking_skill_level": {"type": "string", "enum": ["beginner", "intermediate", "advanced"], "description": "User's cooking skill level"}}, "required": ["week_start_date", "target_calories_per_day"]}, "database_query_strategy": {"base_filters": ["is_active = TRUE", "rating_average >= 3.5 OR rating_count < 5"], "user_filters": ["dietary_tags @> $dietary_preferences", "NOT (allergens && $user_allergens)", "total_time_minutes <= $max_prep_time_minutes", "difficulty_level = ANY($acceptable_difficulty_levels)", "equipment_required <@ $available_equipment OR equipment_required = '{}'", "estimated_cost_per_serving <= $max_cost_per_serving"], "preference_scoring": ["calculate_recipe_compatibility(id, $user_id) as compatibility_score", "CASE WHEN cuisine_type = ANY($preferred_cuisines) THEN 0.2 ELSE 0 END as cuisine_bonus", "CASE WHEN meal_prep_friendly = TRUE AND $meal_prep_preference = TRUE THEN 0.1 ELSE 0 END as meal_prep_bonus"], "optimization_strategy": "Balance nutrition targets, variety across cuisines, user compatibility scores, and meal timing preferences across 7 days", "performance_notes": "Uses indexes on dietary_tags, total_time_minutes, difficulty_level, equipment_required for sub-100ms queries"}}, "recipe_finder": {"name": "recipe_finder", "description": "Find recipes matching specific criteria", "parameters": {"type": "object", "properties": {"search_query": {"type": "string", "description": "Text search for recipe names and descriptions"}, "cuisine_type": {"type": "string", "description": "Specific cuisine type"}, "dietary_tags": {"type": "array", "items": {"type": "string"}, "description": "Required dietary tags"}, "max_prep_time": {"type": "integer", "description": "Maximum preparation time in minutes"}, "max_total_time": {"type": "integer", "description": "Maximum total cooking time in minutes"}, "difficulty_level": {"type": "string", "enum": ["beginner", "intermediate", "advanced"]}, "meal_type": {"type": "string", "enum": ["breakfast", "lunch", "dinner", "snack", "dessert"]}, "min_protein": {"type": "integer", "description": "Minimum protein grams per serving"}, "max_calories": {"type": "integer", "description": "Maximum calories per serving"}, "available_ingredients": {"type": "array", "items": {"type": "string"}, "description": "Ingredients user has available"}, "exclude_allergens": {"type": "array", "items": {"type": "string"}, "description": "Allergens to avoid"}, "budget_max": {"type": "number", "description": "Maximum cost per serving"}, "occasion": {"type": "string", "description": "Specific occasion or context"}, "season": {"type": "string", "enum": ["spring", "summer", "fall", "winter", "year-round"], "description": "Seasonal preference"}, "flavor_preferences": {"type": "array", "items": {"type": "string"}, "description": "Preferred flavor profiles"}, "exclude_recipes": {"type": "array", "items": {"type": "string"}, "description": "Recipe IDs to exclude from results"}, "sort_by": {"type": "string", "enum": ["rating", "popularity", "prep_time", "cost", "compatibility"], "default": "compatibility", "description": "How to sort results"}, "limit": {"type": "integer", "default": 10, "maximum": 50, "description": "Maximum number of recipes to return"}}}, "database_query_optimization": {"primary_query": "SELECT r.*, calculate_recipe_compatibility(r.id, $user_id) as compatibility_score FROM recipes r", "performance_indexes_used": ["idx_recipes_meal_types (for meal_type filter)", "idx_recipes_dietary_tags (for dietary_tags filter)", "idx_recipes_total_time (for max_total_time filter)", "idx_recipes_search (for search_query full-text search)", "idx_recipes_rating (for quality filtering)"], "estimated_query_time": "< 50ms for typical filters"}}, "grocery_list_generator": {"name": "grocery_list_generator", "description": "Generate organized grocery shopping list from meal plan", "parameters": {"type": "object", "properties": {"meal_plan_id": {"type": "string", "format": "uuid", "description": "ID of the meal plan to generate grocery list from"}, "servings_multiplier": {"type": "number", "default": 1.0, "description": "Multiply ingredient quantities (for meal prep)"}, "organize_by_category": {"type": "boolean", "default": true, "description": "Group ingredients by store category"}, "exclude_pantry_staples": {"type": "boolean", "default": false, "description": "Exclude common pantry items"}, "store_preference": {"type": "string", "description": "Preferred grocery store for organization"}}, "required": ["meal_plan_id"]}, "processing_logic": {"steps": ["Aggregate all ingredients from meal plan recipes", "Combine duplicate ingredients and sum quantities", "Convert units to shopping-friendly measurements", "Categorize by store sections (produce, meat, dairy, etc.)", "Sort within categories by typical store layout"]}}, "recipe_modifier": {"name": "recipe_modifier", "description": "Suggest modifications to recipes for dietary needs or preferences", "parameters": {"type": "object", "properties": {"recipe_id": {"type": "string", "format": "uuid", "description": "ID of recipe to modify"}, "modification_type": {"type": "string", "enum": ["make_vegetarian", "make_vegan", "make_gluten_free", "make_dairy_free", "reduce_calories", "increase_protein", "reduce_sodium", "make_keto_friendly"], "description": "Type of modification requested"}, "target_servings": {"type": "integer", "description": "Scale recipe to different serving size"}, "substitute_ingredients": {"type": "object", "description": "Specific ingredient substitutions requested"}}, "required": ["recipe_id", "modification_type"]}}, "nutrition_analyzer": {"name": "nutrition_analyzer", "description": "Analyze nutritional content of meals or meal plans", "parameters": {"type": "object", "properties": {"analysis_type": {"type": "string", "enum": ["single_recipe", "daily_meals", "weekly_plan"], "description": "Scope of nutritional analysis"}, "recipe_ids": {"type": "array", "items": {"type": "string"}, "description": "Recipe IDs to analyze"}, "meal_plan_id": {"type": "string", "format": "uuid", "description": "Meal plan ID for weekly analysis"}, "user_goals": {"type": "object", "properties": {"target_calories": {"type": "integer"}, "target_protein": {"type": "integer"}, "target_carbs": {"type": "integer"}, "target_fat": {"type": "integer"}}, "description": "User's nutritional goals for comparison"}}, "required": ["analysis_type"]}}}
{"workout_planner": {"name": "workout_planner", "description": "Generate personalized workout routines based on user goals, fitness level, and available equipment", "parameters": {"type": "object", "properties": {"fitness_goals": {"type": "array", "items": {"type": "string", "enum": ["weight-loss", "muscle-gain", "strength", "endurance", "flexibility", "general-fitness", "athletic-performance"]}, "description": "Primary fitness goals"}, "fitness_level": {"type": "string", "enum": ["beginner", "intermediate", "advanced"], "description": "Current fitness level"}, "available_equipment": {"type": "array", "items": {"type": "string"}, "description": "Equipment available for workouts"}, "workout_duration_minutes": {"type": "integer", "minimum": 15, "maximum": 180, "description": "Desired workout duration"}, "workouts_per_week": {"type": "integer", "minimum": 1, "maximum": 7, "description": "Number of workouts per week"}, "target_muscle_groups": {"type": "array", "items": {"type": "string", "enum": ["chest", "back", "shoulders", "arms", "legs", "glutes", "core", "full-body"]}, "description": "Muscle groups to focus on"}, "workout_type_preference": {"type": "string", "enum": ["strength", "cardio", "hiit", "circuit", "yoga", "pilates", "mixed"], "description": "Preferred workout style"}, "intensity_preference": {"type": "string", "enum": ["low", "moderate", "high", "variable"], "description": "Preferred workout intensity"}, "physical_limitations": {"type": "array", "items": {"type": "string"}, "description": "Any physical limitations or injuries to consider"}, "space_available": {"type": "string", "enum": ["minimal", "small", "large", "gym"], "description": "Available workout space"}}, "required": ["fitness_goals", "fitness_level", "workout_duration_minutes", "workouts_per_week"]}, "database_query_strategy": {"filters": ["fitness_goals && $fitness_goals", "difficulty_level <= $fitness_level", "estimated_duration_minutes <= $workout_duration_minutes + 15", "equipment_required <@ $available_equipment OR equipment_required = '{}'", "space_required <= $space_available"], "optimization": "Balance muscle groups, progression, and recovery across weekly schedule"}}, "exercise_finder": {"name": "exercise_finder", "description": "Find exercises matching specific criteria", "parameters": {"type": "object", "properties": {"search_query": {"type": "string", "description": "Text search for exercise names and descriptions"}, "muscle_groups": {"type": "array", "items": {"type": "string"}, "description": "Target muscle groups"}, "equipment_available": {"type": "array", "items": {"type": "string"}, "description": "Available equipment"}, "exercise_category": {"type": "string", "enum": ["strength", "cardio", "flexibility", "balance", "plyometric"], "description": "Exercise category"}, "difficulty_level": {"type": "string", "enum": ["beginner", "intermediate", "advanced"]}, "movement_pattern": {"type": "string", "enum": ["push", "pull", "squat", "hinge", "lunge", "carry"], "description": "Movement pattern for strength exercises"}, "unilateral_only": {"type": "boolean", "description": "Only single-arm/leg exercises"}, "compound_only": {"type": "boolean", "description": "Only compound (multi-joint) exercises"}, "exclude_exercises": {"type": "array", "items": {"type": "string"}, "description": "Exercise IDs to exclude"}, "limit": {"type": "integer", "default": 10, "maximum": 50, "description": "Maximum number of exercises to return"}}}}, "workout_program_generator": {"name": "workout_program_generator", "description": "Create multi-week progressive workout programs", "parameters": {"type": "object", "properties": {"program_duration_weeks": {"type": "integer", "minimum": 4, "maximum": 52, "description": "Length of the program in weeks"}, "primary_goal": {"type": "string", "enum": ["strength", "muscle-gain", "weight-loss", "endurance", "athletic-performance"], "description": "Primary program goal"}, "training_frequency": {"type": "integer", "minimum": 2, "maximum": 6, "description": "Workouts per week"}, "progression_strategy": {"type": "string", "enum": ["linear", "periodized", "undulating"], "default": "linear", "description": "How to progress over time"}, "deload_frequency": {"type": "integer", "default": 4, "description": "Deload every X weeks"}, "user_preferences": {"type": "object", "properties": {"fitness_level": {"type": "string"}, "available_equipment": {"type": "array"}, "workout_duration": {"type": "integer"}, "preferred_split": {"type": "string"}}}}, "required": ["program_duration_weeks", "primary_goal", "training_frequency"]}}, "workout_modifier": {"name": "workout_modifier", "description": "Adapt existing workouts for different constraints or needs", "parameters": {"type": "object", "properties": {"workout_id": {"type": "string", "format": "uuid", "description": "ID of workout to modify"}, "modification_type": {"type": "string", "enum": ["reduce_time", "increase_intensity", "decrease_intensity", "substitute_equipment", "make_bodyweight", "add_cardio", "remove_cardio", "focus_muscle_group"], "description": "Type of modification needed"}, "new_duration_minutes": {"type": "integer", "description": "New target duration"}, "available_equipment": {"type": "array", "items": {"type": "string"}, "description": "Equipment available for substitutions"}, "intensity_adjustment": {"type": "number", "minimum": 0.5, "maximum": 2.0, "description": "Intensity multiplier (1.0 = no change)"}, "focus_muscle_group": {"type": "string", "description": "Muscle group to emphasize"}, "physical_limitations": {"type": "array", "items": {"type": "string"}, "description": "Limitations to work around"}}, "required": ["workout_id", "modification_type"]}}, "progress_tracker": {"name": "progress_tracker", "description": "Track and analyze workout progress and performance", "parameters": {"type": "object", "properties": {"analysis_period": {"type": "string", "enum": ["week", "month", "quarter", "year"], "description": "Time period to analyze"}, "metrics_to_analyze": {"type": "array", "items": {"type": "string", "enum": ["strength_gains", "volume_progression", "consistency", "workout_duration", "perceived_exertion", "body_composition"]}, "description": "Specific metrics to track"}, "exercise_focus": {"type": "array", "items": {"type": "string"}, "description": "Specific exercises to analyze"}, "comparison_baseline": {"type": "string", "format": "date", "description": "Date to compare progress against"}}, "required": ["analysis_period"]}}, "recovery_planner": {"name": "recovery_planner", "description": "Plan rest days and recovery activities based on workout intensity", "parameters": {"type": "object", "properties": {"recent_workouts": {"type": "array", "items": {"type": "string"}, "description": "Recent workout session IDs"}, "recovery_preferences": {"type": "array", "items": {"type": "string", "enum": ["active_recovery", "complete_rest", "stretching", "yoga", "light_cardio", "massage", "sauna"]}, "description": "Preferred recovery methods"}, "upcoming_workouts": {"type": "array", "items": {"type": "string"}, "description": "Planned upcoming workouts"}, "recovery_time_available": {"type": "integer", "description": "Minutes available for recovery activities"}}, "required": ["recent_workouts"]}}}
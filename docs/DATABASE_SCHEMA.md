# Database Schema: PlateMotion

This document outlines the proposed database schema for the PlateMotion application, designed to be implemented in Supabase (PostgreSQL).

## Table of Contents

1.  [Users & Profiles](#1-users--profiles)
2.  [Content (Exercises & Recipes)](#2-content-exercises--recipes)
3.  [User Plans (Workouts & Meals)](#3-user-plans-workouts--meals)
4.  [Feedback & Progress](#4-feedback--progress)
5.  [Application Management](#5-application-management)

---

### 1. Users & Profiles

**`users`** (Handled by Supabase Auth)

- `id` (uuid, primary key)
- `email` (text)
- ... other Supabase Auth fields

**`profiles`**

- `id` (uuid, primary key, foreign key to `users.id`)
- `full_name` (text)
- `age` (integer)
- `height_cm` (integer)
- `weight_kg` (numeric)
- `goals` (text[]) - e.g., ['weight_loss', 'muscle_gain']
- `experience_level` (text) - e.g., 'beginner', 'intermediate', 'advanced'
- `ai_persona_preference` (text) - e.g., 'motivator', 'drill_sergeant'
- `dietary_restrictions` (text[]) - e.g., ['gluten_free', 'vegan']
- `food_allergies` (text[])
- `budget_preference` (text) - e.g., 'low', 'medium', 'high'
- `known_injuries` (text[])
- `created_at` (timestamp with time zone)
- `updated_at` (timestamp with time zone)

---

### 2. Content (Exercises & Recipes)

**`exercises`**

- `id` (uuid, primary key)
- `name` (text)
- `description` (text)
- `video_url` (text) - Link to the instructional video in Supabase Storage
- `muscle_group` (text)
- `equipment_needed` (text[])
- `created_at` (timestamp with time zone)

**`recipes`**

- `id` (uuid, primary key)
- `name` (text)
- `description` (text)
- `instructions` (text)
- `ingredients` (jsonb)
- `prep_time_minutes` (integer)
- `cook_time_minutes` (integer)
- `cuisine_type` (text)
- `nutritional_info` (jsonb) - e.g., { "calories": 500, "protein": 30, ... }
- `is_human_reviewed` (boolean, default: false)
- `created_at` (timestamp with time zone)

---

### 3. User Plans (Workouts & Meals)

**`workout_plans`**

- `id` (uuid, primary key)
- `user_id` (uuid, foreign key to `users.id`)
- `plan_date` (date)
- `created_at` (timestamp with time zone)

**`workout_plan_exercises`** (Join Table)

- `id` (uuid, primary key)
- `workout_plan_id` (uuid, foreign key to `workout_plans.id`)
- `exercise_id` (uuid, foreign key to `exercises.id`)
- `sets` (integer)
- `reps` (integer)
- `rest_seconds` (integer)
- `order` (integer)

**`meal_plans`**

- `id` (uuid, primary key)
- `user_id` (uuid, foreign key to `users.id`)
- `plan_date` (date)
- `created_at` (timestamp with time zone)

**`meal_plan_recipes`** (Join Table)

- `id` (uuid, primary key)
- `meal_plan_id` (uuid, foreign key to `meal_plans.id`)
- `recipe_id` (uuid, foreign key to `recipes.id`)
- `meal_type` (text) - e.g., 'breakfast', 'lunch', 'dinner', 'snack'

---

### 4. Feedback & Progress

**`user_feedback`**

- `id` (uuid, primary key)
- `user_id` (uuid, foreign key to `users.id`)
- `content_id` (uuid) - Can be an `exercise_id` or `recipe_id`
- `content_type` (text) - 'exercise' or 'recipe'
- `is_liked` (boolean) - Thumbs up (true) or thumbs down (false)
- `created_at` (timestamp with time zone)

**`progress_logs`**

- `id` (uuid, primary key)
- `user_id` (uuid, foreign key to `users.id`)
- `log_date` (date)
- `weight_kg` (numeric, nullable)
- `body_measurements` (jsonb, nullable)
- `workout_performance` (jsonb, nullable) - e.g., { "exercise_id": "...", "sets_completed": 3, ... }
- `subjective_feeling` (text, nullable)
- `created_at` (timestamp with time zone)

---

### 5. Application Management

**`subscriptions`**

- `id` (uuid, primary key)
- `user_id` (uuid, foreign key to `users.id`)
- `status` (text) - e.g., 'trialing', 'active', 'canceled', 'past_due'
- `current_period_start` (timestamp with time zone)
- `current_period_end` (timestamp with time zone)
- `provider` (text) - e.g., 'stripe', 'apple', 'google'
- `provider_subscription_id` (text)

**`support_tickets`**

- `id` (uuid, primary key)
- `user_id` (uuid, foreign key to `users.id`)
- `subject` (text)
- `body` (text)
- `status` (text) - e.g., 'open', 'in_progress', 'closed'
- `created_at` (timestamp with time zone)
- `updated_at` (timestamp with time zone)

# AI Persona System - PlateMotion

## Overview

The AI Persona System creates and maintains personalized user profiles to provide highly tailored fitness and nutrition advice. The AI learns from user interactions, preferences, and behaviors to build a comprehensive understanding of each user.

## Architecture

### Database Schema

#### `user_personas` Table

Stores the main persona data for each user:

```sql
- id (UUID, primary key)
- user_id (UUID, foreign key to auth.users)
- personality_traits (JSONB) - Communication preferences, motivation style
- communication_style (JSONB) - Formal/casual, detailed/brief preferences
- motivation_style (JSONB) - What motivates the user (competition, encouragement, etc.)
- learning_preferences (JSONB) - Visual, step-by-step, etc.
- fitness_persona (JSONB) - Exercise preferences, limitations, goals
- nutrition_persona (JSONB) - Dietary preferences, restrictions, habits
- goal_orientation (JSONB) - How user approaches goals
- activity_patterns (JSONB) - When and how user is most active
- engagement_patterns (JSONB) - How user interacts with the app
- success_factors (JSONB) - What helps user succeed
- challenges (JSONB) - Common obstacles and difficulties
- confidence_score (NUMERIC) - AI confidence in persona accuracy
- last_analysis_date (TIMESTAMP)
- total_interactions (INTEGER)
```

#### `persona_insights` Table

Tracks specific observations about users:

```sql
- id (UUID, primary key)
- user_id (UUID, foreign key)
- insight_type (TEXT) - 'preference', 'behavior', 'goal', 'challenge'
- insight_category (TEXT) - 'fitness', 'nutrition', 'motivation', 'communication'
- insight_text (TEXT) - The actual insight
- confidence_score (NUMERIC) - How confident the AI is
- source_interaction (TEXT) - What triggered this insight
- supporting_data (JSONB) - Additional context
- is_active (BOOLEAN) - Whether insight is still relevant
- verified_by_user (BOOLEAN) - User confirmation
```

#### `persona_updates` Table

Tracks how personas evolve over time:

```sql
- id (UUID, primary key)
- user_id (UUID, foreign key)
- update_type (TEXT) - Type of update made
- field_updated (TEXT) - Which persona field changed
- previous_value (JSONB) - Old value
- new_value (JSONB) - New value
- trigger_event (TEXT) - What caused the update
- ai_reasoning (TEXT) - Why AI made this change
- confidence_score (NUMERIC)
```

### AI Integration

#### Persona-Aware Responses

The AI system:

1. Retrieves user persona before generating responses
2. Includes persona context in the system prompt
3. Tailors communication style and recommendations
4. Updates persona based on new interactions

#### New AI Tools

**`update_persona_insight`**

- Adds specific insights about user behavior/preferences
- Categories: fitness, nutrition, motivation, communication, lifestyle
- Confidence scoring for insight reliability

**`update_persona_trait`**

- Updates broader personality traits and patterns
- Categories: personality_traits, communication_style, motivation_style, etc.
- Includes AI reasoning for transparency

### Example Persona Data

```json
{
  "personality_traits": {
    "motivation_type": "intrinsic",
    "goal_orientation": "process_focused",
    "communication_preference": "encouraging",
    "detail_level": "moderate"
  },
  "fitness_persona": {
    "preferred_workout_time": "morning",
    "exercise_types": ["strength_training", "yoga"],
    "intensity_preference": "moderate",
    "equipment_access": ["home_gym", "bodyweight"]
  },
  "nutrition_persona": {
    "meal_prep_style": "batch_cooking",
    "dietary_approach": "flexible",
    "cooking_skill": "intermediate",
    "time_constraints": "busy_weekdays"
  }
}
```

## Implementation Benefits

### For Users

- **Highly Personalized Advice**: AI understands individual preferences and adapts
- **Improved Engagement**: Communication style matches user preferences
- **Better Results**: Recommendations align with user's lifestyle and constraints
- **Learning System**: Gets better over time as it learns more about the user

### For the App

- **Increased Retention**: More relevant and engaging interactions
- **Better Outcomes**: Users more likely to follow personalized advice
- **Data Insights**: Understanding user patterns and preferences
- **Competitive Advantage**: Truly personalized AI coaching

## Privacy & Security

- **Row Level Security**: Users can only access their own persona data
- **User Control**: Users can view and modify their persona insights
- **Data Transparency**: Clear reasoning for AI decisions
- **Opt-out Options**: Users can disable persona features if desired

## Future Enhancements

1. **Persona Visualization**: Show users their AI-generated persona
2. **Manual Corrections**: Allow users to correct AI assumptions
3. **Persona Sharing**: Optional sharing with trainers or nutritionists
4. **Advanced Analytics**: Insights into user behavior patterns
5. **Predictive Modeling**: Anticipate user needs and challenges

## Getting Started

The persona system is automatically enabled for all users. The AI will:

1. Start building a persona from the first interaction
2. Continuously refine understanding through conversations
3. Use persona data to provide increasingly personalized advice
4. Allow users to view and manage their persona data

This creates a truly personalized AI coaching experience that adapts to each user's unique needs, preferences, and goals.

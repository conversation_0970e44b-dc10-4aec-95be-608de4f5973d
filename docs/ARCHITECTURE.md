# System Architecture: PlateMotion

This document provides a high-level visual overview of the PlateMotion system architecture. It illustrates the relationships and data flows between the client applications, the backend infrastructure, and external services.

## Architecture Diagram

```mermaid
graph TD
    subgraph "End Users"
        User(Mobile App User)
        Admin(Administrator)
    end

    subgraph "Client Applications"
        MobileApp[Expo Mobile App (iOS/Android)]
        AdminPanel[Next.js Admin Panel (Web)]
    end

    subgraph "Deployment & Hosting"
        Netlify(Netlify Hosting)
        EAS(Expo Application Services)
    end

    subgraph "Backend Platform (Supabase)"
        SupabaseAuth[Supabase Auth]
        SupabaseAPI[API Layer (Edge Functions)]
        SupabaseDB[(PostgreSQL Database)]
        SupabaseStorage[(File Storage for Videos)]
    end

    subgraph "External Services"
        GeminiAPI[Google AI - Gemini API]
        PaymentGateways(Payment Providers e.g., Stripe)
    end

    %% User to Client Interactions
    User --> MobileApp
    Admin --> AdminPanel

    %% Client to Backend Interactions
    MobileApp --> SupabaseAuth
    MobileApp --> SupabaseAPI
    AdminPanel --> SupabaseAuth
    AdminPanel --> SupabaseAPI

    %% Backend Internal & External Interactions
    SupabaseAPI --> SupabaseDB
    SupabaseAPI --> SupabaseStorage
    SupabaseAPI --> GeminiAPI
    SupabaseAPI --> PaymentGateways

    %% Deployment Flow
    AdminPanel -- Deployed to --> Netlify
    MobileApp -- Built & Deployed via --> EAS
```

## Component Breakdown

1.  **End Users:**
    - **Mobile App User:** The primary consumer of the PlateMotion service. They interact with the Expo application.
    - **Administrator:** A member of the PlateMotion team who manages the application via the Next.js Admin Panel.

2.  **Client Applications:**
    - **Expo Mobile App:** The cross-platform application for iOS and Android. It handles the entire user-facing experience, from onboarding to daily plan interaction.
    - **Next.js Admin Panel:** The web-based portal for administrators to manage users, content, subscriptions, and support.

3.  **Backend Platform (Supabase):**
    - **Supabase Auth:** Manages all user authentication (signup, login, password reset).
    - **API Layer (Edge Functions):** This is the core of our backend logic. These serverless functions handle requests from the clients, interact with the database, and call external services like the Gemini API.
    - **PostgreSQL Database:** The primary data store for the application, following the schema defined in `DATABASE_SCHEMA.md`.
    - **File Storage:** Used to host and serve the in-house workout videos.

4.  **External Services:**
    - **Gemini API:** The AI engine that powers the personalized plan generation.
    - **Payment Providers:** Services like Stripe, the Apple App Store, and the Google Play Store will handle all subscription and payment processing.

5.  **Deployment & Hosting:**
    - **Netlify:** Hosts the static assets and serverless functions for the Next.js Admin Panel.
    - **Expo Application Services (EAS):** Manages the build and submission process for the mobile app to the respective app stores.

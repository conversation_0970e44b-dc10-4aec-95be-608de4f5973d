# API Design: PlateMotion MVP (v1.0)

This document outlines the design for the RESTful API that will be built using Supabase Edge Functions. This API will serve as the bridge between the client applications (mobile, admin panel) and the Supabase backend.

## Base URL

All API routes will be prefixed with `/api/v1`.

---

## 1. Authentication & User Profile

Authentication itself will be handled by the Supabase client libraries (`supabase-js`). These endpoints are for managing the user's profile data, which extends the built-in `auth.users` table.

### `POST /api/v1/profiles`

- **Description:** Creates a new user profile after successful signup. This is typically called at the end of the onboarding flow.
- **User Stories:** 3, 4, 5, 6, 7
- **Request Body:**
  ```json
  {
    "full_name": "<PERSON>",
    "age": 30,
    "height_cm": 170,
    "weight_kg": 65,
    "goals": ["weight_loss"],
    "experience_level": "beginner",
    "ai_persona_preference": "motivator",
    "dietary_restrictions": ["gluten_free"],
    "food_allergies": ["peanuts"],
    "budget_preference": "medium",
    "known_injuries": ["right_knee"]
  }
  ```
- **Response (201 Created):** Returns the newly created profile object.

### `GET /api/v1/profiles/me`

- **Description:** Retrieves the profile for the currently authenticated user.
- **Request Body:** None
- **Response (200 OK):** Returns the user's full profile object.

---

## 2. Plans (Workout & Meal)

### `GET /api/v1/plans/daily`

- **Description:** Retrieves the workout and/or meal plan for a specific day for the authenticated user. If a plan for that day doesn't exist, the AI will be triggered to generate and save one before returning it.
- **User Stories:** 8, 9, 10
- **Query Parameters:**
  - `date` (string, format: `YYYY-MM-DD`)
- **Response (200 OK):**
  ```json
  {
    "date": "2025-08-01",
    "workout_plan": {
      "id": "workout-plan-uuid",
      "exercises": [
        {
          "exercise_id": "...",
          "name": "Push Ups",
          "sets": 3,
          "reps": 10,
          "video_url": "..."
        },
        {
          "exercise_id": "...",
          "name": "Squats",
          "sets": 3,
          "reps": 12,
          "video_url": "..."
        }
      ]
    },
    "meal_plan": {
      "id": "meal-plan-uuid",
      "recipes": [
        {
          "meal_type": "breakfast",
          "recipe_id": "...",
          "name": "Oatmeal with Berries"
        },
        {
          "meal_type": "lunch",
          "recipe_id": "...",
          "name": "Grilled Chicken Salad"
        }
      ]
    }
  }
  ```

---

## 3. Feedback

### `POST /api/v1/feedback`

- **Description:** Allows a user to submit feedback (thumbs down) for a piece of content.
- **User Story:** 11
- **Request Body:**
  ```json
  {
    "content_id": "exercise-or-recipe-uuid",
    "content_type": "exercise", // or "recipe"
    "is_liked": false
  }
  ```
- **Response (201 Created):** Returns the created feedback record.

### `POST /api/v1/feedback/alternative`

- **Description:** Requests an alternative for a disliked exercise within a specific workout plan.
- **User Story:** 12
- **Request Body:**
  ```json
  {
    "workout_plan_exercise_id": "workout-plan-exercise-join-table-uuid",
    "original_exercise_id": "exercise-uuid-to-replace"
  }
  ```
- **Response (200 OK):** Returns the new exercise object that will replace the old one in the user's plan.

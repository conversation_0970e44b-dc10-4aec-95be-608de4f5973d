# User Stories: PlateMotion MVP (v1.0)

This document breaks down the MVP features into actionable user stories. Each story represents a specific piece of functionality from the user's perspective.

## Epic: User Authentication & Onboarding

- **Story 1 (Auth):** As a new user, I can create an account using my email and a password so that my data is secure.
- **Story 2 (Auth):** As a returning user, I can log in with my email and password to access my account.
- **Story 3 (Onboarding - General):** As a first-time user after signup, I am prompted to complete a general profile questionnaire (age, height, weight, gender, primary goals) so the AI can establish a baseline.
- **Story 4 (Onboarding - Branching):** As a user completing my general profile, I can choose whether I want personalized workout plans, meal plans, or both.
- **Story 5 (Onboarding - Workout):** As a user who wants workout plans, I am guided through a workout-specific questionnaire (experience level, known injuries, preferred exercise types, available equipment).
- **Story 6 (Onboarding - Nutrition):** As a user who wants meal plans, I am guided through a nutrition-specific questionnaire (dietary preferences, allergies, budget, cuisine types, cooking difficulty).
- **Story 7 (Onboarding - AI Persona):** As a final step in onboarding, I can choose the personality of my AI coach (e.g., '<PERSON><PERSON><PERSON><PERSON>', 'Drill Sergeant', 'Nutritionist') to personalize my experience.

## Epic: Core AI & Plan Interaction

- **Story 8 (Plan Generation):** As a user who has completed onboarding, I can view my first AI-generated weekly plan (workouts and/or meals) on my dashboard.
- **Story 9 (Workout View):** As a user, I can tap on a day's workout to see a detailed list of exercises, including sets, reps, and a link to the instructional video.
- **Story 10 (Meal View):** As a user, I can tap on a day's meal to see the recipe, including ingredients and instructions.
- **Story 11 (Feedback - Negative):** As a user viewing a workout or meal, I can give it a 'thumbs down' to signal that I don't like it or can't do it.
- **Story 12 (Feedback - Alternative):** As a user who gives an exercise a 'thumbs down', the AI immediately suggests a suitable alternative exercise for that workout.
- **Story 13 (Feedback - History):** As a user, I can access a settings screen where I can view and manage all the exercises and recipes I have 'thumbed down'.

## Epic: Admin Panel (MVP)

- **Story 14 (Admin - Login):** As an admin, I can log in to a secure web portal.
- **Story 15 (Admin - User Management):** As an admin, I can view a list of all registered users and see their basic profile information.
- **Story 16 (Admin - Content Management):** As an admin, I can upload and manage workout videos and their associated metadata (name, muscle group, etc.).
- **Story 17 (Admin - Recipe Review):** As an admin, I can view AI-generated recipes and approve them by marking them as 'human-reviewed'.

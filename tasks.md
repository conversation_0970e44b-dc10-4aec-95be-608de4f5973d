# Tasks

## Done (2025-08-11)

- Fix CI lint errors in @platemotion/admin: removed unused imports/vars, escaped JSX entities, tightened types in TicketFilters, adjusted Calendar icon components
- Verified `pnpm lint` passes at root (turbo lint): admin + mobile OK

## Next

- Optional: Address remaining warnings (no-explicit-any in tests and services, react-hooks/exhaustive-deps in KB/Support pages, next/no-img-element in nutrition)
- Fix remaining admin lint warnings: completed; added ESLint override for tests, replaced <img> with Image in nutrition, refined service types, ensured hooks deps are safe

- Enforce strict linting in CI and pre-commit: completed; added lint:strict scripts and CI step, updated pre-commit hook

- Optional: Replace <img> with Next <Image> in nutrition/page.tsx for LCP
- Optional: Type tightening in services (remove any casts) and tests

## Done (2025-08-11)

- **Code Quality Cleanup**: Achieved 9.5/10 code quality score with comprehensive formatting and linting fixes
- **Prettier Formatting**: Applied consistent code formatting across entire codebase (mobile + admin)
- **ESLint Configuration**: Set up strict Next.js ESLint rules for admin panel with zero errors
- **Configuration Cleanup**: Removed deprecated npm settings and fixed all configuration warnings
- **Code Standards**: Established consistent TypeScript, React, and documentation formatting standards

## Done (2025-01-11)

- **Flexible Meal Plan Duration System**: Implemented custom duration input (1-21 days) replacing fixed preset options
- **Comprehensive Testing**: Added 19 test cases for duration validation with 100% coverage
- **Enhanced UX**: Real-time validation, preset buttons, visual feedback, and user-friendly error messages
- **Technical Integration**: Updated navigation types, modal component, and meal planning infrastructure

## Done (2025-08-10)

- Edit Profile polish: validation, optimistic cache update, success toast, navigate back
- Fix repo-wide TypeScript errors (mobile): AI Coach, Habits/Home navigation typing, Onboarding setter, Privacy handler ref, Profile icon import, Progress SDK 53 compatibility, vector DB service cleanups, shared ErrorBoundary typing
- Prettier + ESLint + TypeScript checks on mobile app

- Start meal photo logging MVP: wire meal analyzer stub and persistence

## Next

- Implement international units: questionnaire + settings + normalized storage [done]
- Add unit tests for measurement utils; component tests for height/weight inline controls [done]
- Prepare PR: format, test, push branch, open PR to main, monitor CI [in progress]

- Analytics scaffold (no-op) and instrumentation: App tabs view, AI Coach tab open, chat send, quick actions, progress logged

- Monitor CI on PR feat/chatbot-progress-logging; address any regressions
- Optional: server-side increments for vectorDatabaseService (RPC/trigger) to handle numeric fields (success_rate, correction_count)
- Optional: tighten types around AI tool responses and message DTOs; add unit tests for toolRegistry wrappers
- Optional: add tests for Edit Profile validation logic and navigation behavior

-- =============================================
-- PLATEMOTION WORKOUT DATABASE SCHEMA
-- =============================================

-- Individual exercises library
CREATE TABLE exercises (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Exercise Classification
    category VARCHAR(50) NOT NULL, -- 'strength', 'cardio', 'flexibility', 'balance', 'plyometric'
    primary_muscle_groups TEXT[] NOT NULL, -- ['chest', 'shoulders', 'triceps']
    secondary_muscle_groups TEXT[] DEFAULT '{}',
    movement_pattern VARCHAR(50), -- 'push', 'pull', 'squat', 'hinge', 'lunge', 'carry'
    
    -- Difficulty and Requirements
    difficulty_level VARCHAR(20) CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    equipment_required TEXT[] DEFAULT '{}', -- ['dumbbells', 'barbell', 'bench', 'bodyweight']
    space_required VARCHAR(50), -- 'minimal', 'small', 'large', 'gym'
    
    -- Exercise Specifications
    is_unilateral BOOLEAN DEFAULT FALSE, -- Single arm/leg exercises
    is_compound BOOLEAN DEFAULT TRUE, -- Multi-joint vs isolation
    force_type VARCHAR(20), -- 'push', 'pull', 'static'
    
    -- Instructions and Media
    setup_instructions TEXT,
    execution_instructions TEXT,
    breathing_cues TEXT,
    common_mistakes TEXT,
    modifications TEXT, -- Easier/harder variations
    
    -- Media
    demonstration_video_url TEXT,
    image_url TEXT,
    animation_url TEXT,
    
    -- Safety and Notes
    safety_notes TEXT,
    contraindications TEXT, -- When NOT to do this exercise
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES auth.users(id)
);

-- Complete workout templates
CREATE TABLE workouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Workout Classification
    workout_type VARCHAR(50) NOT NULL, -- 'strength', 'cardio', 'hiit', 'yoga', 'pilates', 'circuit'
    target_muscle_groups TEXT[] DEFAULT '{}', -- ['upper-body', 'lower-body', 'core', 'full-body']
    fitness_goals TEXT[] DEFAULT '{}', -- ['weight-loss', 'muscle-gain', 'endurance', 'strength', 'flexibility']
    
    -- Time and Intensity
    estimated_duration_minutes INTEGER NOT NULL,
    intensity_level VARCHAR(20) CHECK (intensity_level IN ('low', 'moderate', 'high', 'very-high')),
    difficulty_level VARCHAR(20) CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    
    -- Requirements
    equipment_required TEXT[] DEFAULT '{}',
    space_required VARCHAR(50),
    
    -- Workout Structure
    warm_up_duration_minutes INTEGER DEFAULT 5,
    cool_down_duration_minutes INTEGER DEFAULT 5,
    rest_between_exercises_seconds INTEGER DEFAULT 60,
    rest_between_sets_seconds INTEGER DEFAULT 90,
    
    -- Progression and Adaptation
    progression_notes TEXT,
    regression_notes TEXT,
    
    -- Media and Attribution
    image_url TEXT,
    video_url TEXT,
    source_attribution TEXT,
    
    -- User Engagement
    rating_average DECIMAL(3,2) DEFAULT 0.00,
    rating_count INTEGER DEFAULT 0,
    completion_count INTEGER DEFAULT 0,
    favorite_count INTEGER DEFAULT 0,
    
    -- System
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES auth.users(id)
);

-- Exercises within workouts (workout structure)
CREATE TABLE workout_exercises (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workout_id UUID REFERENCES workouts(id) ON DELETE CASCADE,
    exercise_id UUID REFERENCES exercises(id),
    
    -- Exercise Order and Grouping
    exercise_order INTEGER NOT NULL,
    superset_group INTEGER, -- Exercises with same number are supersetted
    circuit_group INTEGER, -- For circuit training
    
    -- Sets and Reps
    sets INTEGER NOT NULL DEFAULT 3,
    reps_min INTEGER, -- For rep ranges like 8-12
    reps_max INTEGER,
    reps_target INTEGER, -- For fixed reps
    
    -- Weight and Resistance
    weight_percentage DECIMAL(5,2), -- % of 1RM or bodyweight
    resistance_level VARCHAR(20), -- 'light', 'medium', 'heavy' for bands
    
    -- Time-based parameters
    duration_seconds INTEGER, -- For planks, wall sits, etc.
    tempo VARCHAR(20), -- '3-1-2-1' (eccentric-pause-concentric-pause)
    
    -- Rest periods
    rest_after_seconds INTEGER,
    
    -- Instructions and Notes
    specific_instructions TEXT,
    coaching_cues TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User workout programs (multiple workouts over time)
CREATE TABLE workout_programs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Program Structure
    duration_weeks INTEGER NOT NULL,
    workouts_per_week INTEGER NOT NULL,
    program_type VARCHAR(50), -- 'strength', 'weight-loss', 'muscle-gain', 'endurance'
    
    -- Progression
    progression_strategy VARCHAR(50), -- 'linear', 'periodized', 'undulating'
    deload_frequency_weeks INTEGER DEFAULT 4,
    
    -- Program Configuration
    target_goals TEXT[] DEFAULT '{}',
    available_equipment TEXT[] DEFAULT '{}',
    max_workout_duration_minutes INTEGER,
    
    -- Metadata
    generated_by_ai BOOLEAN DEFAULT FALSE,
    ai_prompt TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Individual workouts scheduled in programs
CREATE TABLE program_workouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    program_id UUID REFERENCES workout_programs(id) ON DELETE CASCADE,
    workout_id UUID REFERENCES workouts(id),
    
    -- Scheduling
    week_number INTEGER NOT NULL,
    day_of_week INTEGER CHECK (day_of_week BETWEEN 0 AND 6),
    workout_order INTEGER, -- If multiple workouts per day
    
    -- Customization
    intensity_modifier DECIMAL(3,2) DEFAULT 1.00, -- 0.8 = 80% intensity
    duration_modifier DECIMAL(3,2) DEFAULT 1.00,
    notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User workout sessions (actual completed workouts)
CREATE TABLE workout_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    workout_id UUID REFERENCES workouts(id),
    program_id UUID REFERENCES workout_programs(id), -- Optional: if part of program
    
    -- Session Details
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER,
    
    -- Performance
    perceived_exertion INTEGER CHECK (perceived_exertion BETWEEN 1 AND 10), -- RPE scale
    energy_level INTEGER CHECK (energy_level BETWEEN 1 AND 5),
    mood_rating INTEGER CHECK (mood_rating BETWEEN 1 AND 5),
    
    -- Session Data
    exercises_completed INTEGER DEFAULT 0,
    total_exercises INTEGER,
    calories_burned INTEGER,
    
    -- Notes
    session_notes TEXT,
    modifications_made TEXT,
    
    -- Status
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('completed', 'partial', 'skipped')),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual exercise performance within sessions
CREATE TABLE session_exercises (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES workout_sessions(id) ON DELETE CASCADE,
    exercise_id UUID REFERENCES exercises(id),
    workout_exercise_id UUID REFERENCES workout_exercises(id), -- Links to planned exercise
    
    -- Performance Data
    sets_completed INTEGER NOT NULL,
    reps_achieved INTEGER[],
    weights_used DECIMAL(6,2)[],
    duration_seconds INTEGER,
    distance_meters DECIMAL(8,2),
    
    -- Subjective measures
    difficulty_rating INTEGER CHECK (difficulty_rating BETWEEN 1 AND 5),
    form_rating INTEGER CHECK (form_rating BETWEEN 1 AND 5),
    
    -- Notes
    performance_notes TEXT,
    
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_exercises_category ON exercises(category);
CREATE INDEX idx_exercises_muscle_groups ON exercises USING GIN(primary_muscle_groups);
CREATE INDEX idx_exercises_equipment ON exercises USING GIN(equipment_required);
CREATE INDEX idx_exercises_difficulty ON exercises(difficulty_level);

CREATE INDEX idx_workouts_type ON workouts(workout_type);
CREATE INDEX idx_workouts_duration ON workouts(estimated_duration_minutes);
CREATE INDEX idx_workouts_difficulty ON workouts(difficulty_level);
CREATE INDEX idx_workouts_goals ON workouts USING GIN(fitness_goals);

CREATE INDEX idx_workout_sessions_user_date ON workout_sessions(user_id, started_at);
CREATE INDEX idx_program_workouts_schedule ON program_workouts(program_id, week_number, day_of_week);

-- Full-text search
CREATE INDEX idx_exercises_search ON exercises USING GIN(to_tsvector('english', name || ' ' || COALESCE(description, '')));
CREATE INDEX idx_workouts_search ON workouts USING GIN(to_tsvector('english', name || ' ' || COALESCE(description, '')));

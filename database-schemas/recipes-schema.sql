-- =============================================
-- PLATEMOTION RECIPE DATABASE SCHEMA
-- =============================================

-- Core recipes table
CREATE TABLE recipes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    cuisine_type VARCHAR(100), -- 'italian', 'mexican', 'asian', 'american', etc.

    -- Time & Difficulty (CRITICAL)
    prep_time_minutes INTEGER NOT NULL,
    cook_time_minutes INTEGER NOT NULL,
    total_time_minutes INTEGER GENERATED ALWAYS AS (prep_time_minutes + cook_time_minutes) STORED,
    active_time_minutes INTEGER, -- Hands-on time vs passive waiting time
    difficulty_level VARCHAR(20) CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    servings INTEGER NOT NULL DEFAULT 4,
    serving_size VARCHAR(100), -- '1 cup', '1 piece', '1 bowl'

    -- Detailed Ingredients (CRITICAL)
    ingredients JSONB NOT NULL, -- [{"name": "chicken breast", "amount": "1", "unit": "lb", "category": "meat", "optional": false, "substitutes": ["turkey breast"]}]
    equipment_required TEXT[] DEFAULT '{}', -- ['oven', 'blender', 'food-processor']
    equipment_alternatives JSONB, -- [{"required": "blender", "alternatives": ["food processor", "immersion blender"]}]
    pantry_staples TEXT[] DEFAULT '{}', -- ['salt', 'pepper', 'olive oil'] - common ingredients

    -- Enhanced Nutritional Information (per serving)
    calories_per_serving INTEGER,
    protein_grams DECIMAL(5,2),
    carbs_grams DECIMAL(5,2),
    fat_grams DECIMAL(5,2),
    fiber_grams DECIMAL(5,2),
    sugar_grams DECIMAL(5,2),
    sodium_mg DECIMAL(7,2),
    saturated_fat_grams DECIMAL(5,2),
    cholesterol_mg DECIMAL(6,2),

    -- Micronutrients (optional advanced nutrition)
    vitamin_c_mg DECIMAL(6,2),
    iron_mg DECIMAL(5,2),
    calcium_mg DECIMAL(6,2),
    potassium_mg DECIMAL(7,2),

    -- Budget Information
    estimated_cost_per_serving DECIMAL(5,2),
    cost_category VARCHAR(20) CHECK (cost_category IN ('budget', 'moderate', 'premium')),

    -- Dietary & Allergy Information (CRITICAL)
    dietary_tags TEXT[] DEFAULT '{}', -- ['vegetarian', 'vegan', 'gluten-free', 'dairy-free', 'keto', 'paleo', 'low-carb', 'high-protein']
    allergens TEXT[] DEFAULT '{}', -- ['nuts', 'dairy', 'eggs', 'shellfish', 'soy', 'wheat', 'fish']
    meal_types TEXT[] DEFAULT '{}', -- ['breakfast', 'lunch', 'dinner', 'snack', 'dessert', 'appetizer']
    cooking_methods TEXT[] DEFAULT '{}', -- ['baking', 'grilling', 'stovetop', 'slow-cooker', 'no-cook', 'air-fryer']

    -- Meal Planning Specific (IMPORTANT)
    seasonality TEXT[] DEFAULT '{}', -- ['spring', 'summer', 'fall', 'winter', 'year-round']
    storage_instructions TEXT, -- 'Refrigerate up to 3 days, freezer-friendly'
    reheating_instructions TEXT,
    meal_prep_friendly BOOLEAN DEFAULT FALSE,
    make_ahead_instructions TEXT,
    batch_cooking_notes TEXT,
    leftover_suggestions TEXT,

    -- Flavor & Experience Profile
    spice_level VARCHAR(20) CHECK (spice_level IN ('none', 'mild', 'medium', 'hot', 'very-hot')),
    flavor_profile TEXT[] DEFAULT '{}', -- ['sweet', 'savory', 'umami', 'tangy', 'spicy', 'creamy', 'fresh']
    texture_profile TEXT[] DEFAULT '{}', -- ['creamy', 'crunchy', 'chewy', 'smooth', 'crispy', 'tender']
    temperature_served VARCHAR(20) CHECK (temperature_served IN ('hot', 'warm', 'room-temp', 'cold', 'frozen')),

    -- Occasion & Context Tags
    occasion_tags TEXT[] DEFAULT '{}', -- ['kid-friendly', 'date-night', 'party', 'comfort-food', 'healthy', 'indulgent']
    energy_level VARCHAR(20) CHECK (energy_level IN ('light', 'moderate', 'heavy')),
    time_of_day_best TEXT[] DEFAULT '{}', -- ['morning', 'afternoon', 'evening', 'late-night']

    -- Health & Wellness
    glycemic_index VARCHAR(20) CHECK (glycemic_index IN ('low', 'medium', 'high')),
    health_benefits TEXT[] DEFAULT '{}', -- ['anti-inflammatory', 'heart-healthy', 'gut-friendly', 'immune-boosting']

    -- Detailed Instructions
    instructions JSONB NOT NULL, -- [{"step": 1, "instruction": "Preheat oven to 375°F", "time_minutes": 5, "temperature": "375°F", "tips": "Use convection if available"}]
    prep_instructions TEXT, -- Detailed prep steps
    cooking_tips TEXT,
    troubleshooting_tips TEXT,
    common_mistakes TEXT,

    -- Scaling Information
    scales_well BOOLEAN DEFAULT TRUE,
    scaling_notes TEXT, -- 'Double cooking time when doubling recipe'
    min_servings INTEGER DEFAULT 1,
    max_servings INTEGER DEFAULT 20,

    -- Media and Attribution
    image_url TEXT,
    video_url TEXT,
    animation_url TEXT, -- For technique demonstrations
    source_url TEXT,
    source_attribution TEXT,
    chef_name VARCHAR(255),

    -- User Engagement
    rating_average DECIMAL(3,2) DEFAULT 0.00,
    rating_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    favorite_count INTEGER DEFAULT 0,
    completion_count INTEGER DEFAULT 0, -- How many users successfully made it
    success_rate DECIMAL(3,2) DEFAULT 0.00, -- completion_count / attempt_count

    -- System
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES auth.users(id),
    last_reviewed_at TIMESTAMP WITH TIME ZONE,
    review_status VARCHAR(20) DEFAULT 'pending' CHECK (review_status IN ('pending', 'approved', 'rejected'))
);

-- Recipe nutritional breakdown (for detailed tracking)
CREATE TABLE recipe_nutrition_details (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,

    -- Detailed macronutrient breakdown
    total_carbs_grams DECIMAL(5,2),
    net_carbs_grams DECIMAL(5,2), -- total_carbs - fiber
    added_sugars_grams DECIMAL(5,2),
    polyunsaturated_fat_grams DECIMAL(5,2),
    monounsaturated_fat_grams DECIMAL(5,2),
    trans_fat_grams DECIMAL(5,2),

    -- Additional vitamins and minerals
    vitamin_a_iu INTEGER,
    vitamin_d_iu INTEGER,
    vitamin_e_mg DECIMAL(5,2),
    vitamin_k_mcg DECIMAL(6,2),
    thiamin_mg DECIMAL(5,2),
    riboflavin_mg DECIMAL(5,2),
    niacin_mg DECIMAL(5,2),
    vitamin_b6_mg DECIMAL(5,2),
    folate_mcg DECIMAL(6,2),
    vitamin_b12_mcg DECIMAL(6,2),
    magnesium_mg DECIMAL(6,2),
    phosphorus_mg DECIMAL(6,2),
    zinc_mg DECIMAL(5,2),
    selenium_mcg DECIMAL(6,2),

    -- Calculated nutritional scores
    nutrition_density_score DECIMAL(3,2), -- Overall nutritional value
    protein_quality_score DECIMAL(3,2), -- Amino acid completeness

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recipe ingredient substitutions (for dietary modifications)
CREATE TABLE recipe_substitutions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,
    original_ingredient VARCHAR(255) NOT NULL,
    substitute_ingredient VARCHAR(255) NOT NULL,
    substitution_ratio VARCHAR(50), -- '1:1', '2:1', '1/2 cup per 1 cup'
    dietary_benefit VARCHAR(100), -- 'gluten-free', 'vegan', 'lower-calorie'
    taste_impact VARCHAR(20) CHECK (taste_impact IN ('none', 'minimal', 'moderate', 'significant')),
    texture_impact VARCHAR(20) CHECK (texture_impact IN ('none', 'minimal', 'moderate', 'significant')),
    notes TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User meal plans
CREATE TABLE meal_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    week_start_date DATE NOT NULL,
    
    -- Plan Configuration
    target_calories_per_day INTEGER,
    target_protein_grams INTEGER,
    target_carbs_grams INTEGER,
    target_fat_grams INTEGER,
    
    -- Preferences used for generation
    dietary_preferences JSONB, -- User preferences at time of generation
    excluded_ingredients TEXT[] DEFAULT '{}',
    preferred_cuisines TEXT[] DEFAULT '{}',
    max_prep_time_minutes INTEGER,
    
    -- Metadata
    generated_by_ai BOOLEAN DEFAULT FALSE,
    ai_prompt TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- Individual meals in meal plans
CREATE TABLE meal_plan_meals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    meal_plan_id UUID REFERENCES meal_plans(id) ON DELETE CASCADE,
    recipe_id UUID REFERENCES recipes(id),
    
    -- Scheduling
    day_of_week INTEGER CHECK (day_of_week BETWEEN 0 AND 6), -- 0 = Sunday
    meal_type VARCHAR(20) CHECK (meal_type IN ('breakfast', 'lunch', 'dinner', 'snack')),
    
    -- Customization
    servings DECIMAL(4,2) DEFAULT 1.00,
    notes TEXT,
    
    -- Nutritional totals (calculated)
    total_calories INTEGER,
    total_protein_grams DECIMAL(5,2),
    total_carbs_grams DECIMAL(5,2),
    total_fat_grams DECIMAL(5,2),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Grocery lists generated from meal plans
CREATE TABLE grocery_lists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    meal_plan_id UUID REFERENCES meal_plans(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    
    -- List data
    ingredients JSONB NOT NULL, -- [{"name": "chicken breast", "amount": "2 lbs", "category": "meat", "checked": false}]
    
    -- Organization
    organized_by_category BOOLEAN DEFAULT TRUE,
    estimated_cost DECIMAL(8,2),
    store_preference VARCHAR(100),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_completed BOOLEAN DEFAULT FALSE
);

-- User recipe ratings and reviews (ENHANCED)
CREATE TABLE recipe_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,

    -- Overall rating
    rating INTEGER CHECK (rating BETWEEN 1 AND 5),
    review_text TEXT,
    review_title VARCHAR(255),

    -- Detailed ratings
    taste_rating INTEGER CHECK (taste_rating BETWEEN 1 AND 5),
    difficulty_rating INTEGER CHECK (difficulty_rating BETWEEN 1 AND 5),
    value_rating INTEGER CHECK (value_rating BETWEEN 1 AND 5), -- Worth the cost/effort
    instruction_clarity_rating INTEGER CHECK (instruction_clarity_rating BETWEEN 1 AND 5),

    -- Experience feedback
    would_make_again BOOLEAN,
    actual_prep_time_minutes INTEGER, -- How long it actually took them
    actual_cook_time_minutes INTEGER,
    actual_difficulty VARCHAR(20) CHECK (actual_difficulty IN ('easier', 'as-expected', 'harder')),

    -- Modifications made
    modifications_made TEXT,
    substitutions_used TEXT,
    serving_size_made INTEGER,

    -- Results
    turned_out_as_expected BOOLEAN,
    family_liked_it BOOLEAN,

    -- Media
    user_photo_url TEXT, -- User's photo of their result

    -- Helpfulness tracking
    helpful_votes INTEGER DEFAULT 0,
    total_votes INTEGER DEFAULT 0,

    -- System
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_verified_purchase BOOLEAN DEFAULT FALSE, -- If they bought ingredients through app

    UNIQUE(recipe_id, user_id)
);

-- Recipe cooking attempts (track success/failure rates)
CREATE TABLE recipe_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,

    -- Attempt details
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'in-progress' CHECK (status IN ('in-progress', 'completed', 'abandoned')),

    -- Timing
    actual_prep_time_minutes INTEGER,
    actual_cook_time_minutes INTEGER,

    -- Experience
    difficulty_experienced VARCHAR(20) CHECK (difficulty_experienced IN ('easy', 'moderate', 'hard')),
    issues_encountered TEXT,
    modifications_made TEXT,

    -- Results
    satisfaction_level INTEGER CHECK (satisfaction_level BETWEEN 1 AND 5),
    would_attempt_again BOOLEAN,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Comprehensive indexes for performance (CRITICAL for fast AI queries)
-- Basic recipe filtering
CREATE INDEX idx_recipes_cuisine_type ON recipes(cuisine_type);
CREATE INDEX idx_recipes_difficulty ON recipes(difficulty_level);
CREATE INDEX idx_recipes_total_time ON recipes(total_time_minutes);
CREATE INDEX idx_recipes_prep_time ON recipes(prep_time_minutes);
CREATE INDEX idx_recipes_servings ON recipes(servings);
CREATE INDEX idx_recipes_active ON recipes(is_active) WHERE is_active = TRUE;
CREATE INDEX idx_recipes_featured ON recipes(is_featured) WHERE is_featured = TRUE;

-- Array-based filtering (GIN indexes for fast array operations)
CREATE INDEX idx_recipes_dietary_tags ON recipes USING GIN(dietary_tags);
CREATE INDEX idx_recipes_allergens ON recipes USING GIN(allergens);
CREATE INDEX idx_recipes_meal_types ON recipes USING GIN(meal_types);
CREATE INDEX idx_recipes_cooking_methods ON recipes USING GIN(cooking_methods);
CREATE INDEX idx_recipes_equipment ON recipes USING GIN(equipment_required);
CREATE INDEX idx_recipes_occasion_tags ON recipes USING GIN(occasion_tags);
CREATE INDEX idx_recipes_flavor_profile ON recipes USING GIN(flavor_profile);

-- Nutritional filtering
CREATE INDEX idx_recipes_calories ON recipes(calories_per_serving);
CREATE INDEX idx_recipes_protein ON recipes(protein_grams);
CREATE INDEX idx_recipes_carbs ON recipes(carbs_grams);
CREATE INDEX idx_recipes_cost ON recipes(estimated_cost_per_serving);

-- User engagement and quality
CREATE INDEX idx_recipes_rating ON recipes(rating_average DESC, rating_count DESC);
CREATE INDEX idx_recipes_popularity ON recipes(view_count DESC, favorite_count DESC);
CREATE INDEX idx_recipes_success_rate ON recipes(success_rate DESC);

-- Meal planning indexes
CREATE INDEX idx_meal_plans_user_date ON meal_plans(user_id, week_start_date);
CREATE INDEX idx_meal_plan_meals_plan_day ON meal_plan_meals(meal_plan_id, day_of_week, meal_type);
CREATE INDEX idx_meal_plan_meals_recipe ON meal_plan_meals(recipe_id);

-- Grocery list indexes
CREATE INDEX idx_grocery_lists_user ON grocery_lists(user_id, created_at DESC);
CREATE INDEX idx_grocery_lists_meal_plan ON grocery_lists(meal_plan_id);

-- Review and attempt indexes
CREATE INDEX idx_recipe_reviews_recipe ON recipe_reviews(recipe_id, rating DESC);
CREATE INDEX idx_recipe_reviews_user ON recipe_reviews(user_id, created_at DESC);
CREATE INDEX idx_recipe_attempts_recipe ON recipe_attempts(recipe_id, status, completed_at);
CREATE INDEX idx_recipe_attempts_user ON recipe_attempts(user_id, started_at DESC);

-- Substitution indexes
CREATE INDEX idx_recipe_substitutions_recipe ON recipe_substitutions(recipe_id);
CREATE INDEX idx_recipe_substitutions_ingredient ON recipe_substitutions(original_ingredient);

-- Composite indexes for common AI queries
CREATE INDEX idx_recipes_meal_planning ON recipes(meal_types, dietary_tags, total_time_minutes, difficulty_level)
    WHERE is_active = TRUE;
CREATE INDEX idx_recipes_quick_healthy ON recipes(total_time_minutes, calories_per_serving, rating_average)
    WHERE is_active = TRUE AND 'healthy' = ANY(occasion_tags);
CREATE INDEX idx_recipes_budget_friendly ON recipes(estimated_cost_per_serving, rating_average, total_time_minutes)
    WHERE is_active = TRUE AND cost_category IN ('budget', 'moderate');

-- Full-text search (enhanced)
CREATE INDEX idx_recipes_search ON recipes USING GIN(to_tsvector('english',
    name || ' ' || COALESCE(description, '') || ' ' ||
    COALESCE(chef_name, '') || ' ' ||
    array_to_string(dietary_tags, ' ') || ' ' ||
    array_to_string(occasion_tags, ' ')
));

-- Seasonal and contextual search
CREATE INDEX idx_recipes_seasonal ON recipes USING GIN(seasonality);
CREATE INDEX idx_recipes_spice_level ON recipes(spice_level) WHERE spice_level IS NOT NULL;

-- =============================================
-- HELPER TABLES AND FUNCTIONS
-- =============================================

-- Recipe collections (curated lists by theme)
CREATE TABLE recipe_collections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    theme VARCHAR(100), -- 'quick-weeknight', 'meal-prep-sunday', 'date-night', 'kid-friendly'
    image_url TEXT,
    is_featured BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE recipe_collection_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    collection_id UUID REFERENCES recipe_collections(id) ON DELETE CASCADE,
    recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,
    sort_order INTEGER DEFAULT 0,
    curator_notes TEXT,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(collection_id, recipe_id)
);

-- Recipe tags taxonomy (standardized tags)
CREATE TABLE recipe_tag_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category_name VARCHAR(100) NOT NULL UNIQUE, -- 'dietary', 'cuisine', 'occasion', 'cooking-method'
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0
);

CREATE TABLE recipe_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category_id UUID REFERENCES recipe_tag_categories(id),
    tag_name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    color_hex VARCHAR(7), -- For UI display
    icon_name VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0
);

-- User recipe preferences and restrictions
CREATE TABLE user_recipe_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,

    -- Dietary preferences
    preferred_cuisines TEXT[] DEFAULT '{}',
    dietary_restrictions TEXT[] DEFAULT '{}',
    allergens_to_avoid TEXT[] DEFAULT '{}',

    -- Cooking preferences
    max_prep_time_minutes INTEGER DEFAULT 60,
    max_cook_time_minutes INTEGER DEFAULT 90,
    preferred_difficulty VARCHAR(20) DEFAULT 'intermediate',
    available_equipment TEXT[] DEFAULT '{}',

    -- Taste preferences
    spice_tolerance VARCHAR(20) DEFAULT 'medium',
    preferred_flavors TEXT[] DEFAULT '{}',
    disliked_ingredients TEXT[] DEFAULT '{}',

    -- Lifestyle preferences
    meal_prep_preference BOOLEAN DEFAULT FALSE,
    budget_preference VARCHAR(20) DEFAULT 'moderate',
    health_focus TEXT[] DEFAULT '{}', -- 'weight-loss', 'muscle-gain', 'heart-healthy'

    -- Family considerations
    cooking_for_family BOOLEAN DEFAULT FALSE,
    number_of_people INTEGER DEFAULT 2,
    has_kids BOOLEAN DEFAULT FALSE,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(user_id)
);

-- Recipe recommendation tracking (for AI learning)
CREATE TABLE recipe_recommendations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,

    -- Recommendation context
    recommended_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    recommendation_source VARCHAR(50), -- 'ai-meal-planner', 'similar-recipes', 'trending'
    recommendation_reason TEXT,
    confidence_score DECIMAL(3,2), -- AI confidence in recommendation

    -- User response
    user_action VARCHAR(20), -- 'viewed', 'saved', 'cooked', 'ignored', 'dismissed'
    action_timestamp TIMESTAMP WITH TIME ZONE,
    user_feedback INTEGER CHECK (user_feedback BETWEEN 1 AND 5),

    -- Context at time of recommendation
    meal_type VARCHAR(20),
    day_of_week INTEGER,
    season VARCHAR(20),
    user_preferences_snapshot JSONB
);

-- =============================================
-- UTILITY FUNCTIONS
-- =============================================

-- Function to calculate recipe compatibility score for a user
CREATE OR REPLACE FUNCTION calculate_recipe_compatibility(
    p_recipe_id UUID,
    p_user_id UUID
) RETURNS DECIMAL(3,2) AS $$
DECLARE
    compatibility_score DECIMAL(3,2) := 0.00;
    user_prefs RECORD;
    recipe_data RECORD;
BEGIN
    -- Get user preferences
    SELECT * INTO user_prefs FROM user_recipe_preferences WHERE user_id = p_user_id;

    -- Get recipe data
    SELECT * INTO recipe_data FROM recipes WHERE id = p_recipe_id;

    IF user_prefs IS NULL OR recipe_data IS NULL THEN
        RETURN 0.00;
    END IF;

    -- Calculate compatibility based on various factors
    -- Time compatibility (30% weight)
    IF recipe_data.total_time_minutes <= user_prefs.max_prep_time_minutes + user_prefs.max_cook_time_minutes THEN
        compatibility_score := compatibility_score + 0.30;
    END IF;

    -- Dietary compatibility (40% weight)
    IF NOT (recipe_data.allergens && user_prefs.allergens_to_avoid) THEN
        compatibility_score := compatibility_score + 0.20;
    END IF;

    IF recipe_data.dietary_tags && user_prefs.dietary_restrictions THEN
        compatibility_score := compatibility_score + 0.20;
    END IF;

    -- Equipment compatibility (20% weight)
    IF recipe_data.equipment_required <@ user_prefs.available_equipment OR
       array_length(recipe_data.equipment_required, 1) IS NULL THEN
        compatibility_score := compatibility_score + 0.20;
    END IF;

    -- Difficulty compatibility (10% weight)
    IF recipe_data.difficulty_level <= user_prefs.preferred_difficulty THEN
        compatibility_score := compatibility_score + 0.10;
    END IF;

    RETURN LEAST(compatibility_score, 1.00);
END;
$$ LANGUAGE plpgsql;

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log("Test Gemini function started");

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_ANON_KEY") ?? "",
    );

    console.log("Supabase client initialized");

    // Test API key retrieval
    console.log("Testing API key retrieval...");
    const { data: apiKeyData, error: apiKeyError } =
      await supabaseClient.rpc("get_gemini_api_key");

    if (apiKeyError) {
      console.error("API key error:", apiKeyError);
      return new Response(
        JSON.stringify({
          error: "API key retrieval failed",
          details: apiKeyError,
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    console.log("API key retrieved, length:", apiKeyData?.length || 0);

    // Test simple Gemini API call
    const geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKeyData}`;

    const testRequest = {
      contents: [
        {
          role: "user",
          parts: [{ text: "Say hello in one word" }],
        },
      ],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 50,
      },
    };

    console.log("Making test request to Gemini...");
    const geminiResponse = await fetch(geminiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(testRequest),
    });

    console.log("Gemini response status:", geminiResponse.status);

    if (!geminiResponse.ok) {
      const errorText = await geminiResponse.text();
      console.error("Gemini error:", errorText);
      return new Response(
        JSON.stringify({
          error: "Gemini API failed",
          status: geminiResponse.status,
          details: errorText,
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    const geminiData = await geminiResponse.json();
    console.log("Gemini response received");

    return new Response(
      JSON.stringify({
        success: true,
        apiKeyLength: apiKeyData?.length || 0,
        geminiResponse: geminiData,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      },
    );
  } catch (error) {
    console.error("Test function error:", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        details: error.message,
        stack: error.stack,
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      },
    );
  }
});

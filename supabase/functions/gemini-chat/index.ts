import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface ChatMessage {
  role: "user" | "assistant";
  content: string;
}

interface ChatRequest {
  messages: ChatMessage[];
  tools?: any[];
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Verify the request is authenticated
    const authHeader = req.headers.get("Authorization");
    console.log("Authorization header:", authHeader ? "Present" : "Missing");
    console.log("All headers:", Object.fromEntries(req.headers.entries()));

    if (!authHeader) {
      console.error("Missing authorization header");
      return new Response(
        JSON.stringify({ error: "Missing authorization header" }),
        {
          status: 401,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_ANON_KEY") ?? "",
      {
        global: {
          headers: { Authorization: authHeader },
        },
      },
    );

    // Verify user authentication
    console.log("Attempting to get user from Supabase...");
    const {
      data: { user },
      error: authError,
    } = await supabaseClient.auth.getUser();

    console.log("Auth result:", {
      user: user ? "Found" : "Not found",
      authError,
    });

    if (authError || !user) {
      console.error("Authentication failed:", authError);
      return new Response(
        JSON.stringify({
          error: "Unauthorized",
          details: authError?.message || "No user found",
        }),
        {
          status: 401,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    console.log("User authenticated successfully:", user.id);

    // Parse request body
    const { messages, tools }: ChatRequest = await req.json();

    if (!messages || !Array.isArray(messages)) {
      return new Response(
        JSON.stringify({ error: "Invalid messages format" }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // Retrieve Gemini API key from Vault using database function
    console.log("Attempting to retrieve Gemini API key from vault...");
    const { data: apiKeyData, error: apiKeyError } =
      await supabaseClient.rpc("get_gemini_api_key");

    console.log("API key retrieval result:", { apiKeyData, apiKeyError });

    if (apiKeyError) {
      console.error("API key retrieval error:", apiKeyError);
      return new Response(
        JSON.stringify({
          error: "Configuration error",
          details: apiKeyError.message,
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    if (!apiKeyData) {
      console.error("No API key found in vault");
      return new Response(
        JSON.stringify({
          error: "Configuration error",
          details: "API key not found in vault",
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    const geminiApiKey = apiKeyData;
    console.log("Successfully retrieved API key, length:", geminiApiKey.length);

    // Get user persona and questionnaire status for personalized responses
    console.log("Retrieving user persona and questionnaire status...");
    let personaData = null;
    let personaError = null;

    try {
      const result = await supabaseClient.rpc(
        "get_user_persona_with_questionnaires",
        { p_user_id: user.id },
      );
      personaData = result.data;
      personaError = result.error;

      if (personaError) {
        console.error("Failed to retrieve persona:", personaError);
      } else {
        console.log("Persona retrieved successfully");
      }
    } catch (error) {
      console.log(
        "Persona function not available (likely not migrated yet):",
        error.message,
      );
      personaError = error;
    }

    // Get user preferences for personalized recommendations
    console.log("Retrieving user preferences...");
    let preferencesData = null;
    let preferencesError = null;

    try {
      const result = await supabaseClient.rpc(
        "get_user_preferences_for_recommendations",
        { p_user_id: user.id },
      );
      preferencesData = result.data;
      preferencesError = result.error;

      if (preferencesError) {
        console.error("Failed to retrieve preferences:", preferencesError);
      } else {
        console.log("Preferences retrieved successfully");
      }
    } catch (error) {
      console.log(
        "Preferences function not available (likely not migrated yet):",
        error.message,
      );
      preferencesError = error;
    }
    // Retrieve latest user profile for personalization
    console.log("Retrieving user profile...");
    let profileRow: any = null;
    let profileFetchError: any = null;
    try {
      const { data, error } = await supabaseClient
        .from("profiles")
        .select("*")
        .eq("id", user.id)
        .single();
      profileRow = data;
      profileFetchError = error;
      if (error) {
        console.error("Failed to retrieve profile:", error);
      } else {
        console.log("Profile retrieved successfully");
      }
    } catch (error) {
      console.log(
        "Profile table not available or query failed (likely not migrated yet):",
        (error as any)?.message || error,
      );
      profileFetchError = error;
    }

    // Prepare Gemini API request - using stable Gemini 1.5 Flash
    const geminiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${geminiApiKey}`;

    // Questionnaire status is now included in persona data

    // Create persona-aware system prompt
    let systemPrompt = `You are an AI fitness and nutrition coach for PlateMotion. You provide personalized advice, create workout plans, meal plans, and help users achieve their health goals.

You have access to various tools to help users. Use them when appropriate to provide practical assistance.

Always be encouraging, supportive, and adapt your communication style to the user's preferences.

## QUESTIONNAIRE SYSTEM:
You have a conversational questionnaire system to learn about users. Here's how it works:

**FIRST-TIME USERS:** When a user first chats with you and hasn't completed any questionnaires, offer to start with the Basic Profile questionnaire. Present it as a natural conversation, not a form.

**QUESTIONNAIRE FLOW:**
1. Use "start_questionnaire" tool to begin (basic, nutrition, or exercise)
2. Present questions conversationally with personality and emojis
3. Use "save_questionnaire_answer" tool for each response
4. Use "complete_questionnaire" tool when finished
5. Use "get_questionnaire_status" tool to check completion status

**CONVERSATION STYLE:**
- Make questionnaires feel like getting to know a friend
- Explain WHY you need each piece of information
- Use emojis and personality in your responses
- Offer quick-tap options but allow custom answers
- Show progress and celebrate completion

**TIMING:**
- Offer Basic Profile to new users immediately
- Suggest Nutrition/Exercise profiles after basic completion or when relevant
- Never force questionnaires - always give "maybe later" option`;

    // Add persona context if available
    if (personaData && !personaError) {
      const persona = personaData.persona || {};
      // Merge profile from persona function with live profile row
      const profile = {
        ...(personaData.profile || {}),
        ...(profileRow || {}),
      };
      const insights = personaData.recent_insights || [];

      if (
        Object.keys(persona).length > 0 ||
        Object.keys(profile).length > 0 ||
        insights.length > 0
      ) {
        systemPrompt += `\n\n## USER PERSONA CONTEXT:
This information helps you provide personalized responses:

**Profile:** ${JSON.stringify(profile, null, 2)}

**Persona Traits:** ${JSON.stringify(persona, null, 2)}

**Recent Insights:** ${insights.map((i) => `- ${i.insight_text} (${i.insight_category})`).join("\n")}

Use this context to tailor your responses, communication style, and recommendations to this specific user.`;
      }
    }

    // Add preferences context if available
    if (preferencesData && !preferencesError) {
      const { liked_items, disliked_items, preference_patterns } =
        preferencesData;

      if (liked_items.length > 0 || disliked_items.length > 0) {
        systemPrompt += `\n\n## USER PREFERENCES:
This user has expressed preferences that you MUST respect in all recommendations:

**LIKED ITEMS (recommend similar):**
${liked_items.map((item) => `- ${item.item_type}: ${item.item_identifier} (strength: ${item.preference_strength})`).join("\n")}

**DISLIKED ITEMS (NEVER recommend these or similar):**
${disliked_items.map((item) => `- ${item.item_type}: ${item.item_identifier} (strength: ${item.preference_strength})`).join("\n")}

**PREFERENCE PATTERNS:**
- Total preferences recorded: ${preference_patterns.total_preferences}
- Likes: ${preference_patterns.likes_count}, Dislikes: ${preference_patterns.dislikes_count}
- Most liked category: ${preference_patterns.most_liked_type || "None"}
- Most disliked category: ${preference_patterns.most_disliked_type || "None"}

**CRITICAL INSTRUCTIONS:**
1. NEVER suggest any items from the disliked list
2. When creating meal plans or workout plans, actively filter out disliked items
3. Prioritize items similar to those in the liked list
4. Ask about preferences when suggesting new items
5. Explain how you're personalizing based on their preferences`;
        // Include basic profile context when persona function is unavailable
        if (
          (!personaData || personaError) &&
          profileRow &&
          !profileFetchError
        ) {
          systemPrompt += `\n\n## USER PROFILE (BASIC):\n${JSON.stringify(profileRow, null, 2)}\nUse this information to personalize recommendations, diet, and workouts.`;
        }
      }
    }

    // Add questionnaire status context from persona data
    if (personaData && !personaError && personaData.questionnaire_status) {
      const completionStatus = personaData.questionnaire_status;
      const hasAnyCompleted =
        completionStatus.basic ||
        completionStatus.nutrition ||
        completionStatus.exercise;

      systemPrompt += `\n\n## QUESTIONNAIRE STATUS:
**Completed:** Basic: ${completionStatus.basic ? "✅" : "❌"}, Nutrition: ${completionStatus.nutrition ? "✅" : "❌"}, Exercise: ${completionStatus.exercise ? "✅" : "❌"}

**IMPORTANT BEHAVIOR:**
${
  !hasAnyCompleted
    ? "- This user is NEW and hasn't completed any questionnaires yet\n- IMMEDIATELY offer to start with the Basic Profile questionnaire\n- Make it sound exciting and valuable, not like a chore\n- Explain it takes 2-3 minutes and unlocks personalized advice"
    : "- User has completed some questionnaires\n- Offer remaining questionnaires when contextually relevant\n- Focus on providing personalized advice based on completed profiles"
}`;
    } else {
      // Fallback when database functions aren't available
      systemPrompt += `\n\n## DEVELOPMENT MODE:
**Note:** User profile and preference data is not available (database functions not migrated yet).
- Provide general fitness and nutrition advice
- Offer to help with meal planning, workout planning, and general guidance
- Encourage users to share their goals and preferences in conversation
- Be extra helpful and ask clarifying questions to personalize advice`;
    }

    // Convert messages to Gemini format with system prompt
    const geminiMessages = [
      {
        role: "user",
        parts: [{ text: systemPrompt }],
      },
      ...messages.map((msg) => ({
        role: msg.role === "assistant" ? "model" : "user",
        parts: [{ text: msg.content }],
      })),
    ];

    const geminiRequest = {
      contents: geminiMessages,
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 2048,
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE",
        },
      ],
    };

    // Add tools if provided
    if (tools && tools.length > 0) {
      geminiRequest.tools = tools;
    }

    // Call Gemini API with detailed logging
    console.log(
      "Making request to Gemini API:",
      geminiUrl.substring(0, 100) + "...",
    );
    console.log("Request payload:", JSON.stringify(geminiRequest, null, 2));

    const geminiResponse = await fetch(geminiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(geminiRequest),
    });

    console.log("Gemini API response status:", geminiResponse.status);
    console.log(
      "Gemini API response headers:",
      Object.fromEntries(geminiResponse.headers.entries()),
    );

    if (!geminiResponse.ok) {
      const errorText = await geminiResponse.text();
      console.error("Gemini API error response:", errorText);
      console.error("Gemini API status:", geminiResponse.status);
      return new Response(
        JSON.stringify({
          error: "AI service unavailable",
          details: `Gemini API returned ${geminiResponse.status}: ${errorText}`,
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    const geminiData = await geminiResponse.json();
    console.log("Gemini response data:", JSON.stringify(geminiData, null, 2));

    // Check if Gemini wants to call a function
    const candidate = geminiData.candidates?.[0];
    const parts = candidate?.content?.parts || [];

    // Look for function calls in the response
    const functionCall = parts.find((part) => part.functionCall);

    if (functionCall) {
      console.log("Function call detected:", functionCall);

      // Return the function call information to the mobile app
      return new Response(
        JSON.stringify({
          message: {
            role: "assistant",
            content: "", // Empty content - no message when using tools
            functionCall: {
              name: functionCall.functionCall.name,
              args: functionCall.functionCall.args || {},
            },
          },
          usage: geminiData.usageMetadata || null,
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // Extract regular text response
    const responseContent =
      parts.find((part) => part.text)?.text || "No response generated";

    // Return formatted response
    return new Response(
      JSON.stringify({
        message: {
          role: "assistant",
          content: responseContent,
        },
        usage: geminiData.usageMetadata || null,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      },
    );
  } catch (error) {
    console.error("Edge Function error:", error);
    console.error("Error stack:", error.stack);
    console.error("Error message:", error.message);

    // Return more detailed error information for debugging
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        details: error.message,
        timestamp: new Date().toISOString(),
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      },
    );
  }
});

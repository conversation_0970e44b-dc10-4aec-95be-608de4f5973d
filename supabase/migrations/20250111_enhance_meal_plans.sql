-- Enhanced Meal Plans Migration
-- This migration enhances the existing meal_plans table to support advanced meal planning features

-- First, add new columns to the existing meal_plans table
ALTER TABLE meal_plans 
ADD COLUMN IF NOT EXISTS week_start_date DATE,
ADD COLUMN IF NOT EXISTS duration_days INTEGER DEFAULT 7,
ADD COLUMN IF NOT EXISTS target_calories_per_day INTEGER,
ADD COLUMN IF NOT EXISTS target_protein_grams INTEGER,
ADD COLUMN IF NOT EXISTS target_carbs_grams INTEGER,
ADD COLUMN IF NOT EXISTS target_fat_grams INTEGER,
ADD COLUMN IF NOT EXISTS dietary_preferences JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS excluded_ingredients TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS preferred_cuisines TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS max_prep_time_minutes INTEGER,
ADD COLUMN IF NOT EXISTS cooking_skill_level TEXT CHECK (cooking_skill_level IN ('beginner', 'intermediate', 'advanced')),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update existing meal plans to have week_start_date based on plan_date if it exists
UPDATE meal_plans 
SET week_start_date = plan_date 
WHERE week_start_date IS NULL AND plan_date IS NOT NULL;

-- For meal plans without plan_date, set week_start_date to created_at date
UPDATE meal_plans 
SET week_start_date = created_at::date 
WHERE week_start_date IS NULL;

-- Create or replace the meal_plan_meals table with enhanced structure
DROP TABLE IF EXISTS meal_plan_meals CASCADE;

CREATE TABLE meal_plan_meals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    meal_plan_id UUID REFERENCES meal_plans(id) ON DELETE CASCADE,
    recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,
    
    -- Scheduling
    day_of_week INTEGER CHECK (day_of_week BETWEEN 0 AND 6), -- 0 = Sunday
    meal_type VARCHAR(20) CHECK (meal_type IN ('breakfast', 'lunch', 'dinner', 'snack')),
    
    -- Customization
    servings DECIMAL(4,2) DEFAULT 1.00,
    notes TEXT,
    
    -- Nutritional totals (calculated)
    total_calories INTEGER,
    total_protein_grams DECIMAL(5,2),
    total_carbs_grams DECIMAL(5,2),
    total_fat_grams DECIMAL(5,2),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Migrate existing meal_plan_recipes data to meal_plan_meals if it exists
INSERT INTO meal_plan_meals (meal_plan_id, recipe_id, servings, notes)
SELECT meal_plan_id, recipe_id, serving_size, notes
FROM meal_plan_recipes
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'meal_plan_recipes')
ON CONFLICT DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_meal_plans_user_week ON meal_plans(user_id, week_start_date);
CREATE INDEX IF NOT EXISTS idx_meal_plans_duration ON meal_plans(week_start_date, duration_days);
CREATE INDEX IF NOT EXISTS idx_meal_plan_meals_plan_day ON meal_plan_meals(meal_plan_id, day_of_week, meal_type);
CREATE INDEX IF NOT EXISTS idx_meal_plan_meals_recipe ON meal_plan_meals(recipe_id);

-- Enable RLS for meal_plan_meals
ALTER TABLE meal_plan_meals ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for meal_plan_meals
CREATE POLICY "Users can manage own meal plan meals" ON meal_plan_meals
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM meal_plans 
      WHERE meal_plans.id = meal_plan_meals.meal_plan_id 
      AND meal_plans.user_id = auth.uid()
    )
  );

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_meal_plan_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS trigger_meal_plans_updated_at ON meal_plans;
CREATE TRIGGER trigger_meal_plans_updated_at
    BEFORE UPDATE ON meal_plans
    FOR EACH ROW
    EXECUTE FUNCTION update_meal_plan_updated_at();

-- Create function to get meal plans for a date range
CREATE OR REPLACE FUNCTION get_meal_plans_for_date_range(
    p_user_id UUID,
    p_start_date DATE,
    p_end_date DATE
)
RETURNS TABLE (
    meal_plan_id UUID,
    meal_plan_name TEXT,
    week_start_date DATE,
    duration_days INTEGER,
    covers_date BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mp.id,
        mp.name,
        mp.week_start_date,
        mp.duration_days,
        (p_start_date <= mp.week_start_date + INTERVAL '1 day' * (mp.duration_days - 1) 
         AND p_end_date >= mp.week_start_date) as covers_date
    FROM meal_plans mp
    WHERE mp.user_id = p_user_id
    AND mp.week_start_date <= p_end_date
    AND mp.week_start_date + INTERVAL '1 day' * (mp.duration_days - 1) >= p_start_date
    ORDER BY mp.week_start_date DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to calculate meal plan nutritional totals
CREATE OR REPLACE FUNCTION calculate_meal_plan_nutrition(p_meal_plan_id UUID)
RETURNS TABLE (
    total_calories BIGINT,
    total_protein NUMERIC,
    total_carbs NUMERIC,
    total_fat NUMERIC,
    avg_calories_per_day NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        SUM(mpm.total_calories)::BIGINT,
        SUM(mpm.total_protein_grams),
        SUM(mpm.total_carbs_grams),
        SUM(mpm.total_fat_grams),
        CASE 
            WHEN mp.duration_days > 0 THEN SUM(mpm.total_calories)::NUMERIC / mp.duration_days
            ELSE 0
        END
    FROM meal_plan_meals mpm
    JOIN meal_plans mp ON mp.id = mpm.meal_plan_id
    WHERE mpm.meal_plan_id = p_meal_plan_id
    GROUP BY mp.duration_days;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comments for documentation
COMMENT ON TABLE meal_plan_meals IS 'Individual meals within meal plans with scheduling and nutritional information';
COMMENT ON COLUMN meal_plans.week_start_date IS 'Start date of the meal plan week';
COMMENT ON COLUMN meal_plans.duration_days IS 'Number of days the meal plan covers (default 7)';
COMMENT ON COLUMN meal_plans.dietary_preferences IS 'JSON array of dietary preferences used for this plan';
COMMENT ON COLUMN meal_plan_meals.day_of_week IS 'Day of week (0=Sunday, 1=Monday, etc.)';
COMMENT ON COLUMN meal_plan_meals.meal_type IS 'Type of meal (breakfast, lunch, dinner, snack)';

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON meal_plan_meals TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT EXECUTE ON FUNCTION get_meal_plans_for_date_range TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_meal_plan_nutrition TO authenticated;

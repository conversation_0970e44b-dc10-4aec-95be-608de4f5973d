-- GDPR Compliance Implementation for PlateMotion
-- This migration adds comprehensive GDPR compliance features including:
-- 1. Data deletion (Right to be Forgotten)
-- 2. Data export (Data Portability)
-- 3. Privacy controls and audit logging

-- ============================================================================
-- GDPR AUDIT LOG TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS gdpr_audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL, -- 'data_export', 'data_deletion', 'consent_update'
  action_details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for audit log
ALTER TABLE gdpr_audit_log ENABLE ROW LEVEL SECURITY;

-- RLS policies for audit log
CREATE POLICY "Users can view their own audit log" ON gdpr_audit_log
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert audit log entries" ON gdpr_audit_log
  FOR INSERT WITH CHECK (true);

-- ============================================================================
-- USER CONSENT MANAGEMENT TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS user_consent (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  analytics_consent BOOLEAN DEFAULT false,
  marketing_consent BOOLEAN DEFAULT false,
  personalization_consent BOOLEAN DEFAULT true,
  data_processing_consent BOOLEAN DEFAULT true,
  consent_version TEXT DEFAULT '1.0',
  consent_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for consent
ALTER TABLE user_consent ENABLE ROW LEVEL SECURITY;

-- RLS policies for consent
CREATE POLICY "Users can manage their own consent" ON user_consent
  FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- DATA EXPORT FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION export_user_data(p_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_data JSONB;
  profile_data JSONB;
  questionnaire_data JSONB;
  persona_data JSONB;
  meal_data JSONB;
  workout_data JSONB;
  progress_data JSONB;
  feedback_data JSONB;
  preferences_data JSONB;
  consent_data JSONB;
BEGIN
  -- Verify user exists and is requesting their own data
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Get user profile data
  SELECT to_jsonb(p.*) INTO profile_data
  FROM profiles p
  WHERE p.id = p_user_id;

  -- Get questionnaire data
  SELECT jsonb_build_object(
    'responses', COALESCE(jsonb_agg(qr.*), '[]'::jsonb),
    'sessions', COALESCE(
      (SELECT jsonb_agg(qs.*) FROM questionnaire_sessions qs WHERE qs.user_id = p_user_id),
      '[]'::jsonb
    )
  ) INTO questionnaire_data
  FROM questionnaire_responses qr
  WHERE qr.user_id = p_user_id;

  -- Get persona data
  SELECT jsonb_build_object(
    'persona', COALESCE(
      (SELECT to_jsonb(up.*) FROM user_personas up WHERE up.user_id = p_user_id),
      '{}'::jsonb
    ),
    'insights', COALESCE(
      (SELECT jsonb_agg(pi.*) FROM persona_insights pi WHERE pi.user_id = p_user_id),
      '[]'::jsonb
    ),
    'updates', COALESCE(
      (SELECT jsonb_agg(pu.*) FROM persona_updates pu WHERE pu.user_id = p_user_id),
      '[]'::jsonb
    )
  ) INTO persona_data;

  -- Get meal data
  SELECT jsonb_build_object(
    'meal_plans', COALESCE(
      (SELECT jsonb_agg(mp.*) FROM meal_plans mp WHERE mp.user_id = p_user_id),
      '[]'::jsonb
    ),
    'meal_logs', COALESCE(
      (SELECT jsonb_agg(ml.*) FROM meal_logs ml WHERE ml.user_id = p_user_id),
      '[]'::jsonb
    )
  ) INTO meal_data;

  -- Get workout data
  SELECT jsonb_build_object(
    'workout_plans', COALESCE(
      (SELECT jsonb_agg(wp.*) FROM workout_plans wp WHERE wp.user_id = p_user_id),
      '[]'::jsonb
    ),
    'workout_logs', COALESCE(
      (SELECT jsonb_agg(wl.*) FROM workout_logs wl WHERE wl.user_id = p_user_id),
      '[]'::jsonb
    )
  ) INTO workout_data;

  -- Get progress data
  SELECT COALESCE(jsonb_agg(pl.*), '[]'::jsonb) INTO progress_data
  FROM progress_logs pl
  WHERE pl.user_id = p_user_id;

  -- Get feedback data
  SELECT COALESCE(jsonb_agg(uf.*), '[]'::jsonb) INTO feedback_data
  FROM user_feedback uf
  WHERE uf.user_id = p_user_id;

  -- Get user preferences data
  SELECT COALESCE(jsonb_agg(up.*), '[]'::jsonb) INTO preferences_data
  FROM user_preferences up
  WHERE up.user_id = p_user_id;

  -- Get consent data
  SELECT to_jsonb(uc.*) INTO consent_data
  FROM user_consent uc
  WHERE uc.user_id = p_user_id;

  -- Combine all data
  user_data := jsonb_build_object(
    'export_info', jsonb_build_object(
      'user_id', p_user_id,
      'export_date', NOW(),
      'export_version', '1.0',
      'format', 'JSON'
    ),
    'profile', COALESCE(profile_data, '{}'::jsonb),
    'questionnaires', COALESCE(questionnaire_data, '{}'::jsonb),
    'persona', COALESCE(persona_data, '{}'::jsonb),
    'meals', COALESCE(meal_data, '{}'::jsonb),
    'workouts', COALESCE(workout_data, '{}'::jsonb),
    'progress', COALESCE(progress_data, '[]'::jsonb),
    'feedback', COALESCE(feedback_data, '[]'::jsonb),
    'preferences', COALESCE(preferences_data, '[]'::jsonb),
    'consent', COALESCE(consent_data, '{}'::jsonb)
  );

  -- Log the export action
  INSERT INTO gdpr_audit_log (user_id, action_type, action_details)
  VALUES (p_user_id, 'data_export', jsonb_build_object(
    'export_size_bytes', length(user_data::text),
    'tables_included', jsonb_build_array(
      'profiles', 'questionnaires', 'persona', 'meals', 'workouts', 'progress', 'feedback', 'preferences', 'consent'
    )
  ));

  RETURN user_data;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION export_user_data(UUID) TO authenticated;

-- ============================================================================
-- DATA DELETION FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION delete_user_data(p_user_id UUID, p_confirmation_token TEXT DEFAULT NULL)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deletion_summary JSONB;
  tables_affected TEXT[] := ARRAY[]::TEXT[];
  rows_deleted INTEGER := 0;
  temp_count INTEGER;
BEGIN
  -- Verify user exists and is requesting deletion of their own data
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- For production, you might want to require a confirmation token
  -- IF p_confirmation_token IS NULL OR p_confirmation_token != 'CONFIRM_DELETE' THEN
  --   RAISE EXCEPTION 'Confirmation token required for data deletion';
  -- END IF;

  -- Delete user data in correct order (respecting foreign key constraints)
  
  -- Delete persona updates
  DELETE FROM persona_updates WHERE user_id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'persona_updates');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete persona insights
  DELETE FROM persona_insights WHERE user_id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'persona_insights');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete user personas
  DELETE FROM user_personas WHERE user_id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'user_personas');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete questionnaire responses
  DELETE FROM questionnaire_responses WHERE user_id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'questionnaire_responses');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete questionnaire sessions
  DELETE FROM questionnaire_sessions WHERE user_id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'questionnaire_sessions');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete meal plan recipes (join table)
  DELETE FROM meal_plan_recipes WHERE meal_plan_id IN (
    SELECT id FROM meal_plans WHERE user_id = p_user_id
  );
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'meal_plan_recipes');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete meal logs
  DELETE FROM meal_logs WHERE user_id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'meal_logs');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete meal plans
  DELETE FROM meal_plans WHERE user_id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'meal_plans');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete workout plan exercises (join table)
  DELETE FROM workout_plan_exercises WHERE workout_plan_id IN (
    SELECT id FROM workout_plans WHERE user_id = p_user_id
  );
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'workout_plan_exercises');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete workout logs
  DELETE FROM workout_logs WHERE user_id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'workout_logs');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete workout plans
  DELETE FROM workout_plans WHERE user_id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'workout_plans');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete progress logs
  DELETE FROM progress_logs WHERE user_id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'progress_logs');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete user feedback
  DELETE FROM user_feedback WHERE user_id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'user_feedback');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete user preferences
  DELETE FROM user_preferences WHERE user_id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'user_preferences');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete user consent
  DELETE FROM user_consent WHERE user_id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'user_consent');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Delete user profile
  DELETE FROM profiles WHERE id = p_user_id;
  GET DIAGNOSTICS temp_count = ROW_COUNT;
  IF temp_count > 0 THEN
    tables_affected := array_append(tables_affected, 'profiles');
    rows_deleted := rows_deleted + temp_count;
  END IF;

  -- Create deletion summary
  deletion_summary := jsonb_build_object(
    'user_id', p_user_id,
    'deletion_date', NOW(),
    'tables_affected', to_jsonb(tables_affected),
    'total_rows_deleted', rows_deleted,
    'status', 'completed'
  );

  -- Log the deletion action (before deleting audit log entries)
  INSERT INTO gdpr_audit_log (user_id, action_type, action_details)
  VALUES (p_user_id, 'data_deletion', deletion_summary);

  -- Note: We don't delete the audit log entry for the deletion itself
  -- This provides a record that the deletion occurred, which may be required for compliance

  RETURN deletion_summary;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION delete_user_data(UUID, TEXT) TO authenticated;

-- ============================================================================
-- CONSENT MANAGEMENT FUNCTIONS
-- ============================================================================

CREATE OR REPLACE FUNCTION update_user_consent(
  p_user_id UUID,
  p_analytics_consent BOOLEAN DEFAULT NULL,
  p_marketing_consent BOOLEAN DEFAULT NULL,
  p_personalization_consent BOOLEAN DEFAULT NULL,
  p_data_processing_consent BOOLEAN DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  consent_record user_consent%ROWTYPE;
BEGIN
  -- Verify user exists and is updating their own consent
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Insert or update consent record
  INSERT INTO user_consent (
    user_id,
    analytics_consent,
    marketing_consent,
    personalization_consent,
    data_processing_consent,
    updated_at
  ) VALUES (
    p_user_id,
    COALESCE(p_analytics_consent, false),
    COALESCE(p_marketing_consent, false),
    COALESCE(p_personalization_consent, true),
    COALESCE(p_data_processing_consent, true),
    NOW()
  )
  ON CONFLICT (user_id) DO UPDATE SET
    analytics_consent = COALESCE(p_analytics_consent, user_consent.analytics_consent),
    marketing_consent = COALESCE(p_marketing_consent, user_consent.marketing_consent),
    personalization_consent = COALESCE(p_personalization_consent, user_consent.personalization_consent),
    data_processing_consent = COALESCE(p_data_processing_consent, user_consent.data_processing_consent),
    updated_at = NOW()
  RETURNING * INTO consent_record;

  -- Log the consent update
  INSERT INTO gdpr_audit_log (user_id, action_type, action_details)
  VALUES (p_user_id, 'consent_update', jsonb_build_object(
    'analytics_consent', consent_record.analytics_consent,
    'marketing_consent', consent_record.marketing_consent,
    'personalization_consent', consent_record.personalization_consent,
    'data_processing_consent', consent_record.data_processing_consent,
    'consent_version', consent_record.consent_version
  ));

  RETURN to_jsonb(consent_record);
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION update_user_consent(UUID, BOOLEAN, BOOLEAN, BOOLEAN, BOOLEAN) TO authenticated;

-- ============================================================================
-- GDPR COMPLIANCE CHECK FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION check_gdpr_compliance(p_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  compliance_status JSONB;
  consent_record user_consent%ROWTYPE;
  data_retention_days INTEGER := 365; -- Configurable retention period
  old_data_count INTEGER;
BEGIN
  -- Get user consent
  SELECT * INTO consent_record
  FROM user_consent
  WHERE user_id = p_user_id;

  -- Check for old data that might need cleanup
  SELECT COUNT(*) INTO old_data_count
  FROM (
    SELECT created_at FROM questionnaire_responses WHERE user_id = p_user_id AND created_at < NOW() - INTERVAL '1 year'
    UNION ALL
    SELECT created_at FROM persona_insights WHERE user_id = p_user_id AND created_at < NOW() - INTERVAL '1 year'
    UNION ALL
    SELECT created_at FROM progress_logs WHERE user_id = p_user_id AND created_at < NOW() - INTERVAL '1 year'
  ) old_records;

  compliance_status := jsonb_build_object(
    'user_id', p_user_id,
    'check_date', NOW(),
    'consent_status', CASE
      WHEN consent_record.user_id IS NOT NULL THEN 'recorded'
      ELSE 'missing'
    END,
    'consent_details', COALESCE(to_jsonb(consent_record), '{}'::jsonb),
    'data_retention', jsonb_build_object(
      'retention_period_days', data_retention_days,
      'old_data_records', old_data_count,
      'cleanup_recommended', old_data_count > 0
    ),
    'rights_available', jsonb_build_array(
      'data_export',
      'data_deletion',
      'consent_management',
      'data_portability'
    )
  );

  RETURN compliance_status;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION check_gdpr_compliance(UUID) TO authenticated;

-- ============================================================================
-- HELPER FUNCTIONS FOR GDPR OPERATIONS
-- ============================================================================

-- Function to get user's GDPR audit history
CREATE OR REPLACE FUNCTION get_gdpr_audit_history(p_user_id UUID, p_limit INTEGER DEFAULT 50)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  audit_history JSONB;
BEGIN
  SELECT COALESCE(jsonb_agg(
    jsonb_build_object(
      'id', id,
      'action_type', action_type,
      'action_details', action_details,
      'created_at', created_at
    ) ORDER BY created_at DESC
  ), '[]'::jsonb) INTO audit_history
  FROM gdpr_audit_log
  WHERE user_id = p_user_id
  LIMIT p_limit;

  RETURN jsonb_build_object(
    'user_id', p_user_id,
    'audit_history', audit_history,
    'total_records', (SELECT COUNT(*) FROM gdpr_audit_log WHERE user_id = p_user_id)
  );
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_gdpr_audit_history(UUID, INTEGER) TO authenticated;

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Indexes for GDPR audit log
CREATE INDEX IF NOT EXISTS idx_gdpr_audit_log_user_id ON gdpr_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_gdpr_audit_log_action_type ON gdpr_audit_log(action_type);
CREATE INDEX IF NOT EXISTS idx_gdpr_audit_log_created_at ON gdpr_audit_log(created_at);

-- Indexes for user consent
CREATE INDEX IF NOT EXISTS idx_user_consent_user_id ON user_consent(user_id);
CREATE INDEX IF NOT EXISTS idx_user_consent_updated_at ON user_consent(updated_at);

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE gdpr_audit_log IS 'Audit log for GDPR compliance actions including data exports, deletions, and consent updates';
COMMENT ON TABLE user_consent IS 'User consent preferences for data processing, analytics, marketing, and personalization';

COMMENT ON FUNCTION export_user_data(UUID) IS 'Exports all user data in JSON format for GDPR data portability compliance';
COMMENT ON FUNCTION delete_user_data(UUID, TEXT) IS 'Permanently deletes all user data for GDPR right to be forgotten compliance';
COMMENT ON FUNCTION update_user_consent(UUID, BOOLEAN, BOOLEAN, BOOLEAN, BOOLEAN) IS 'Updates user consent preferences with audit logging';
COMMENT ON FUNCTION check_gdpr_compliance(UUID) IS 'Checks GDPR compliance status for a user including consent and data retention';
COMMENT ON FUNCTION get_gdpr_audit_history(UUID, INTEGER) IS 'Retrieves GDPR audit history for a user';

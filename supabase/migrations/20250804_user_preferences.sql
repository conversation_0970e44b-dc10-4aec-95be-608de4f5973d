-- User Preferences System for Like/Dislike Functionality
-- This migration creates the user preferences system for tracking likes/dislikes
-- of meals, exercises, recipes, and other content

-- ============================================================================
-- USER PREFERENCES TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS user_preferences (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  item_type TEXT NOT NULL CHECK (item_type IN ('meal', 'exercise', 'recipe', 'ingredient', 'workout_plan', 'meal_plan')),
  item_identifier TEXT NOT NULL, -- specific meal/exercise name, recipe ID, or ingredient name
  item_metadata JSONB DEFAULT '{}', -- additional context like recipe details, exercise equipment, etc.
  preference_type TEXT NOT NULL CHECK (preference_type IN ('like', 'dislike', 'love', 'hate', 'neutral')),
  preference_strength INTEGER NOT NULL CHECK (preference_strength >= -2 AND preference_strength <= 2), -- -2=hate, -1=dislike, 0=neutral, 1=like, 2=love
  context JSONB DEFAULT '{}', -- why they liked/disliked it, dietary restrictions, equipment issues, etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, item_type, item_identifier)
);

-- Enable RLS for user preferences
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- RLS policies for user preferences
CREATE POLICY "Users can manage own preferences" ON user_preferences
  FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_item_type ON user_preferences(item_type);
CREATE INDEX IF NOT EXISTS idx_user_preferences_preference_type ON user_preferences(preference_type);
CREATE INDEX IF NOT EXISTS idx_user_preferences_strength ON user_preferences(preference_strength);
CREATE INDEX IF NOT EXISTS idx_user_preferences_created_at ON user_preferences(created_at);
CREATE INDEX IF NOT EXISTS idx_user_preferences_lookup ON user_preferences(user_id, item_type, item_identifier);

-- ============================================================================
-- DATABASE FUNCTIONS
-- ============================================================================

-- Function to save or update user preference
CREATE OR REPLACE FUNCTION save_user_preference(
  p_user_id UUID,
  p_item_type TEXT,
  p_item_identifier TEXT,
  p_preference_type TEXT,
  p_item_metadata JSONB DEFAULT '{}',
  p_context JSONB DEFAULT '{}'
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  preference_strength INTEGER;
  result_record user_preferences%ROWTYPE;
BEGIN
  -- Verify user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Convert preference type to strength
  preference_strength := CASE p_preference_type
    WHEN 'hate' THEN -2
    WHEN 'dislike' THEN -1
    WHEN 'neutral' THEN 0
    WHEN 'like' THEN 1
    WHEN 'love' THEN 2
    ELSE 0
  END;

  -- Insert or update preference
  INSERT INTO user_preferences (
    user_id,
    item_type,
    item_identifier,
    item_metadata,
    preference_type,
    preference_strength,
    context,
    updated_at
  ) VALUES (
    p_user_id,
    p_item_type,
    p_item_identifier,
    p_item_metadata,
    p_preference_type,
    preference_strength,
    p_context,
    NOW()
  )
  ON CONFLICT (user_id, item_type, item_identifier)
  DO UPDATE SET
    preference_type = EXCLUDED.preference_type,
    preference_strength = EXCLUDED.preference_strength,
    item_metadata = EXCLUDED.item_metadata,
    context = EXCLUDED.context,
    updated_at = NOW()
  RETURNING * INTO result_record;

  -- Return the preference record as JSON
  RETURN to_jsonb(result_record);
END;
$$;

-- Function to get user preferences for recommendations
CREATE OR REPLACE FUNCTION get_user_preferences_for_recommendations(
  p_user_id UUID,
  p_item_type TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  liked_items JSONB;
  disliked_items JSONB;
  preference_patterns JSONB;
  result JSONB;
BEGIN
  -- Get liked items (preference_strength >= 1)
  SELECT COALESCE(jsonb_agg(
    jsonb_build_object(
      'item_identifier', item_identifier,
      'item_type', item_type,
      'preference_strength', preference_strength,
      'item_metadata', item_metadata
    )
  ), '[]'::jsonb) INTO liked_items
  FROM user_preferences
  WHERE user_id = p_user_id
    AND preference_strength >= 1
    AND (p_item_type IS NULL OR item_type = p_item_type);

  -- Get disliked items (preference_strength <= -1)
  SELECT COALESCE(jsonb_agg(
    jsonb_build_object(
      'item_identifier', item_identifier,
      'item_type', item_type,
      'preference_strength', preference_strength,
      'item_metadata', item_metadata
    )
  ), '[]'::jsonb) INTO disliked_items
  FROM user_preferences
  WHERE user_id = p_user_id
    AND preference_strength <= -1
    AND (p_item_type IS NULL OR item_type = p_item_type);

  -- Generate preference patterns summary
  SELECT jsonb_build_object(
    'total_preferences', COUNT(*),
    'likes_count', COUNT(*) FILTER (WHERE preference_strength >= 1),
    'dislikes_count', COUNT(*) FILTER (WHERE preference_strength <= -1),
    'most_liked_type', (
      SELECT item_type
      FROM user_preferences
      WHERE user_id = p_user_id AND preference_strength >= 1
      GROUP BY item_type
      ORDER BY COUNT(*) DESC
      LIMIT 1
    ),
    'most_disliked_type', (
      SELECT item_type
      FROM user_preferences
      WHERE user_id = p_user_id AND preference_strength <= -1
      GROUP BY item_type
      ORDER BY COUNT(*) DESC
      LIMIT 1
    )
  ) INTO preference_patterns
  FROM user_preferences
  WHERE user_id = p_user_id
    AND (p_item_type IS NULL OR item_type = p_item_type);

  -- Combine results
  result := jsonb_build_object(
    'liked_items', liked_items,
    'disliked_items', disliked_items,
    'preference_patterns', preference_patterns
  );

  RETURN result;
END;
$$;

-- Function to get user preference for a specific item
CREATE OR REPLACE FUNCTION get_user_preference(
  p_user_id UUID,
  p_item_type TEXT,
  p_item_identifier TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  preference_record user_preferences%ROWTYPE;
BEGIN
  SELECT * INTO preference_record
  FROM user_preferences
  WHERE user_id = p_user_id
    AND item_type = p_item_type
    AND item_identifier = p_item_identifier;

  IF preference_record.id IS NULL THEN
    RETURN jsonb_build_object(
      'exists', false,
      'preference_type', 'neutral',
      'preference_strength', 0
    );
  END IF;

  RETURN jsonb_build_object(
    'exists', true,
    'preference_type', preference_record.preference_type,
    'preference_strength', preference_record.preference_strength,
    'item_metadata', preference_record.item_metadata,
    'context', preference_record.context,
    'created_at', preference_record.created_at,
    'updated_at', preference_record.updated_at
  );
END;
$$;

-- Function to delete user preference
CREATE OR REPLACE FUNCTION delete_user_preference(
  p_user_id UUID,
  p_item_type TEXT,
  p_item_identifier TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM user_preferences
  WHERE user_id = p_user_id
    AND item_type = p_item_type
    AND item_identifier = p_item_identifier;

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count > 0;
END;
$$;

-- Function to get preference statistics for user
CREATE OR REPLACE FUNCTION get_user_preference_stats(p_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  stats JSONB;
BEGIN
  SELECT jsonb_build_object(
    'total_preferences', COUNT(*),
    'by_type', jsonb_object_agg(
      item_type,
      jsonb_build_object(
        'total', type_counts.total,
        'likes', type_counts.likes,
        'dislikes', type_counts.dislikes
      )
    ),
    'by_preference', jsonb_object_agg(
      preference_type,
      pref_counts.count
    ),
    'recent_activity', (
      SELECT COALESCE(jsonb_agg(
        jsonb_build_object(
          'item_type', item_type,
          'item_identifier', item_identifier,
          'preference_type', preference_type,
          'updated_at', updated_at
        ) ORDER BY updated_at DESC
      ), '[]'::jsonb)
      FROM user_preferences
      WHERE user_id = p_user_id
      ORDER BY updated_at DESC
      LIMIT 10
    )
  ) INTO stats
  FROM (
    SELECT 
      item_type,
      COUNT(*) as total,
      COUNT(*) FILTER (WHERE preference_strength >= 1) as likes,
      COUNT(*) FILTER (WHERE preference_strength <= -1) as dislikes
    FROM user_preferences
    WHERE user_id = p_user_id
    GROUP BY item_type
  ) type_counts
  FULL OUTER JOIN (
    SELECT 
      preference_type,
      COUNT(*) as count
    FROM user_preferences
    WHERE user_id = p_user_id
    GROUP BY preference_type
  ) pref_counts ON true;

  RETURN COALESCE(stats, '{}'::jsonb);
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION save_user_preference(UUID, TEXT, TEXT, TEXT, JSONB, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_preferences_for_recommendations(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_preference(UUID, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION delete_user_preference(UUID, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_preference_stats(UUID) TO authenticated;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE user_preferences IS 'User preferences for meals, exercises, recipes, and other content';
COMMENT ON COLUMN user_preferences.item_type IS 'Type of item being rated: meal, exercise, recipe, ingredient, workout_plan, meal_plan';
COMMENT ON COLUMN user_preferences.item_identifier IS 'Unique identifier for the item (name, ID, etc.)';
COMMENT ON COLUMN user_preferences.preference_strength IS 'Numeric preference: -2=hate, -1=dislike, 0=neutral, 1=like, 2=love';
COMMENT ON COLUMN user_preferences.item_metadata IS 'Additional context about the item (recipe details, exercise equipment, etc.)';
COMMENT ON COLUMN user_preferences.context IS 'User context for the preference (why they liked/disliked it)';

COMMENT ON FUNCTION save_user_preference(UUID, TEXT, TEXT, TEXT, JSONB, JSONB) IS 'Save or update a user preference for an item';
COMMENT ON FUNCTION get_user_preferences_for_recommendations(UUID, TEXT) IS 'Get user preferences formatted for AI recommendations';
COMMENT ON FUNCTION get_user_preference(UUID, TEXT, TEXT) IS 'Get a specific user preference for an item';
COMMENT ON FUNCTION delete_user_preference(UUID, TEXT, TEXT) IS 'Delete a user preference for an item';
COMMENT ON FUNCTION get_user_preference_stats(UUID) IS 'Get comprehensive preference statistics for a user';

-- Core Database Schema for PlateMotion
-- This migration creates all the necessary tables for the application
-- including persona system, questionnaires, meal/workout plans, and progress tracking

-- ============================================================================
-- PROFILES TABLE (Enhanced)
-- ============================================================================

CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  full_name TEXT,
  age INTEGER,
  height_cm INTEGER,
  weight_kg NUMERIC,
  goals TEXT[],
  experience_level TEXT,
  ai_persona_preference TEXT,
  dietary_restrictions TEXT[],
  food_allergies TEXT[],
  budget_preference TEXT,
  known_injuries TEXT[],
  onboarding_complete BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- RLS policies for profiles
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- ============================================================================
-- QUESTIONNAIRE SYSTEM TABLES
-- ============================================================================

-- Questionnaire Sessions
CREATE TABLE IF NOT EXISTS questionnaire_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  questionnaire_type TEXT NOT NULL, -- 'basic', 'nutrition', 'exercise'
  status TEXT DEFAULT 'in_progress', -- 'in_progress', 'completed', 'abandoned'
  current_question_index INTEGER DEFAULT 0,
  total_questions INTEGER,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Questionnaire Responses
CREATE TABLE IF NOT EXISTS questionnaire_responses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  session_id UUID REFERENCES questionnaire_sessions(id) ON DELETE CASCADE,
  questionnaire_type TEXT NOT NULL,
  question_id TEXT NOT NULL,
  question_text TEXT,
  answer_value TEXT, -- JSON string of the answer
  answer_display_text TEXT, -- Human readable answer
  answered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for questionnaire tables
ALTER TABLE questionnaire_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE questionnaire_responses ENABLE ROW LEVEL SECURITY;

-- RLS policies for questionnaire tables
CREATE POLICY "Users can manage own questionnaire sessions" ON questionnaire_sessions
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own questionnaire responses" ON questionnaire_responses
  FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- PERSONA SYSTEM TABLES
-- ============================================================================

-- User Personas
CREATE TABLE IF NOT EXISTS user_personas (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  personality_traits JSONB DEFAULT '{}',
  communication_style JSONB DEFAULT '{}',
  motivation_style JSONB DEFAULT '{}',
  learning_preferences JSONB DEFAULT '{}',
  fitness_persona JSONB DEFAULT '{}',
  nutrition_persona JSONB DEFAULT '{}',
  goal_orientation JSONB DEFAULT '{}',
  activity_patterns JSONB DEFAULT '{}',
  engagement_patterns JSONB DEFAULT '{}',
  success_factors JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Persona Insights
CREATE TABLE IF NOT EXISTS persona_insights (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  insight_category TEXT NOT NULL,
  insight_text TEXT NOT NULL,
  confidence_score NUMERIC CHECK (confidence_score >= 0 AND confidence_score <= 1),
  supporting_data JSONB DEFAULT '{}',
  source TEXT, -- 'ai_interaction', 'questionnaire', 'behavior_analysis'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Persona Updates (Audit Trail)
CREATE TABLE IF NOT EXISTS persona_updates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  update_type TEXT NOT NULL,
  field_updated TEXT,
  previous_value JSONB,
  new_value JSONB,
  trigger_event TEXT,
  ai_reasoning TEXT,
  confidence_score NUMERIC,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for persona tables
ALTER TABLE user_personas ENABLE ROW LEVEL SECURITY;
ALTER TABLE persona_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE persona_updates ENABLE ROW LEVEL SECURITY;

-- RLS policies for persona tables
CREATE POLICY "Users can manage own persona" ON user_personas
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own persona insights" ON persona_insights
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert persona insights" ON persona_insights
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can view own persona updates" ON persona_updates
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert persona updates" ON persona_updates
  FOR INSERT WITH CHECK (true);

-- ============================================================================
-- CONTENT TABLES (Exercises & Recipes)
-- ============================================================================

-- Exercises
CREATE TABLE IF NOT EXISTS exercises (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  video_url TEXT,
  muscle_group TEXT,
  equipment_needed TEXT[],
  difficulty_level TEXT, -- 'beginner', 'intermediate', 'advanced'
  duration_minutes INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recipes
CREATE TABLE IF NOT EXISTS recipes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  instructions TEXT,
  ingredients JSONB,
  prep_time_minutes INTEGER,
  cook_time_minutes INTEGER,
  cuisine_type TEXT,
  nutritional_info JSONB,
  is_human_reviewed BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- USER PLANS TABLES (Workouts & Meals)
-- ============================================================================

-- Workout Plans
CREATE TABLE IF NOT EXISTS workout_plans (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  difficulty_level TEXT,
  estimated_duration_minutes INTEGER,
  ai_generated BOOLEAN DEFAULT false,
  ai_reasoning TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workout Plan Exercises (Join Table)
CREATE TABLE IF NOT EXISTS workout_plan_exercises (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  workout_plan_id UUID REFERENCES workout_plans(id) ON DELETE CASCADE,
  exercise_id UUID REFERENCES exercises(id) ON DELETE CASCADE,
  order_index INTEGER,
  sets INTEGER,
  reps INTEGER,
  duration_seconds INTEGER,
  rest_seconds INTEGER,
  notes TEXT
);

-- Meal Plans
CREATE TABLE IF NOT EXISTS meal_plans (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  plan_date DATE,
  meal_type TEXT, -- 'breakfast', 'lunch', 'dinner', 'snack'
  ai_generated BOOLEAN DEFAULT false,
  ai_reasoning TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Meal Plan Recipes (Join Table)
CREATE TABLE IF NOT EXISTS meal_plan_recipes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  meal_plan_id UUID REFERENCES meal_plans(id) ON DELETE CASCADE,
  recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,
  serving_size NUMERIC DEFAULT 1,
  notes TEXT
);

-- ============================================================================
-- LOGGING TABLES (User Activity)
-- ============================================================================

-- Workout Logs
CREATE TABLE IF NOT EXISTS workout_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  workout_plan_id UUID REFERENCES workout_plans(id) ON DELETE SET NULL,
  exercise_id UUID REFERENCES exercises(id) ON DELETE CASCADE,
  sets_completed INTEGER,
  reps_completed INTEGER,
  duration_seconds INTEGER,
  calories_burned INTEGER,
  notes TEXT,
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Meal Logs
CREATE TABLE IF NOT EXISTS meal_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  meal_plan_id UUID REFERENCES meal_plans(id) ON DELETE SET NULL,
  recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,
  serving_size NUMERIC DEFAULT 1,
  calories_consumed INTEGER,
  meal_type TEXT,
  notes TEXT,
  consumed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Progress Logs
CREATE TABLE IF NOT EXISTS progress_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  log_type TEXT NOT NULL, -- 'weight', 'body_fat', 'measurements', 'photo', 'mood'
  value NUMERIC,
  unit TEXT,
  measurements JSONB, -- For body measurements
  notes TEXT,
  photo_url TEXT,
  logged_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- FEEDBACK TABLES
-- ============================================================================

-- User Feedback
CREATE TABLE IF NOT EXISTS user_feedback (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  feedback_type TEXT NOT NULL, -- 'meal_rating', 'workout_rating', 'app_feedback', 'bug_report'
  item_id UUID, -- ID of the meal, workout, etc.
  item_type TEXT, -- 'meal', 'workout', 'recipe', 'exercise'
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  feedback_text TEXT,
  tags TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for all user-specific tables
ALTER TABLE workout_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE meal_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE workout_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE meal_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE progress_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_feedback ENABLE ROW LEVEL SECURITY;

-- RLS policies for user-specific tables
CREATE POLICY "Users can manage own workout plans" ON workout_plans
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own meal plans" ON meal_plans
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own workout logs" ON workout_logs
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own meal logs" ON meal_logs
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own progress logs" ON progress_logs
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own feedback" ON user_feedback
  FOR ALL USING (auth.uid() = user_id);

-- Public read access for content tables
ALTER TABLE exercises ENABLE ROW LEVEL SECURITY;
ALTER TABLE recipes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can read exercises" ON exercises
  FOR SELECT USING (true);

CREATE POLICY "Anyone can read recipes" ON recipes
  FOR SELECT USING (true);

-- ============================================================================
-- DATABASE FUNCTIONS
-- ============================================================================

-- Function to update user persona
CREATE OR REPLACE FUNCTION update_user_persona(
  p_user_id UUID,
  p_persona_updates JSONB,
  p_update_source TEXT DEFAULT 'manual'
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  persona_record user_personas%ROWTYPE;
  update_key TEXT;
  update_value JSONB;
BEGIN
  -- Verify user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Get or create persona record
  SELECT * INTO persona_record
  FROM user_personas
  WHERE user_id = p_user_id;

  IF persona_record.user_id IS NULL THEN
    -- Create new persona record
    INSERT INTO user_personas (user_id)
    VALUES (p_user_id)
    RETURNING * INTO persona_record;
  END IF;

  -- Apply updates
  FOR update_key, update_value IN SELECT * FROM jsonb_each(p_persona_updates)
  LOOP
    -- Log the update
    INSERT INTO persona_updates (
      user_id,
      update_type,
      field_updated,
      previous_value,
      new_value,
      trigger_event,
      ai_reasoning
    ) VALUES (
      p_user_id,
      'field_update',
      update_key,
      CASE update_key
        WHEN 'personality_traits' THEN persona_record.personality_traits
        WHEN 'communication_style' THEN persona_record.communication_style
        WHEN 'motivation_style' THEN persona_record.motivation_style
        WHEN 'learning_preferences' THEN persona_record.learning_preferences
        WHEN 'fitness_persona' THEN persona_record.fitness_persona
        WHEN 'nutrition_persona' THEN persona_record.nutrition_persona
        WHEN 'goal_orientation' THEN persona_record.goal_orientation
        WHEN 'activity_patterns' THEN persona_record.activity_patterns
        WHEN 'engagement_patterns' THEN persona_record.engagement_patterns
        WHEN 'success_factors' THEN persona_record.success_factors
        ELSE '{}'::jsonb
      END,
      update_value,
      p_update_source,
      'Persona updated via ' || p_update_source
    );

    -- Update the persona record
    UPDATE user_personas
    SET
      personality_traits = CASE WHEN update_key = 'personality_traits' THEN
        COALESCE(personality_traits, '{}'::jsonb) || update_value
        ELSE personality_traits END,
      communication_style = CASE WHEN update_key = 'communication_style' THEN
        COALESCE(communication_style, '{}'::jsonb) || update_value
        ELSE communication_style END,
      motivation_style = CASE WHEN update_key = 'motivation_style' THEN
        COALESCE(motivation_style, '{}'::jsonb) || update_value
        ELSE motivation_style END,
      learning_preferences = CASE WHEN update_key = 'learning_preferences' THEN
        COALESCE(learning_preferences, '{}'::jsonb) || update_value
        ELSE learning_preferences END,
      fitness_persona = CASE WHEN update_key = 'fitness_persona' THEN
        COALESCE(fitness_persona, '{}'::jsonb) || update_value
        ELSE fitness_persona END,
      nutrition_persona = CASE WHEN update_key = 'nutrition_persona' THEN
        COALESCE(nutrition_persona, '{}'::jsonb) || update_value
        ELSE nutrition_persona END,
      goal_orientation = CASE WHEN update_key = 'goal_orientation' THEN
        COALESCE(goal_orientation, '{}'::jsonb) || update_value
        ELSE goal_orientation END,
      activity_patterns = CASE WHEN update_key = 'activity_patterns' THEN
        COALESCE(activity_patterns, '{}'::jsonb) || update_value
        ELSE activity_patterns END,
      engagement_patterns = CASE WHEN update_key = 'engagement_patterns' THEN
        COALESCE(engagement_patterns, '{}'::jsonb) || update_value
        ELSE engagement_patterns END,
      success_factors = CASE WHEN update_key = 'success_factors' THEN
        COALESCE(success_factors, '{}'::jsonb) || update_value
        ELSE success_factors END,
      updated_at = NOW()
    WHERE user_id = p_user_id;
  END LOOP;

  -- Return updated persona
  SELECT to_jsonb(up.*) INTO persona_record
  FROM user_personas up
  WHERE up.user_id = p_user_id;

  RETURN persona_record;
END;
$$;

-- Function to get user persona with questionnaire status
CREATE OR REPLACE FUNCTION get_user_persona_with_questionnaires(p_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
  persona_data JSONB;
  profile_data JSONB;
  insights_data JSONB;
  questionnaire_status JSONB;
BEGIN
  -- Get persona data
  SELECT to_jsonb(up.*) INTO persona_data
  FROM user_personas up
  WHERE up.user_id = p_user_id;

  -- Get profile data
  SELECT to_jsonb(p.*) INTO profile_data
  FROM profiles p
  WHERE p.id = p_user_id;

  -- Get recent insights
  SELECT COALESCE(jsonb_agg(
    jsonb_build_object(
      'category', insight_category,
      'insight', insight_text,
      'confidence', confidence_score,
      'created_at', created_at
    ) ORDER BY created_at DESC
  ), '[]'::jsonb) INTO insights_data
  FROM persona_insights
  WHERE user_id = p_user_id
  AND created_at > NOW() - INTERVAL '30 days'
  LIMIT 10;

  -- Get questionnaire completion status
  SELECT jsonb_build_object(
    'basic', COALESCE(
      (SELECT status = 'completed' FROM questionnaire_sessions
       WHERE user_id = p_user_id AND questionnaire_type = 'basic'
       ORDER BY created_at DESC LIMIT 1),
      false
    ),
    'nutrition', COALESCE(
      (SELECT status = 'completed' FROM questionnaire_sessions
       WHERE user_id = p_user_id AND questionnaire_type = 'nutrition'
       ORDER BY created_at DESC LIMIT 1),
      false
    ),
    'exercise', COALESCE(
      (SELECT status = 'completed' FROM questionnaire_sessions
       WHERE user_id = p_user_id AND questionnaire_type = 'exercise'
       ORDER BY created_at DESC LIMIT 1),
      false
    )
  ) INTO questionnaire_status;

  -- Combine all data
  result := jsonb_build_object(
    'persona', COALESCE(persona_data, '{}'::jsonb),
    'profile', COALESCE(profile_data, '{}'::jsonb),
    'recent_insights', COALESCE(insights_data, '[]'::jsonb),
    'questionnaire_status', questionnaire_status
  );

  RETURN result;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION update_user_persona(UUID, JSONB, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_persona_with_questionnaires(UUID) TO authenticated;

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Questionnaire indexes
CREATE INDEX IF NOT EXISTS idx_questionnaire_sessions_user_id ON questionnaire_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_sessions_type ON questionnaire_sessions(questionnaire_type);
CREATE INDEX IF NOT EXISTS idx_questionnaire_sessions_status ON questionnaire_sessions(status);

CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_user_id ON questionnaire_responses(user_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_session_id ON questionnaire_responses(session_id);
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_type ON questionnaire_responses(questionnaire_type);

-- Persona indexes
CREATE INDEX IF NOT EXISTS idx_user_personas_user_id ON user_personas(user_id);
CREATE INDEX IF NOT EXISTS idx_persona_insights_user_id ON persona_insights(user_id);
CREATE INDEX IF NOT EXISTS idx_persona_insights_category ON persona_insights(insight_category);
CREATE INDEX IF NOT EXISTS idx_persona_insights_created_at ON persona_insights(created_at);
CREATE INDEX IF NOT EXISTS idx_persona_updates_user_id ON persona_updates(user_id);

-- Plan indexes
CREATE INDEX IF NOT EXISTS idx_workout_plans_user_id ON workout_plans(user_id);
CREATE INDEX IF NOT EXISTS idx_meal_plans_user_id ON meal_plans(user_id);
CREATE INDEX IF NOT EXISTS idx_meal_plans_date ON meal_plans(plan_date);

-- Log indexes
CREATE INDEX IF NOT EXISTS idx_workout_logs_user_id ON workout_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_workout_logs_completed_at ON workout_logs(completed_at);
CREATE INDEX IF NOT EXISTS idx_meal_logs_user_id ON meal_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_meal_logs_consumed_at ON meal_logs(consumed_at);
CREATE INDEX IF NOT EXISTS idx_progress_logs_user_id ON progress_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_progress_logs_type ON progress_logs(log_type);
CREATE INDEX IF NOT EXISTS idx_progress_logs_logged_at ON progress_logs(logged_at);

-- Feedback indexes
CREATE INDEX IF NOT EXISTS idx_user_feedback_user_id ON user_feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_user_feedback_type ON user_feedback(feedback_type);
CREATE INDEX IF NOT EXISTS idx_user_feedback_item ON user_feedback(item_id, item_type);

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE profiles IS 'User profile information including personal details and preferences';
COMMENT ON TABLE questionnaire_sessions IS 'Tracks questionnaire completion sessions';
COMMENT ON TABLE questionnaire_responses IS 'Stores individual questionnaire answers';
COMMENT ON TABLE user_personas IS 'AI-generated user personality profiles for personalization';
COMMENT ON TABLE persona_insights IS 'AI insights about user behavior and preferences';
COMMENT ON TABLE persona_updates IS 'Audit trail of persona changes';
COMMENT ON TABLE exercises IS 'Exercise library with instructions and metadata';
COMMENT ON TABLE recipes IS 'Recipe library with ingredients and nutritional information';
COMMENT ON TABLE workout_plans IS 'User workout plans (AI-generated or manual)';
COMMENT ON TABLE meal_plans IS 'User meal plans (AI-generated or manual)';
COMMENT ON TABLE workout_logs IS 'User workout completion logs';
COMMENT ON TABLE meal_logs IS 'User meal consumption logs';
COMMENT ON TABLE progress_logs IS 'User progress tracking (weight, measurements, etc.)';
COMMENT ON TABLE user_feedback IS 'User feedback and ratings for meals, workouts, and app features';

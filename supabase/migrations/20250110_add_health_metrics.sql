-- Add health metrics columns to profiles table
-- These will be calculated automatically after questionnaire completion

ALTER TABLE profiles ADD COLUMN IF NOT EXISTS bmr DECIMAL(8,2);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS bmi DECIMAL(5,2);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS tdee DECIMAL(8,2);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS target_calories_min INTEGER;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS target_calories_max INTEGER;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS ideal_weight_min DECIMAL(5,2);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS ideal_weight_max DECIMAL(5,2);
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS health_metrics_calculated_at TIMESTAMP WITH TIME ZONE;

-- Add comments for documentation
COMMENT ON COLUMN profiles.bmr IS 'Basal Metabolic Rate - calories burned at rest (calculated using Mifflin-St Jeor equation)';
COMMENT ON COLUMN profiles.bmi IS 'Body Mass Index - weight(kg) / height(m)²';
COMMENT ON COLUMN profiles.tdee IS 'Total Daily Energy Expenditure - BMR adjusted for activity level';
COMMENT ON COLUMN profiles.target_calories_min IS 'Minimum target calories per day based on goals';
COMMENT ON COLUMN profiles.target_calories_max IS 'Maximum target calories per day based on goals';
COMMENT ON COLUMN profiles.ideal_weight_min IS 'Minimum ideal weight based on height (BMI 18.5-24.9)';
COMMENT ON COLUMN profiles.ideal_weight_max IS 'Maximum ideal weight based on height (BMI 18.5-24.9)';
COMMENT ON COLUMN profiles.health_metrics_calculated_at IS 'Timestamp when health metrics were last calculated';

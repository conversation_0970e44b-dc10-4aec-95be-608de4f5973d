-- Migration: Vault Functions for API Key Management
-- Description: Creates functions to securely retrieve API keys from Supabase Vault
-- Date: 2025-08-04

-- Function to retrieve Gemini API key from vault
-- This function is used by the gemini-chat edge function
CREATE OR REPLACE FUNCTION get_gemini_api_key()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    api_key TEXT;
BEGIN
    -- Retrieve the Gemini API key from Supabase Vault
    -- The vault.decrypted_secrets view provides access to decrypted secrets
    SELECT decrypted_secret 
    INTO api_key
    FROM vault.decrypted_secrets 
    WHERE name = 'GEMINI_API_KEY';
    
    -- Return the API key (will be null if not found)
    RETURN api_key;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and return null
        RAISE LOG 'Error retrieving Gemini API key from vault: %', SQLERRM;
        RETURN NULL;
END;
$$;

-- Grant execute permission to authenticated users
-- This allows the edge function to call this function
GRANT EXECUTE ON FUNCTION get_gemini_api_key() TO authenticated;
GRANT EXECUTE ON FUNCTION get_gemini_api_key() TO service_role;

-- Add comment for documentation
COMMENT ON FUNCTION get_gemini_api_key() IS 'Retrieves the Gemini API key from Supabase Vault for use in edge functions';

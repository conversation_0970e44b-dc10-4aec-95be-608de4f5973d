# PlateMotion

Welcome to PlateMotion, the AI-powered health and fitness application designed to provide personalized meal plans and workout schedules.

## Overview

This monorepo contains the entire PlateMotion ecosystem, including:

- **Mobile App:** An Expo-based application for iOS and Android.
- **Web App:** A Next.js-based admin panel for managing the application.
- **Shared Packages:** Reusable UI components, configurations, and utility functions.

## Tech Stack

- **Monorepo:** Turborepo
- **Backend:** Supabase
- **Deployment:**
  - Web: Netlify
  - Mobile: Expo EAS
- **AI:** Gemini 2.5 Flash

## Getting Started

To get started with development, please refer to the documentation in the respective `apps` and `packages` directories.

## Package Manager

We use pnpm in this monorepo.

- Install: pnpm install
- Lint: pnpm lint
- Format: pnpm format
- Test: pnpm test --if-present

If you prefer running app-specific tasks, prefix with --filter mobile. For example:

- pnpm --filter mobile lint
- pnpm --filter mobile format:check

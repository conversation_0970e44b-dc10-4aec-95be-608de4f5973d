{"name": "@platemotion/admin", "version": "1.0.0", "description": "PlateMotion Admin Panel - Web-based administration dashboard", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start --port 3001", "lint": "next lint", "lint:strict": "eslint . --ext .ts,.tsx --max-warnings=0", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@hookform/resolvers": "^3.3.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.0", "@radix-ui/react-tooltip": "^1.0.0", "@radix-ui/themes": "^2.0.0", "@supabase/supabase-js": "^2.38.0", "@tanstack/react-query": "^5.0.0", "@tanstack/react-query-devtools": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "next": "^14.0.0", "postcss": "^8.4.0", "posthog-js": "^1.88.0", "react": "^18.0.0", "react-day-picker": "^8.10.1", "react-dom": "^18.0.0", "react-hook-form": "^7.47.0", "recharts": "^2.8.0", "sonner": "^1.2.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "vaul": "^0.7.0", "zod": "^3.22.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^13.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "ts-jest": "^29.4.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["admin", "dashboard", "platemotion", "next.js", "react", "supabase", "typescript"], "author": "PlateMotion Team", "license": "MIT"}
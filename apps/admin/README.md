# PlateMotion Admin Panel

A comprehensive web-based administration dashboard for managing the PlateMotion platform.

## 🚀 Features

- **User Management**: View, edit, and manage user accounts and subscriptions
- **Content Management**: Manage recipes, exercises, and approval workflows
- **Customer Support**: Real-time chat system and ticket management
- **Analytics Dashboard**: Comprehensive insights and reporting
- **System Administration**: Feature flags, notifications, and configuration

## 🛠️ Tech Stack

- **Frontend**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + shadcn/ui
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth with RBAC
- **State Management**: React Query/TanStack Query
- **Charts**: Recharts
- **Real-time**: Supabase Realtime

## 📋 Prerequisites

- Node.js 18+ and npm 8+
- Supabase project with PlateMotion database
- Admin database schema applied (see `database-schema.sql`)

## 🏗️ Setup Instructions

### 1. Install Dependencies

```bash
cd apps/admin
npm install
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env.local

# Edit .env.local with your actual values
# - Supabase URL and keys
# - Admin panel configuration
# - Optional: Analytics and monitoring keys
```

### 3. Database Setup

```bash
# Apply the admin schema to your Supabase database
# Run the SQL commands in database-schema.sql
```

### 4. Development Server

```bash
npm run dev
```

The admin panel will be available at `http://localhost:3001`

## 📁 Project Structure

```
apps/admin/
├── src/
│   ├── app/                 # Next.js App Router pages
│   │   ├── dashboard/       # Analytics dashboard
│   │   ├── users/          # User management
│   │   ├── content/        # Content management
│   │   ├── support/        # Customer support
│   │   ├── settings/       # Admin settings
│   │   └── layout.tsx      # Root layout
│   ├── components/         # Reusable UI components
│   │   ├── ui/            # shadcn/ui components
│   │   ├── charts/        # Chart components
│   │   ├── forms/         # Form components
│   │   └── layout/        # Layout components
│   ├── lib/               # Utilities and configurations
│   │   ├── supabase.ts    # Supabase client
│   │   ├── auth.ts        # Authentication helpers
│   │   ├── utils.ts       # Utility functions
│   │   └── validations.ts # Zod schemas
│   ├── hooks/             # Custom React hooks
│   ├── types/             # TypeScript type definitions
│   └── styles/            # Global styles
├── public/                # Static assets
├── database-schema.sql    # Database schema
├── PRD.md                # Product Requirements Document
└── package.json
```

## 🔐 Authentication & Authorization

The admin panel uses role-based access control (RBAC) with the following roles:

- **Super Admin**: Full system access
- **Content Manager**: Content management and analytics
- **Support Agent**: Customer support and user management
- **Analytics Viewer**: Read-only analytics access

## 🎯 Key Features

### Dashboard

- Real-time metrics and KPIs
- User activity monitoring
- System health indicators
- Quick action shortcuts

### User Management

- User list with search and filtering
- User profile details and history
- Subscription management
- Bulk operations

### Content Management

- Recipe and exercise CRUD operations
- Content approval workflow
- Version control and history
- Performance analytics

### Customer Support

- Support ticket management
- Real-time chat with mobile users
- Canned responses and templates
- SLA monitoring and alerts

### Analytics & Reporting

- User engagement metrics
- Content performance tracking
- Business intelligence insights
- Custom report generation

## 🚀 Deployment

### Production Build

```bash
npm run build
npm start
```

### Environment Variables

Ensure all production environment variables are configured:

- Supabase production URLs and keys
- Authentication secrets
- Analytics and monitoring keys
- SMTP configuration for notifications

### Recommended Hosting

- **Vercel**: Seamless Next.js deployment
- **Netlify**: Alternative hosting option
- **Docker**: Containerized deployment

## 📊 Monitoring & Analytics

The admin panel includes built-in monitoring:

- **Performance**: Page load times and API response times
- **Errors**: Error tracking and reporting
- **Usage**: Admin user activity and feature usage
- **Security**: Authentication attempts and access logs

## 🔧 Development

### Code Quality

```bash
# Type checking
npm run type-check

# Linting
npm run lint

# Testing
npm run test
npm run test:watch
npm run test:coverage
```

### Database Migrations

When updating the database schema:

1. Update `database-schema.sql`
2. Apply changes to Supabase
3. Update TypeScript types
4. Test thoroughly

## 📚 Documentation

- [Product Requirements Document](./PRD.md)
- [Database Schema](./database-schema.sql)
- [API Documentation](../mobile/docs/API_DESIGN.md)
- [Architecture Overview](../mobile/docs/ARCHITECTURE.md)

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Add tests for new features
3. Update documentation as needed
4. Ensure all checks pass before submitting

## 📄 License

This project is part of the PlateMotion platform and is proprietary software.

---

**PlateMotion Admin Panel** - Empowering efficient platform management 🚀

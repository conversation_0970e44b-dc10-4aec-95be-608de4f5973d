#!/usr/bin/env tsx

/**
 * Setup script for Supabase Storage bucket for recipe images
 * Run with: npx tsx scripts/setup-storage.ts
 */

import { supabaseAdmin } from "../src/lib/supabase";

async function setupStorage() {
  console.log("🚀 Setting up Supabase Storage for recipe images...");

  try {
    // Check if bucket already exists
    const { data: buckets, error: listError } =
      await supabaseAdmin.storage.listBuckets();

    if (listError) {
      console.error("❌ Error listing buckets:", listError);
      return;
    }

    const bucketExists = buckets?.some(
      (bucket) => bucket.id === "recipe-images",
    );

    if (!bucketExists) {
      // Create the bucket
      const { error } = await supabaseAdmin.storage.createBucket(
        "recipe-images",
        {
          public: true,
          fileSizeLimit: 5242880, // 5MB
          allowedMimeTypes: [
            "image/jpeg",
            "image/jpg",
            "image/png",
            "image/webp",
          ],
        },
      );

      if (error) {
        console.error("❌ Error creating bucket:", error);
        return;
      }

      console.log("✅ Created recipe-images bucket successfully");
    } else {
      console.log("✅ recipe-images bucket already exists");
    }

    // Test upload permissions
    console.log("🧪 Testing upload permissions...");

    // Create a small test file
    const testFile = new File(["test"], "test.txt", { type: "text/plain" });

    const { error: uploadError } = await supabaseAdmin.storage
      .from("recipe-images")
      .upload("test/test.txt", testFile);

    if (uploadError) {
      console.error("❌ Upload test failed:", uploadError);
    } else {
      console.log("✅ Upload test successful");

      // Clean up test file
      await supabaseAdmin.storage
        .from("recipe-images")
        .remove(["test/test.txt"]);

      console.log("✅ Test file cleaned up");
    }

    console.log("🎉 Storage setup completed successfully!");
    console.log("\n📋 Next steps:");
    console.log("1. Ensure your Supabase project has the correct RLS policies");
    console.log(
      "2. Run the SQL script in apps/admin/scripts/setup-storage.sql if needed",
    );
    console.log("3. Test image upload in the admin panel");
  } catch (error) {
    console.error("❌ Setup failed:", error);
  }
}

// Run the setup
setupStorage().catch(console.error);

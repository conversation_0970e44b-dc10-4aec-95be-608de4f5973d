import '@testing-library/jest-dom';

// Extend Jest matchers
expect.extend({
  toBeInTheDocument: (received) => {
    const pass = received !== null && received !== undefined;
    return {
      message: () => `expected element ${pass ? 'not ' : ''}to be in the document`,
      pass,
    };
  },
  toHaveTextContent: (received, expected) => {
    const pass = received && received.textContent && received.textContent.includes(expected);
    return {
      message: () => `expected element ${pass ? 'not ' : ''}to have text content "${expected}"`,
      pass,
    };
  },
  toHaveClass: (received, expected) => {
    const pass = received && received.classList && received.classList.contains(expected);
    return {
      message: () => `expected element ${pass ? 'not ' : ''}to have class "${expected}"`,
      pass,
    };
  },
  toHaveValue: (received, expected) => {
    const pass = received && received.value === expected;
    return {
      message: () => `expected element ${pass ? 'not ' : ''}to have value "${expected}"`,
      pass,
    };
  },
  toHaveAttribute: (received, attr, value) => {
    const pass = received && received.getAttribute(attr) === value;
    return {
      message: () => `expected element ${pass ? 'not ' : ''}to have attribute "${attr}" with value "${value}"`,
      pass,
    };
  },
  toBeDisabled: (received) => {
    const pass = received && received.disabled === true;
    return {
      message: () => `expected element ${pass ? 'not ' : ''}to be disabled`,
      pass,
    };
  },
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock Next.js router
const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  prefetch: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  beforePopState: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  isPreview: false,
};

jest.mock('next/router', () => ({
  useRouter: () => mockRouter,
}));

jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter,
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
}));

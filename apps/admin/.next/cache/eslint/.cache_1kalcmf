[{"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/dashboard/page.tsx": "1", "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/layout.tsx": "2", "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/nutrition/__tests__/page.test.tsx": "3", "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/nutrition/page.tsx": "4", "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/page.tsx": "5", "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/users/page.test.tsx": "6", "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/users/page.tsx": "7", "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/workouts/page.tsx": "8", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/AdminLayout.tsx": "9", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/Header.tsx": "10", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/Sidebar.tsx": "11", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/badge.tsx": "12", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/button.test.tsx": "13", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/button.tsx": "14", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/card.test.tsx": "15", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/card.tsx": "16", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/checkbox.tsx": "17", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dialog.tsx": "18", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/input.tsx": "19", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/select.tsx": "20", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/table.tsx": "21", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/textarea.tsx": "22", "/Users/<USER>/Desktop/platemotion/apps/admin/src/lib/__mocks__/supabase.ts": "23", "/Users/<USER>/Desktop/platemotion/apps/admin/src/lib/supabase.ts": "24", "/Users/<USER>/Desktop/platemotion/apps/admin/src/lib/utils.test.ts": "25", "/Users/<USER>/Desktop/platemotion/apps/admin/src/lib/utils.ts": "26", "/Users/<USER>/Desktop/platemotion/apps/admin/src/services/__tests__/nutritionService.test.ts": "27", "/Users/<USER>/Desktop/platemotion/apps/admin/src/services/nutritionService.ts": "28", "/Users/<USER>/Desktop/platemotion/apps/admin/src/__tests__/e2e/knowledge-base-workflow.test.tsx": "29", "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/__tests__/page.test.tsx": "30", "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx": "31", "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/support/page.tsx": "32", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx": "33", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx": "34", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx": "35", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateCategoryModal.tsx": "36", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/__tests__/ArticleCard.test.tsx": "37", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/__tests__/CategoryCard.test.tsx": "38", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/__tests__/CreateArticleModal.test.tsx": "39", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/__tests__/CreateCategoryModal.test.tsx": "40", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/support/StatusBadge.tsx": "41", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/support/TicketCard.tsx": "42", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/support/TicketDetailModal.tsx": "43", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/support/TicketFilters.tsx": "44", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/avatar.tsx": "45", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/calendar.tsx": "46", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx": "47", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/label.tsx": "48", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/popover.tsx": "49", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/separator.tsx": "50", "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/tabs.tsx": "51", "/Users/<USER>/Desktop/platemotion/apps/admin/src/services/__tests__/knowledgeBaseService.test.ts": "52", "/Users/<USER>/Desktop/platemotion/apps/admin/src/services/knowledgeBaseService.ts": "53", "/Users/<USER>/Desktop/platemotion/apps/admin/src/services/supportService.ts": "54", "/Users/<USER>/Desktop/platemotion/apps/admin/src/types/jest.d.ts": "55"}, {"size": 6260, "mtime": 1754882168540, "results": "56", "hashOfConfig": "57"}, {"size": 828, "mtime": 1754883953311, "results": "58", "hashOfConfig": "57"}, {"size": 11192, "mtime": 1754884656808, "results": "59", "hashOfConfig": "57"}, {"size": 42036, "mtime": 1754953495859, "results": "60", "hashOfConfig": "57"}, {"size": 161, "mtime": 1754882168547, "results": "61", "hashOfConfig": "57"}, {"size": 2878, "mtime": 1754882168555, "results": "62", "hashOfConfig": "57"}, {"size": 9727, "mtime": 1754882236991, "results": "63", "hashOfConfig": "57"}, {"size": 11786, "mtime": 1754885068989, "results": "64", "hashOfConfig": "57"}, {"size": 558, "mtime": 1754882168573, "results": "65", "hashOfConfig": "57"}, {"size": 1820, "mtime": 1754882168577, "results": "66", "hashOfConfig": "57"}, {"size": 6902, "mtime": 1754950254736, "results": "67", "hashOfConfig": "57"}, {"size": 1147, "mtime": 1754882168596, "results": "68", "hashOfConfig": "57"}, {"size": 1996, "mtime": 1754882168604, "results": "69", "hashOfConfig": "57"}, {"size": 1864, "mtime": 1754950254737, "results": "70", "hashOfConfig": "57"}, {"size": 2781, "mtime": 1754882168616, "results": "71", "hashOfConfig": "57"}, {"size": 1922, "mtime": 1754882168621, "results": "72", "hashOfConfig": "57"}, {"size": 1289, "mtime": 1754885614682, "results": "73", "hashOfConfig": "57"}, {"size": 4122, "mtime": 1754885634634, "results": "74", "hashOfConfig": "57"}, {"size": 776, "mtime": 1754882168631, "results": "75", "hashOfConfig": "57"}, {"size": 6465, "mtime": 1754885765912, "results": "76", "hashOfConfig": "57"}, {"size": 2882, "mtime": 1754882168646, "results": "77", "hashOfConfig": "57"}, {"size": 656, "mtime": 1754882168650, "results": "78", "hashOfConfig": "57"}, {"size": 2471, "mtime": 1754884450098, "results": "79", "hashOfConfig": "57"}, {"size": 7039, "mtime": 1754885765964, "results": "80", "hashOfConfig": "57"}, {"size": 865, "mtime": 1754882168661, "results": "81", "hashOfConfig": "57"}, {"size": 169, "mtime": 1754882168663, "results": "82", "hashOfConfig": "57"}, {"size": 12632, "mtime": 1754950254737, "results": "83", "hashOfConfig": "57"}, {"size": 12784, "mtime": 1754888291751, "results": "84", "hashOfConfig": "57"}, {"size": 17418, "mtime": 1754942049766, "results": "85", "hashOfConfig": "57"}, {"size": 15540, "mtime": 1754942049791, "results": "86", "hashOfConfig": "57"}, {"size": 16556, "mtime": 1754953495814, "results": "87", "hashOfConfig": "57"}, {"size": 20855, "mtime": 1754953495873, "results": "88", "hashOfConfig": "57"}, {"size": 8926, "mtime": 1754942297363, "results": "89", "hashOfConfig": "57"}, {"size": 7237, "mtime": 1754942733150, "results": "90", "hashOfConfig": "57"}, {"size": 14167, "mtime": 1754953496001, "results": "91", "hashOfConfig": "57"}, {"size": 12437, "mtime": 1754950254737, "results": "92", "hashOfConfig": "57"}, {"size": 8587, "mtime": 1754942070335, "results": "93", "hashOfConfig": "57"}, {"size": 8213, "mtime": 1754942049844, "results": "94", "hashOfConfig": "57"}, {"size": 18447, "mtime": 1754942049865, "results": "95", "hashOfConfig": "57"}, {"size": 12303, "mtime": 1754942049880, "results": "96", "hashOfConfig": "57"}, {"size": 5917, "mtime": 1754942297434, "results": "97", "hashOfConfig": "57"}, {"size": 10243, "mtime": 1754942758105, "results": "98", "hashOfConfig": "57"}, {"size": 12806, "mtime": 1754953496039, "results": "99", "hashOfConfig": "57"}, {"size": 11962, "mtime": 1754953496052, "results": "100", "hashOfConfig": "57"}, {"size": 1432, "mtime": 1754942297485, "results": "101", "hashOfConfig": "57"}, {"size": 2611, "mtime": 1754942790249, "results": "102", "hashOfConfig": "57"}, {"size": 7350, "mtime": 1754942297518, "results": "103", "hashOfConfig": "57"}, {"size": 734, "mtime": 1754942297523, "results": "104", "hashOfConfig": "57"}, {"size": 1254, "mtime": 1754942297526, "results": "105", "hashOfConfig": "57"}, {"size": 780, "mtime": 1754942297533, "results": "106", "hashOfConfig": "57"}, {"size": 1912, "mtime": 1754942297541, "results": "107", "hashOfConfig": "57"}, {"size": 18269, "mtime": 1754942049899, "results": "108", "hashOfConfig": "57"}, {"size": 21484, "mtime": 1754953496148, "results": "109", "hashOfConfig": "57"}, {"size": 15121, "mtime": 1754953496166, "results": "110", "hashOfConfig": "57"}, {"size": 526, "mtime": 1754942284138, "results": "111", "hashOfConfig": "57"}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xkm9kb", {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/dashboard/page.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/nutrition/__tests__/page.test.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/nutrition/page.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/page.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/users/page.test.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/users/page.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/workouts/page.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/Header.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/Sidebar.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/button.test.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/button.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/card.test.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/card.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/checkbox.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dialog.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/input.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/select.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/table.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/textarea.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/lib/__mocks__/supabase.ts", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/lib/supabase.ts", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/lib/utils.test.ts", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/lib/utils.ts", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/services/__tests__/nutritionService.test.ts", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/services/nutritionService.ts", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/__tests__/e2e/knowledge-base-workflow.test.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/__tests__/page.test.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx", [], ["277", "278"], "/Users/<USER>/Desktop/platemotion/apps/admin/src/app/support/page.tsx", [], ["279", "280"], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateCategoryModal.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/__tests__/ArticleCard.test.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/__tests__/CategoryCard.test.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/__tests__/CreateArticleModal.test.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/__tests__/CreateCategoryModal.test.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/support/StatusBadge.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/support/TicketCard.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/support/TicketDetailModal.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/support/TicketFilters.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/avatar.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/calendar.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/label.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/popover.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/separator.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/tabs.tsx", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/services/__tests__/knowledgeBaseService.test.ts", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/services/knowledgeBaseService.ts", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/services/supportService.ts", [], [], "/Users/<USER>/Desktop/platemotion/apps/admin/src/types/jest.d.ts", [], [], {"ruleId": "281", "severity": 1, "message": "282", "line": 97, "column": 6, "nodeType": "283", "endLine": 97, "endColumn": 8, "suggestions": "284", "suppressions": "285"}, {"ruleId": "281", "severity": 1, "message": "286", "line": 105, "column": 6, "nodeType": "283", "endLine": 105, "endColumn": 15, "suggestions": "287", "suppressions": "288"}, {"ruleId": "281", "severity": 1, "message": "282", "line": 108, "column": 6, "nodeType": "283", "endLine": 108, "endColumn": 8, "suggestions": "289", "suppressions": "290"}, {"ruleId": "281", "severity": 1, "message": "286", "line": 116, "column": 6, "nodeType": "283", "endLine": 116, "endColumn": 15, "suggestions": "291", "suppressions": "292"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", "ArrayExpression", ["293"], ["294"], "React Hook useEffect has missing dependencies: 'loadData' and 'loading'. Either include them or remove the dependency array.", ["295"], ["296"], ["297"], ["298"], ["299"], ["300"], {"desc": "301", "fix": "302"}, {"kind": "303", "justification": "304"}, {"desc": "305", "fix": "306"}, {"kind": "303", "justification": "304"}, {"desc": "301", "fix": "307"}, {"kind": "303", "justification": "304"}, {"desc": "305", "fix": "308"}, {"kind": "303", "justification": "304"}, "Update the dependencies array to be: [loadData]", {"range": "309", "text": "310"}, "directive", "", "Update the dependencies array to be: [filters, loadData, loading]", {"range": "311", "text": "312"}, {"range": "313", "text": "310"}, {"range": "314", "text": "312"}, [2701, 2703], "[loadData]", [2873, 2882], "[filters, loadData, loading]", [3276, 3278], [3448, 3457]]
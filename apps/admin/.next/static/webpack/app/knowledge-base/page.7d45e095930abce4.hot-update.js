"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/knowledge-base/page",{

/***/ "(app-pages-browser)/./src/components/knowledge-base/CreateArticleModal.tsx":
/*!**************************************************************!*\
  !*** ./src/components/knowledge-base/CreateArticleModal.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateArticleModal: function() { return /* binding */ CreateArticleModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _services_knowledgeBaseService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/services/knowledgeBaseService */ \"(app-pages-browser)/./src/services/knowledgeBaseService.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CreateArticleModal(param) {\n    let { open, onClose, onSuccess, categories, editArticle } = param;\n    var _formData_tags;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"content\");\n    const [newTag, setNewTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        slug: \"\",\n        content: \"\",\n        excerpt: \"\",\n        category_id: \"\",\n        meta_title: \"\",\n        meta_description: \"\",\n        tags: [],\n        featured: false,\n        sort_order: 0,\n        status: \"draft\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Auto-generate slug from title\n    const generateSlug = (title)=>{\n        return title.toLowerCase().replace(/[^a-z0-9\\s-]/g, \"\").replace(/\\s+/g, \"-\").replace(/-+/g, \"-\").trim();\n    };\n    const handleTitleChange = (title)=>{\n        setFormData((prev)=>({\n                ...prev,\n                title,\n                slug: generateSlug(title),\n                meta_title: title\n            }));\n    };\n    const handleAddTag = ()=>{\n        var _formData_tags;\n        if (newTag.trim() && !((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.includes(newTag.trim()))) {\n            setFormData((prev)=>({\n                    ...prev,\n                    tags: [\n                        ...prev.tags || [],\n                        newTag.trim()\n                    ]\n                }));\n            setNewTag(\"\");\n        }\n    };\n    const handleRemoveTag = (tagToRemove)=>{\n        setFormData((prev)=>{\n            var _prev_tags;\n            return {\n                ...prev,\n                tags: ((_prev_tags = prev.tags) === null || _prev_tags === void 0 ? void 0 : _prev_tags.filter((tag)=>tag !== tagToRemove)) || []\n            };\n        });\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) {\n            newErrors.title = \"Title is required\";\n        }\n        if (!formData.slug.trim()) {\n            newErrors.slug = \"Slug is required\";\n        } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {\n            newErrors.slug = \"Slug can only contain lowercase letters, numbers, and hyphens\";\n        }\n        if (!formData.content.trim()) {\n            newErrors.content = \"Content is required\";\n        }\n        if (formData.content.length < 50) {\n            newErrors.content = \"Content must be at least 50 characters long\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (status)=>{\n        const submitData = {\n            ...formData,\n            status\n        };\n        if (!validateForm()) {\n            return;\n        }\n        setLoading(true);\n        try {\n            await _services_knowledgeBaseService__WEBPACK_IMPORTED_MODULE_11__.knowledgeBaseService.createArticle(submitData);\n            onSuccess();\n            handleClose();\n        } catch (error) {\n            console.error(\"Error creating article:\", error);\n            setErrors({\n                submit: \"Failed to create article. Please try again.\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleClose = ()=>{\n        setFormData({\n            title: \"\",\n            slug: \"\",\n            content: \"\",\n            excerpt: \"\",\n            category_id: \"\",\n            meta_title: \"\",\n            meta_description: \"\",\n            tags: [],\n            featured: false,\n            sort_order: 0,\n            status: \"draft\"\n        });\n        setErrors({});\n        setNewTag(\"\");\n        setActiveTab(\"content\");\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: handleClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                editArticle ? \"Edit Article\" : \"Create New Article\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            children: editArticle ? \"Update your knowledge base article\" : \"Create a new help article for your knowledge base\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsList, {\n                            className: \"grid w-full grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"content\",\n                                    children: \"Content\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"settings\",\n                                    children: \"Settings\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"seo\",\n                                    children: \"SEO & Meta\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"content\",\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"title\",\n                                                children: \"Article Title *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"title\",\n                                                value: formData.title,\n                                                onChange: (e)=>handleTitleChange(e.target.value),\n                                                placeholder: \"How to create your first meal plan\",\n                                                className: errors.title ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-500 mt-1\",\n                                                children: errors.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"slug\",\n                                                children: \"URL Slug *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm\",\n                                                        children: \"/kb/\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"slug\",\n                                                        value: formData.slug,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    slug: e.target.value\n                                                                })),\n                                                        placeholder: \"how-to-create-first-meal-plan\",\n                                                        className: \"rounded-l-none \".concat(errors.slug ? \"border-red-500\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.slug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-500 mt-1\",\n                                                children: errors.slug\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"excerpt\",\n                                                children: \"Excerpt\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                id: \"excerpt\",\n                                                value: formData.excerpt,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            excerpt: e.target.value\n                                                        })),\n                                                placeholder: \"Brief summary of the article for search results...\",\n                                                rows: 2\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                children: \"Optional short summary displayed in search results\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"content\",\n                                                children: \"Article Content *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                id: \"content\",\n                                                value: formData.content,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            content: e.target.value\n                                                        })),\n                                                placeholder: \"Write your article content here. You can use Markdown formatting...\",\n                                                rows: 12,\n                                                className: errors.content ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-500 mt-1\",\n                                                children: errors.content\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                children: \"Supports Markdown formatting. Minimum 50 characters required.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"settings\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"category\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                    value: formData.category_id,\n                                                    onValueChange: (value)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                category_id: value\n                                                            })),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                placeholder: \"Select a category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                    value: \"none\",\n                                                                    children: \"No Category\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                        value: category.id,\n                                                                        children: category.name\n                                                                    }, category.id, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"sort_order\",\n                                                    children: \"Sort Order\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"sort_order\",\n                                                    type: \"number\",\n                                                    value: formData.sort_order,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                sort_order: parseInt(e.target.value) || 0\n                                                            })),\n                                                    placeholder: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground mt-1\",\n                                                    children: \"Lower numbers appear first\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                            id: \"featured\",\n                                            checked: formData.featured,\n                                            onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        featured: !!checked\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"featured\",\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Featured Article\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"Tags\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-2\",\n                                            children: (_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        tag,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-3 h-3 cursor-pointer hover:text-red-500\",\n                                                            onClick: ()=>handleRemoveTag(tag)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    value: newTag,\n                                                    onChange: (e)=>setNewTag(e.target.value),\n                                                    placeholder: \"Add a tag...\",\n                                                    onKeyPress: (e)=>e.key === \"Enter\" && handleAddTag()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    onClick: handleAddTag,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"seo\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"meta_title\",\n                                            children: \"Meta Title\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"meta_title\",\n                                            value: formData.meta_title,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        meta_title: e.target.value\n                                                    })),\n                                            placeholder: \"SEO title for search engines\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mt-1\",\n                                            children: \"Recommended: 50-60 characters\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"meta_description\",\n                                            children: \"Meta Description\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                            id: \"meta_description\",\n                                            value: formData.meta_description,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        meta_description: e.target.value\n                                                    })),\n                                            placeholder: \"Brief description for search engine results\",\n                                            rows: 3\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mt-1\",\n                                            children: \"Recommended: 150-160 characters\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this),\n                errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-md p-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600\",\n                        children: errors.submit\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                    lineNumber: 374,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between pt-4 border-t\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: handleClose,\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>handleSubmit(\"draft\"),\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Save as Draft\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>handleSubmit(\"published\"),\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this),\n                                        loading ? \"Publishing...\" : \"Publish Article\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n            lineNumber: 173,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n        lineNumber: 172,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateArticleModal, \"nye5qRNPh/YSezTc/iheTEIXQkI=\");\n_c = CreateArticleModal;\nvar _c;\n$RefreshReg$(_c, \"CreateArticleModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/knowledge-base/CreateArticleModal.tsx\n"));

/***/ })

});
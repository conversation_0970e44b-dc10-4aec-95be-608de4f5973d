"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/knowledge-base/page",{

/***/ "(app-pages-browser)/./src/components/ui/dropdown-menu.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/dropdown-menu.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: function() { return /* binding */ DropdownMenu; },\n/* harmony export */   DropdownMenuCheckboxItem: function() { return /* binding */ DropdownMenuCheckboxItem; },\n/* harmony export */   DropdownMenuContent: function() { return /* binding */ DropdownMenuContent; },\n/* harmony export */   DropdownMenuGroup: function() { return /* binding */ DropdownMenuGroup; },\n/* harmony export */   DropdownMenuItem: function() { return /* binding */ DropdownMenuItem; },\n/* harmony export */   DropdownMenuLabel: function() { return /* binding */ DropdownMenuLabel; },\n/* harmony export */   DropdownMenuPortal: function() { return /* binding */ DropdownMenuPortal; },\n/* harmony export */   DropdownMenuRadioGroup: function() { return /* binding */ DropdownMenuRadioGroup; },\n/* harmony export */   DropdownMenuRadioItem: function() { return /* binding */ DropdownMenuRadioItem; },\n/* harmony export */   DropdownMenuSeparator: function() { return /* binding */ DropdownMenuSeparator; },\n/* harmony export */   DropdownMenuShortcut: function() { return /* binding */ DropdownMenuShortcut; },\n/* harmony export */   DropdownMenuSub: function() { return /* binding */ DropdownMenuSub; },\n/* harmony export */   DropdownMenuSubContent: function() { return /* binding */ DropdownMenuSubContent; },\n/* harmony export */   DropdownMenuSubTrigger: function() { return /* binding */ DropdownMenuSubTrigger; },\n/* harmony export */   DropdownMenuTrigger: function() { return /* binding */ DropdownMenuTrigger; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(app-pages-browser)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuGroup,DropdownMenuPortal,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuRadioGroup auto */ \n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, inset, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = DropdownMenuSubTrigger;\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined);\n});\n_c3 = DropdownMenuSubContent;\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>{\n    let { className, sideOffset = 4, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined);\n});\n_c5 = DropdownMenuContent;\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = (param, ref)=>{\n    let { className, inset, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n        lineNumber: 83,\n        columnNumber: 3\n    }, undefined);\n});\n_c7 = DropdownMenuItem;\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c8 = (param, ref)=>{\n    let { className, children, checked, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n                lineNumber: 108,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined);\n});\n_c9 = DropdownMenuCheckboxItem;\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c10 = (param, ref)=>{\n    let { className, children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined);\n});\n_c11 = DropdownMenuRadioItem;\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c12 = (param, ref)=>{\n    let { className, inset, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n        lineNumber: 147,\n        columnNumber: 3\n    }, undefined);\n});\n_c13 = DropdownMenuLabel;\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c14 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n        lineNumber: 163,\n        columnNumber: 3\n    }, undefined);\n});\n_c15 = DropdownMenuSeparator;\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = (param)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/dropdown-menu.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\n_c16 = DropdownMenuShortcut;\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"DropdownMenuSubTrigger$React.forwardRef\");\n$RefreshReg$(_c1, \"DropdownMenuSubTrigger\");\n$RefreshReg$(_c2, \"DropdownMenuSubContent$React.forwardRef\");\n$RefreshReg$(_c3, \"DropdownMenuSubContent\");\n$RefreshReg$(_c4, \"DropdownMenuContent$React.forwardRef\");\n$RefreshReg$(_c5, \"DropdownMenuContent\");\n$RefreshReg$(_c6, \"DropdownMenuItem$React.forwardRef\");\n$RefreshReg$(_c7, \"DropdownMenuItem\");\n$RefreshReg$(_c8, \"DropdownMenuCheckboxItem$React.forwardRef\");\n$RefreshReg$(_c9, \"DropdownMenuCheckboxItem\");\n$RefreshReg$(_c10, \"DropdownMenuRadioItem$React.forwardRef\");\n$RefreshReg$(_c11, \"DropdownMenuRadioItem\");\n$RefreshReg$(_c12, \"DropdownMenuLabel$React.forwardRef\");\n$RefreshReg$(_c13, \"DropdownMenuLabel\");\n$RefreshReg$(_c14, \"DropdownMenuSeparator$React.forwardRef\");\n$RefreshReg$(_c15, \"DropdownMenuSeparator\");\n$RefreshReg$(_c16, \"DropdownMenuShortcut\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: function() { return /* binding */ Label; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Label;\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Label$React.forwardRef\");\n$RefreshReg$(_c1, \"Label\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUUrQjtBQUN5QjtBQUNVO0FBRWpDO0FBRWpDLE1BQU1JLGdCQUFnQkYsNkRBQUdBLENBQ3ZCO0FBR0YsTUFBTUcsc0JBQVFMLDZDQUFnQixNQUk1QixRQUEwQk87UUFBekIsRUFBRUMsU0FBUyxFQUFFLEdBQUdDLE9BQU87eUJBQ3hCLDhEQUFDUix1REFBbUI7UUFDbEJNLEtBQUtBO1FBQ0xDLFdBQVdMLDhDQUFFQSxDQUFDQyxpQkFBaUJJO1FBQzlCLEdBQUdDLEtBQUs7Ozs7Ozs7O0FBR2JKLE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4PzEzZWIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0ICogYXMgTGFiZWxQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1sYWJlbFwiO1xuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIjtcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIjtcblxuY29uc3QgbGFiZWxWYXJpYW50cyA9IGN2YShcbiAgXCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGxlYWRpbmctbm9uZSBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNzBcIixcbik7XG5cbmNvbnN0IExhYmVsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgbGFiZWxWYXJpYW50cz5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKGxhYmVsVmFyaWFudHMoKSwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKTtcbkxhYmVsLmRpc3BsYXlOYW1lID0gTGFiZWxQcmltaXRpdmUuUm9vdC5kaXNwbGF5TmFtZTtcblxuZXhwb3J0IHsgTGFiZWwgfTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwicmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJSb290IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/label.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/tabs.tsx":
/*!************************************!*\
  !*** ./src/components/ui/tabs.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: function() { return /* binding */ Tabs; },\n/* harmony export */   TabsContent: function() { return /* binding */ TabsContent; },\n/* harmony export */   TabsList: function() { return /* binding */ TabsList; },\n/* harmony export */   TabsTrigger: function() { return /* binding */ TabsTrigger; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tabs */ \"(app-pages-browser)/./node_modules/@radix-ui/react-tabs/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tabs,TabsList,TabsTrigger,TabsContent auto */ \n\n\n\nconst Tabs = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst TabsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/tabs.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = TabsList;\nTabsList.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List.displayName;\nconst TabsTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/tabs.tsx\",\n        lineNumber: 29,\n        columnNumber: 3\n    }, undefined);\n});\n_c3 = TabsTrigger;\nTabsTrigger.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst TabsContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/ui/tabs.tsx\",\n        lineNumber: 44,\n        columnNumber: 3\n    }, undefined);\n});\n_c5 = TabsContent;\nTabsContent.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"TabsList$React.forwardRef\");\n$RefreshReg$(_c1, \"TabsList\");\n$RefreshReg$(_c2, \"TabsTrigger$React.forwardRef\");\n$RefreshReg$(_c3, \"TabsTrigger\");\n$RefreshReg$(_c4, \"TabsContent$React.forwardRef\");\n$RefreshReg$(_c5, \"TabsContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/tabs.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/knowledgeBaseService.ts":
/*!**********************************************!*\
  !*** ./src/services/knowledgeBaseService.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   knowledgeBaseService: function() { return /* binding */ knowledgeBaseService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n// Helper function to check if Supabase is properly configured\nfunction isSupabaseConfigured() {\n    return !!( true && process.env.SUPABASE_SERVICE_ROLE_KEY);\n}\nclass KnowledgeBaseService {\n    /**\n   * Get all categories with optional hierarchy\n   */ async getCategories() {\n        let includeInactive = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (!isSupabaseConfigured()) {\n            console.warn(\"Supabase not configured, returning mock categories\");\n            return this.getMockCategories();\n        }\n        try {\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_categories\").select(\"\\n          id,\\n          name,\\n          description,\\n          slug,\\n          parent_id,\\n          sort_order,\\n          icon,\\n          color,\\n          is_active,\\n          created_at,\\n          updated_at,\\n          created_by\\n        \").order(\"sort_order\", {\n                ascending: true\n            }).order(\"name\", {\n                ascending: true\n            });\n            if (!includeInactive) {\n                query = query.eq(\"is_active\", true);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error(\"Error fetching categories:\", error);\n                throw error;\n            }\n            // Get article counts for each category\n            const categoriesWithCounts = await Promise.all((data || []).map(async (category)=>{\n                const { count } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_articles\").select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"category_id\", category.id).eq(\"status\", \"published\");\n                return {\n                    ...category,\n                    article_count: count || 0\n                };\n            }));\n            return categoriesWithCounts;\n        } catch (error) {\n            console.error(\"Error in getCategories:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get a single category by ID or slug\n   */ async getCategoryById(id) {\n        if (!isSupabaseConfigured()) {\n            const mockCategories = this.getMockCategories();\n            return mockCategories.find((c)=>c.id === id) || null;\n        }\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_categories\").select(\"*\").eq(\"id\", id).single();\n            if (error) {\n                console.error(\"Error fetching category:\", error);\n                throw error;\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error in getCategoryById:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new category\n   */ async createCategory(categoryData) {\n        if (!isSupabaseConfigured()) {\n            console.warn(\"Supabase not configured, returning mock ID\");\n            return \"mock-category-id\";\n        }\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_categories\").insert({\n                ...categoryData,\n                created_by: null\n            }).select(\"id\").single();\n            if (error) {\n                console.error(\"Error creating category:\", error);\n                throw error;\n            }\n            return data.id;\n        } catch (error) {\n            console.error(\"Error in createCategory:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Update an existing category\n   */ async updateCategory(id, updates) {\n        if (!isSupabaseConfigured()) {\n            console.warn(\"Supabase not configured, skipping update\");\n            return;\n        }\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_categories\").update({\n                ...updates,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", id);\n            if (error) {\n                console.error(\"Error updating category:\", error);\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"Error in updateCategory:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Delete a category\n   */ async deleteCategory(id) {\n        if (!isSupabaseConfigured()) {\n            console.warn(\"Supabase not configured, skipping delete\");\n            return;\n        }\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_categories\").delete().eq(\"id\", id);\n            if (error) {\n                console.error(\"Error deleting category:\", error);\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"Error in deleteCategory:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get articles with filtering and pagination\n   */ async getArticles() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        if (!isSupabaseConfigured()) {\n            console.warn(\"Supabase not configured, returning mock articles\");\n            return this.getMockArticles();\n        }\n        try {\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_articles\").select(\"\\n          id,\\n          title,\\n          slug,\\n          content,\\n          excerpt,\\n          category_id,\\n          author_id,\\n          status,\\n          published_at,\\n          meta_title,\\n          meta_description,\\n          tags,\\n          view_count,\\n          helpful_count,\\n          not_helpful_count,\\n          featured,\\n          sort_order,\\n          created_at,\\n          updated_at,\\n          support_kb_categories!category_id (\\n            name,\\n            slug\\n          ),\\n          profiles!author_id (\\n            full_name,\\n            email\\n          )\\n        \").order(\"created_at\", {\n                ascending: false\n            });\n            // Apply filters\n            if (filters.search) {\n                query = query.textSearch(\"search_vector\", filters.search);\n            }\n            if (filters.category_id) {\n                query = query.eq(\"category_id\", filters.category_id);\n            }\n            if (filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters.featured !== undefined) {\n                query = query.eq(\"featured\", filters.featured);\n            }\n            if (filters.author_id) {\n                query = query.eq(\"author_id\", filters.author_id);\n            }\n            if (filters.tags && filters.tags.length > 0) {\n                query = query.overlaps(\"tags\", filters.tags);\n            }\n            // Pagination\n            if (filters.limit) {\n                query = query.limit(filters.limit);\n            }\n            if (filters.offset) {\n                query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);\n            }\n            const { data, error } = await query;\n            if (error) {\n                console.error(\"Error fetching articles:\", error);\n                throw error;\n            }\n            // Transform the data to include joined fields\n            return (data || []).map((article)=>{\n                var _article_support_kb_categories, _article_support_kb_categories1, _article_profiles, _article_profiles1;\n                return {\n                    ...article,\n                    category_name: ((_article_support_kb_categories = article.support_kb_categories) === null || _article_support_kb_categories === void 0 ? void 0 : _article_support_kb_categories.name) || null,\n                    category_slug: ((_article_support_kb_categories1 = article.support_kb_categories) === null || _article_support_kb_categories1 === void 0 ? void 0 : _article_support_kb_categories1.slug) || null,\n                    author_name: ((_article_profiles = article.profiles) === null || _article_profiles === void 0 ? void 0 : _article_profiles.full_name) || \"Unknown Author\",\n                    author_email: ((_article_profiles1 = article.profiles) === null || _article_profiles1 === void 0 ? void 0 : _article_profiles1.email) || \"\",\n                    helpfulness_ratio: article.helpful_count + article.not_helpful_count > 0 ? article.helpful_count / (article.helpful_count + article.not_helpful_count) : 0\n                };\n            });\n        } catch (error) {\n            console.error(\"Error in getArticles:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get a single article by ID or slug\n   */ async getArticleById(id) {\n        if (!isSupabaseConfigured()) {\n            const mockArticles = this.getMockArticles();\n            return mockArticles.find((a)=>a.id === id) || null;\n        }\n        try {\n            var _data_support_kb_categories, _data_support_kb_categories1, _data_profiles, _data_profiles1;\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_articles\").select(\"\\n          *,\\n          support_kb_categories!category_id (\\n            name,\\n            slug\\n          ),\\n          profiles!author_id (\\n            full_name,\\n            email\\n          )\\n        \").eq(\"id\", id).single();\n            if (error) {\n                console.error(\"Error fetching article:\", error);\n                throw error;\n            }\n            if (!data) return null;\n            return {\n                ...data,\n                category_name: ((_data_support_kb_categories = data.support_kb_categories) === null || _data_support_kb_categories === void 0 ? void 0 : _data_support_kb_categories.name) || null,\n                category_slug: ((_data_support_kb_categories1 = data.support_kb_categories) === null || _data_support_kb_categories1 === void 0 ? void 0 : _data_support_kb_categories1.slug) || null,\n                author_name: ((_data_profiles = data.profiles) === null || _data_profiles === void 0 ? void 0 : _data_profiles.full_name) || \"Unknown Author\",\n                author_email: ((_data_profiles1 = data.profiles) === null || _data_profiles1 === void 0 ? void 0 : _data_profiles1.email) || \"\",\n                helpfulness_ratio: data.helpful_count + data.not_helpful_count > 0 ? data.helpful_count / (data.helpful_count + data.not_helpful_count) : 0\n            };\n        } catch (error) {\n            console.error(\"Error in getArticleById:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create a new article\n   */ async createArticle(articleData) {\n        if (!isSupabaseConfigured()) {\n            console.warn(\"Supabase not configured, returning mock ID\");\n            return \"mock-article-id\";\n        }\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_articles\").insert({\n                ...articleData,\n                author_id: null,\n                published_at: articleData.status === \"published\" ? new Date().toISOString() : null\n            }).select(\"id\").single();\n            if (error) {\n                console.error(\"Error creating article:\", error);\n                throw error;\n            }\n            return data.id;\n        } catch (error) {\n            console.error(\"Error in createArticle:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Update an existing article\n   */ async updateArticle(id, updates) {\n        if (!isSupabaseConfigured()) {\n            console.warn(\"Supabase not configured, skipping update\");\n            return;\n        }\n        try {\n            const updateData = {\n                ...updates,\n                updated_at: new Date().toISOString()\n            };\n            // Set published_at when publishing\n            if (updates.status === \"published\") {\n                updateData.published_at = new Date().toISOString();\n            }\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_articles\").update(updateData).eq(\"id\", id);\n            if (error) {\n                console.error(\"Error updating article:\", error);\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"Error in updateArticle:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Delete an article\n   */ async deleteArticle(id) {\n        if (!isSupabaseConfigured()) {\n            console.warn(\"Supabase not configured, skipping delete\");\n            return;\n        }\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_articles\").delete().eq(\"id\", id);\n            if (error) {\n                console.error(\"Error deleting article:\", error);\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"Error in deleteArticle:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Search articles with full-text search\n   */ async searchArticles(query) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        if (!isSupabaseConfigured()) {\n            console.warn(\"Supabase not configured, returning mock search results\");\n            return [];\n        }\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_articles\").select(\"\\n          *,\\n          support_kb_categories!category_id (\\n            name,\\n            slug\\n          )\\n        \").textSearch(\"search_vector\", query).eq(\"status\", \"published\").limit(limit);\n            if (error) {\n                console.error(\"Error searching articles:\", error);\n                throw error;\n            }\n            return (data || []).map((article, index)=>{\n                var _article_support_kb_categories, _article_support_kb_categories1;\n                return {\n                    article: {\n                        ...article,\n                        category_name: ((_article_support_kb_categories = article.support_kb_categories) === null || _article_support_kb_categories === void 0 ? void 0 : _article_support_kb_categories.name) || null,\n                        category_slug: ((_article_support_kb_categories1 = article.support_kb_categories) === null || _article_support_kb_categories1 === void 0 ? void 0 : _article_support_kb_categories1.slug) || null\n                    },\n                    rank: index + 1,\n                    headline: this.generateHeadline(article.content, query)\n                };\n            });\n        } catch (error) {\n            console.error(\"Error in searchArticles:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get knowledge base statistics\n   */ async getKBStats() {\n        if (!isSupabaseConfigured()) {\n            console.warn(\"Supabase not configured, returning mock stats\");\n            return this.getMockStats();\n        }\n        try {\n            const [articlesData, categoriesData, viewsData, feedbackData] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_articles\").select(\"status, view_count\"),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_categories\").select(\"id\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_article_views\").select(\"id\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_feedback\").select(\"is_helpful\")\n            ]);\n            const articles = articlesData.data || [];\n            const totalViews = articles.reduce((sum, article)=>sum + (article.view_count || 0), 0);\n            const feedback = feedbackData.data || [];\n            const helpfulFeedback = feedback.filter((f)=>f.is_helpful).length;\n            const avgHelpfulness = feedback.length > 0 ? helpfulFeedback / feedback.length : 0;\n            // Get popular and recent articles\n            const [popularArticles, recentArticles] = await Promise.all([\n                this.getArticles({\n                    status: \"published\",\n                    limit: 5\n                }),\n                this.getArticles({\n                    status: \"published\",\n                    limit: 5\n                })\n            ]);\n            return {\n                total_articles: articles.length,\n                published_articles: articles.filter((a)=>a.status === \"published\").length,\n                draft_articles: articles.filter((a)=>a.status === \"draft\").length,\n                total_categories: categoriesData.count || 0,\n                total_views: totalViews,\n                total_feedback: feedback.length,\n                avg_helpfulness: avgHelpfulness,\n                popular_articles: popularArticles.slice(0, 5),\n                recent_articles: recentArticles.slice(0, 5)\n            };\n        } catch (error) {\n            console.error(\"Error in getKBStats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Record article feedback\n   */ async recordFeedback(articleId, isHelpful, feedbackText, userId, sessionId) {\n        if (!isSupabaseConfigured()) {\n            console.warn(\"Supabase not configured, skipping feedback\");\n            return;\n        }\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"support_kb_feedback\").insert({\n                article_id: articleId,\n                user_id: userId || null,\n                session_id: sessionId || null,\n                is_helpful: isHelpful,\n                feedback_text: feedbackText || null\n            });\n            if (error) {\n                console.error(\"Error recording feedback:\", error);\n                throw error;\n            }\n        } catch (error) {\n            console.error(\"Error in recordFeedback:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Generate search headline with highlighted terms\n   */ generateHeadline(content, query) {\n        const words = query.toLowerCase().split(\" \");\n        const sentences = content.split(\". \");\n        // Find sentence containing query terms\n        const matchingSentence = sentences.find((sentence)=>words.some((word)=>sentence.toLowerCase().includes(word)));\n        if (matchingSentence) {\n            return matchingSentence.substring(0, 150) + \"...\";\n        }\n        return content.substring(0, 150) + \"...\";\n    }\n    /**\n   * Mock data for development/testing\n   */ getMockCategories() {\n        return [\n            {\n                id: \"1\",\n                name: \"Getting Started\",\n                description: \"New user guides and basic app navigation\",\n                slug: \"getting-started\",\n                parent_id: null,\n                sort_order: 0,\n                icon: \"play-circle\",\n                color: \"#06B6D4\",\n                is_active: true,\n                created_at: \"2024-01-01T00:00:00Z\",\n                updated_at: \"2024-01-01T00:00:00Z\",\n                created_by: null,\n                article_count: 5\n            },\n            {\n                id: \"2\",\n                name: \"Meal Planning\",\n                description: \"AI meal planner, dietary restrictions, and nutrition guidance\",\n                slug: \"meal-planning\",\n                parent_id: null,\n                sort_order: 1,\n                icon: \"utensils\",\n                color: \"#10B981\",\n                is_active: true,\n                created_at: \"2024-01-01T00:00:00Z\",\n                updated_at: \"2024-01-01T00:00:00Z\",\n                created_by: null,\n                article_count: 8\n            }\n        ];\n    }\n    getMockArticles() {\n        return [\n            {\n                id: \"1\",\n                title: \"How to Create Your First Meal Plan\",\n                slug: \"how-to-create-first-meal-plan\",\n                content: \"Learn how to use PlateMotion's AI meal planner to create personalized meal plans...\",\n                excerpt: \"Step-by-step guide to creating your first AI-generated meal plan\",\n                category_id: \"2\",\n                author_id: \"admin1\",\n                status: \"published\",\n                published_at: \"2024-01-01T00:00:00Z\",\n                meta_title: null,\n                meta_description: null,\n                tags: [\n                    \"meal-planning\",\n                    \"getting-started\",\n                    \"ai\"\n                ],\n                view_count: 150,\n                helpful_count: 12,\n                not_helpful_count: 2,\n                featured: true,\n                sort_order: 0,\n                created_at: \"2024-01-01T00:00:00Z\",\n                updated_at: \"2024-01-01T00:00:00Z\",\n                category_name: \"Meal Planning\",\n                category_slug: \"meal-planning\",\n                author_name: \"Support Team\",\n                author_email: \"<EMAIL>\",\n                helpfulness_ratio: 0.86\n            }\n        ];\n    }\n    getMockStats() {\n        return {\n            total_articles: 25,\n            published_articles: 20,\n            draft_articles: 5,\n            total_categories: 6,\n            total_views: 1250,\n            total_feedback: 85,\n            avg_helpfulness: 0.82,\n            popular_articles: [],\n            recent_articles: []\n        };\n    }\n}\nconst knowledgeBaseService = new KnowledgeBaseService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/knowledgeBaseService.ts\n"));

/***/ })

});
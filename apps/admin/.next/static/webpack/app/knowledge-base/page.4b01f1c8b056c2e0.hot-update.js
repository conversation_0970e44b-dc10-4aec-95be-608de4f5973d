"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/knowledge-base/page",{

/***/ "(app-pages-browser)/./src/app/knowledge-base/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/knowledge-base/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KnowledgeBasePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AdminLayout */ \"(app-pages-browser)/./src/components/AdminLayout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,Plus,RefreshCw,Search,Settings,Tag,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,Plus,RefreshCw,Search,Settings,Tag,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,Plus,RefreshCw,Search,Settings,Tag,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,Plus,RefreshCw,Search,Settings,Tag,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,Plus,RefreshCw,Search,Settings,Tag,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,Plus,RefreshCw,Search,Settings,Tag,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,Plus,RefreshCw,Search,Settings,Tag,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Eye,Plus,RefreshCw,Search,Settings,Tag,ThumbsUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _services_knowledgeBaseService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/knowledgeBaseService */ \"(app-pages-browser)/./src/services/knowledgeBaseService.ts\");\n/* harmony import */ var _components_knowledge_base_ArticleCard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/knowledge-base/ArticleCard */ \"(app-pages-browser)/./src/components/knowledge-base/ArticleCard.tsx\");\n/* harmony import */ var _components_knowledge_base_CategoryCard__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/knowledge-base/CategoryCard */ \"(app-pages-browser)/./src/components/knowledge-base/CategoryCard.tsx\");\n/* harmony import */ var _components_knowledge_base_CreateArticleModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/knowledge-base/CreateArticleModal */ \"(app-pages-browser)/./src/components/knowledge-base/CreateArticleModal.tsx\");\n/* harmony import */ var _components_knowledge_base_CreateCategoryModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/knowledge-base/CreateCategoryModal */ \"(app-pages-browser)/./src/components/knowledge-base/CreateCategoryModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Import services and components\n\n\n\n\n\nfunction KnowledgeBasePage() {\n    _s();\n    const [articles, setArticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total_articles: 0,\n        published_articles: 0,\n        draft_articles: 0,\n        total_categories: 0,\n        total_views: 0,\n        total_feedback: 0,\n        avg_helpfulness: 0,\n        popular_articles: [],\n        recent_articles: []\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"articles\");\n    const [showCreateArticle, setShowCreateArticle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCreateCategory, setShowCreateCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filters\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        category_id: \"\",\n        status: \"all\",\n        featured: undefined,\n        limit: 20,\n        offset: 0\n    });\n    // Load data\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            const [articlesData, categoriesData, statsData] = await Promise.all([\n                _services_knowledgeBaseService__WEBPACK_IMPORTED_MODULE_8__.knowledgeBaseService.getArticles(filters),\n                _services_knowledgeBaseService__WEBPACK_IMPORTED_MODULE_8__.knowledgeBaseService.getCategories(),\n                _services_knowledgeBaseService__WEBPACK_IMPORTED_MODULE_8__.knowledgeBaseService.getKBStats()\n            ]);\n            setArticles(articlesData);\n            setCategories(categoriesData);\n            setStats(statsData);\n        } catch (error) {\n            console.error(\"Error loading knowledge base data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n    }, []);\n    // Reload when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading) {\n            loadData();\n        }\n    }, [\n        filters\n    ]);\n    // Refresh data\n    const handleRefresh = async ()=>{\n        setRefreshing(true);\n        await loadData();\n        setRefreshing(false);\n    };\n    // Filter handlers\n    const handleSearchChange = (search)=>{\n        setFilters((prev)=>({\n                ...prev,\n                search,\n                offset: 0\n            }));\n    };\n    const handleCategoryFilter = (category_id)=>{\n        setFilters((prev)=>({\n                ...prev,\n                category_id: category_id === \"all\" ? \"\" : category_id,\n                offset: 0\n            }));\n    };\n    const handleStatusFilter = (status)=>{\n        setFilters((prev)=>({\n                ...prev,\n                status: status === \"all\" ? \"\" : status,\n                offset: 0\n            }));\n    };\n    const handleFeaturedFilter = (featured)=>{\n        setFilters((prev)=>({\n                ...prev,\n                featured: featured === \"all\" ? undefined : featured === \"true\",\n                offset: 0\n            }));\n    };\n    // Article actions\n    const handleArticleCreated = ()=>{\n        setShowCreateArticle(false);\n        loadData();\n    };\n    const handleArticleEdit = (article)=>{\n        // TODO: Open edit modal\n        console.log(\"Edit article:\", article.id);\n    };\n    const handleArticleDelete = async (article)=>{\n        if (confirm('Are you sure you want to delete \"'.concat(article.title, '\"?'))) {\n            try {\n                await _services_knowledgeBaseService__WEBPACK_IMPORTED_MODULE_8__.knowledgeBaseService.deleteArticle(article.id);\n                loadData();\n            } catch (error) {\n                console.error(\"Error deleting article:\", error);\n            }\n        }\n    };\n    // Category actions\n    const handleCategoryCreated = ()=>{\n        setShowCreateCategory(false);\n        loadData();\n    };\n    const handleCategoryEdit = (category)=>{\n        // TODO: Open edit modal\n        console.log(\"Edit category:\", category.id);\n    };\n    const handleCategoryDelete = async (category)=>{\n        if (confirm('Are you sure you want to delete \"'.concat(category.name, '\"?'))) {\n            try {\n                await _services_knowledgeBaseService__WEBPACK_IMPORTED_MODULE_8__.knowledgeBaseService.deleteCategory(category.id);\n                loadData();\n            } catch (error) {\n                console.error(\"Error deleting category:\", error);\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdminLayout__WEBPACK_IMPORTED_MODULE_2__.AdminLayout, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"Loading knowledge base...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n            lineNumber: 202,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdminLayout__WEBPACK_IMPORTED_MODULE_2__.AdminLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold tracking-tight\",\n                                    children: \"Knowledge Base\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Manage help articles and FAQs for customer self-service\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleRefresh,\n                                    disabled: refreshing,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2 \".concat(refreshing ? \"animate-spin\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Refresh\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>setShowCreateCategory(true),\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Manage Categories\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>setShowCreateArticle(true),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"New Article\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Articles\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.total_articles\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                stats.published_articles,\n                                                \" published, \",\n                                                stats.draft_articles,\n                                                \" \",\n                                                \"drafts\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: stats.total_categories\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Active categories\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Views\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: stats.total_views.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Article views\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Helpfulness\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: [\n                                                Math.round(stats.avg_helpfulness * 100),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                stats.total_feedback,\n                                                \" total ratings\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"Knowledge Base Management\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                                value: activeTab,\n                                onValueChange: setActiveTab,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                        className: \"grid w-full grid-cols-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"articles\",\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Articles (\",\n                                                    stats.total_articles,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                                value: \"categories\",\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Categories (\",\n                                                    stats.total_categories,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"articles\",\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"absolute left-3 top-3 h-4 w-4 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    placeholder: \"Search articles...\",\n                                                                    value: filters.search,\n                                                                    onChange: (e)=>handleSearchChange(e.target.value),\n                                                                    className: \"pl-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        value: filters.category_id || \"all\",\n                                                        onValueChange: handleCategoryFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                className: \"w-[180px]\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"All Categories\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All Categories\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: category.id,\n                                                                            children: category.name\n                                                                        }, category.id, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                            lineNumber: 363,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        value: filters.status || \"all\",\n                                                        onValueChange: handleStatusFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                className: \"w-[140px]\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"All Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: \"published\",\n                                                                        children: \"Published\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: \"draft\",\n                                                                        children: \"Draft\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: \"archived\",\n                                                                        children: \"Archived\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        value: filters.featured === undefined ? \"all\" : filters.featured.toString(),\n                                                        onValueChange: handleFeaturedFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                className: \"w-[130px]\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Featured\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All Articles\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                        lineNumber: 397,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: \"true\",\n                                                                        children: \"Featured\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: \"false\",\n                                                                        children: \"Not Featured\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-4\",\n                                                children: articles.length > 0 ? articles.map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_knowledge_base_ArticleCard__WEBPACK_IMPORTED_MODULE_9__.ArticleCard, {\n                                                        article: article,\n                                                        onEdit: handleArticleEdit,\n                                                        onDelete: handleArticleDelete\n                                                    }, article.id, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 23\n                                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"mt-4 text-lg font-semibold\",\n                                                            children: \"No articles found\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-2 text-muted-foreground\",\n                                                            children: filters.search || filters.category_id || filters.status !== \"all\" ? \"Try adjusting your search or filters.\" : \"Get started by creating your first article.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        !filters.search && !filters.category_id && filters.status === \"all\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            className: \"mt-4\",\n                                                            onClick: ()=>setShowCreateArticle(true),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                \"Create First Article\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                        value: \"categories\",\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid gap-4 md:grid-cols-2 lg:grid-cols-3\",\n                                            children: categories.length > 0 ? categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_knowledge_base_CategoryCard__WEBPACK_IMPORTED_MODULE_10__.CategoryCard, {\n                                                    category: category,\n                                                    onEdit: handleCategoryEdit,\n                                                    onDelete: handleCategoryDelete\n                                                }, category.id, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 23\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-full text-center py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"mx-auto h-12 w-12 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mt-4 text-lg font-semibold\",\n                                                        children: \"No categories found\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-2 text-muted-foreground\",\n                                                        children: \"Create categories to organize your articles.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        className: \"mt-4\",\n                                                        onClick: ()=>setShowCreateCategory(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Eye_Plus_RefreshCw_Search_Settings_Tag_ThumbsUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Create First Category\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_knowledge_base_CreateArticleModal__WEBPACK_IMPORTED_MODULE_11__.CreateArticleModal, {\n                    open: showCreateArticle,\n                    onClose: ()=>setShowCreateArticle(false),\n                    onSuccess: handleArticleCreated,\n                    categories: categories\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                    lineNumber: 481,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_knowledge_base_CreateCategoryModal__WEBPACK_IMPORTED_MODULE_12__.CreateCategoryModal, {\n                    open: showCreateCategory,\n                    onClose: ()=>setShowCreateCategory(false),\n                    onSuccess: handleCategoryCreated,\n                    categories: categories\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/app/knowledge-base/page.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(KnowledgeBasePage, \"25VHQ5/bLLltlgMq9sPu9dThcYk=\");\n_c = KnowledgeBasePage;\nvar _c;\n$RefreshReg$(_c, \"KnowledgeBasePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/knowledge-base/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/knowledge-base/ArticleCard.tsx":
/*!*******************************************************!*\
  !*** ./src/components/knowledge-base/ArticleCard.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArticleCard: function() { return /* binding */ ArticleCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Copy,Edit,ExternalLink,Eye,MoreVertical,Star,Tag,ThumbsDown,ThumbsUp,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Copy,Edit,ExternalLink,Eye,MoreVertical,Star,Tag,ThumbsDown,ThumbsUp,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Copy,Edit,ExternalLink,Eye,MoreVertical,Star,Tag,ThumbsDown,ThumbsUp,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Copy,Edit,ExternalLink,Eye,MoreVertical,Star,Tag,ThumbsDown,ThumbsUp,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Copy,Edit,ExternalLink,Eye,MoreVertical,Star,Tag,ThumbsDown,ThumbsUp,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Copy,Edit,ExternalLink,Eye,MoreVertical,Star,Tag,ThumbsDown,ThumbsUp,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Copy,Edit,ExternalLink,Eye,MoreVertical,Star,Tag,ThumbsDown,ThumbsUp,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Copy,Edit,ExternalLink,Eye,MoreVertical,Star,Tag,ThumbsDown,ThumbsUp,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Copy,Edit,ExternalLink,Eye,MoreVertical,Star,Tag,ThumbsDown,ThumbsUp,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Copy,Edit,ExternalLink,Eye,MoreVertical,Star,Tag,ThumbsDown,ThumbsUp,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Copy,Edit,ExternalLink,Eye,MoreVertical,Star,Tag,ThumbsDown,ThumbsUp,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Copy,Edit,ExternalLink,Eye,MoreVertical,Star,Tag,ThumbsDown,ThumbsUp,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Copy,Edit,ExternalLink,Eye,MoreVertical,Star,Tag,ThumbsDown,ThumbsUp,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-down.js\");\n\n\n\n\n\n\n\nfunction ArticleCard(param) {\n    let { article, onEdit, onDelete, onArchive, onDuplicate, onView } = param;\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"published\":\n                return \"default\";\n            case \"draft\":\n                return \"secondary\";\n            case \"archived\":\n                return \"outline\";\n            default:\n                return \"default\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"published\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-3 h-3\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 16\n                }, this);\n            case \"draft\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-3 h-3\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 16\n                }, this);\n            case \"archived\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-3 h-3\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            month: \"short\",\n            day: \"numeric\",\n            year: \"numeric\"\n        });\n    };\n    const getHelpfulnessColor = (ratio)=>{\n        if (ratio >= 0.8) return \"text-green-600\";\n        if (ratio >= 0.6) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    const truncateContent = function(content) {\n        let maxLength = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 150;\n        if (content.length <= maxLength) return content;\n        return content.substring(0, maxLength) + \"...\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"hover:shadow-md transition-shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"pb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: getStatusColor(article.status),\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                getStatusIcon(article.status),\n                                                article.status.charAt(0).toUpperCase() + article.status.slice(1)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        article.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-3 h-3 fill-current\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Featured\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this),\n                                        article.category_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"outline\",\n                                            style: {\n                                                backgroundColor: \"\".concat(article.category_name, \"20\")\n                                            },\n                                            children: article.category_name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-lg leading-tight hover:text-blue-600 cursor-pointer\",\n                                    onClick: ()=>onView === null || onView === void 0 ? void 0 : onView(article),\n                                    children: article.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-sm text-muted-foreground space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                article.author_name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, this),\n                                                formatDate(article.created_at)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        article.published_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Published \",\n                                                formatDate(article.published_at)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                    align: \"end\",\n                                    children: [\n                                        onView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                            onClick: ()=>onView(article),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"View Article\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this),\n                                        onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                            onClick: ()=>onEdit(article),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Edit\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        onDuplicate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                            onClick: ()=>onDuplicate(article),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Duplicate\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        onArchive && article.status !== \"archived\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                            onClick: ()=>onArchive(article),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Archive\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this),\n                                        onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                            onClick: ()=>onDelete(article),\n                                            className: \"text-red-600 focus:text-red-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Delete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"pt-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        article.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: truncateContent(article.excerpt)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this),\n                        article.tags && article.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-4 h-4 text-muted-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-1\",\n                                    children: [\n                                        article.tags.slice(0, 3).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                variant: \"outline\",\n                                                className: \"text-xs\",\n                                                children: tag\n                                            }, index, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, this)),\n                                        article.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                \"+\",\n                                                article.tags.length - 3,\n                                                \" more\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-2 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4 text-sm text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, this),\n                                                article.view_count.toLocaleString(),\n                                                \" views\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        (article.helpful_count > 0 || article.not_helpful_count > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        article.helpful_count\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-1 text-red-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        article.not_helpful_count\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium \".concat(getHelpfulnessColor(article.helpfulness_ratio || 0)),\n                                                    children: [\n                                                        Math.round((article.helpfulness_ratio || 0) * 100),\n                                                        \"% helpful\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>onEdit(article),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Edit\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this),\n                                        onView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"default\",\n                                            size: \"sm\",\n                                            onClick: ()=>onView(article),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Copy_Edit_ExternalLink_Eye_MoreVertical_Star_Tag_ThumbsDown_ThumbsUp_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"View\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/ArticleCard.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_c = ArticleCard;\nvar _c;\n$RefreshReg$(_c, \"ArticleCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/knowledge-base/ArticleCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/knowledge-base/CategoryCard.tsx":
/*!********************************************************!*\
  !*** ./src/components/knowledge-base/CategoryCard.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CategoryCard: function() { return /* binding */ CategoryCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,EyeOff,MoreVertical,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,EyeOff,MoreVertical,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,EyeOff,MoreVertical,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,EyeOff,MoreVertical,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,EyeOff,MoreVertical,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,EyeOff,MoreVertical,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Edit,Eye,EyeOff,MoreVertical,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n\n\n\n\n\n\n\nfunction CategoryCard(param) {\n    let { category, onEdit, onDelete, onToggleActive, onView } = param;\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            month: \"short\",\n            day: \"numeric\",\n            year: \"numeric\"\n        });\n    };\n    const getCategoryIcon = (iconName)=>{\n        // Map icon names to actual icons\n        const iconMap = {\n            \"play-circle\": \"▶️\",\n            utensils: \"\\uD83C\\uDF7D️\",\n            user: \"\\uD83D\\uDC64\",\n            \"credit-card\": \"\\uD83D\\uDCB3\",\n            wrench: \"\\uD83D\\uDD27\",\n            dumbbell: \"\\uD83C\\uDFCB️\",\n            folder: \"\\uD83D\\uDCC1\"\n        };\n        return iconMap[iconName || \"folder\"] || \"\\uD83D\\uDCC1\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"hover:shadow-md transition-shadow \".concat(!category.is_active ? \"opacity-60\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"pb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 rounded-lg flex items-center justify-center text-lg\",\n                                    style: {\n                                        backgroundColor: \"\".concat(category.color, \"20\")\n                                    },\n                                    children: getCategoryIcon(category.icon)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-lg leading-tight hover:text-blue-600 cursor-pointer\",\n                                            onClick: ()=>onView === null || onView === void 0 ? void 0 : onView(category),\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    variant: category.is_active ? \"default\" : \"secondary\",\n                                                    children: category.is_active ? \"Active\" : \"Inactive\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this),\n                                                category.parent_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    variant: \"outline\",\n                                                    className: \"text-xs\",\n                                                    children: \"Subcategory\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"h-8 w-8 p-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                    align: \"end\",\n                                    children: [\n                                        onView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                            onClick: ()=>onView(category),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"View Articles\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this),\n                                        onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                            onClick: ()=>onEdit(category),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Edit Category\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this),\n                                        onToggleActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                            onClick: ()=>onToggleActive(category),\n                                            children: category.is_active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Deactivate\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Activate\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuSeparator, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                            onClick: ()=>onDelete(category),\n                                            className: \"text-red-600 focus:text-red-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Delete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"pt-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        category.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: category.description\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Slug:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"bg-muted px-2 py-1 rounded text-xs\",\n                                            children: [\n                                                \"/\",\n                                                category.slug\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Sort Order:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: category.sort_order\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Articles:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this),\n                                                category.article_count || 0\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Theme Color:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 rounded border\",\n                                            style: {\n                                                backgroundColor: category.color\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                            className: \"text-xs\",\n                                            children: category.color\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-2 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-xs text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-3 h-3 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Created \",\n                                        formatDate(category.created_at)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>onEdit(category),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Edit\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this),\n                                        onView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"default\",\n                                            size: \"sm\",\n                                            onClick: ()=>onView(category),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Edit_Eye_EyeOff_MoreVertical_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"View Articles\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CategoryCard.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_c = CategoryCard;\nvar _c;\n$RefreshReg$(_c, \"CategoryCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/knowledge-base/CategoryCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/knowledge-base/CreateArticleModal.tsx":
/*!**************************************************************!*\
  !*** ./src/components/knowledge-base/CreateArticleModal.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateArticleModal: function() { return /* binding */ CreateArticleModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,FileText,Hash,Plus,Save,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _services_knowledgeBaseService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/services/knowledgeBaseService */ \"(app-pages-browser)/./src/services/knowledgeBaseService.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CreateArticleModal(param) {\n    let { open, onClose, onSuccess, categories, editArticle } = param;\n    var _formData_tags;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"content\");\n    const [newTag, setNewTag] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        slug: \"\",\n        content: \"\",\n        excerpt: \"\",\n        category_id: \"\",\n        meta_title: \"\",\n        meta_description: \"\",\n        tags: [],\n        featured: false,\n        sort_order: 0,\n        status: \"draft\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Auto-generate slug from title\n    const generateSlug = (title)=>{\n        return title.toLowerCase().replace(/[^a-z0-9\\s-]/g, \"\").replace(/\\s+/g, \"-\").replace(/-+/g, \"-\").trim();\n    };\n    const handleTitleChange = (title)=>{\n        setFormData((prev)=>({\n                ...prev,\n                title,\n                slug: generateSlug(title),\n                meta_title: title\n            }));\n    };\n    const handleAddTag = ()=>{\n        var _formData_tags;\n        if (newTag.trim() && !((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.includes(newTag.trim()))) {\n            setFormData((prev)=>({\n                    ...prev,\n                    tags: [\n                        ...prev.tags || [],\n                        newTag.trim()\n                    ]\n                }));\n            setNewTag(\"\");\n        }\n    };\n    const handleRemoveTag = (tagToRemove)=>{\n        setFormData((prev)=>{\n            var _prev_tags;\n            return {\n                ...prev,\n                tags: ((_prev_tags = prev.tags) === null || _prev_tags === void 0 ? void 0 : _prev_tags.filter((tag)=>tag !== tagToRemove)) || []\n            };\n        });\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) {\n            newErrors.title = \"Title is required\";\n        }\n        if (!formData.slug.trim()) {\n            newErrors.slug = \"Slug is required\";\n        } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {\n            newErrors.slug = \"Slug can only contain lowercase letters, numbers, and hyphens\";\n        }\n        if (!formData.content.trim()) {\n            newErrors.content = \"Content is required\";\n        }\n        if (formData.content.length < 50) {\n            newErrors.content = \"Content must be at least 50 characters long\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (status)=>{\n        const submitData = {\n            ...formData,\n            status\n        };\n        if (!validateForm()) {\n            return;\n        }\n        setLoading(true);\n        try {\n            await _services_knowledgeBaseService__WEBPACK_IMPORTED_MODULE_11__.knowledgeBaseService.createArticle(submitData);\n            onSuccess();\n            handleClose();\n        } catch (error) {\n            console.error(\"Error creating article:\", error);\n            setErrors({\n                submit: \"Failed to create article. Please try again.\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleClose = ()=>{\n        setFormData({\n            title: \"\",\n            slug: \"\",\n            content: \"\",\n            excerpt: \"\",\n            category_id: \"\",\n            meta_title: \"\",\n            meta_description: \"\",\n            tags: [],\n            featured: false,\n            sort_order: 0,\n            status: \"draft\"\n        });\n        setErrors({});\n        setNewTag(\"\");\n        setActiveTab(\"content\");\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: handleClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-5 h-5 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                editArticle ? \"Edit Article\" : \"Create New Article\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            children: editArticle ? \"Update your knowledge base article\" : \"Create a new help article for your knowledge base\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsList, {\n                            className: \"grid w-full grid-cols-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"content\",\n                                    children: \"Content\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"settings\",\n                                    children: \"Settings\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsTrigger, {\n                                    value: \"seo\",\n                                    children: \"SEO & Meta\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"content\",\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"title\",\n                                                children: \"Article Title *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"title\",\n                                                value: formData.title,\n                                                onChange: (e)=>handleTitleChange(e.target.value),\n                                                placeholder: \"How to create your first meal plan\",\n                                                className: errors.title ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-500 mt-1\",\n                                                children: errors.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"slug\",\n                                                children: \"URL Slug *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm\",\n                                                        children: \"/kb/\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"slug\",\n                                                        value: formData.slug,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    slug: e.target.value\n                                                                })),\n                                                        placeholder: \"how-to-create-first-meal-plan\",\n                                                        className: \"rounded-l-none \".concat(errors.slug ? \"border-red-500\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.slug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-500 mt-1\",\n                                                children: errors.slug\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"excerpt\",\n                                                children: \"Excerpt\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                id: \"excerpt\",\n                                                value: formData.excerpt,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            excerpt: e.target.value\n                                                        })),\n                                                placeholder: \"Brief summary of the article for search results...\",\n                                                rows: 2\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                children: \"Optional short summary displayed in search results\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"content\",\n                                                children: \"Article Content *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                id: \"content\",\n                                                value: formData.content,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            content: e.target.value\n                                                        })),\n                                                placeholder: \"Write your article content here. You can use Markdown formatting...\",\n                                                rows: 12,\n                                                className: errors.content ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            errors.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-500 mt-1\",\n                                                children: errors.content\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                children: \"Supports Markdown formatting. Minimum 50 characters required.\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"settings\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"category\",\n                                                    children: \"Category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.Select, {\n                                                    value: formData.category_id || \"none\",\n                                                    onValueChange: (value)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                category_id: value === \"none\" ? \"\" : value\n                                                            })),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectTrigger, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectValue, {\n                                                                placeholder: \"Select a category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                    value: \"none\",\n                                                                    children: \"No Category\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_9__.SelectItem, {\n                                                                        value: category.id,\n                                                                        children: category.name\n                                                                    }, category.id, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"sort_order\",\n                                                    children: \"Sort Order\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"sort_order\",\n                                                    type: \"number\",\n                                                    value: formData.sort_order,\n                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                ...prev,\n                                                                sort_order: parseInt(e.target.value) || 0\n                                                            })),\n                                                    placeholder: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground mt-1\",\n                                                    children: \"Lower numbers appear first\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_8__.Checkbox, {\n                                            id: \"featured\",\n                                            checked: formData.featured,\n                                            onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        featured: !!checked\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"featured\",\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Featured Article\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            children: \"Tags\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-2\",\n                                            children: (_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        tag,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-3 h-3 cursor-pointer hover:text-red-500\",\n                                                            onClick: ()=>handleRemoveTag(tag)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    value: newTag,\n                                                    onChange: (e)=>setNewTag(e.target.value),\n                                                    placeholder: \"Add a tag...\",\n                                                    onKeyPress: (e)=>e.key === \"Enter\" && handleAddTag()\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    type: \"button\",\n                                                    variant: \"outline\",\n                                                    onClick: handleAddTag,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_10__.TabsContent, {\n                            value: \"seo\",\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"meta_title\",\n                                            children: \"Meta Title\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"meta_title\",\n                                            value: formData.meta_title,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        meta_title: e.target.value\n                                                    })),\n                                            placeholder: \"SEO title for search engines\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mt-1\",\n                                            children: \"Recommended: 50-60 characters\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"meta_description\",\n                                            children: \"Meta Description\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                            id: \"meta_description\",\n                                            value: formData.meta_description,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        meta_description: e.target.value\n                                                    })),\n                                            placeholder: \"Brief description for search engine results\",\n                                            rows: 3\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mt-1\",\n                                            children: \"Recommended: 150-160 characters\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this),\n                errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-md p-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-red-600\",\n                        children: errors.submit\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                    lineNumber: 420,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between pt-4 border-t\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: handleClose,\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>handleSubmit(\"draft\"),\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Save as Draft\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>handleSubmit(\"published\"),\n                                    disabled: loading,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_FileText_Hash_Plus_Save_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this),\n                                        loading ? \"Publishing...\" : \"Publish Article\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n                    lineNumber: 425,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/platemotion/apps/admin/src/components/knowledge-base/CreateArticleModal.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateArticleModal, \"nye5qRNPh/YSezTc/iheTEIXQkI=\");\n_c = CreateArticleModal;\nvar _c;\n$RefreshReg$(_c, \"CreateArticleModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/knowledge-base/CreateArticleModal.tsx\n"));

/***/ })

});
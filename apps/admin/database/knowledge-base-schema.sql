-- Knowledge Base System Database Schema for PlateMotion Support
-- This schema supports comprehensive knowledge base management with categories, articles, and user interactions

-- Knowledge base categories for organizing articles
CREATE TABLE IF NOT EXISTS support_kb_categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  slug TEXT NOT NULL UNIQUE,
  parent_id UUID REFERENCES support_kb_categories(id) ON DELETE SET NULL,
  sort_order INTEGER DEFAULT 0,
  icon TEXT, -- Icon name for UI display
  color TEXT DEFAULT '#6B7280', -- Hex color for category theming
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  -- Ensure slug is URL-friendly
  CONSTRAINT valid_slug CHECK (slug ~ '^[a-z0-9-]+$')
);

-- Knowledge base articles
CREATE TABLE IF NOT EXISTS support_kb_articles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  content TEXT NOT NULL,
  excerpt TEXT, -- Short summary for search results
  category_id UUID REFERENCES support_kb_categories(id) ON DELETE SET NULL,
  author_id UUID REFERENCES auth.users(id) NOT NULL,
  
  -- Publishing workflow
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  published_at TIMESTAMP WITH TIME ZONE,
  
  -- SEO and metadata
  meta_title TEXT,
  meta_description TEXT,
  tags TEXT[] DEFAULT '{}',
  
  -- User engagement metrics
  view_count INTEGER DEFAULT 0,
  helpful_count INTEGER DEFAULT 0,
  not_helpful_count INTEGER DEFAULT 0,
  
  -- Content management
  featured BOOLEAN DEFAULT FALSE,
  sort_order INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Search optimization
  search_vector tsvector GENERATED ALWAYS AS (
    setweight(to_tsvector('english', coalesce(title, '')), 'A') ||
    setweight(to_tsvector('english', coalesce(excerpt, '')), 'B') ||
    setweight(to_tsvector('english', coalesce(content, '')), 'C') ||
    setweight(to_tsvector('english', array_to_string(tags, ' ')), 'D')
  ) STORED,
  
  -- Ensure slug is URL-friendly
  CONSTRAINT valid_article_slug CHECK (slug ~ '^[a-z0-9-]+$')
);

-- Article feedback from users (helpful/not helpful ratings)
CREATE TABLE IF NOT EXISTS support_kb_feedback (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  article_id UUID REFERENCES support_kb_articles(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id), -- NULL for anonymous feedback
  session_id TEXT, -- For tracking anonymous users
  is_helpful BOOLEAN NOT NULL,
  feedback_text TEXT, -- Optional detailed feedback
  user_agent TEXT, -- For analytics
  ip_address INET, -- For spam prevention
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Prevent duplicate feedback from same user/session
  UNIQUE(article_id, user_id),
  UNIQUE(article_id, session_id)
);

-- Article view tracking for analytics
CREATE TABLE IF NOT EXISTS support_kb_article_views (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  article_id UUID REFERENCES support_kb_articles(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id), -- NULL for anonymous views
  session_id TEXT,
  referrer TEXT, -- Where the user came from
  user_agent TEXT,
  ip_address INET,
  viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- For deduplication (count unique views per day)
  view_date DATE GENERATED ALWAYS AS (viewed_at::date) STORED
);

-- Related articles mapping (for "See Also" sections)
CREATE TABLE IF NOT EXISTS support_kb_related_articles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  article_id UUID REFERENCES support_kb_articles(id) ON DELETE CASCADE,
  related_article_id UUID REFERENCES support_kb_articles(id) ON DELETE CASCADE,
  relevance_score DECIMAL(3,2) DEFAULT 1.0, -- 0.0 to 1.0 relevance score
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  -- Prevent self-references and duplicates
  CONSTRAINT no_self_reference CHECK (article_id != related_article_id),
  UNIQUE(article_id, related_article_id)
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_kb_categories_parent ON support_kb_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_kb_categories_active ON support_kb_categories(is_active, sort_order);
CREATE INDEX IF NOT EXISTS idx_kb_categories_slug ON support_kb_categories(slug);

CREATE INDEX IF NOT EXISTS idx_kb_articles_category ON support_kb_articles(category_id);
CREATE INDEX IF NOT EXISTS idx_kb_articles_status ON support_kb_articles(status);
CREATE INDEX IF NOT EXISTS idx_kb_articles_published ON support_kb_articles(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_kb_articles_featured ON support_kb_articles(featured, sort_order);
CREATE INDEX IF NOT EXISTS idx_kb_articles_search ON support_kb_articles USING gin(search_vector);
CREATE INDEX IF NOT EXISTS idx_kb_articles_slug ON support_kb_articles(slug);
CREATE INDEX IF NOT EXISTS idx_kb_articles_tags ON support_kb_articles USING gin(tags);

CREATE INDEX IF NOT EXISTS idx_kb_feedback_article ON support_kb_feedback(article_id);
CREATE INDEX IF NOT EXISTS idx_kb_feedback_helpful ON support_kb_feedback(article_id, is_helpful);

CREATE INDEX IF NOT EXISTS idx_kb_views_article ON support_kb_article_views(article_id);
CREATE INDEX IF NOT EXISTS idx_kb_views_date ON support_kb_article_views(view_date);
CREATE INDEX IF NOT EXISTS idx_kb_views_dedup ON support_kb_article_views(article_id, session_id, view_date);

CREATE INDEX IF NOT EXISTS idx_kb_related_article ON support_kb_related_articles(article_id);

-- Row Level Security (RLS) Policies
ALTER TABLE support_kb_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_kb_articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_kb_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_kb_article_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_kb_related_articles ENABLE ROW LEVEL SECURITY;

-- Admin users can manage all knowledge base content
CREATE POLICY "Admin full access to kb_categories" ON support_kb_categories
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_user_roles aur
      JOIN admin_roles ar ON aur.role_id = ar.id
      WHERE aur.user_id = auth.uid() AND ar.name IN ('super_admin', 'admin', 'support_manager')
    )
  );

CREATE POLICY "Admin full access to kb_articles" ON support_kb_articles
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_user_roles aur
      JOIN admin_roles ar ON aur.role_id = ar.id
      WHERE aur.user_id = auth.uid() AND ar.name IN ('super_admin', 'admin', 'support_manager', 'support_agent')
    )
  );

-- Public read access to published articles and active categories
CREATE POLICY "Public read access to active categories" ON support_kb_categories
  FOR SELECT USING (is_active = true);

CREATE POLICY "Public read access to published articles" ON support_kb_articles
  FOR SELECT USING (status = 'published');

-- Users can provide feedback on published articles
CREATE POLICY "Users can provide feedback" ON support_kb_feedback
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM support_kb_articles 
      WHERE id = article_id AND status = 'published'
    )
  );

CREATE POLICY "Users can view their own feedback" ON support_kb_feedback
  FOR SELECT USING (user_id = auth.uid() OR user_id IS NULL);

-- Article views can be inserted by anyone (for analytics)
CREATE POLICY "Anyone can record article views" ON support_kb_article_views
  FOR INSERT WITH CHECK (true);

-- Admin users can manage related articles
CREATE POLICY "Admin manage related articles" ON support_kb_related_articles
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_user_roles aur
      JOIN admin_roles ar ON aur.role_id = ar.id
      WHERE aur.user_id = auth.uid() AND ar.name IN ('super_admin', 'admin', 'support_manager')
    )
  );

-- Functions for common operations
CREATE OR REPLACE FUNCTION update_article_helpful_counts()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    IF NEW.is_helpful THEN
      UPDATE support_kb_articles 
      SET helpful_count = helpful_count + 1 
      WHERE id = NEW.article_id;
    ELSE
      UPDATE support_kb_articles 
      SET not_helpful_count = not_helpful_count + 1 
      WHERE id = NEW.article_id;
    END IF;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Handle feedback changes
    IF OLD.is_helpful != NEW.is_helpful THEN
      IF NEW.is_helpful THEN
        UPDATE support_kb_articles 
        SET helpful_count = helpful_count + 1, not_helpful_count = not_helpful_count - 1 
        WHERE id = NEW.article_id;
      ELSE
        UPDATE support_kb_articles 
        SET helpful_count = helpful_count - 1, not_helpful_count = not_helpful_count + 1 
        WHERE id = NEW.article_id;
      END IF;
    END IF;
  ELSIF TG_OP = 'DELETE' THEN
    IF OLD.is_helpful THEN
      UPDATE support_kb_articles 
      SET helpful_count = helpful_count - 1 
      WHERE id = OLD.article_id;
    ELSE
      UPDATE support_kb_articles 
      SET not_helpful_count = not_helpful_count - 1 
      WHERE id = OLD.article_id;
    END IF;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update helpful counts
CREATE TRIGGER update_article_helpful_counts_trigger
  AFTER INSERT OR UPDATE OR DELETE ON support_kb_feedback
  FOR EACH ROW EXECUTE FUNCTION update_article_helpful_counts();

-- Function to increment view count (with deduplication)
CREATE OR REPLACE FUNCTION increment_article_view_count(
  p_article_id UUID,
  p_session_id TEXT DEFAULT NULL,
  p_user_id UUID DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  -- Insert view record (will be deduplicated by unique constraints)
  INSERT INTO support_kb_article_views (article_id, user_id, session_id)
  VALUES (p_article_id, p_user_id, p_session_id)
  ON CONFLICT DO NOTHING;
  
  -- Update article view count (increment once per day per session/user)
  UPDATE support_kb_articles 
  SET view_count = (
    SELECT COUNT(DISTINCT COALESCE(user_id::text, session_id))
    FROM support_kb_article_views 
    WHERE article_id = p_article_id
  )
  WHERE id = p_article_id;
END;
$$ LANGUAGE plpgsql;

-- Insert default categories for PlateMotion
INSERT INTO support_kb_categories (name, description, slug, icon, color, sort_order) VALUES
('Meal Planning', 'AI meal planner, dietary restrictions, and nutrition guidance', 'meal-planning', 'utensils', '#10B981', 1),
('Workout Tracking', 'Exercise logging, fitness goals, and workout plans', 'workout-tracking', 'dumbbell', '#3B82F6', 2),
('Account Management', 'Profile settings, preferences, and account security', 'account-management', 'user', '#8B5CF6', 3),
('Billing & Subscriptions', 'Payment issues, subscription management, and billing questions', 'billing-subscriptions', 'credit-card', '#F59E0B', 4),
('Technical Support', 'App issues, bugs, and troubleshooting', 'technical-support', 'wrench', '#EF4444', 5),
('Getting Started', 'New user guides and basic app navigation', 'getting-started', 'play-circle', '#06B6D4', 0)
ON CONFLICT (slug) DO NOTHING;

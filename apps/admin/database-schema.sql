-- ============================================================================
-- PLATEMOTION ADMIN PANEL DATABASE SCHEMA
-- ============================================================================
-- This schema extends the existing PlateMotion database with admin-specific
-- tables for user management, content management, customer support, and analytics.
-- 
-- Prerequisites: Existing PlateMotion core tables (profiles, recipes, exercises, etc.)
-- ============================================================================

-- ============================================================================
-- ADMIN AUTHENTICATION & AUTHORIZATION
-- ============================================================================

-- Admin roles and permissions
CREATE TABLE admin_roles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE, -- 'super_admin', 'content_manager', 'support_agent'
  description TEXT,
  permissions JSONB NOT NULL DEFAULT '[]', -- Array of permission strings
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Admin user assignments
CREATE TABLE admin_user_roles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role_id UUID REFERENCES admin_roles(id) ON DELETE CASCADE,
  assigned_by UUID REFERENCES auth.users(id),
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT TRUE,
  UNIQUE(user_id, role_id)
);

-- Admin activity logging
CREATE TABLE admin_activity_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  admin_user_id UUID REFERENCES auth.users(id),
  action TEXT NOT NULL, -- 'login', 'user_update', 'content_approve', etc.
  resource_type TEXT, -- 'user', 'recipe', 'exercise', 'ticket'
  resource_id UUID,
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- CUSTOMER SUPPORT SYSTEM
-- ============================================================================

-- Support tickets
CREATE TABLE support_tickets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  category TEXT DEFAULT 'general' CHECK (category IN (
    'general', 'technical', 'billing', 'content', 'feature_request', 'bug_report'
  )),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  status TEXT DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'waiting_user', 'resolved', 'closed')),
  assigned_to UUID REFERENCES auth.users(id), -- Admin user
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  resolved_at TIMESTAMP WITH TIME ZONE,
  closed_at TIMESTAMP WITH TIME ZONE,
  
  -- SLA tracking
  first_response_at TIMESTAMP WITH TIME ZONE,
  sla_breach BOOLEAN DEFAULT FALSE,
  
  -- Metadata
  source TEXT DEFAULT 'web' CHECK (source IN ('web', 'mobile', 'email')),
  tags TEXT[] DEFAULT '{}',
  internal_notes TEXT
);

-- Support messages (chat system)
CREATE TABLE support_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  ticket_id UUID REFERENCES support_tickets(id) ON DELETE CASCADE,
  sender_id UUID REFERENCES auth.users(id),
  message TEXT NOT NULL,
  message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'system')),
  is_admin BOOLEAN DEFAULT FALSE,
  is_internal BOOLEAN DEFAULT FALSE, -- Internal admin notes
  attachments JSONB DEFAULT '[]',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  read_at TIMESTAMP WITH TIME ZONE
);

-- Support ticket history/audit trail
CREATE TABLE support_ticket_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  ticket_id UUID REFERENCES support_tickets(id) ON DELETE CASCADE,
  admin_user_id UUID REFERENCES auth.users(id),
  action TEXT NOT NULL, -- 'created', 'assigned', 'status_changed', 'priority_changed'
  old_value TEXT,
  new_value TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Canned responses for support
CREATE TABLE support_canned_responses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  category TEXT,
  tags TEXT[] DEFAULT '{}',
  usage_count INTEGER DEFAULT 0,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- CONTENT MANAGEMENT SYSTEM
-- ============================================================================

-- Content approval workflow
CREATE TABLE content_approval_queue (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content_type TEXT NOT NULL CHECK (content_type IN ('recipe', 'exercise')),
  content_id UUID NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'needs_revision')),
  submitted_by UUID REFERENCES auth.users(id), -- AI system or admin
  assigned_to UUID REFERENCES auth.users(id), -- Content manager
  priority INTEGER DEFAULT 3 CHECK (priority BETWEEN 1 AND 5), -- 1=highest, 5=lowest
  
  -- Review details
  review_notes TEXT,
  quality_score INTEGER CHECK (quality_score BETWEEN 1 AND 10),
  reviewed_by UUID REFERENCES auth.users(id),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content versions for tracking changes
CREATE TABLE content_versions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content_type TEXT NOT NULL CHECK (content_type IN ('recipe', 'exercise')),
  content_id UUID NOT NULL,
  version_number INTEGER NOT NULL,
  changes JSONB NOT NULL, -- What was changed
  changed_by UUID REFERENCES auth.users(id),
  change_reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content analytics
CREATE TABLE content_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content_type TEXT NOT NULL CHECK (content_type IN ('recipe', 'exercise')),
  content_id UUID NOT NULL,
  metric_name TEXT NOT NULL, -- 'views', 'likes', 'shares', 'completions'
  metric_value INTEGER DEFAULT 0,
  date DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(content_type, content_id, metric_name, date)
);

-- ============================================================================
-- ANALYTICS & REPORTING
-- ============================================================================

-- Daily analytics snapshots
CREATE TABLE daily_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  date DATE NOT NULL UNIQUE,
  
  -- User metrics
  total_users INTEGER DEFAULT 0,
  new_users INTEGER DEFAULT 0,
  active_users INTEGER DEFAULT 0,
  deleted_users INTEGER DEFAULT 0,
  
  -- Engagement metrics
  total_sessions INTEGER DEFAULT 0,
  avg_session_duration INTERVAL,
  total_ai_chats INTEGER DEFAULT 0,
  total_meal_plans_generated INTEGER DEFAULT 0,
  total_workouts_created INTEGER DEFAULT 0,
  
  -- Content metrics
  total_recipes INTEGER DEFAULT 0,
  total_exercises INTEGER DEFAULT 0,
  content_views INTEGER DEFAULT 0,
  
  -- Support metrics
  new_tickets INTEGER DEFAULT 0,
  resolved_tickets INTEGER DEFAULT 0,
  avg_response_time INTERVAL,
  
  -- System metrics
  api_requests INTEGER DEFAULT 0,
  error_rate DECIMAL(5,4) DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Real-time metrics for dashboard
CREATE TABLE real_time_metrics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  metric_name TEXT NOT NULL,
  metric_value DECIMAL(15,4) NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Keep only last 24 hours of data
  CONSTRAINT recent_metrics CHECK (timestamp > NOW() - INTERVAL '24 hours')
);

-- User subscription analytics
CREATE TABLE subscription_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  subscription_status TEXT NOT NULL, -- 'active', 'cancelled', 'expired', 'trial'
  plan_type TEXT, -- 'free', 'premium', 'pro'
  subscription_start DATE,
  subscription_end DATE,
  monthly_revenue DECIMAL(10,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- SYSTEM CONFIGURATION
-- ============================================================================

-- Feature flags
CREATE TABLE feature_flags (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  flag_name TEXT NOT NULL UNIQUE,
  description TEXT,
  is_enabled BOOLEAN DEFAULT FALSE,
  rollout_percentage INTEGER DEFAULT 0 CHECK (rollout_percentage BETWEEN 0 AND 100),
  target_users JSONB DEFAULT '[]', -- Array of user IDs or criteria
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System notifications
CREATE TABLE system_notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'error', 'success')),
  target_audience TEXT DEFAULT 'all' CHECK (target_audience IN ('all', 'admins', 'users')),
  is_active BOOLEAN DEFAULT TRUE,
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_date TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Admin activity indexes
CREATE INDEX idx_admin_activity_user_id ON admin_activity_log(admin_user_id);
CREATE INDEX idx_admin_activity_created_at ON admin_activity_log(created_at DESC);
CREATE INDEX idx_admin_activity_action ON admin_activity_log(action);

-- Support ticket indexes
CREATE INDEX idx_support_tickets_user_id ON support_tickets(user_id);
CREATE INDEX idx_support_tickets_assigned_to ON support_tickets(assigned_to);
CREATE INDEX idx_support_tickets_status ON support_tickets(status);
CREATE INDEX idx_support_tickets_priority ON support_tickets(priority);
CREATE INDEX idx_support_tickets_created_at ON support_tickets(created_at DESC);

-- Support message indexes
CREATE INDEX idx_support_messages_ticket_id ON support_messages(ticket_id);
CREATE INDEX idx_support_messages_created_at ON support_messages(created_at DESC);

-- Content approval indexes
CREATE INDEX idx_content_approval_status ON content_approval_queue(status);
CREATE INDEX idx_content_approval_assigned_to ON content_approval_queue(assigned_to);
CREATE INDEX idx_content_approval_content_type ON content_approval_queue(content_type);

-- Analytics indexes
CREATE INDEX idx_daily_analytics_date ON daily_analytics(date DESC);
CREATE INDEX idx_real_time_metrics_name ON real_time_metrics(metric_name);
CREATE INDEX idx_real_time_metrics_timestamp ON real_time_metrics(timestamp DESC);
CREATE INDEX idx_content_analytics_date ON content_analytics(date DESC);
CREATE INDEX idx_content_analytics_content ON content_analytics(content_type, content_id);

-- ============================================================================
-- ROW LEVEL SECURITY (RLS)
-- ============================================================================

-- Enable RLS on all admin tables
ALTER TABLE admin_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_ticket_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_canned_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_approval_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE real_time_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_notifications ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- RLS POLICIES
-- ============================================================================

-- Admin access policies (only users with admin roles can access)
CREATE POLICY "Admin access only" ON admin_roles
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_user_roles aur
      JOIN admin_roles ar ON aur.role_id = ar.id
      WHERE aur.user_id = auth.uid()
      AND aur.is_active = TRUE
      AND ar.name IN ('super_admin', 'content_manager', 'support_agent')
    )
  );

-- Support ticket policies
CREATE POLICY "Support agents can access tickets" ON support_tickets
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_user_roles aur
      JOIN admin_roles ar ON aur.role_id = ar.id
      WHERE aur.user_id = auth.uid()
      AND aur.is_active = TRUE
      AND ar.name IN ('super_admin', 'support_agent')
    )
  );

-- Content management policies
CREATE POLICY "Content managers can access approval queue" ON content_approval_queue
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_user_roles aur
      JOIN admin_roles ar ON aur.role_id = ar.id
      WHERE aur.user_id = auth.uid()
      AND aur.is_active = TRUE
      AND ar.name IN ('super_admin', 'content_manager')
    )
  );

-- Analytics access policies
CREATE POLICY "Admins can view analytics" ON daily_analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM admin_user_roles aur
      JOIN admin_roles ar ON aur.role_id = ar.id
      WHERE aur.user_id = auth.uid()
      AND aur.is_active = TRUE
    )
  );

-- ============================================================================
-- FUNCTIONS FOR ADMIN OPERATIONS
-- ============================================================================

-- Function to check if user has admin permission
CREATE OR REPLACE FUNCTION has_admin_permission(permission_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM admin_user_roles aur
    JOIN admin_roles ar ON aur.role_id = ar.id
    WHERE aur.user_id = auth.uid()
    AND aur.is_active = TRUE
    AND (
      ar.name = 'super_admin' OR
      ar.permissions ? permission_name
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get admin dashboard metrics
CREATE OR REPLACE FUNCTION get_admin_dashboard_metrics()
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_users', (SELECT COUNT(*) FROM profiles),
    'active_users_today', (
      SELECT COUNT(DISTINCT user_id)
      FROM chat_sessions
      WHERE created_at >= CURRENT_DATE
    ),
    'new_users_today', (
      SELECT COUNT(*)
      FROM profiles
      WHERE created_at >= CURRENT_DATE
    ),
    'open_tickets', (
      SELECT COUNT(*)
      FROM support_tickets
      WHERE status IN ('open', 'in_progress')
    ),
    'pending_content', (
      SELECT COUNT(*)
      FROM content_approval_queue
      WHERE status = 'pending'
    ),
    'total_recipes', (SELECT COUNT(*) FROM recipes),
    'total_exercises', (SELECT COUNT(*) FROM exercises),
    'meal_plans_today', (
      SELECT COUNT(*)
      FROM meal_plans
      WHERE created_at >= CURRENT_DATE
    )
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create support ticket
CREATE OR REPLACE FUNCTION create_support_ticket(
  p_user_id UUID,
  p_title TEXT,
  p_description TEXT,
  p_category TEXT DEFAULT 'general',
  p_priority TEXT DEFAULT 'medium'
)
RETURNS UUID AS $$
DECLARE
  ticket_id UUID;
BEGIN
  INSERT INTO support_tickets (
    user_id, title, description, category, priority
  ) VALUES (
    p_user_id, p_title, p_description, p_category, p_priority
  ) RETURNING id INTO ticket_id;

  -- Log the ticket creation
  INSERT INTO support_ticket_history (
    ticket_id, action, new_value
  ) VALUES (
    ticket_id, 'created', p_title
  );

  RETURN ticket_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- TRIGGERS FOR AUTOMATION
-- ============================================================================

-- Update timestamps trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply to relevant tables
CREATE TRIGGER update_support_tickets_updated_at
  BEFORE UPDATE ON support_tickets
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_support_messages_updated_at
  BEFORE UPDATE ON support_messages
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_approval_updated_at
  BEFORE UPDATE ON content_approval_queue
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- INITIAL DATA SETUP
-- ============================================================================

-- Insert default admin roles
INSERT INTO admin_roles (name, description, permissions) VALUES
('super_admin', 'Full system access', '["*"]'),
('content_manager', 'Content management access', '["content.read", "content.write", "content.approve", "analytics.read"]'),
('support_agent', 'Customer support access', '["support.read", "support.write", "users.read", "analytics.read"]'),
('analytics_viewer', 'Read-only analytics access', '["analytics.read"]');

-- Insert default canned responses
INSERT INTO support_canned_responses (title, content, category) VALUES
('Welcome Message', 'Thank you for contacting PlateMotion support! We''ve received your message and will respond within 24 hours.', 'general'),
('Account Issue', 'I understand you''re having trouble with your account. Let me help you resolve this issue.', 'technical'),
('Billing Question', 'I''d be happy to help you with your billing question. Let me review your account details.', 'billing'),
('Feature Request', 'Thank you for your feature suggestion! We''ll review it with our product team.', 'feature_request');

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE admin_roles IS 'Admin role definitions with permissions';
COMMENT ON TABLE admin_user_roles IS 'Admin user role assignments';
COMMENT ON TABLE admin_activity_log IS 'Audit trail for admin actions';
COMMENT ON TABLE support_tickets IS 'Customer support ticket system';
COMMENT ON TABLE support_messages IS 'Support chat messages';
COMMENT ON TABLE support_ticket_history IS 'Ticket change history';
COMMENT ON TABLE content_approval_queue IS 'Content review and approval workflow';
COMMENT ON TABLE content_versions IS 'Content change tracking';
COMMENT ON TABLE daily_analytics IS 'Daily aggregated analytics data';
COMMENT ON TABLE real_time_metrics IS 'Real-time dashboard metrics';
COMMENT ON TABLE feature_flags IS 'Feature toggle system';
COMMENT ON TABLE system_notifications IS 'System-wide notifications';

-- ============================================================================
-- ADMIN PANEL SCHEMA COMPLETE
-- ============================================================================
--
-- This schema provides:
-- 1. Role-based admin authentication and authorization
-- 2. Comprehensive customer support ticket system with real-time chat
-- 3. Content management with approval workflows
-- 4. Analytics and reporting infrastructure
-- 5. System configuration and feature flags
-- 6. Complete audit trails and activity logging
-- 7. Performance-optimized indexes
-- 8. Row-level security for data protection
--
-- Next steps:
-- 1. Run this migration on your Supabase database
-- 2. Set up the Next.js admin panel frontend
-- 3. Configure admin user roles and permissions
-- 4. Integrate with existing mobile app for support chat
--
-- ============================================================================

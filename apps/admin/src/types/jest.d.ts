import "@testing-library/jest-dom";

declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveTextContent(text: string): R;
      toHaveClass(className: string): R;
      toHaveValue(value: string): R;
      toHaveAttribute(attr: string, value?: string): R;
      toBeDisabled(): R;
    }
  }
}

// Extend HTMLElement to include common properties used in tests
declare global {
  interface HTMLElement {
    value?: string;
    checked?: boolean;
    disabled?: boolean;
  }
}

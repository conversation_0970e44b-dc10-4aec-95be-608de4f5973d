import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import KnowledgeBasePage from "@/app/knowledge-base/page";
import * as knowledgeBaseService from "@/services/knowledgeBaseService";

// Mock the entire service module
jest.mock("@/services/knowledgeBaseService", () => {
  const mockCategories = [
    {
      id: "cat-1",
      name: "Getting Started",
      slug: "getting-started",
      description: "New user guides",
      is_active: true,
      article_count: 2,
      icon: "play-circle",
      color: "#06B6D4",
      sort_order: 0,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
      parent_id: null,
      created_by: null,
    },
  ];

  const mockArticles = [
    {
      id: "art-1",
      title: "Welcome to PlateMotion",
      slug: "welcome-to-platemotion",
      content:
        "This is a comprehensive welcome guide with enough content to meet validation requirements.",
      excerpt: "Welcome guide for new users",
      category_id: "cat-1",
      category_name: "Getting Started",
      status: "published",
      view_count: 100,
      helpful_count: 10,
      not_helpful_count: 2,
      featured: true,
      tags: ["welcome", "getting-started"],
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
      author_id: "admin-1",
      author_name: "Admin User",
      published_at: "2024-01-01T00:00:00Z",
      helpfulness_ratio: 0.83,
      sort_order: 0,
      meta_title: null,
      meta_description: null,
    },
  ];

  const mockStats = {
    total_articles: 1,
    published_articles: 1,
    draft_articles: 0,
    total_categories: 1,
    total_views: 100,
    total_feedback: 12,
    avg_helpfulness: 0.83,
    popular_articles: mockArticles,
    recent_articles: mockArticles,
  };

  return {
    knowledgeBaseService: {
      getCategories: jest.fn().mockResolvedValue(mockCategories),
      getArticles: jest.fn().mockResolvedValue(mockArticles),
      getKBStats: jest.fn().mockResolvedValue(mockStats),
      createCategory: jest.fn().mockResolvedValue("new-cat-id"),
      createArticle: jest.fn().mockResolvedValue("new-art-id"),
      updateArticle: jest.fn().mockResolvedValue(undefined),
      deleteArticle: jest.fn().mockResolvedValue(undefined),
      deleteCategory: jest.fn().mockResolvedValue(undefined),
      searchArticles: jest.fn().mockResolvedValue([]),
    },
  };
});

// Mock AdminLayout
jest.mock("@/components/AdminLayout", () => ({
  AdminLayout: ({ children }: any) => (
    <div data-testid="admin-layout">{children}</div>
  ),
}));

// Mock all UI components to be functional
jest.mock("@/components/ui/card", () => ({
  Card: ({ children, className }: any) => (
    <div className={className}>{children}</div>
  ),
  CardContent: ({ children }: any) => <div>{children}</div>,
  CardHeader: ({ children }: any) => <div>{children}</div>,
  CardTitle: ({ children }: any) => <h3>{children}</h3>,
}));

jest.mock("@/components/ui/button", () => ({
  Button: ({ children, onClick, disabled, variant }: any) => (
    <button onClick={onClick} disabled={disabled} data-variant={variant}>
      {children}
    </button>
  ),
}));

jest.mock("@/components/ui/input", () => ({
  Input: ({ value, onChange, placeholder, className }: any) => (
    <input
      value={value || ""}
      onChange={onChange}
      placeholder={placeholder}
      className={className}
    />
  ),
}));

jest.mock("@/components/ui/tabs", () => ({
  Tabs: ({ children, value, onValueChange }: any) => (
    <div data-testid="tabs" data-value={value}>
      {React.Children.map(children, (child: any) => {
        if (child?.type?.displayName === "TabsList") {
          return React.cloneElement(child, { onValueChange });
        }
        return child;
      })}
    </div>
  ),
  TabsContent: ({ children, value }: any) => (
    <div data-value={value}>{children}</div>
  ),
  TabsList: ({ onValueChange }: any) => (
    <div>
      <button onClick={() => onValueChange("articles")}>Articles (1)</button>
      <button onClick={() => onValueChange("categories")}>
        Categories (1)
      </button>
    </div>
  ),
  TabsTrigger: ({ children, value }: any) => (
    <button data-value={value}>{children}</button>
  ),
}));

jest.mock("@/components/ui/select", () => ({
  Select: ({ value, onValueChange }: any) => (
    <div data-testid="select" data-value={value}>
      <button onClick={() => onValueChange && onValueChange("all")}>
        {value || "All"}
      </button>
    </div>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value }: any) => (
    <div data-value={value}>{children}</div>
  ),
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
}));

jest.mock("@/components/ui/badge", () => ({
  Badge: ({ children, variant }: any) => (
    <span data-variant={variant}>{children}</span>
  ),
}));

// Mock child components with realistic behavior
jest.mock("@/components/knowledge-base/ArticleCard", () => ({
  ArticleCard: ({ article, onEdit, onDelete, onView }: any) => (
    <div data-testid="article-card" data-article-id={article.id}>
      <h4>{article.title}</h4>
      <span>Status: {article.status}</span>
      <span>Views: {article.view_count}</span>
      <button onClick={() => onEdit && onEdit(article)}>Edit</button>
      <button onClick={() => onDelete && onDelete(article)}>Delete</button>
      <button onClick={() => onView && onView(article)}>View</button>
    </div>
  ),
}));

jest.mock("@/components/knowledge-base/CategoryCard", () => ({
  CategoryCard: ({ category, onEdit, onDelete, onView }: any) => (
    <div data-testid="category-card" data-category-id={category.id}>
      <h4>{category.name}</h4>
      <span>Articles: {category.article_count}</span>
      <button onClick={() => onEdit && onEdit(category)}>Edit</button>
      <button onClick={() => onDelete && onDelete(category)}>Delete</button>
      <button onClick={() => onView && onView(category)}>View Articles</button>
    </div>
  ),
}));

jest.mock("@/components/knowledge-base/CreateArticleModal", () => ({
  CreateArticleModal: ({ open, onClose, onSuccess, categories }: any) =>
    open ? (
      <div data-testid="create-article-modal">
        <h3>Create New Article</h3>
        <p>Categories available: {categories.length}</p>
        <button
          onClick={() => {
            onSuccess();
            onClose();
          }}
        >
          Create Article
        </button>
        <button onClick={onClose}>Cancel</button>
      </div>
    ) : null,
}));

jest.mock("@/components/knowledge-base/CreateCategoryModal", () => ({
  CreateCategoryModal: ({ open, onClose, onSuccess }: any) =>
    open ? (
      <div data-testid="create-category-modal">
        <h3>Create New Category</h3>
        <button
          onClick={() => {
            onSuccess();
            onClose();
          }}
        >
          Create Category
        </button>
        <button onClick={onClose}>Cancel</button>
      </div>
    ) : null,
}));

describe("Knowledge Base E2E Workflow", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should complete full category creation workflow", async () => {
    render(<KnowledgeBasePage />);

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText("Knowledge Base")).toBeInTheDocument();
    });

    // Step 1: Open category creation modal
    const manageCategoriesButton = screen.getByText("Manage Categories");
    fireEvent.click(manageCategoriesButton);

    await waitFor(() => {
      expect(screen.getByTestId("create-category-modal")).toBeInTheDocument();
    });

    // Step 2: Create category
    const createCategoryButton = screen.getByText("Create Category");
    fireEvent.click(createCategoryButton);

    // Step 3: Verify modal closes and data reloads
    await waitFor(() => {
      expect(
        screen.queryByTestId("create-category-modal"),
      ).not.toBeInTheDocument();
      expect(
        knowledgeBaseService.knowledgeBaseService.createCategory,
      ).toHaveBeenCalled();
      expect(
        knowledgeBaseService.knowledgeBaseService.getCategories,
      ).toHaveBeenCalledTimes(2); // Initial + after creation
    });
  });

  it("should complete full article creation workflow", async () => {
    render(<KnowledgeBasePage />);

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText("Knowledge Base")).toBeInTheDocument();
    });

    // Step 1: Open article creation modal
    const newArticleButton = screen.getByText("New Article");
    fireEvent.click(newArticleButton);

    await waitFor(() => {
      expect(screen.getByTestId("create-article-modal")).toBeInTheDocument();
      expect(screen.getByText("Categories available: 1")).toBeInTheDocument();
    });

    // Step 2: Create article
    const createArticleButton = screen.getByText("Create Article");
    fireEvent.click(createArticleButton);

    // Step 3: Verify modal closes and data reloads
    await waitFor(() => {
      expect(
        screen.queryByTestId("create-article-modal"),
      ).not.toBeInTheDocument();
      expect(
        knowledgeBaseService.knowledgeBaseService.createArticle,
      ).toHaveBeenCalled();
      expect(
        knowledgeBaseService.knowledgeBaseService.getArticles,
      ).toHaveBeenCalledTimes(2); // Initial + after creation
    });
  });

  it("should complete search and filter workflow", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      expect(screen.getByText("Knowledge Base")).toBeInTheDocument();
    });

    // Step 1: Perform search
    const searchInput = screen.getByPlaceholderText("Search articles...");
    fireEvent.change(searchInput, { target: { value: "meal planning" } });

    // Step 2: Apply category filter
    const categorySelect = screen.getAllByTestId("select")[0]; // First select is category filter
    const categoryButton = categorySelect.querySelector("button");
    if (categoryButton) {
      fireEvent.click(categoryButton);
    }

    // Step 3: Apply status filter
    const statusSelect = screen.getAllByTestId("select")[1]; // Second select is status filter
    const statusButton = statusSelect.querySelector("button");
    if (statusButton) {
      fireEvent.click(statusButton);
    }

    // Verify filters are applied
    expect(
      knowledgeBaseService.knowledgeBaseService.getArticles,
    ).toHaveBeenCalledWith(
      expect.objectContaining({
        search: "meal planning",
      }),
    );
  });

  it("should complete article deletion workflow", async () => {
    // Mock window.confirm
    const originalConfirm = window.confirm;
    window.confirm = jest.fn(() => true);

    render(<KnowledgeBasePage />);

    await waitFor(() => {
      expect(screen.getByTestId("article-card")).toBeInTheDocument();
    });

    // Step 1: Delete article
    const deleteButton = screen.getByText("Delete");
    fireEvent.click(deleteButton);

    // Step 2: Confirm deletion
    expect(window.confirm).toHaveBeenCalledWith(
      'Are you sure you want to delete "Welcome to PlateMotion"?',
    );

    // Step 3: Verify deletion and data reload
    await waitFor(() => {
      expect(
        knowledgeBaseService.knowledgeBaseService.deleteArticle,
      ).toHaveBeenCalledWith("art-1");
      expect(
        knowledgeBaseService.knowledgeBaseService.getArticles,
      ).toHaveBeenCalledTimes(2); // Initial + after deletion
    });

    window.confirm = originalConfirm;
  });

  it("should complete tab switching workflow", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      expect(screen.getByText("Knowledge Base")).toBeInTheDocument();
    });

    // Step 1: Start on articles tab (default)
    expect(screen.getByTestId("article-card")).toBeInTheDocument();

    // Step 2: Switch to categories tab
    const categoriesTab = screen.getByText("Categories (1)");
    fireEvent.click(categoriesTab);

    await waitFor(() => {
      expect(screen.getByTestId("category-card")).toBeInTheDocument();
    });

    // Step 3: Switch back to articles tab
    const articlesTab = screen.getByText("Articles (1)");
    fireEvent.click(articlesTab);

    await waitFor(() => {
      expect(screen.getByTestId("article-card")).toBeInTheDocument();
    });
  });

  it("should handle complete error recovery workflow", async () => {
    // Start with working service
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      expect(screen.getByText("Knowledge Base")).toBeInTheDocument();
    });

    // Simulate service error
    (
      knowledgeBaseService.knowledgeBaseService.getArticles as jest.Mock
    ).mockRejectedValueOnce(new Error("Network error"));

    // Mock console.error to avoid test output noise
    const originalConsoleError = console.error;
    console.error = jest.fn();

    // Trigger refresh
    const refreshButton = screen.getByText("Refresh");
    fireEvent.click(refreshButton);

    // Should handle error gracefully and still show UI
    await waitFor(() => {
      expect(screen.getByText("Knowledge Base")).toBeInTheDocument();
    });

    console.error = originalConsoleError;
  });

  it("should maintain state consistency across operations", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      expect(screen.getByText("Knowledge Base")).toBeInTheDocument();
    });

    // Step 1: Apply search filter
    const searchInput = screen.getByPlaceholderText("Search articles...");
    fireEvent.change(searchInput, { target: { value: "welcome" } });

    // Step 2: Create new article (should reload data)
    const newArticleButton = screen.getByText("New Article");
    fireEvent.click(newArticleButton);

    const createArticleButton = screen.getByText("Create Article");
    fireEvent.click(createArticleButton);

    // Step 3: Verify search filter is maintained after reload
    await waitFor(() => {
      expect(searchInput.value).toBe("welcome");
      expect(
        knowledgeBaseService.knowledgeBaseService.getArticles,
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          search: "welcome",
        }),
      );
    });
  });

  it("should display real-time statistics updates", async () => {
    // Start with initial stats
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      expect(screen.getByText("1")).toBeInTheDocument(); // total articles
    });

    // Update mock to return different stats
    const updatedStats = {
      total_articles: 2,
      published_articles: 2,
      draft_articles: 0,
      total_categories: 1,
      total_views: 150,
      total_feedback: 15,
      avg_helpfulness: 0.85,
      popular_articles: [],
      recent_articles: [],
    };

    (
      knowledgeBaseService.knowledgeBaseService.getKBStats as jest.Mock
    ).mockResolvedValueOnce(updatedStats);

    // Trigger refresh
    const refreshButton = screen.getByText("Refresh");
    fireEvent.click(refreshButton);

    // Verify stats update
    await waitFor(() => {
      expect(screen.getByText("2")).toBeInTheDocument(); // updated total articles
      expect(screen.getByText("85%")).toBeInTheDocument(); // updated helpfulness
    });
  });

  it("should handle empty state to populated state transition", async () => {
    // Start with empty data
    (
      knowledgeBaseService.knowledgeBaseService.getArticles as jest.Mock
    ).mockResolvedValueOnce([]);
    (
      knowledgeBaseService.knowledgeBaseService.getCategories as jest.Mock
    ).mockResolvedValueOnce([]);

    render(<KnowledgeBasePage />);

    await waitFor(() => {
      expect(screen.getByText("No articles found")).toBeInTheDocument();
    });

    // Switch to categories tab
    const categoriesTab = screen.getByText("Categories (0)");
    fireEvent.click(categoriesTab);

    await waitFor(() => {
      expect(screen.getByText("No categories found")).toBeInTheDocument();
    });

    // Create first category
    const createFirstCategoryButton = screen.getByText("Create First Category");
    fireEvent.click(createFirstCategoryButton);

    const createCategoryButton = screen.getByText("Create Category");
    fireEvent.click(createCategoryButton);

    // Verify transition to populated state
    await waitFor(() => {
      expect(
        knowledgeBaseService.knowledgeBaseService.createCategory,
      ).toHaveBeenCalled();
    });
  });

  it("should validate complete article publishing workflow", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      expect(screen.getByText("Knowledge Base")).toBeInTheDocument();
    });

    // Verify initial published article
    expect(screen.getByText("Status: published")).toBeInTheDocument();

    // Create new draft article
    const newArticleButton = screen.getByText("New Article");
    fireEvent.click(newArticleButton);

    const createArticleButton = screen.getByText("Create Article");
    fireEvent.click(createArticleButton);

    // Verify article creation workflow
    await waitFor(() => {
      expect(
        knowledgeBaseService.knowledgeBaseService.createArticle,
      ).toHaveBeenCalled();
      expect(
        knowledgeBaseService.knowledgeBaseService.getArticles,
      ).toHaveBeenCalledTimes(2);
    });
  });
});

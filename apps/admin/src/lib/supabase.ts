import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || "";

// Only throw error in runtime, not during build
if (typeof window !== "undefined" && (!supabaseUrl || !supabaseAnonKey)) {
  throw new Error("Missing Supabase environment variables");
}

// Client for browser/client-side operations
export const supabase = createClient(
  supabaseUrl || "https://placeholder.supabase.co",
  supabaseAnonKey || "placeholder-key",
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
  },
);

// Admin client with service role key for server-side operations
export const supabaseAdmin = createClient(
  supabaseUrl || "https://placeholder.supabase.co",
  supabaseServiceKey || "placeholder-key",
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  },
);

// Database types (to be generated from Supabase CLI)
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          full_name: string | null;
          age: number | null;
          height_cm: number | null;
          weight_kg: number | null;
          goals: string[] | null;
          experience_level: string | null;
          ai_persona_preference: string | null;
          dietary_restrictions: string[] | null;
          food_allergies: string[] | null;
          budget_preference: string | null;
          known_injuries: string[] | null;
          onboarding_complete: boolean | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          full_name?: string | null;
          age?: number | null;
          height_cm?: number | null;
          weight_kg?: number | null;
          goals?: string[] | null;
          experience_level?: string | null;
          ai_persona_preference?: string | null;
          dietary_restrictions?: string[] | null;
          food_allergies?: string[] | null;
          budget_preference?: string | null;
          known_injuries?: string[] | null;
          onboarding_complete?: boolean | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          full_name?: string | null;
          age?: number | null;
          height_cm?: number | null;
          weight_kg?: number | null;
          goals?: string[] | null;
          experience_level?: string | null;
          ai_persona_preference?: string | null;
          dietary_restrictions?: string[] | null;
          food_allergies?: string[] | null;
          budget_preference?: string | null;
          known_injuries?: string[] | null;
          onboarding_complete?: boolean | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      admin_roles: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          permissions: Record<string, unknown>;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          permissions?: Record<string, unknown>;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          permissions?: Record<string, unknown>;
          created_at?: string;
          updated_at?: string;
        };
      };
      support_tickets: {
        Row: {
          id: string;
          user_id: string;
          title: string;
          description: string | null;
          category: string;
          priority: string;
          status: string;
          assigned_to: string | null;
          created_at: string;
          updated_at: string;
          resolved_at: string | null;
          closed_at: string | null;
          first_response_at: string | null;
          sla_breach: boolean;
          source: string;
          tags: string[];
          internal_notes: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          title: string;
          description?: string | null;
          category?: string;
          priority?: string;
          status?: string;
          assigned_to?: string | null;
          created_at?: string;
          updated_at?: string;
          resolved_at?: string | null;
          closed_at?: string | null;
          first_response_at?: string | null;
          sla_breach?: boolean;
          source?: string;
          tags?: string[];
          internal_notes?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          title?: string;
          description?: string | null;
          category?: string;
          priority?: string;
          status?: string;
          assigned_to?: string | null;
          created_at?: string;
          updated_at?: string;
          resolved_at?: string | null;
          closed_at?: string | null;
          first_response_at?: string | null;
          sla_breach?: boolean;
          source?: string;
          tags?: string[];
          internal_notes?: string | null;
        };
      };
      // Add more table types as needed
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      has_admin_permission: {
        Args: {
          permission_name: string;
        };
        Returns: boolean;
      };
      get_admin_dashboard_metrics: {
        Args: Record<PropertyKey, never>;
        Returns: Record<string, unknown>;
      };
      create_support_ticket: {
        Args: {
          p_user_id: string;
          p_title: string;
          p_description: string;
          p_category?: string;
          p_priority?: string;
        };
        Returns: string;
      };
    };
    Enums: {
      [_ in never]: never;
    };
  };
};

// Helper function to check admin permissions
export async function checkAdminPermission(
  permission: string,
): Promise<boolean> {
  try {
    const { data, error } = await supabase.rpc("has_admin_permission", {
      permission_name: permission,
    });

    if (error) {
      console.error("Error checking admin permission:", error);
      return false;
    }

    return data || false;
  } catch (error) {
    console.error("Error checking admin permission:", error);
    return false;
  }
}

// Helper function to get dashboard metrics
export async function getDashboardMetrics() {
  try {
    const { data, error } = await supabase.rpc("get_admin_dashboard_metrics");

    if (error) {
      console.error("Error fetching dashboard metrics:", error);
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error fetching dashboard metrics:", error);
    return null;
  }
}

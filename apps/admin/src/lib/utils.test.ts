import { cn } from "./utils";

describe("utils", () => {
  describe("cn", () => {
    it("should merge class names correctly", () => {
      const result = cn("class1", "class2");
      expect(result).toBe("class1 class2");
    });

    it("should handle conditional class names", () => {
      const result = cn("class1", { class2: true, class3: false });
      expect(result).toBe("class1 class2");
    });

    it("should handle tailwind conflicts", () => {
      const result = cn("px-2 py-1 bg-white dark:bg-black", "bg-red-500");
      // The actual result may vary depending on tailwind-merge implementation
      // but should contain bg-red-500
      expect(result).toContain("bg-red-500");
    });

    it("should handle empty inputs", () => {
      const result = cn("", null, undefined, "class1");
      expect(result).toBe("class1");
    });
  });
});

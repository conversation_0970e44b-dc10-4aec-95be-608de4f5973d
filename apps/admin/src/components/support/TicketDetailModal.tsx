import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import {
  Calendar,
  Tag,
  MessageSquare,
  Send,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import { SupportTicket } from "@/services/supportService";
import { StatusBadge, PriorityBadge, CategoryBadge } from "./StatusBadge";

interface TicketDetailModalProps {
  ticket: SupportTicket | null;
  open: boolean;
  onClose: () => void;
  onStatusChange?: (ticketId: string, newStatus: string) => void;
  // onAssign?: (ticketId: string, agentId: string) => void; // reserved for future feature
}

export function TicketDetailModal({
  ticket,
  open,
  onClose,
  onStatusChange,
}: TicketDetailModalProps) {
  const [newMessage, setNewMessage] = useState("");
  const [sending, setSending] = useState(false);

  if (!ticket) return null;

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    setSending(true);
    try {
      // TODO: Implement message sending
      console.log("Sending message:", newMessage);
      setNewMessage("");
    } catch (error) {
      console.error("Error sending message:", error);
    } finally {
      setSending(false);
    }
  };

  const handleStatusChange = (newStatus: string) => {
    if (onStatusChange) {
      onStatusChange(ticket.id, newStatus);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <DialogTitle className="text-xl">{ticket.title}</DialogTitle>
              <DialogDescription className="flex items-center space-x-4 text-sm">
                <span>#{ticket.id.slice(0, 8)}</span>
                <span>•</span>
                <span>Created {formatDate(ticket.created_at)}</span>
                <span>•</span>
                <CategoryBadge category={ticket.category} size="sm" />
              </DialogDescription>
            </div>

            <div className="flex items-center space-x-2">
              <PriorityBadge priority={ticket.priority} />
              <StatusBadge status={ticket.status} />
              {ticket.sla_breach && (
                <Badge variant="destructive" className="text-xs">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  SLA Breach
                </Badge>
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Ticket Description */}
            <div className="space-y-3">
              <h3 className="font-semibold">Description</h3>
              <div className="bg-muted/50 rounded-lg p-4">
                <p className="text-sm whitespace-pre-wrap">
                  {ticket.description || "No description provided."}
                </p>
              </div>
            </div>

            {/* Messages Section */}
            <div className="space-y-3">
              <h3 className="font-semibold flex items-center">
                <MessageSquare className="w-4 h-4 mr-2" />
                Conversation
              </h3>

              {/* Mock conversation - replace with real messages */}
              <div className="space-y-4">
                <div className="flex space-x-3">
                  <Avatar className="w-8 h-8">
                    <AvatarFallback className="text-xs">
                      {getInitials(ticket.user_name || "U")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="bg-muted/50 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium text-sm">
                          {ticket.user_name}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatDate(ticket.created_at)}
                        </span>
                      </div>
                      <p className="text-sm">{ticket.description}</p>
                    </div>
                  </div>
                </div>

                {ticket.first_response_at && (
                  <div className="flex space-x-3">
                    <Avatar className="w-8 h-8">
                      <AvatarFallback className="text-xs bg-blue-100">
                        {getInitials(ticket.assigned_agent_name || "A")}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="bg-blue-50 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-sm">
                            {ticket.assigned_agent_name || "Support Agent"}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {formatDate(ticket.first_response_at)}
                          </span>
                        </div>
                        <p className="text-sm">
                          Thank you for contacting us. We&apos;re looking into
                          your issue and will get back to you shortly.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Message Input */}
              <div className="space-y-3">
                <Textarea
                  placeholder="Type your response..."
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  className="min-h-[100px]"
                />
                <div className="flex justify-between items-center">
                  <div className="text-xs text-muted-foreground">
                    This message will be sent to the customer
                  </div>
                  <Button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim() || sending}
                    size="sm"
                  >
                    <Send className="w-4 h-4 mr-2" />
                    {sending ? "Sending..." : "Send Reply"}
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Customer Info */}
            <div className="space-y-3">
              <h3 className="font-semibold">Customer</h3>
              <div className="flex items-center space-x-3">
                <Avatar className="w-10 h-10">
                  <AvatarFallback>
                    {getInitials(ticket.user_name || "U")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{ticket.user_name}</div>
                  <div className="text-sm text-muted-foreground">
                    {ticket.user_email}
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Ticket Details */}
            <div className="space-y-4">
              <h3 className="font-semibold">Ticket Details</h3>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Status</span>
                  <Select
                    value={ticket.status}
                    onValueChange={handleStatusChange}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="open">Open</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="waiting_user">Waiting User</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    Priority
                  </span>
                  <PriorityBadge priority={ticket.priority} size="sm" />
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    Category
                  </span>
                  <CategoryBadge category={ticket.category} size="sm" />
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Source</span>
                  <Badge variant="outline" className="text-xs">
                    {ticket.source.toUpperCase()}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    Assigned to
                  </span>
                  <span className="text-sm">
                    {ticket.assigned_agent_name || (
                      <span className="text-muted-foreground">Unassigned</span>
                    )}
                  </span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Timeline */}
            <div className="space-y-3">
              <h3 className="font-semibold">Timeline</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <span className="text-muted-foreground">Created:</span>
                  <span>{formatDate(ticket.created_at)}</span>
                </div>

                {ticket.first_response_at && (
                  <div className="flex items-center space-x-2">
                    <MessageSquare className="w-4 h-4 text-blue-500" />
                    <span className="text-muted-foreground">
                      First response:
                    </span>
                    <span>{formatDate(ticket.first_response_at)}</span>
                  </div>
                )}

                {ticket.resolved_at && (
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-muted-foreground">Resolved:</span>
                    <span>{formatDate(ticket.resolved_at)}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Tags */}
            {ticket.tags && ticket.tags.length > 0 && (
              <>
                <Separator />
                <div className="space-y-3">
                  <h3 className="font-semibold flex items-center">
                    <Tag className="w-4 h-4 mr-2" />
                    Tags
                  </h3>
                  <div className="flex flex-wrap gap-1">
                    {ticket.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  User,
  MessageSquare,
  Calendar,
  Tag,
  AlertCircle,
} from "lucide-react";
import { SupportTicket } from "@/services/supportService";

interface TicketCardProps {
  ticket: SupportTicket;
  onView?: (ticket: SupportTicket) => void;
  onAssign?: (ticket: SupportTicket) => void;
  onStatusChange?: (ticket: SupportTicket, newStatus: string) => void;
  compact?: boolean;
}

export function TicketCard({
  ticket,
  onView,
  onAssign,
  onStatusChange,
  compact = false,
}: TicketCardProps) {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "destructive";
      case "high":
        return "destructive";
      case "medium":
        return "default";
      case "low":
        return "secondary";
      default:
        return "default";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "destructive";
      case "in_progress":
        return "default";
      case "waiting_user":
        return "secondary";
      case "resolved":
        return "default";
      case "closed":
        return "secondary";
      default:
        return "default";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "open":
        return <AlertTriangle className="w-4 h-4" />;
      case "in_progress":
        return <Clock className="w-4 h-4" />;
      case "waiting_user":
        return <User className="w-4 h-4" />;
      case "resolved":
        return <CheckCircle className="w-4 h-4" />;
      case "closed":
        return <XCircle className="w-4 h-4" />;
      default:
        return <MessageSquare className="w-4 h-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffHours < 1) {
      const diffMinutes = Math.floor(diffHours * 60);
      return `${diffMinutes}m ago`;
    } else if (diffHours < 24) {
      return `${Math.floor(diffHours)}h ago`;
    } else if (diffHours < 48) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: date.getFullYear() !== now.getFullYear() ? "numeric" : undefined,
      });
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  if (compact) {
    return (
      <Card
        className="hover:shadow-md transition-shadow cursor-pointer"
        onClick={() => onView?.(ticket)}
      >
        <CardContent className="p-4">
          <div className="flex items-start justify-between space-x-4">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-2">
                <Badge
                  variant={getPriorityColor(ticket.priority)}
                  className="text-xs"
                >
                  {ticket.priority.toUpperCase()}
                </Badge>
                <div className="flex items-center space-x-1">
                  {getStatusIcon(ticket.status)}
                  <Badge
                    variant={getStatusColor(ticket.status)}
                    className="text-xs"
                  >
                    {ticket.status.replace("_", " ").toUpperCase()}
                  </Badge>
                </div>
                {ticket.sla_breach && (
                  <Badge variant="destructive" className="text-xs">
                    <AlertCircle className="w-3 h-3 mr-1" />
                    SLA
                  </Badge>
                )}
              </div>

              <h3 className="font-semibold text-sm truncate mb-1">
                {ticket.title}
              </h3>

              <div className="flex items-center text-xs text-muted-foreground space-x-3">
                <span>#{ticket.id.slice(0, 8)}</span>
                <span>{ticket.user_name}</span>
                <span>{formatDate(ticket.created_at)}</span>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {ticket.assigned_agent_name && (
                <Avatar className="w-6 h-6">
                  <AvatarFallback className="text-xs">
                    {getInitials(ticket.assigned_agent_name)}
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Badge variant={getPriorityColor(ticket.priority)}>
                {ticket.priority.toUpperCase()}
              </Badge>
              <div className="flex items-center space-x-1">
                {getStatusIcon(ticket.status)}
                <Badge variant={getStatusColor(ticket.status)}>
                  {ticket.status.replace("_", " ").toUpperCase()}
                </Badge>
              </div>
              {ticket.sla_breach && (
                <Badge variant="destructive">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  SLA Breach
                </Badge>
              )}
            </div>

            <h3 className="font-semibold text-lg leading-tight">
              {ticket.title}
            </h3>

            <div className="flex items-center text-sm text-muted-foreground space-x-4">
              <span className="flex items-center">
                <MessageSquare className="w-4 h-4 mr-1" />#
                {ticket.id.slice(0, 8)}
              </span>
              <span className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {formatDate(ticket.created_at)}
              </span>
              <span className="capitalize">{ticket.category}</span>
            </div>
          </div>

          <div className="flex flex-col items-end space-y-2">
            {ticket.assigned_agent_name ? (
              <div className="flex items-center space-x-2">
                <Avatar className="w-8 h-8">
                  <AvatarFallback>
                    {getInitials(ticket.assigned_agent_name)}
                  </AvatarFallback>
                </Avatar>
                <div className="text-right">
                  <div className="text-sm font-medium">
                    {ticket.assigned_agent_name}
                  </div>
                  <div className="text-xs text-muted-foreground">Assigned</div>
                </div>
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">Unassigned</div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-4">
          {ticket.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {ticket.description}
            </p>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Avatar className="w-6 h-6">
                  <AvatarFallback className="text-xs">
                    {getInitials(ticket.user_name || "U")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="text-sm font-medium">{ticket.user_name}</div>
                  <div className="text-xs text-muted-foreground">
                    {ticket.user_email}
                  </div>
                </div>
              </div>

              {ticket.tags && ticket.tags.length > 0 && (
                <div className="flex items-center space-x-1">
                  <Tag className="w-3 h-3 text-muted-foreground" />
                  <div className="flex space-x-1">
                    {ticket.tags.slice(0, 2).map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {ticket.tags.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{ticket.tags.length - 2}
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </div>

            <div className="flex space-x-2">
              {!ticket.assigned_to && onAssign && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onAssign(ticket);
                  }}
                >
                  Assign
                </Button>
              )}

              {ticket.status === "open" && onStatusChange && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onStatusChange(ticket, "in_progress");
                  }}
                >
                  Take
                </Button>
              )}

              {onView && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onView(ticket);
                  }}
                >
                  View
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

import React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { Search, X, Calendar as CalendarIcon } from "lucide-react";
import { format } from "date-fns";

export interface TicketFilterState {
  search: string;
  status: string;
  priority: string;
  category: string;
  assigned_to: string;
  sla_breach: boolean | null;
  date_from: Date | null;
  date_to: Date | null;
}

interface TicketFiltersProps {
  filters: TicketFilterState;
  onFiltersChange: (filters: TicketFilterState) => void;
  onReset: () => void;
  agents?: Array<{ id: string; name: string }>;
  categories?: string[];
  className?: string;
}

export function TicketFilters({
  filters,
  onFiltersChange,
  onReset,
  agents = [],
  categories = [],
  className = "",
}: TicketFiltersProps) {
  const updateFilter = (
    key: keyof TicketFilterState,
    value: string | boolean | Date | null,
  ) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const hasActiveFilters = () => {
    return (
      filters.search ||
      filters.status !== "all" ||
      filters.priority !== "all" ||
      filters.category !== "all" ||
      filters.assigned_to !== "all" ||
      filters.sla_breach !== null ||
      filters.date_from ||
      filters.date_to
    );
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.status !== "all") count++;
    if (filters.priority !== "all") count++;
    if (filters.category !== "all") count++;
    if (filters.assigned_to !== "all") count++;
    if (filters.sla_breach !== null) count++;
    if (filters.date_from || filters.date_to) count++;
    return count;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search tickets, customers, or issues..."
          value={filters.search}
          onChange={(e) => updateFilter("search", e.target.value)}
          className="pl-10 pr-10"
        />
        {filters.search && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1 h-8 w-8 p-0"
            onClick={() => updateFilter("search", "")}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Filter Row */}
      <div className="flex flex-wrap gap-3 items-center">
        {/* Status Filter */}
        <Select
          value={filters.status}
          onValueChange={(value) => updateFilter("status", value)}
        >
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="open">Open</SelectItem>
            <SelectItem value="in_progress">In Progress</SelectItem>
            <SelectItem value="waiting_user">Waiting User</SelectItem>
            <SelectItem value="resolved">Resolved</SelectItem>
            <SelectItem value="closed">Closed</SelectItem>
          </SelectContent>
        </Select>

        {/* Priority Filter */}
        <Select
          value={filters.priority}
          onValueChange={(value) => updateFilter("priority", value)}
        >
          <SelectTrigger className="w-[130px]">
            <SelectValue placeholder="Priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Priorities</SelectItem>
            <SelectItem value="urgent">Urgent</SelectItem>
            <SelectItem value="high">High</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="low">Low</SelectItem>
          </SelectContent>
        </Select>

        {/* Category Filter */}
        {categories.length > 0 && (
          <Select
            value={filters.category}
            onValueChange={(value) => updateFilter("category", value)}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {/* Assigned Agent Filter */}
        {agents.length > 0 && (
          <Select
            value={filters.assigned_to}
            onValueChange={(value) => updateFilter("assigned_to", value)}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Assigned to" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Agents</SelectItem>
              <SelectItem value="unassigned">Unassigned</SelectItem>
              {agents.map((agent) => (
                <SelectItem key={agent.id} value={agent.id}>
                  {agent.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {/* SLA Breach Filter */}
        <Select
          value={
            filters.sla_breach === null ? "all" : filters.sla_breach.toString()
          }
          onValueChange={(value) =>
            updateFilter(
              "sla_breach",
              value === "all" ? null : value === "true",
            )
          }
        >
          <SelectTrigger className="w-[130px]">
            <SelectValue placeholder="SLA Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Tickets</SelectItem>
            <SelectItem value="true">SLA Breach</SelectItem>
            <SelectItem value="false">Within SLA</SelectItem>
          </SelectContent>
        </Select>

        {/* Date Range Filter */}
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="w-[200px] justify-start text-left font-normal"
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {filters.date_from ? (
                filters.date_to ? (
                  <>
                    {format(filters.date_from, "LLL dd")} -{" "}
                    {format(filters.date_to, "LLL dd")}
                  </>
                ) : (
                  format(filters.date_from, "LLL dd, y")
                )
              ) : (
                <span>Date range</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <div className="p-3 space-y-3">
              <div className="text-sm font-medium">Select date range</div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-xs text-muted-foreground">From</label>
                  <Calendar
                    mode="single"
                    selected={filters.date_from || undefined}
                    onSelect={(date) => updateFilter("date_from", date || null)}
                    className="rounded-md border"
                  />
                </div>
                <div>
                  <label className="text-xs text-muted-foreground">To</label>
                  <Calendar
                    mode="single"
                    selected={filters.date_to || undefined}
                    onSelect={(date) => updateFilter("date_to", date || null)}
                    className="rounded-md border"
                  />
                </div>
              </div>
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    updateFilter("date_from", null);
                    updateFilter("date_to", null);
                  }}
                >
                  Clear
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Reset Filters */}
        {hasActiveFilters() && (
          <Button
            variant="outline"
            size="sm"
            onClick={onReset}
            className="ml-auto"
          >
            <X className="h-4 w-4 mr-2" />
            Reset ({getActiveFilterCount()})
          </Button>
        )}
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters() && (
        <div className="flex flex-wrap gap-2">
          {filters.search && (
            <Badge variant="secondary" className="gap-1">
              Search: &quot;{filters.search}&quot;
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter("search", "")}
              />
            </Badge>
          )}

          {filters.status !== "all" && (
            <Badge variant="secondary" className="gap-1">
              Status: {filters.status.replace("_", " ")}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter("status", "all")}
              />
            </Badge>
          )}

          {filters.priority !== "all" && (
            <Badge variant="secondary" className="gap-1">
              Priority: {filters.priority}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter("priority", "all")}
              />
            </Badge>
          )}

          {filters.category !== "all" && (
            <Badge variant="secondary" className="gap-1">
              Category: {filters.category}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter("category", "all")}
              />
            </Badge>
          )}

          {filters.assigned_to !== "all" && (
            <Badge variant="secondary" className="gap-1">
              Assigned:{" "}
              {filters.assigned_to === "unassigned"
                ? "Unassigned"
                : agents.find((a) => a.id === filters.assigned_to)?.name ||
                  filters.assigned_to}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter("assigned_to", "all")}
              />
            </Badge>
          )}

          {filters.sla_breach !== null && (
            <Badge variant="secondary" className="gap-1">
              SLA: {filters.sla_breach ? "Breach" : "Within SLA"}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => updateFilter("sla_breach", null)}
              />
            </Badge>
          )}

          {(filters.date_from || filters.date_to) && (
            <Badge variant="secondary" className="gap-1">
              Date:
              {filters.date_from
                ? format(filters.date_from, "MMM dd")
                : "Start"}{" "}
              -{filters.date_to ? format(filters.date_to, "MMM dd") : "End"}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => {
                  updateFilter("date_from", null);
                  updateFilter("date_to", null);
                }}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}

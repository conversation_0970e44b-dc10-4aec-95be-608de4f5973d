import React from "react";
import { Badge } from "@/components/ui/badge";
import {
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  User,
  MessageSquare,
  Pause,
} from "lucide-react";

interface StatusBadgeProps {
  status: "open" | "in_progress" | "waiting_user" | "resolved" | "closed";
  showIcon?: boolean;
  size?: "sm" | "default" | "lg";
  className?: string;
}

export function StatusBadge({
  status,
  showIcon = true,
  size = "default",
  className = "",
}: StatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case "open":
        return {
          variant: "destructive" as const,
          icon: <AlertTriangle className="w-4 h-4" />,
          label: "Open",
          description: "Ticket needs attention",
        };
      case "in_progress":
        return {
          variant: "default" as const,
          icon: <Clock className="w-4 h-4" />,
          label: "In Progress",
          description: "Being worked on",
        };
      case "waiting_user":
        return {
          variant: "secondary" as const,
          icon: <User className="w-4 h-4" />,
          label: "Waiting User",
          description: "Waiting for customer response",
        };
      case "resolved":
        return {
          variant: "default" as const,
          icon: <CheckCircle className="w-4 h-4" />,
          label: "Resolved",
          description: "Issue has been resolved",
        };
      case "closed":
        return {
          variant: "secondary" as const,
          icon: <XCircle className="w-4 h-4" />,
          label: "Closed",
          description: "Ticket is closed",
        };
      default:
        return {
          variant: "default" as const,
          icon: <MessageSquare className="w-4 h-4" />,
          label: "Unknown",
          description: "Unknown status",
        };
    }
  };

  const config = getStatusConfig(status);
  const sizeClasses = {
    sm: "text-xs px-2 py-1",
    default: "text-sm",
    lg: "text-base px-3 py-2",
  };

  return (
    <Badge
      variant={config.variant}
      className={`${sizeClasses[size]} ${className}`}
      title={config.description}
    >
      {showIcon && (
        <span className="mr-1 flex items-center">
          {React.cloneElement(config.icon, {
            className:
              size === "sm" ? "w-3 h-3" : size === "lg" ? "w-5 h-5" : "w-4 h-4",
          })}
        </span>
      )}
      {config.label}
    </Badge>
  );
}

interface PriorityBadgeProps {
  priority: "low" | "medium" | "high" | "urgent";
  showIcon?: boolean;
  size?: "sm" | "default" | "lg";
  className?: string;
}

export function PriorityBadge({
  priority,
  showIcon = false,
  size = "default",
  className = "",
}: PriorityBadgeProps) {
  const getPriorityConfig = (priority: string) => {
    switch (priority) {
      case "urgent":
        return {
          variant: "destructive" as const,
          icon: <AlertTriangle className="w-4 h-4" />,
          label: "Urgent",
          description: "Requires immediate attention",
        };
      case "high":
        return {
          variant: "destructive" as const,
          icon: <AlertTriangle className="w-4 h-4" />,
          label: "High",
          description: "High priority issue",
        };
      case "medium":
        return {
          variant: "default" as const,
          icon: <Clock className="w-4 h-4" />,
          label: "Medium",
          description: "Standard priority",
        };
      case "low":
        return {
          variant: "secondary" as const,
          icon: <Pause className="w-4 h-4" />,
          label: "Low",
          description: "Low priority issue",
        };
      default:
        return {
          variant: "default" as const,
          icon: <Clock className="w-4 h-4" />,
          label: "Medium",
          description: "Standard priority",
        };
    }
  };

  const config = getPriorityConfig(priority);
  const sizeClasses = {
    sm: "text-xs px-2 py-1",
    default: "text-sm",
    lg: "text-base px-3 py-2",
  };

  return (
    <Badge
      variant={config.variant}
      className={`${sizeClasses[size]} ${className}`}
      title={config.description}
    >
      {showIcon && (
        <span className="mr-1 flex items-center">
          {React.cloneElement(config.icon, {
            className:
              size === "sm" ? "w-3 h-3" : size === "lg" ? "w-5 h-5" : "w-4 h-4",
          })}
        </span>
      )}
      {config.label}
    </Badge>
  );
}

interface CategoryBadgeProps {
  category: string;
  size?: "sm" | "default" | "lg";
  className?: string;
}

export function CategoryBadge({
  category,
  size = "default",
  className = "",
}: CategoryBadgeProps) {
  const getCategoryConfig = (category: string) => {
    switch (category.toLowerCase()) {
      case "technical":
        return {
          variant: "default" as const,
          label: "Technical",
        };
      case "billing":
        return {
          variant: "secondary" as const,
          label: "Billing",
        };
      case "content":
        return {
          variant: "outline" as const,
          label: "Content",
        };
      case "feature_request":
        return {
          variant: "secondary" as const,
          label: "Feature Request",
        };
      case "general":
        return {
          variant: "outline" as const,
          label: "General",
        };
      default:
        return {
          variant: "outline" as const,
          label: category.charAt(0).toUpperCase() + category.slice(1),
        };
    }
  };

  const config = getCategoryConfig(category);
  const sizeClasses = {
    sm: "text-xs px-2 py-1",
    default: "text-sm",
    lg: "text-base px-3 py-2",
  };

  return (
    <Badge
      variant={config.variant}
      className={`${sizeClasses[size]} ${className}`}
    >
      {config.label}
    </Badge>
  );
}

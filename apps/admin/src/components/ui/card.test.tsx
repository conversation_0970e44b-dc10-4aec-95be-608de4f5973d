import React from "react";
import { render, screen } from "@testing-library/react";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "./card";

describe("Card", () => {
  it("should render Card component correctly", () => {
    render(<Card>Card Content</Card>);
    const card = screen.getByText("Card Content");
    expect(card).toBeInTheDocument();
    // Check for some key classes
    expect(card).toHaveClass("rounded-lg");
    expect(card).toHaveClass("border");
  });

  it("should render CardHeader component correctly", () => {
    render(<CardHeader>Header Content</CardHeader>);
    const header = screen.getByText("Header Content");
    expect(header).toBeInTheDocument();
    // Check for key classes
    expect(header).toHaveClass("flex");
    expect(header).toHaveClass("flex-col");
  });

  it("should render CardTitle component correctly", () => {
    render(<CardTitle>Card Title</CardTitle>);
    const title = screen.getByText("Card Title");
    expect(title).toBeInTheDocument();
    // Check for key classes
    expect(title).toHaveClass("font-semibold");
  });

  it("should render CardDescription component correctly", () => {
    render(<CardDescription>Card Description</CardDescription>);
    const description = screen.getByText("Card Description");
    expect(description).toBeInTheDocument();
    // Check for key classes
    expect(description).toHaveClass("text-sm");
  });

  it("should render CardContent component correctly", () => {
    render(<CardContent>Card Content</CardContent>);
    const content = screen.getByText("Card Content");
    expect(content).toBeInTheDocument();
    // Check for key classes
    expect(content).toHaveClass("p-6");
  });

  it("should render CardFooter component correctly", () => {
    render(<CardFooter>Card Footer</CardFooter>);
    const footer = screen.getByText("Card Footer");
    expect(footer).toBeInTheDocument();
    // Check for key classes
    expect(footer).toHaveClass("flex");
    expect(footer).toHaveClass("items-center");
  });

  it("should render complete card with all subcomponents", () => {
    render(
      <Card>
        <CardHeader>
          <CardTitle>Test Card</CardTitle>
          <CardDescription>This is a test card</CardDescription>
        </CardHeader>
        <CardContent>
          <p>Card content goes here</p>
        </CardContent>
        <CardFooter>
          <p>Card footer</p>
        </CardFooter>
      </Card>,
    );

    expect(screen.getByText("Test Card")).toBeInTheDocument();
    expect(screen.getByText("This is a test card")).toBeInTheDocument();
    expect(screen.getByText("Card content goes here")).toBeInTheDocument();
    expect(screen.getByText("Card footer")).toBeInTheDocument();
  });
});

import React from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Eye,
  ThumbsUp,
  ThumbsDown,
  Calendar,
  User,
  Tag,
  Star,
  MoreVertical,
  Edit,
  Trash2,
  ExternalLink,
  Copy,
  Archive,
} from "lucide-react";
import { KBArticle } from "@/services/knowledgeBaseService";

interface ArticleCardProps {
  article: KBArticle;
  onEdit?: (article: KBArticle) => void;
  onDelete?: (article: KBArticle) => void;
  onArchive?: (article: KBArticle) => void;
  onDuplicate?: (article: KBArticle) => void;
  onView?: (article: KBArticle) => void;
}

export function ArticleCard({
  article,
  onEdit,
  onDelete,
  onArchive,
  onDuplicate,
  onView,
}: ArticleCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "published":
        return "default";
      case "draft":
        return "secondary";
      case "archived":
        return "outline";
      default:
        return "default";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "published":
        return <ExternalLink className="w-3 h-3" />;
      case "draft":
        return <Edit className="w-3 h-3" />;
      case "archived":
        return <Archive className="w-3 h-3" />;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getHelpfulnessColor = (ratio: number) => {
    if (ratio >= 0.8) return "text-green-600";
    if (ratio >= 0.6) return "text-yellow-600";
    return "text-red-600";
  };

  const truncateContent = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + "...";
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-2">
            <div className="flex items-center space-x-2">
              <Badge
                variant={getStatusColor(article.status)}
                className="flex items-center gap-1"
              >
                {getStatusIcon(article.status)}
                {article.status.charAt(0).toUpperCase() +
                  article.status.slice(1)}
              </Badge>

              {article.featured && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Star className="w-3 h-3 fill-current" />
                  Featured
                </Badge>
              )}

              {article.category_name && (
                <Badge
                  variant="outline"
                  style={{ backgroundColor: `${article.category_name}20` }}
                >
                  {article.category_name}
                </Badge>
              )}
            </div>

            <h3
              className="font-semibold text-lg leading-tight hover:text-blue-600 cursor-pointer"
              onClick={() => onView?.(article)}
            >
              {article.title}
            </h3>

            <div className="flex items-center text-sm text-muted-foreground space-x-4">
              <span className="flex items-center">
                <User className="w-4 h-4 mr-1" />
                {article.author_name}
              </span>
              <span className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {formatDate(article.created_at)}
              </span>
              {article.published_at && (
                <span className="flex items-center">
                  <ExternalLink className="w-4 h-4 mr-1" />
                  Published {formatDate(article.published_at)}
                </span>
              )}
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(article)}>
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View Article
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(article)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
              )}
              {onDuplicate && (
                <DropdownMenuItem onClick={() => onDuplicate(article)}>
                  <Copy className="mr-2 h-4 w-4" />
                  Duplicate
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {onArchive && article.status !== "archived" && (
                <DropdownMenuItem onClick={() => onArchive(article)}>
                  <Archive className="mr-2 h-4 w-4" />
                  Archive
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem
                  onClick={() => onDelete(article)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Article Excerpt */}
          {article.excerpt && (
            <p className="text-sm text-muted-foreground">
              {truncateContent(article.excerpt)}
            </p>
          )}

          {/* Tags */}
          {article.tags && article.tags.length > 0 && (
            <div className="flex items-center space-x-2">
              <Tag className="w-4 h-4 text-muted-foreground" />
              <div className="flex flex-wrap gap-1">
                {article.tags.slice(0, 3).map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {article.tags.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{article.tags.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Metrics */}
          <div className="flex items-center justify-between pt-2 border-t">
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span className="flex items-center">
                <Eye className="w-4 h-4 mr-1" />
                {article.view_count.toLocaleString()} views
              </span>

              {(article.helpful_count > 0 || article.not_helpful_count > 0) && (
                <div className="flex items-center space-x-2">
                  <span className="flex items-center">
                    <ThumbsUp className="w-4 h-4 mr-1 text-green-500" />
                    {article.helpful_count}
                  </span>
                  <span className="flex items-center">
                    <ThumbsDown className="w-4 h-4 mr-1 text-red-500" />
                    {article.not_helpful_count}
                  </span>
                  <span
                    className={`font-medium ${getHelpfulnessColor(article.helpfulness_ratio || 0)}`}
                  >
                    {Math.round((article.helpfulness_ratio || 0) * 100)}%
                    helpful
                  </span>
                </div>
              )}
            </div>

            <div className="flex space-x-2">
              {onEdit && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEdit(article)}
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </Button>
              )}

              {onView && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => onView(article)}
                >
                  <ExternalLink className="w-4 h-4 mr-1" />
                  View
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

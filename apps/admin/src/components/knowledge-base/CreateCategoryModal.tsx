import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Save, Folder } from "lucide-react";
import {
  knowledgeBaseService,
  KBCategory,
  CreateCategoryData,
} from "@/services/knowledgeBaseService";

interface CreateCategoryModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  categories: KBCategory[];
  editCategory?: KBCategory; // For future edit functionality
}

const ICON_OPTIONS = [
  { value: "play-circle", label: "▶️ Getting Started", emoji: "▶️" },
  { value: "utensils", label: "🍽️ Meal Planning", emoji: "🍽️" },
  { value: "dumbbell", label: "🏋️ Workout Tracking", emoji: "🏋️" },
  { value: "user", label: "👤 Account Management", emoji: "👤" },
  { value: "credit-card", label: "💳 Billing", emoji: "💳" },
  { value: "wrench", label: "🔧 Technical Support", emoji: "🔧" },
  { value: "folder", label: "📁 General", emoji: "📁" },
];

const COLOR_OPTIONS = [
  "#06B6D4", // Cyan
  "#10B981", // Emerald
  "#3B82F6", // Blue
  "#8B5CF6", // Violet
  "#F59E0B", // Amber
  "#EF4444", // Red
  "#6B7280", // Gray
  "#EC4899", // Pink
];

export function CreateCategoryModal({
  open,
  onClose,
  onSuccess,
  categories,
  editCategory,
}: CreateCategoryModalProps) {
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState<CreateCategoryData>({
    name: "",
    description: "",
    slug: "",
    parent_id: "",
    icon: "folder",
    color: "#6B7280",
    sort_order: 0,
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Auto-generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  };

  const handleNameChange = (name: string) => {
    setFormData((prev) => ({
      ...prev,
      name,
      slug: generateSlug(name),
    }));
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = "Category name is required";
    }

    if (!formData.slug.trim()) {
      newErrors.slug = "Slug is required";
    } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {
      newErrors.slug =
        "Slug can only contain lowercase letters, numbers, and hyphens";
    }

    // Check for duplicate slugs
    const existingCategory = categories.find(
      (cat) => cat.slug === formData.slug && cat.id !== editCategory?.id,
    );
    if (existingCategory) {
      newErrors.slug = "A category with this slug already exists";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      if (editCategory) {
        await knowledgeBaseService.updateCategory(editCategory.id, formData);
      } else {
        await knowledgeBaseService.createCategory(formData);
      }
      onSuccess();
      handleClose();
    } catch (error) {
      console.error("Error saving category:", error);
      setErrors({ submit: "Failed to save category. Please try again." });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: "",
      description: "",
      slug: "",
      parent_id: "",
      icon: "folder",
      color: "#6B7280",
      sort_order: 0,
    });
    setErrors({});
    onClose();
  };

  // Get available parent categories (exclude current category and its children)
  const availableParentCategories = categories.filter(
    (cat) => cat.id !== editCategory?.id && cat.parent_id !== editCategory?.id,
  );

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Folder className="w-5 h-5 mr-2" />
            {editCategory ? "Edit Category" : "Create New Category"}
          </DialogTitle>
          <DialogDescription>
            {editCategory
              ? "Update your knowledge base category"
              : "Create a new category to organize your articles"}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Category Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleNameChange(e.target.value)}
                placeholder="Meal Planning"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name}</p>
              )}
            </div>

            <div>
              <Label htmlFor="slug">URL Slug *</Label>
              <div className="flex">
                <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                  /kb/
                </span>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, slug: e.target.value }))
                  }
                  placeholder="meal-planning"
                  className={`rounded-l-none ${errors.slug ? "border-red-500" : ""}`}
                />
              </div>
              {errors.slug && (
                <p className="text-sm text-red-500 mt-1">{errors.slug}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Brief description of what this category covers..."
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="parent_id">Parent Category</Label>
              <Select
                value={formData.parent_id || "none"}
                onValueChange={(value) =>
                  setFormData((prev) => ({
                    ...prev,
                    parent_id: value === "none" ? "" : value,
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="No parent (top-level)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">No parent (top-level)</SelectItem>
                  {availableParentCategories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="sort_order">Sort Order</Label>
              <Input
                id="sort_order"
                type="number"
                value={formData.sort_order}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    sort_order: parseInt(e.target.value) || 0,
                  }))
                }
                placeholder="0"
              />
              <p className="text-sm text-muted-foreground mt-1">
                Lower numbers appear first
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="icon">Icon</Label>
              <Select
                value={formData.icon}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, icon: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {ICON_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <span className="flex items-center">
                        <span className="mr-2">{option.emoji}</span>
                        {option.label}
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="color">Theme Color</Label>
              <div className="flex items-center space-x-2">
                <div
                  className="w-8 h-8 rounded border cursor-pointer"
                  style={{ backgroundColor: formData.color }}
                  onClick={() => {
                    // Simple color picker - cycle through options
                    const currentIndex = COLOR_OPTIONS.indexOf(
                      formData.color || COLOR_OPTIONS[0],
                    );
                    const nextIndex = (currentIndex + 1) % COLOR_OPTIONS.length;
                    setFormData((prev) => ({
                      ...prev,
                      color: COLOR_OPTIONS[nextIndex],
                    }));
                  }}
                />
                <Input
                  id="color"
                  value={formData.color}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, color: e.target.value }))
                  }
                  placeholder="#6B7280"
                  className="flex-1"
                />
              </div>
              <div className="flex flex-wrap gap-1 mt-2">
                {COLOR_OPTIONS.map((color) => (
                  <div
                    key={color}
                    className="w-6 h-6 rounded border cursor-pointer hover:scale-110 transition-transform"
                    style={{ backgroundColor: color }}
                    onClick={() => setFormData((prev) => ({ ...prev, color }))}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Preview */}
          <div className="border rounded-lg p-4 bg-muted/50">
            <Label>Preview</Label>
            <div className="flex items-center space-x-3 mt-2">
              <div
                className="w-10 h-10 rounded-lg flex items-center justify-center text-lg"
                style={{ backgroundColor: `${formData.color}20` }}
              >
                {ICON_OPTIONS.find((opt) => opt.value === formData.icon)
                  ?.emoji || "📁"}
              </div>
              <div>
                <h3 className="font-semibold">
                  {formData.name || "Category Name"}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {formData.description || "Category description"}
                </p>
              </div>
            </div>
          </div>
        </div>

        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3">
            <p className="text-sm text-red-600">{errors.submit}</p>
          </div>
        )}

        <div className="flex justify-between pt-4 border-t">
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>

          <Button onClick={handleSubmit} disabled={loading}>
            <Save className="w-4 h-4 mr-2" />
            {loading
              ? "Saving..."
              : editCategory
                ? "Update Category"
                : "Create Category"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  BookOpen,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Calendar,
} from "lucide-react";
import { KBCategory } from "@/services/knowledgeBaseService";

interface CategoryCardProps {
  category: KBCategory;
  onEdit?: (category: KBCategory) => void;
  onDelete?: (category: KBCategory) => void;
  onToggleActive?: (category: KBCategory) => void;
  onView?: (category: KBCategory) => void;
}

export function CategoryCard({
  category,
  onEdit,
  onDelete,
  onToggleActive,
  onView,
}: CategoryCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const getCategoryIcon = (iconName: string | null) => {
    // Map icon names to actual icons
    const iconMap: { [key: string]: React.ReactNode } = {
      "play-circle": "▶️",
      utensils: "🍽️",
      user: "👤",
      "credit-card": "💳",
      wrench: "🔧",
      dumbbell: "🏋️",
      folder: "📁",
    };

    return iconMap[iconName || "folder"] || "📁";
  };

  return (
    <Card
      className={`hover:shadow-md transition-shadow ${!category.is_active ? "opacity-60" : ""}`}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div
              className="w-10 h-10 rounded-lg flex items-center justify-center text-lg"
              style={{ backgroundColor: `${category.color}20` }}
            >
              {getCategoryIcon(category.icon)}
            </div>
            <div>
              <h3
                className="font-semibold text-lg leading-tight hover:text-blue-600 cursor-pointer"
                onClick={() => onView?.(category)}
              >
                {category.name}
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                <Badge variant={category.is_active ? "default" : "secondary"}>
                  {category.is_active ? "Active" : "Inactive"}
                </Badge>
                {category.parent_id && (
                  <Badge variant="outline" className="text-xs">
                    Subcategory
                  </Badge>
                )}
              </div>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(category)}>
                  <BookOpen className="mr-2 h-4 w-4" />
                  View Articles
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(category)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Category
                </DropdownMenuItem>
              )}
              {onToggleActive && (
                <DropdownMenuItem onClick={() => onToggleActive(category)}>
                  {category.is_active ? (
                    <>
                      <EyeOff className="mr-2 h-4 w-4" />
                      Deactivate
                    </>
                  ) : (
                    <>
                      <Eye className="mr-2 h-4 w-4" />
                      Activate
                    </>
                  )}
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {onDelete && (
                <DropdownMenuItem
                  onClick={() => onDelete(category)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Description */}
          {category.description && (
            <p className="text-sm text-muted-foreground">
              {category.description}
            </p>
          )}

          {/* Category Details */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Slug:</span>
              <code className="bg-muted px-2 py-1 rounded text-xs">
                /{category.slug}
              </code>
            </div>

            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Sort Order:</span>
              <span className="font-medium">{category.sort_order}</span>
            </div>

            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Articles:</span>
              <span className="font-medium flex items-center">
                <BookOpen className="w-4 h-4 mr-1" />
                {category.article_count || 0}
              </span>
            </div>
          </div>

          {/* Color Preview */}
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Theme Color:</span>
            <div className="flex items-center space-x-2">
              <div
                className="w-4 h-4 rounded border"
                style={{ backgroundColor: category.color }}
              ></div>
              <code className="text-xs">{category.color}</code>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between pt-2 border-t">
            <div className="flex items-center text-xs text-muted-foreground">
              <Calendar className="w-3 h-3 mr-1" />
              Created {formatDate(category.created_at)}
            </div>

            <div className="flex space-x-2">
              {onEdit && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEdit(category)}
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </Button>
              )}

              {onView && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => onView(category)}
                >
                  <BookOpen className="w-4 h-4 mr-1" />
                  View Articles
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

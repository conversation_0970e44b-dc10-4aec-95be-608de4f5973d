import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { ArticleCard } from "../ArticleCard";
import { KBArticle } from "@/services/knowledgeBaseService";

// Mock the UI components
jest.mock("@/components/ui/card", () => ({
  Card: ({ children, className }: any) => (
    <div className={className}>{children}</div>
  ),
  CardContent: ({ children }: any) => <div>{children}</div>,
  CardHeader: ({ children }: any) => <div>{children}</div>,
}));

jest.mock("@/components/ui/badge", () => ({
  Badge: ({ children, variant }: any) => (
    <span data-variant={variant}>{children}</span>
  ),
}));

jest.mock("@/components/ui/button", () => ({
  Button: ({ children, onClick, variant, size }: any) => (
    <button onClick={onClick} data-variant={variant} data-size={size}>
      {children}
    </button>
  ),
}));

jest.mock("@/components/ui/dropdown-menu", () => ({
  DropdownMenu: ({ children }: any) => <div>{children}</div>,
  DropdownMenuTrigger: ({ children }: any) => <div>{children}</div>,
  DropdownMenuContent: ({ children }: any) => <div>{children}</div>,
  DropdownMenuItem: ({ children, onClick }: any) => (
    <div onClick={onClick}>{children}</div>
  ),
  DropdownMenuSeparator: () => <hr />,
}));

const mockArticle: KBArticle = {
  id: "test-article-1",
  title: "How to Create Your First Meal Plan",
  slug: "how-to-create-first-meal-plan",
  content:
    "This is a comprehensive guide to creating your first meal plan with PlateMotion...",
  excerpt: "Step-by-step guide to creating your first AI-generated meal plan",
  category_id: "meal-planning-category",
  author_id: "admin-user-1",
  status: "published",
  published_at: "2024-01-01T00:00:00Z",
  meta_title: "How to Create Your First Meal Plan - PlateMotion",
  meta_description:
    "Learn how to create personalized meal plans with PlateMotion AI",
  tags: ["meal-planning", "getting-started", "ai"],
  view_count: 150,
  helpful_count: 12,
  not_helpful_count: 2,
  featured: true,
  sort_order: 0,
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
  category_name: "Meal Planning",
  category_slug: "meal-planning",
  author_name: "Support Team",
  author_email: "<EMAIL>",
  helpfulness_ratio: 0.86,
};

describe("ArticleCard", () => {
  const mockOnEdit = jest.fn();
  const mockOnDelete = jest.fn();
  const mockOnView = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render article information correctly", () => {
    render(
      <ArticleCard
        article={mockArticle}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onView={mockOnView}
      />,
    );

    expect(
      screen.getByText("How to Create Your First Meal Plan"),
    ).toBeInTheDocument();
    expect(screen.getByText("Support Team")).toBeInTheDocument();
    expect(
      screen.getByText(
        "Step-by-step guide to creating your first AI-generated meal plan",
      ),
    ).toBeInTheDocument();
  });

  it("should display correct status badge", () => {
    render(<ArticleCard article={mockArticle} />);

    expect(screen.getByText("Published")).toBeInTheDocument();
  });

  it("should display featured badge when article is featured", () => {
    render(<ArticleCard article={mockArticle} />);

    expect(screen.getByText("Featured")).toBeInTheDocument();
  });

  it("should not display featured badge when article is not featured", () => {
    const nonFeaturedArticle = { ...mockArticle, featured: false };
    render(<ArticleCard article={nonFeaturedArticle} />);

    expect(screen.queryByText("Featured")).not.toBeInTheDocument();
  });

  it("should display category badge when category is assigned", () => {
    render(<ArticleCard article={mockArticle} />);

    expect(screen.getByText("Meal Planning")).toBeInTheDocument();
  });

  it("should display view count and helpfulness metrics", () => {
    render(<ArticleCard article={mockArticle} />);

    expect(screen.getByText("150 views")).toBeInTheDocument();
    expect(screen.getByText("12")).toBeInTheDocument(); // helpful count
    expect(screen.getByText("2")).toBeInTheDocument(); // not helpful count
    expect(screen.getByText("86% helpful")).toBeInTheDocument();
  });

  it("should display tags correctly", () => {
    render(<ArticleCard article={mockArticle} />);

    expect(screen.getByText("meal-planning")).toBeInTheDocument();
    expect(screen.getByText("getting-started")).toBeInTheDocument();
    expect(screen.getByText("ai")).toBeInTheDocument();
  });

  it("should truncate long tag lists", () => {
    const articleWithManyTags = {
      ...mockArticle,
      tags: ["tag1", "tag2", "tag3", "tag4", "tag5", "tag6"],
    };

    render(<ArticleCard article={articleWithManyTags} />);

    expect(screen.getByText("+3 more")).toBeInTheDocument();
  });

  it("should call onEdit when edit button is clicked", () => {
    render(<ArticleCard article={mockArticle} onEdit={mockOnEdit} />);

    const editButtons = screen.getAllByText("Edit");
    const editButton = editButtons[editButtons.length - 1]; // Get the last Edit button (the main one)
    fireEvent.click(editButton);

    expect(mockOnEdit).toHaveBeenCalledWith(mockArticle);
  });

  it("should call onView when view button is clicked", () => {
    render(<ArticleCard article={mockArticle} onView={mockOnView} />);

    const viewButton = screen.getByText("View");
    fireEvent.click(viewButton);

    expect(mockOnView).toHaveBeenCalledWith(mockArticle);
  });

  it("should call onView when title is clicked", () => {
    render(<ArticleCard article={mockArticle} onView={mockOnView} />);

    const title = screen.getByText("How to Create Your First Meal Plan");
    fireEvent.click(title);

    expect(mockOnView).toHaveBeenCalledWith(mockArticle);
  });

  it("should handle articles without excerpts", () => {
    const articleWithoutExcerpt = { ...mockArticle, excerpt: null };

    render(<ArticleCard article={articleWithoutExcerpt} />);

    expect(
      screen.getByText("How to Create Your First Meal Plan"),
    ).toBeInTheDocument();
  });

  it("should handle articles without categories", () => {
    const articleWithoutCategory = {
      ...mockArticle,
      category_id: null,
      category_name: undefined,
      category_slug: undefined,
    };

    render(<ArticleCard article={articleWithoutCategory} />);

    expect(
      screen.getByText("How to Create Your First Meal Plan"),
    ).toBeInTheDocument();
    expect(screen.queryByText("Meal Planning")).not.toBeInTheDocument();
  });

  it("should handle articles with zero feedback", () => {
    const articleWithoutFeedback = {
      ...mockArticle,
      helpful_count: 0,
      not_helpful_count: 0,
      helpfulness_ratio: 0,
    };

    render(<ArticleCard article={articleWithoutFeedback} />);

    expect(screen.getByText("150 views")).toBeInTheDocument();
    // Should not display helpfulness metrics when no feedback
  });

  it("should format dates correctly", () => {
    render(<ArticleCard article={mockArticle} />);

    // Should display formatted date (use getAllByText since date appears twice)
    const dateElements = screen.getAllByText(/Dec.*2023/);
    expect(dateElements.length).toBeGreaterThan(0);
  });

  it("should display correct helpfulness color coding", () => {
    // Test high helpfulness (green)
    const highHelpfulnessArticle = { ...mockArticle, helpfulness_ratio: 0.9 };
    const { rerender } = render(
      <ArticleCard article={highHelpfulnessArticle} />,
    );

    // Test medium helpfulness (yellow)
    const mediumHelpfulnessArticle = { ...mockArticle, helpfulness_ratio: 0.7 };
    rerender(<ArticleCard article={mediumHelpfulnessArticle} />);

    // Test low helpfulness (red)
    const lowHelpfulnessArticle = { ...mockArticle, helpfulness_ratio: 0.4 };
    rerender(<ArticleCard article={lowHelpfulnessArticle} />);

    // All should render without errors
    expect(
      screen.getByText("How to Create Your First Meal Plan"),
    ).toBeInTheDocument();
  });

  it("should handle different article statuses", () => {
    // Test draft status
    const draftArticle = { ...mockArticle, status: "draft" as const };
    const { rerender } = render(<ArticleCard article={draftArticle} />);
    expect(screen.getByText("Draft")).toBeInTheDocument();

    // Test archived status
    const archivedArticle = { ...mockArticle, status: "archived" as const };
    rerender(<ArticleCard article={archivedArticle} />);
    expect(screen.getByText("Archived")).toBeInTheDocument();
  });
});

import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { CreateArticleModal } from "../CreateArticleModal";
import { KBCategory } from "@/services/knowledgeBaseService";
import * as knowledgeBaseService from "@/services/knowledgeBaseService";

// Mock the service
jest.mock("@/services/knowledgeBaseService", () => ({
  knowledgeBaseService: {
    createArticle: jest.fn(),
  },
}));

// Mock UI components
jest.mock("@/components/ui/dialog", () => ({
  Dialog: ({ children, open }: any) =>
    open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div>{children}</div>,
  DialogHeader: ({ children }: any) => <div>{children}</div>,
  DialogTitle: ({ children }: any) => <h2>{children}</h2>,
  DialogDescription: ({ children }: any) => <p>{children}</p>,
}));

jest.mock("@/components/ui/button", () => ({
  Button: ({ children, onClick, disabled, variant }: any) => (
    <button onClick={onClick} disabled={disabled} data-variant={variant}>
      {children}
    </button>
  ),
}));

jest.mock("@/components/ui/input", () => ({
  Input: ({ value, onChange, placeholder, className, ...props }: any) => (
    <input
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className={className}
      {...props}
    />
  ),
}));

jest.mock("@/components/ui/textarea", () => ({
  Textarea: ({ value, onChange, placeholder, rows, className }: any) => (
    <textarea
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      rows={rows}
      className={className}
    />
  ),
}));

jest.mock("@/components/ui/label", () => ({
  Label: ({ children, htmlFor }: any) => (
    <label htmlFor={htmlFor}>{children}</label>
  ),
}));

jest.mock("@/components/ui/select", () => ({
  Select: ({ children, value, onValueChange }: any) => (
    <div data-testid="select" data-value={value}>
      <button onClick={() => onValueChange && onValueChange("test-value")}>
        {value || "Select..."}
      </button>
      {children}
    </div>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value }: any) => (
    <div data-value={value}>{children}</div>
  ),
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
}));

jest.mock("@/components/ui/tabs", () => ({
  Tabs: ({ children, value, onValueChange }: any) => (
    <div data-testid="tabs" data-value={value}>
      <button onClick={() => onValueChange && onValueChange("content")}>
        Content
      </button>
      <button onClick={() => onValueChange && onValueChange("settings")}>
        Settings
      </button>
      <button onClick={() => onValueChange && onValueChange("seo")}>SEO</button>
      {children}
    </div>
  ),
  TabsContent: ({ children, value }: any) => (
    <div data-value={value}>{children}</div>
  ),
  TabsList: ({ children }: any) => <div>{children}</div>,
  TabsTrigger: ({ children, value }: any) => (
    <button data-value={value}>{children}</button>
  ),
}));

jest.mock("@/components/ui/checkbox", () => ({
  Checkbox: ({ checked, onCheckedChange, id }: any) => (
    <input
      type="checkbox"
      id={id}
      checked={checked}
      onChange={(e) => onCheckedChange && onCheckedChange(e.target.checked)}
    />
  ),
}));

const mockCategories: KBCategory[] = [
  {
    id: "getting-started",
    name: "Getting Started",
    description: "New user guides",
    slug: "getting-started",
    parent_id: null,
    sort_order: 0,
    icon: "play-circle",
    color: "#06B6D4",
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    created_by: null,
  },
  {
    id: "meal-planning",
    name: "Meal Planning",
    description: "Meal planning guides",
    slug: "meal-planning",
    parent_id: null,
    sort_order: 1,
    icon: "utensils",
    color: "#10B981",
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    created_by: null,
  },
];

describe("CreateArticleModal", () => {
  const mockOnClose = jest.fn();
  const mockOnSuccess = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render when open", () => {
    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    expect(screen.getByTestId("dialog")).toBeInTheDocument();
    expect(screen.getByText("Create New Article")).toBeInTheDocument();
  });

  it("should not render when closed", () => {
    render(
      <CreateArticleModal
        open={false}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    expect(screen.queryByTestId("dialog")).not.toBeInTheDocument();
  });

  it("should auto-generate slug from title", () => {
    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    const titleInput = screen.getByPlaceholderText(
      "How to create your first meal plan",
    );
    const slugInput = screen.getByPlaceholderText(
      "how-to-create-first-meal-plan",
    );

    fireEvent.change(titleInput, { target: { value: "Test Article Title!" } });

    // The slug should be auto-generated
    expect(slugInput.value).toBe("test-article-title");
  });

  it("should validate required fields", async () => {
    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    const publishButton = screen.getByText("Publish Article");
    fireEvent.click(publishButton);

    await waitFor(() => {
      expect(screen.getByText("Title is required")).toBeInTheDocument();
      expect(screen.getByText("Content is required")).toBeInTheDocument();
    });
  });

  it("should validate slug format", async () => {
    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    const slugInput = screen.getByPlaceholderText(
      "how-to-create-first-meal-plan",
    );
    fireEvent.change(slugInput, {
      target: { value: "Invalid Slug With Spaces!" },
    });

    const publishButton = screen.getByText("Publish Article");
    fireEvent.click(publishButton);

    await waitFor(() => {
      expect(
        screen.getByText(
          "Slug can only contain lowercase letters, numbers, and hyphens",
        ),
      ).toBeInTheDocument();
    });
  });

  it("should validate minimum content length", async () => {
    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    const titleInput = screen.getByPlaceholderText(
      "How to create your first meal plan",
    );
    const contentInput = screen.getByPlaceholderText(
      "Write your article content here. You can use Markdown formatting...",
    );

    fireEvent.change(titleInput, { target: { value: "Test Article" } });
    fireEvent.change(contentInput, { target: { value: "Short content" } });

    const publishButton = screen.getByText("Publish Article");
    fireEvent.click(publishButton);

    await waitFor(() => {
      expect(
        screen.getByText("Content must be at least 50 characters long"),
      ).toBeInTheDocument();
    });
  });

  it("should add and remove tags correctly", () => {
    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Switch to settings tab
    const settingsTab = screen.getByText("Settings");
    fireEvent.click(settingsTab);

    const tagInput = screen.getByPlaceholderText("Add a tag...");
    const addTagButton = screen.getByRole("button", { name: /plus/i });

    // Add a tag
    fireEvent.change(tagInput, { target: { value: "test-tag" } });
    fireEvent.click(addTagButton);

    expect(screen.getByText("test-tag")).toBeInTheDocument();

    // Remove the tag
    const removeTagButton = screen.getByRole("button", { name: /x/i });
    fireEvent.click(removeTagButton);

    expect(screen.queryByText("test-tag")).not.toBeInTheDocument();
  });

  it("should handle tag addition on Enter key", () => {
    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Switch to settings tab
    const settingsTab = screen.getByText("Settings");
    fireEvent.click(settingsTab);

    const tagInput = screen.getByPlaceholderText("Add a tag...");

    fireEvent.change(tagInput, { target: { value: "enter-tag" } });
    fireEvent.keyPress(tagInput, { key: "Enter", code: "Enter" });

    expect(screen.getByText("enter-tag")).toBeInTheDocument();
  });

  it("should prevent duplicate tags", () => {
    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Switch to settings tab
    const settingsTab = screen.getByText("Settings");
    fireEvent.click(settingsTab);

    const tagInput = screen.getByPlaceholderText("Add a tag...");
    const addTagButton = screen.getByRole("button", { name: /plus/i });

    // Add same tag twice
    fireEvent.change(tagInput, { target: { value: "duplicate-tag" } });
    fireEvent.click(addTagButton);
    fireEvent.change(tagInput, { target: { value: "duplicate-tag" } });
    fireEvent.click(addTagButton);

    // Should only appear once
    const tags = screen.getAllByText("duplicate-tag");
    expect(tags).toHaveLength(1);
  });

  it("should call onSuccess and onClose after successful creation", async () => {
    const mockCreateArticle = jest
      .spyOn(knowledgeBaseService.knowledgeBaseService, "createArticle")
      .mockResolvedValue("new-article-id");

    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Fill in required fields
    const titleInput = screen.getByPlaceholderText(
      "How to create your first meal plan",
    );
    const contentInput = screen.getByPlaceholderText(
      "Write your article content here. You can use Markdown formatting...",
    );

    fireEvent.change(titleInput, { target: { value: "Test Article Title" } });
    fireEvent.change(contentInput, {
      target: {
        value:
          "This is a test article with enough content to meet the minimum requirements for validation.",
      },
    });

    const publishButton = screen.getByText("Publish Article");
    fireEvent.click(publishButton);

    await waitFor(() => {
      expect(mockCreateArticle).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Test Article Title",
          slug: "test-article-title",
          status: "published",
        }),
      );
      expect(mockOnSuccess).toHaveBeenCalled();
    });

    mockCreateArticle.mockRestore();
  });

  it("should save as draft when draft button is clicked", async () => {
    const mockCreateArticle = jest
      .spyOn(knowledgeBaseService.knowledgeBaseService, "createArticle")
      .mockResolvedValue("new-article-id");

    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Fill in required fields
    const titleInput = screen.getByPlaceholderText(
      "How to create your first meal plan",
    );
    const contentInput = screen.getByPlaceholderText(
      "Write your article content here. You can use Markdown formatting...",
    );

    fireEvent.change(titleInput, { target: { value: "Draft Article" } });
    fireEvent.change(contentInput, {
      target: {
        value:
          "This is a draft article with enough content to meet requirements.",
      },
    });

    const draftButton = screen.getByText("Save as Draft");
    fireEvent.click(draftButton);

    await waitFor(() => {
      expect(mockCreateArticle).toHaveBeenCalledWith(
        expect.objectContaining({
          title: "Draft Article",
          status: "draft",
        }),
      );
    });

    mockCreateArticle.mockRestore();
  });

  it("should handle service errors gracefully", async () => {
    const mockCreateArticle = jest
      .spyOn(knowledgeBaseService.knowledgeBaseService, "createArticle")
      .mockRejectedValue(new Error("Service error"));

    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Fill in required fields
    const titleInput = screen.getByPlaceholderText(
      "How to create your first meal plan",
    );
    const contentInput = screen.getByPlaceholderText(
      "Write your article content here. You can use Markdown formatting...",
    );

    fireEvent.change(titleInput, { target: { value: "Error Test Article" } });
    fireEvent.change(contentInput, {
      target: {
        value: "This article will trigger a service error during creation.",
      },
    });

    const publishButton = screen.getByText("Publish Article");
    fireEvent.click(publishButton);

    await waitFor(() => {
      expect(
        screen.getByText("Failed to create article. Please try again."),
      ).toBeInTheDocument();
    });

    mockCreateArticle.mockRestore();
  });

  it("should reset form when closed", () => {
    const { rerender } = render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Fill in some data
    const titleInput = screen.getByPlaceholderText(
      "How to create your first meal plan",
    );
    fireEvent.change(titleInput, { target: { value: "Test Title" } });

    // Close and reopen
    rerender(
      <CreateArticleModal
        open={false}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    rerender(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Form should be reset
    const titleInputAfterReopen = screen.getByPlaceholderText(
      "How to create your first meal plan",
    );
    expect(titleInputAfterReopen.value).toBe("");
  });

  it("should auto-populate meta title from title", () => {
    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    const titleInput = screen.getByPlaceholderText(
      "How to create your first meal plan",
    );
    fireEvent.change(titleInput, { target: { value: "Auto Meta Title Test" } });

    // Switch to SEO tab
    const seoTab = screen.getByText("SEO & Meta");
    fireEvent.click(seoTab);

    const metaTitleInput = screen.getByPlaceholderText(
      "SEO title for search engines",
    );
    expect(metaTitleInput.value).toBe("Auto Meta Title Test");
  });

  it("should handle featured checkbox toggle", () => {
    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Switch to settings tab
    const settingsTab = screen.getByText("Settings");
    fireEvent.click(settingsTab);

    const featuredCheckbox = screen.getByRole("checkbox");
    expect(featuredCheckbox.checked).toBe(false);

    fireEvent.click(featuredCheckbox);
    expect(featuredCheckbox.checked).toBe(true);
  });

  it("should handle sort order input", () => {
    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Switch to settings tab
    const settingsTab = screen.getByText("Settings");
    fireEvent.click(settingsTab);

    const sortOrderInput = screen.getByPlaceholderText("0");
    fireEvent.change(sortOrderInput, { target: { value: "5" } });

    expect(sortOrderInput.value).toBe("5");
  });

  it("should display character count recommendations", () => {
    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Switch to SEO tab
    const seoTab = screen.getByText("SEO & Meta");
    fireEvent.click(seoTab);

    expect(
      screen.getByText("Recommended: 50-60 characters"),
    ).toBeInTheDocument();
    expect(
      screen.getByText("Recommended: 150-160 characters"),
    ).toBeInTheDocument();
  });

  it("should show loading state during submission", async () => {
    const mockCreateArticle = jest
      .spyOn(knowledgeBaseService.knowledgeBaseService, "createArticle")
      .mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve("id"), 1000)),
      );

    render(
      <CreateArticleModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Fill in required fields
    const titleInput = screen.getByPlaceholderText(
      "How to create your first meal plan",
    );
    const contentInput = screen.getByPlaceholderText(
      "Write your article content here. You can use Markdown formatting...",
    );

    fireEvent.change(titleInput, { target: { value: "Loading Test Article" } });
    fireEvent.change(contentInput, {
      target: {
        value:
          "This article will test the loading state during creation process.",
      },
    });

    const publishButton = screen.getByText("Publish Article");
    fireEvent.click(publishButton);

    expect(screen.getByText("Publishing...")).toBeInTheDocument();
    expect(publishButton).toBeDisabled();

    mockCreateArticle.mockRestore();
  });
});

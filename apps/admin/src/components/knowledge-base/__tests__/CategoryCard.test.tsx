import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { CategoryCard } from "../CategoryCard";
import { KBCategory } from "@/services/knowledgeBaseService";

// Mock the UI components
jest.mock("@/components/ui/card", () => ({
  Card: ({ children, className }: any) => (
    <div className={className}>{children}</div>
  ),
  CardContent: ({ children }: any) => <div>{children}</div>,
  CardHeader: ({ children }: any) => <div>{children}</div>,
}));

jest.mock("@/components/ui/badge", () => ({
  Badge: ({ children, variant }: any) => (
    <span data-variant={variant}>{children}</span>
  ),
}));

jest.mock("@/components/ui/button", () => ({
  Button: ({ children, onClick, variant, size }: any) => (
    <button onClick={onClick} data-variant={variant} data-size={size}>
      {children}
    </button>
  ),
}));

jest.mock("@/components/ui/dropdown-menu", () => ({
  DropdownMenu: ({ children }: any) => <div>{children}</div>,
  DropdownMenuTrigger: ({ children }: any) => <div>{children}</div>,
  DropdownMenuContent: ({ children }: any) => <div>{children}</div>,
  DropdownMenuItem: ({ children, onClick }: any) => (
    <div onClick={onClick}>{children}</div>
  ),
  DropdownMenuSeparator: () => <hr />,
}));

const mockCategory: KBCategory = {
  id: "meal-planning-category",
  name: "Meal Planning",
  description: "AI meal planner, dietary restrictions, and nutrition guidance",
  slug: "meal-planning",
  parent_id: null,
  sort_order: 1,
  icon: "utensils",
  color: "#10B981",
  is_active: true,
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
  created_by: "admin-user-1",
  article_count: 8,
};

describe("CategoryCard", () => {
  const mockOnEdit = jest.fn();
  const mockOnDelete = jest.fn();
  const mockOnToggleActive = jest.fn();
  const mockOnView = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render category information correctly", () => {
    render(
      <CategoryCard
        category={mockCategory}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onView={mockOnView}
      />,
    );

    expect(screen.getByText("Meal Planning")).toBeInTheDocument();
    expect(
      screen.getByText(
        "AI meal planner, dietary restrictions, and nutrition guidance",
      ),
    ).toBeInTheDocument();
    expect(screen.getByText("/meal-planning")).toBeInTheDocument();
  });

  it("should display active status badge", () => {
    render(<CategoryCard category={mockCategory} />);

    expect(screen.getByText("Active")).toBeInTheDocument();
  });

  it("should display inactive status for inactive categories", () => {
    const inactiveCategory = { ...mockCategory, is_active: false };
    render(<CategoryCard category={inactiveCategory} />);

    expect(screen.getByText("Inactive")).toBeInTheDocument();
  });

  it("should display subcategory badge when category has parent", () => {
    const subcategory = { ...mockCategory, parent_id: "parent-category-id" };
    render(<CategoryCard category={subcategory} />);

    expect(screen.getByText("Subcategory")).toBeInTheDocument();
  });

  it("should not display subcategory badge for top-level categories", () => {
    render(<CategoryCard category={mockCategory} />);

    expect(screen.queryByText("Subcategory")).not.toBeInTheDocument();
  });

  it("should display article count", () => {
    render(<CategoryCard category={mockCategory} />);

    expect(screen.getByText("8")).toBeInTheDocument();
  });

  it("should display sort order", () => {
    render(<CategoryCard category={mockCategory} />);

    expect(screen.getByText("1")).toBeInTheDocument();
  });

  it("should display theme color correctly", () => {
    render(<CategoryCard category={mockCategory} />);

    expect(screen.getByText("#10B981")).toBeInTheDocument();
  });

  it("should call onEdit when edit button is clicked", () => {
    render(<CategoryCard category={mockCategory} onEdit={mockOnEdit} />);

    const editButton = screen.getByText("Edit");
    fireEvent.click(editButton);

    expect(mockOnEdit).toHaveBeenCalledWith(mockCategory);
  });

  it("should call onView when view button is clicked", () => {
    render(<CategoryCard category={mockCategory} onView={mockOnView} />);

    const viewButtons = screen.getAllByText("View Articles");
    const viewButton = viewButtons[viewButtons.length - 1]; // Get the actual button
    fireEvent.click(viewButton);

    expect(mockOnView).toHaveBeenCalledWith(mockCategory);
  });

  it("should call onView when category name is clicked", () => {
    render(<CategoryCard category={mockCategory} onView={mockOnView} />);

    const categoryName = screen.getByText("Meal Planning");
    fireEvent.click(categoryName);

    expect(mockOnView).toHaveBeenCalledWith(mockCategory);
  });

  it("should format creation date correctly", () => {
    render(<CategoryCard category={mockCategory} />);

    expect(screen.getByText(/Created.*Dec.*2023/)).toBeInTheDocument();
  });

  it("should handle categories without descriptions", () => {
    const categoryWithoutDescription = { ...mockCategory, description: null };

    render(<CategoryCard category={categoryWithoutDescription} />);

    expect(screen.getByText("Meal Planning")).toBeInTheDocument();
    expect(screen.queryByText("AI meal planner")).not.toBeInTheDocument();
  });

  it("should handle categories with zero articles", () => {
    const categoryWithoutArticles = { ...mockCategory, article_count: 0 };

    render(<CategoryCard category={categoryWithoutArticles} />);

    expect(screen.getByText("0")).toBeInTheDocument();
  });

  it("should apply opacity styling for inactive categories", () => {
    const inactiveCategory = { ...mockCategory, is_active: false };
    const { container } = render(<CategoryCard category={inactiveCategory} />);

    // Check if the card has opacity styling
    const card = container.firstChild;
    expect(card).toHaveClass("opacity-60");
  });

  it("should display correct icon emoji", () => {
    render(<CategoryCard category={mockCategory} />);

    // Should display the utensils emoji for meal planning
    expect(screen.getByText("🍽️")).toBeInTheDocument();
  });

  it("should handle unknown icon gracefully", () => {
    const categoryWithUnknownIcon = { ...mockCategory, icon: "unknown-icon" };

    render(<CategoryCard category={categoryWithUnknownIcon} />);

    // Should fall back to folder emoji
    expect(screen.getByText("📁")).toBeInTheDocument();
  });

  it("should handle null icon gracefully", () => {
    const categoryWithNullIcon = { ...mockCategory, icon: null };

    render(<CategoryCard category={categoryWithNullIcon} />);

    // Should fall back to folder emoji
    expect(screen.getByText("📁")).toBeInTheDocument();
  });

  it("should display color preview correctly", () => {
    const { container } = render(<CategoryCard category={mockCategory} />);

    // Check if color preview div has correct background color (RGB format)
    const colorPreview = container.querySelector(
      '[style*="background-color: rgb(16, 185, 129)"]',
    );
    expect(colorPreview).toBeInTheDocument();
  });

  it("should handle different color formats", () => {
    const categoryWithDifferentColor = { ...mockCategory, color: "#FF0000" };

    render(<CategoryCard category={categoryWithDifferentColor} />);

    expect(screen.getByText("#FF0000")).toBeInTheDocument();
  });

  it("should not render action buttons when handlers are not provided", () => {
    render(<CategoryCard category={mockCategory} />);

    expect(screen.queryByText("Edit")).not.toBeInTheDocument();
    expect(screen.queryByText("View Articles")).not.toBeInTheDocument();
  });

  it("should render all action buttons when handlers are provided", () => {
    render(
      <CategoryCard
        category={mockCategory}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onToggleActive={mockOnToggleActive}
        onView={mockOnView}
      />,
    );

    expect(screen.getByText("Edit")).toBeInTheDocument();
    const viewButtons = screen.getAllByText("View Articles");
    expect(viewButtons.length).toBeGreaterThan(0);
  });
});

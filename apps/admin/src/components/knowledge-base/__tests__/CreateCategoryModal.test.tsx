import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { CreateCategoryModal } from "../CreateCategoryModal";
import { KBCategory } from "@/services/knowledgeBaseService";
import * as knowledgeBaseService from "@/services/knowledgeBaseService";

// Mock the service
jest.mock("@/services/knowledgeBaseService", () => ({
  knowledgeBaseService: {
    createCategory: jest.fn(),
    updateCategory: jest.fn(),
  },
}));

// Mock UI components (same as ArticleModal)
jest.mock("@/components/ui/dialog", () => ({
  Dialog: ({ children, open }: any) =>
    open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div>{children}</div>,
  DialogHeader: ({ children }: any) => <div>{children}</div>,
  DialogTitle: ({ children }: any) => <h2>{children}</h2>,
  DialogDescription: ({ children }: any) => <p>{children}</p>,
}));

jest.mock("@/components/ui/button", () => ({
  Button: ({ children, onClick, disabled, variant }: any) => (
    <button onClick={onClick} disabled={disabled} data-variant={variant}>
      {children}
    </button>
  ),
}));

jest.mock("@/components/ui/input", () => ({
  Input: ({ value, onChange, placeholder, className, type, ...props }: any) => (
    <input
      type={type || "text"}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className={className}
      {...props}
    />
  ),
}));

jest.mock("@/components/ui/textarea", () => ({
  Textarea: ({ value, onChange, placeholder, rows }: any) => (
    <textarea
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      rows={rows}
    />
  ),
}));

jest.mock("@/components/ui/label", () => ({
  Label: ({ children, htmlFor }: any) => (
    <label htmlFor={htmlFor}>{children}</label>
  ),
}));

jest.mock("@/components/ui/select", () => ({
  Select: ({ children, value, onValueChange }: any) => (
    <div data-testid="select" data-value={value}>
      <button onClick={() => onValueChange && onValueChange("test-value")}>
        {value || "Select..."}
      </button>
      {children}
    </div>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value }: any) => (
    <div data-value={value}>{children}</div>
  ),
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
}));

const mockCategories: KBCategory[] = [
  {
    id: "parent-category",
    name: "Parent Category",
    description: "A parent category",
    slug: "parent-category",
    parent_id: null,
    sort_order: 0,
    icon: "folder",
    color: "#6B7280",
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    created_by: null,
  },
  {
    id: "child-category",
    name: "Child Category",
    description: "A child category",
    slug: "child-category",
    parent_id: "parent-category",
    sort_order: 1,
    icon: "folder",
    color: "#10B981",
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    created_by: null,
  },
];

describe("CreateCategoryModal", () => {
  const mockOnClose = jest.fn();
  const mockOnSuccess = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render when open", () => {
    render(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    expect(screen.getByTestId("dialog")).toBeInTheDocument();
    expect(screen.getByText("Create New Category")).toBeInTheDocument();
  });

  it("should not render when closed", () => {
    render(
      <CreateCategoryModal
        open={false}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    expect(screen.queryByTestId("dialog")).not.toBeInTheDocument();
  });

  it("should auto-generate slug from name", () => {
    render(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    const nameInput = screen.getByPlaceholderText("Meal Planning");
    const slugInput = screen.getByPlaceholderText("meal-planning");

    fireEvent.change(nameInput, { target: { value: "Test Category Name!" } });

    expect(slugInput.value).toBe("test-category-name");
  });

  it("should validate required fields", async () => {
    render(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    const createButton = screen.getByText("Create Category");
    fireEvent.click(createButton);

    await waitFor(() => {
      expect(screen.getByText("Category name is required")).toBeInTheDocument();
      expect(screen.getByText("Slug is required")).toBeInTheDocument();
    });
  });

  it("should validate slug format", async () => {
    render(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    const nameInput = screen.getByPlaceholderText("Meal Planning");
    const slugInput = screen.getByPlaceholderText("meal-planning");

    fireEvent.change(nameInput, { target: { value: "Valid Name" } });
    fireEvent.change(slugInput, {
      target: { value: "Invalid Slug With Spaces!" },
    });

    const createButton = screen.getByText("Create Category");
    fireEvent.click(createButton);

    await waitFor(() => {
      expect(
        screen.getByText(
          "Slug can only contain lowercase letters, numbers, and hyphens",
        ),
      ).toBeInTheDocument();
    });
  });

  it("should handle sort order input", () => {
    render(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    const sortOrderInput = screen.getByPlaceholderText("0");
    fireEvent.change(sortOrderInput, { target: { value: "5" } });

    expect(sortOrderInput.value).toBe("5");
  });

  it("should handle color selection", () => {
    render(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    const colorInput = screen.getByPlaceholderText("#6B7280");
    fireEvent.change(colorInput, { target: { value: "#FF0000" } });

    expect(colorInput.value).toBe("#FF0000");
  });

  it("should update preview when form data changes", () => {
    render(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    const nameInput = screen.getByPlaceholderText("Meal Planning");
    const descriptionInput = screen.getByPlaceholderText(
      "Brief description of what this category covers...",
    );

    fireEvent.change(nameInput, { target: { value: "Preview Test Category" } });
    fireEvent.change(descriptionInput, {
      target: { value: "Preview test description" },
    });

    // Check preview section
    expect(screen.getByText("Preview Test Category")).toBeInTheDocument();
    expect(screen.getByText("Preview test description")).toBeInTheDocument();
  });

  it("should call onSuccess and onClose after successful creation", async () => {
    const mockCreateCategory = jest
      .spyOn(knowledgeBaseService.knowledgeBaseService, "createCategory")
      .mockResolvedValue("new-category-id");

    render(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Fill in required fields
    const nameInput = screen.getByPlaceholderText("Meal Planning");
    fireEvent.change(nameInput, { target: { value: "Test Category" } });

    const createButton = screen.getByText("Create Category");
    fireEvent.click(createButton);

    await waitFor(() => {
      expect(mockCreateCategory).toHaveBeenCalledWith(
        expect.objectContaining({
          name: "Test Category",
          slug: "test-category",
        }),
      );
      expect(mockOnSuccess).toHaveBeenCalled();
    });

    mockCreateCategory.mockRestore();
  });

  it("should handle service errors gracefully", async () => {
    const mockCreateCategory = jest
      .spyOn(knowledgeBaseService.knowledgeBaseService, "createCategory")
      .mockRejectedValue(new Error("Service error"));

    render(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    const nameInput = screen.getByPlaceholderText("Meal Planning");
    fireEvent.change(nameInput, { target: { value: "Error Test Category" } });

    const createButton = screen.getByText("Create Category");
    fireEvent.click(createButton);

    await waitFor(() => {
      expect(
        screen.getByText("Failed to save category. Please try again."),
      ).toBeInTheDocument();
    });

    mockCreateCategory.mockRestore();
  });

  it("should show loading state during submission", async () => {
    const mockCreateCategory = jest
      .spyOn(knowledgeBaseService.knowledgeBaseService, "createCategory")
      .mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve("id"), 1000)),
      );

    render(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    const nameInput = screen.getByPlaceholderText("Meal Planning");
    fireEvent.change(nameInput, { target: { value: "Loading Test" } });

    const createButton = screen.getByText("Create Category");
    fireEvent.click(createButton);

    expect(screen.getByText("Saving...")).toBeInTheDocument();
    expect(createButton).toBeDisabled();

    mockCreateCategory.mockRestore();
  });

  it("should reset form when closed", () => {
    const { rerender } = render(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Fill in some data
    const nameInput = screen.getByPlaceholderText("Meal Planning");
    fireEvent.change(nameInput, { target: { value: "Test Name" } });

    // Close and reopen
    rerender(
      <CreateCategoryModal
        open={false}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    rerender(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
      />,
    );

    // Form should be reset
    const nameInputAfterReopen = screen.getByPlaceholderText("Meal Planning");
    expect(nameInputAfterReopen.value).toBe("");
  });

  it("should filter available parent categories correctly", () => {
    // When editing a category, it should not appear in its own parent options
    const editCategory = mockCategories[0]; // parent-category

    render(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
        editCategory={editCategory}
      />,
    );

    // The parent category should not be able to select itself as parent
    // This is tested through the component logic, not directly visible in UI
    expect(screen.getByTestId("dialog")).toBeInTheDocument();
  });

  it("should display edit mode correctly", () => {
    const editCategory = mockCategories[0];

    render(
      <CreateCategoryModal
        open={true}
        onClose={mockOnClose}
        onSuccess={mockOnSuccess}
        categories={mockCategories}
        editCategory={editCategory}
      />,
    );

    expect(screen.getByText("Edit Category")).toBeInTheDocument();
    expect(
      screen.getByText("Update your knowledge base category"),
    ).toBeInTheDocument();
    expect(screen.getByText("Update Category")).toBeInTheDocument();
  });
});

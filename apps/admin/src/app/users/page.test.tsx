import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import UsersPage from "./page";

// Mock the AdminLayout component
jest.mock("../../components/AdminLayout", () => ({
  AdminLayout: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
}));

// Mock next/navigation
jest.mock("next/navigation", () => ({
  usePathname: () => "/users",
}));

describe("UsersPage", () => {
  it("renders the user management page", () => {
    render(<UsersPage />);

    expect(screen.getByText("User Management")).toBeInTheDocument();
    expect(
      screen.getByText("Manage user accounts and permissions"),
    ).toBeInTheDocument();
    expect(screen.getByText("Add New User")).toBeInTheDocument();
  });

  it("displays the user table with sample data", () => {
    render(<UsersPage />);

    // Check that sample users are displayed
    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();

    // Check that status badges are displayed using more specific selectors
    // Get all status badges and check their text content
    const statusBadges = screen.getAllByText(/Active|Inactive|Pending/);
    expect(statusBadges).toHaveLength(4);

    // Check that we have the expected statuses
    const statusTexts = statusBadges.map((badge) => badge.textContent);
    expect(statusTexts.filter((text) => text === "Active")).toHaveLength(2);
    expect(statusTexts.filter((text) => text === "Inactive")).toHaveLength(1);
    expect(statusTexts.filter((text) => text === "Pending")).toHaveLength(1);
  });

  it("opens the add user dialog when button is clicked", () => {
    render(<UsersPage />);

    // Click the "Add New User" button in the header
    const addButton = screen.getByRole("button", { name: "Add New User" });
    fireEvent.click(addButton);

    // Check that dialog is opened by looking for the dialog title with specific role
    expect(
      screen.getByRole("heading", { name: "Add New User", level: 2 }),
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        "Enter the details for the new user account. Click save when you're done.",
      ),
    ).toBeInTheDocument();
  });

  it("allows filling out the add user form", () => {
    render(<UsersPage />);

    // Open the dialog
    fireEvent.click(screen.getByText("Add New User"));

    // Fill out the form
    const nameInput = screen.getByLabelText("Name");
    const emailInput = screen.getByLabelText("Email");

    fireEvent.change(nameInput, {
      target: { value: "New User" },
    });

    fireEvent.change(emailInput, {
      target: { value: "<EMAIL>" },
    });

    // Check that form fields are filled
    expect(nameInput).toHaveValue("New User");
    expect(emailInput).toHaveValue("<EMAIL>");
  });
});

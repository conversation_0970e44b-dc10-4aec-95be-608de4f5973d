"use client";

import React from "react";
import { AdminLayout } from "@/components/AdminLayout";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function DashboardPage() {
  // Mock data - in a real app, this would come from an API
  const metrics = [
    { title: "Total Users", value: "1,234", change: "+12%" },
    { title: "Active Sessions", value: "567", change: "+5%" },
    { title: "System Health", value: "98%", change: "+2%" },
    { title: "Pending Tasks", value: "23", change: "-3%" },
  ];

  const quickActions = [
    { title: "Manage Users", description: "View and edit user accounts" },
    { title: "System Settings", description: "Configure application settings" },
    {
      title: "View Reports",
      description: "Access system analytics and reports",
    },
    { title: "Audit Logs", description: "Review system activity and changes" },
  ];

  const recentActivity = [
    {
      user: "<PERSON>",
      action: "Created new user account",
      time: "2 minutes ago",
    },
    {
      user: "<PERSON> Smith",
      action: "Updated system configuration",
      time: "15 minutes ago",
    },
    {
      user: "<PERSON> Johnson",
      action: "Generated monthly report",
      time: "1 hour ago",
    },
    {
      user: "Alice Brown",
      action: "Resolved support ticket #123",
      time: "3 hours ago",
    },
  ];

  const systemStatus = [
    { service: "Database", status: "Operational", uptime: "99.9%" },
    { service: "API Server", status: "Operational", uptime: "99.95%" },
    { service: "File Storage", status: "Degraded", uptime: "95.2%" },
    { service: "Email Service", status: "Operational", uptime: "99.8%" },
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Welcome to the PlateMotion Admin Dashboard
          </p>
        </div>

        {/* Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {metrics.map((metric, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {metric.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metric.value}</div>
                <p className="text-xs text-green-600">
                  {metric.change} from last month
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Quick Actions */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {quickActions.map((action, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-4 hover:bg-gray-50"
                    >
                      <h3 className="font-medium text-gray-900">
                        {action.title}
                      </h3>
                      <p className="text-sm text-gray-500 mt-1">
                        {action.description}
                      </p>
                      <Button variant="outline" size="sm" className="mt-3">
                        Go to {action.title}
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-start">
                      <div className="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16" />
                      <div className="ml-4">
                        <h4 className="text-sm font-medium text-gray-900">
                          {activity.user}
                        </h4>
                        <p className="text-sm text-gray-500">
                          {activity.action}
                        </p>
                        <p className="text-xs text-gray-400 mt-1">
                          {activity.time}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* System Status */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {systemStatus.map((status, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between"
                  >
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {status.service}
                      </h4>
                      <p className="text-sm text-gray-500">
                        Uptime: {status.uptime}
                      </p>
                    </div>
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${status.status === "Operational" ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}`}
                    >
                      {status.status}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
}

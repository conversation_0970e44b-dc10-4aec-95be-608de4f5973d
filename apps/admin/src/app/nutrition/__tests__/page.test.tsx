import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import NutritionPage from "../page";
import { nutritionService } from "@/services/nutritionService";

// Mock the AdminLayout component
jest.mock("../../../components/AdminLayout", () => ({
  AdminLayout: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="admin-layout">{children}</div>
  ),
}));

// Mock the nutrition service
jest.mock("@/services/nutritionService", () => ({
  nutritionService: {
    getRecipes: jest.fn(),
    getRecipeStats: jest.fn(),
  },
}));

// Mock toast notifications
jest.mock("sonner", () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

const mockRecipes = [
  {
    id: "1",
    name: "Grilled Chicken Salad",
    cuisine_type: "american",
    calories_per_serving: 350,
    prep_time_minutes: 15,
    difficulty_level: "beginner",
    dietary_tags: ["high-protein", "low-carb", "gluten-free"],
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
  },
  {
    id: "2",
    name: "Quinoa Buddha Bowl",
    cuisine_type: "mediterranean",
    calories_per_serving: 420,
    prep_time_minutes: 25,
    difficulty_level: "intermediate",
    dietary_tags: ["vegetarian", "high-fiber"],
    is_active: true,
    created_at: "2024-01-02T00:00:00Z",
  },
  {
    id: "3",
    name: "Greek Yogurt Parfait",
    cuisine_type: "greek",
    calories_per_serving: 280,
    prep_time_minutes: 5,
    difficulty_level: "beginner",
    dietary_tags: ["high-protein", "quick"],
    is_active: false,
    created_at: "2024-01-03T00:00:00Z",
  },
];

const mockStats = {
  total_recipes: 3,
  active_recipes: 2,
  categories: 3,
  avg_calories: 350,
  avg_prep_time: 15,
};

describe("NutritionPage", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (nutritionService.getRecipes as jest.Mock).mockResolvedValue(mockRecipes);
    (nutritionService.getRecipeStats as jest.Mock).mockResolvedValue(mockStats);
  });

  it("renders the nutrition management page", async () => {
    render(<NutritionPage />);

    expect(screen.getByText("Nutrition Management")).toBeInTheDocument();
    expect(
      screen.getByText(
        "Manage recipes and meal plans for the AI to use in creating personalized nutrition plans",
      ),
    ).toBeInTheDocument();
    expect(screen.getByText("Add New Recipe")).toBeInTheDocument();
  });

  it("displays loading state initially", () => {
    render(<NutritionPage />);

    expect(screen.getAllByText("Loading...")).toHaveLength(4); // 4 stat cards
    expect(screen.getByText("Loading data...")).toBeInTheDocument();
    expect(screen.getByText("Loading recipes...")).toBeInTheDocument();
  });

  it("loads and displays recipe data", async () => {
    render(<NutritionPage />);

    await waitFor(() => {
      expect(screen.getByText("Grilled Chicken Salad")).toBeInTheDocument();
      expect(screen.getByText("Quinoa Buddha Bowl")).toBeInTheDocument();
      expect(screen.getByText("Greek Yogurt Parfait")).toBeInTheDocument();
    });

    expect(nutritionService.getRecipes).toHaveBeenCalledWith({
      is_active: true,
    });
    expect(nutritionService.getRecipeStats).toHaveBeenCalled();
  });

  it("displays recipe statistics correctly", async () => {
    render(<NutritionPage />);

    await waitFor(() => {
      // Use more specific selectors to avoid conflicts
      const totalRecipesCard = screen
        .getByText("Total Recipes")
        .closest(".rounded-lg");
      const activeRecipesCard = screen
        .getByText("Active Recipes")
        .closest(".rounded-lg");
      const avgCaloriesCard = screen
        .getByText("Avg Calories")
        .closest(".rounded-lg");

      expect(totalRecipesCard).toHaveTextContent("3");
      expect(activeRecipesCard).toHaveTextContent("2");
      expect(avgCaloriesCard).toHaveTextContent("350");
    });
  });

  it("displays recipe table with correct data", async () => {
    render(<NutritionPage />);

    await waitFor(() => {
      // Check recipe names
      expect(screen.getByText("Grilled Chicken Salad")).toBeInTheDocument();
      expect(screen.getByText("Quinoa Buddha Bowl")).toBeInTheDocument();

      // Check cuisine types
      expect(screen.getByText("american")).toBeInTheDocument();
      expect(screen.getByText("mediterranean")).toBeInTheDocument();

      // Check prep times
      expect(screen.getByText("15 min")).toBeInTheDocument();
      expect(screen.getByText("25 min")).toBeInTheDocument();

      // Check difficulty levels (capitalized in the component)
      expect(screen.getByText("Beginner")).toBeInTheDocument();
      expect(screen.getByText("Intermediate")).toBeInTheDocument();

      // Check dietary tags
      expect(screen.getByText("high-protein")).toBeInTheDocument();
      expect(screen.getByText("vegetarian")).toBeInTheDocument();

      // Check status badges - be more specific about Active vs Inactive
      const tableBody = screen.getByRole("table").querySelector("tbody");
      expect(tableBody).toHaveTextContent("Active");
      expect(tableBody).toHaveTextContent("Inactive");
    });
  });

  it("filters recipes based on search input", async () => {
    render(<NutritionPage />);

    await waitFor(() => {
      expect(screen.getByText("Grilled Chicken Salad")).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText("Search recipes...");
    fireEvent.change(searchInput, { target: { value: "chicken" } });

    // Should show only the chicken salad
    expect(screen.getByText("Grilled Chicken Salad")).toBeInTheDocument();
    expect(screen.queryByText("Quinoa Buddha Bowl")).not.toBeInTheDocument();
  });

  it("filters recipes based on cuisine type search", async () => {
    render(<NutritionPage />);

    await waitFor(() => {
      expect(screen.getByText("Grilled Chicken Salad")).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText("Search recipes...");
    fireEvent.change(searchInput, { target: { value: "mediterranean" } });

    // Should show only the mediterranean recipe
    expect(screen.getByText("Quinoa Buddha Bowl")).toBeInTheDocument();
    expect(screen.queryByText("Grilled Chicken Salad")).not.toBeInTheDocument();
  });

  it("opens add recipe dialog when button is clicked", async () => {
    render(<NutritionPage />);

    await waitFor(() => {
      expect(screen.getByText("Add New Recipe")).toBeInTheDocument();
    });

    const addButton = screen.getByRole("button", { name: "Add New Recipe" });
    fireEvent.click(addButton);

    // Look for form labels specifically in the dialog
    await waitFor(() => {
      expect(screen.getByLabelText("Recipe Name")).toBeInTheDocument();
      expect(screen.getByLabelText("Category")).toBeInTheDocument();
      expect(screen.getByLabelText("Ingredients")).toBeInTheDocument();
      expect(screen.getByLabelText("Instructions")).toBeInTheDocument();
    });
  });

  it("displays empty state when no recipes match search", async () => {
    render(<NutritionPage />);

    await waitFor(() => {
      expect(screen.getByText("Grilled Chicken Salad")).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText("Search recipes...");
    fireEvent.change(searchInput, { target: { value: "nonexistent" } });

    expect(
      screen.getByText("No recipes found matching your search."),
    ).toBeInTheDocument();
  });

  it("displays empty state when no recipes available", async () => {
    (nutritionService.getRecipes as jest.Mock).mockResolvedValue([]);
    (nutritionService.getRecipeStats as jest.Mock).mockResolvedValue({
      total_recipes: 0,
      active_recipes: 0,
      categories: 0,
      avg_calories: 0,
      avg_prep_time: 0,
    });

    render(<NutritionPage />);

    await waitFor(() => {
      expect(screen.getByText("No recipes available.")).toBeInTheDocument();
    });
  });

  it("handles service errors gracefully", async () => {
    const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
    (nutritionService.getRecipes as jest.Mock).mockRejectedValue(
      new Error("Service error"),
    );
    (nutritionService.getRecipeStats as jest.Mock).mockRejectedValue(
      new Error("Service error"),
    );

    render(<NutritionPage />);

    await waitFor(() => {
      expect(
        screen.getByText("Failed to load nutrition data. Please try again."),
      ).toBeInTheDocument();
      expect(screen.getByRole("button", { name: "Retry" })).toBeInTheDocument();
    });

    consoleErrorSpy.mockRestore();
  });

  it("retries loading data when retry button is clicked", async () => {
    const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
    (nutritionService.getRecipes as jest.Mock)
      .mockRejectedValueOnce(new Error("Service error"))
      .mockResolvedValue(mockRecipes);
    (nutritionService.getRecipeStats as jest.Mock)
      .mockRejectedValueOnce(new Error("Service error"))
      .mockResolvedValue(mockStats);

    render(<NutritionPage />);

    await waitFor(() => {
      expect(
        screen.getByText("Failed to load nutrition data. Please try again."),
      ).toBeInTheDocument();
    });

    const retryButton = screen.getByRole("button", { name: "Retry" });
    fireEvent.click(retryButton);

    await waitFor(() => {
      expect(screen.getByText("Grilled Chicken Salad")).toBeInTheDocument();
    });

    expect(nutritionService.getRecipes).toHaveBeenCalledTimes(2);
    expect(nutritionService.getRecipeStats).toHaveBeenCalledTimes(2);

    consoleErrorSpy.mockRestore();
  });

  it("displays dietary tags with proper truncation", async () => {
    const recipeWithManyTags = {
      ...mockRecipes[0],
      dietary_tags: ["tag1", "tag2", "tag3", "tag4", "tag5"],
    };

    (nutritionService.getRecipes as jest.Mock).mockResolvedValue([
      recipeWithManyTags,
    ]);

    render(<NutritionPage />);

    await waitFor(() => {
      expect(screen.getByText("tag1")).toBeInTheDocument();
      expect(screen.getByText("tag2")).toBeInTheDocument();
      expect(screen.getByText("+3")).toBeInTheDocument(); // Shows +3 for remaining tags
    });
  });

  it("handles recipes with missing data gracefully", async () => {
    const incompleteRecipe = {
      id: "4",
      name: "Incomplete Recipe",
      // Missing most optional fields
    };

    (nutritionService.getRecipes as jest.Mock).mockResolvedValue([
      incompleteRecipe,
    ]);

    render(<NutritionPage />);

    await waitFor(() => {
      expect(screen.getByText("Incomplete Recipe")).toBeInTheDocument();
      expect(screen.getAllByText("N/A")).toHaveLength(4); // cuisine, calories, prep time, difficulty
      expect(screen.getByText("No tags")).toBeInTheDocument();
    });
  });

  it("displays correct action buttons for each recipe", async () => {
    render(<NutritionPage />);

    await waitFor(() => {
      const editButtons = screen.getAllByText("Edit");
      const viewButtons = screen.getAllByText("View");

      expect(editButtons).toHaveLength(3); // One for each recipe
      expect(viewButtons).toHaveLength(3); // One for each recipe
    });
  });
});

"use client";

// Force dynamic rendering to avoid build-time Supabase dependency
export const dynamic = "force-dynamic";

import React, { useState, useEffect } from "react";
import { AdminLayout } from "@/components/AdminLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import {
  nutritionService,
  Recipe,
  RecipeStats,
} from "@/services/nutritionService";
import { toast } from "sonner";
import Image from "next/image";

// Form data interface for new recipe
interface RecipeFormData {
  name: string;
  description: string;
  cuisine_type: string;
  calories_per_serving: number | "";
  protein_grams: number | "";
  carbs_grams: number | "";
  fat_grams: number | "";
  fiber_grams: number | "";
  prep_time_minutes: number | "";
  cook_time_minutes: number | "";
  difficulty_level: "beginner" | "intermediate" | "advanced" | "";
  servings: number | "";
  ingredients: string;
  instructions: string;
  dietary_tags: string;
}

// Form errors interface
interface RecipeFormErrors {
  name?: string;
  description?: string;
  cuisine_type?: string;
  calories_per_serving?: string;
  protein_grams?: string;
  carbs_grams?: string;
  fat_grams?: string;
  fiber_grams?: string;
  prep_time_minutes?: string;
  cook_time_minutes?: string;
  difficulty_level?: string;
  servings?: string;
  ingredients?: string;
  instructions?: string;
  dietary_tags?: string;
}

export default function NutritionPage() {
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [stats, setStats] = useState<RecipeStats>({
    total_recipes: 0,
    active_recipes: 0,
    categories: 0,
    avg_calories: 0,
    avg_prep_time: 0,
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<RecipeFormData>({
    name: "",
    description: "",
    cuisine_type: "",
    calories_per_serving: "",
    protein_grams: "",
    carbs_grams: "",
    fat_grams: "",
    fiber_grams: "",
    prep_time_minutes: "",
    cook_time_minutes: "",
    difficulty_level: "",
    servings: "",
    ingredients: "",
    instructions: "",
    dietary_tags: "",
  });
  const [formErrors, setFormErrors] = useState<RecipeFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageError, setImageError] = useState<string | null>(null);

  // Load recipes and stats on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [recipesData, statsData] = await Promise.all([
        nutritionService.getRecipes({ is_active: true }),
        nutritionService.getRecipeStats(),
      ]);

      setRecipes(recipesData);
      setStats(statsData);
    } catch (err) {
      console.error("Error loading nutrition data:", err);
      setError("Failed to load nutrition data. Please try again.");
      toast.error("Failed to load recipes");
    } finally {
      setLoading(false);
    }
  };

  // Form validation
  const validateForm = (): boolean => {
    const errors: RecipeFormErrors = {};

    if (!formData.name.trim()) {
      errors.name = "Recipe name is required";
    }

    if (!formData.cuisine_type.trim()) {
      errors.cuisine_type = "Category is required";
    }

    if (!formData.ingredients.trim()) {
      errors.ingredients = "Ingredients are required";
    }

    if (!formData.instructions.trim()) {
      errors.instructions = "Instructions are required";
    }

    // Validate numeric fields
    if (
      formData.calories_per_serving !== "" &&
      (isNaN(Number(formData.calories_per_serving)) ||
        Number(formData.calories_per_serving) < 0)
    ) {
      errors.calories_per_serving = "Calories must be a positive number";
    }

    if (
      formData.protein_grams !== "" &&
      (isNaN(Number(formData.protein_grams)) ||
        Number(formData.protein_grams) < 0)
    ) {
      errors.protein_grams = "Protein must be a positive number";
    }

    if (
      formData.carbs_grams !== "" &&
      (isNaN(Number(formData.carbs_grams)) || Number(formData.carbs_grams) < 0)
    ) {
      errors.carbs_grams = "Carbohydrates must be a positive number";
    }

    if (
      formData.fat_grams !== "" &&
      (isNaN(Number(formData.fat_grams)) || Number(formData.fat_grams) < 0)
    ) {
      errors.fat_grams = "Fat must be a positive number";
    }

    if (
      formData.fiber_grams !== "" &&
      (isNaN(Number(formData.fiber_grams)) || Number(formData.fiber_grams) < 0)
    ) {
      errors.fiber_grams = "Fiber must be a positive number";
    }

    if (
      formData.prep_time_minutes !== "" &&
      (isNaN(Number(formData.prep_time_minutes)) ||
        Number(formData.prep_time_minutes) < 0)
    ) {
      errors.prep_time_minutes = "Prep time must be a positive number";
    }

    if (
      formData.cook_time_minutes !== "" &&
      (isNaN(Number(formData.cook_time_minutes)) ||
        Number(formData.cook_time_minutes) < 0)
    ) {
      errors.cook_time_minutes = "Cook time must be a positive number";
    }

    if (
      formData.servings !== "" &&
      (isNaN(Number(formData.servings)) || Number(formData.servings) < 1)
    ) {
      errors.servings = "Servings must be at least 1";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle image selection
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    setImageError(null);

    if (!file) {
      setSelectedImage(null);
      setImagePreview(null);
      return;
    }

    // Validate file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      setImageError("Please select a valid image file (JPG, PNG, or WebP)");
      return;
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      setImageError("Image size must be less than 5MB");
      return;
    }

    setSelectedImage(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Remove selected image
  const removeImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    setImageError(null);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      toast.error("Please fix the form errors");
      return;
    }

    setIsSubmitting(true);
    try {
      // Convert ingredients string to JSONB format (Record<string, unknown>)
      const ingredientsLines = formData.ingredients
        .trim()
        .split("\n")
        .filter((line) => line.trim());
      const ingredientsObject: Record<string, unknown> =
        ingredientsLines.length > 0
          ? {
              ingredients: ingredientsLines.map((line, index) => ({
                step: index + 1,
                ingredient: line.trim(),
              })),
            }
          : {};

      // Convert instructions string to JSONB format (Record<string, unknown>)
      const instructionsLines = formData.instructions
        .trim()
        .split("\n")
        .filter((line) => line.trim());
      const instructionsObject: Record<string, unknown> =
        instructionsLines.length > 0
          ? {
              instructions: instructionsLines.map((line, index) => ({
                step: index + 1,
                instruction: line.trim(),
              })),
            }
          : {};

      const recipeData: Omit<
        Recipe,
        "id" | "created_at" | "updated_at" | "image_url"
      > = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        cuisine_type: formData.cuisine_type.trim(),
        calories_per_serving:
          formData.calories_per_serving !== ""
            ? Number(formData.calories_per_serving)
            : undefined,
        protein_grams:
          formData.protein_grams !== ""
            ? Number(formData.protein_grams)
            : undefined,
        carbs_grams:
          formData.carbs_grams !== ""
            ? Number(formData.carbs_grams)
            : undefined,
        fat_grams:
          formData.fat_grams !== "" ? Number(formData.fat_grams) : undefined,
        fiber_grams:
          formData.fiber_grams !== ""
            ? Number(formData.fiber_grams)
            : undefined,
        prep_time_minutes:
          formData.prep_time_minutes !== ""
            ? Number(formData.prep_time_minutes)
            : undefined,
        cook_time_minutes:
          formData.cook_time_minutes !== ""
            ? Number(formData.cook_time_minutes)
            : undefined,
        difficulty_level: formData.difficulty_level || undefined,
        servings:
          formData.servings !== "" ? Number(formData.servings) : undefined,
        ingredients:
          Object.keys(ingredientsObject).length > 0
            ? ingredientsObject
            : undefined,
        instructions:
          Object.keys(instructionsObject).length > 0
            ? instructionsObject
            : undefined,
        dietary_tags: formData.dietary_tags.trim()
          ? formData.dietary_tags.split(",").map((tag) => tag.trim())
          : undefined,
        is_active: true,
      };

      // Use createRecipeWithImage if image is selected
      if (selectedImage) {
        await nutritionService.createRecipeWithImage(recipeData, selectedImage);
      } else {
        await nutritionService.createRecipe(recipeData);
      }

      toast.success("Recipe created successfully!");
      setIsAddDialogOpen(false);
      resetForm();
      loadData(); // Refresh the data
    } catch (err) {
      console.error("Error creating recipe:", err);
      toast.error("Failed to create recipe. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      cuisine_type: "",
      calories_per_serving: "",
      protein_grams: "",
      carbs_grams: "",
      fat_grams: "",
      fiber_grams: "",
      prep_time_minutes: "",
      cook_time_minutes: "",
      difficulty_level: "",
      servings: "",
      ingredients: "",
      instructions: "",
      dietary_tags: "",
    });
    setFormErrors({});
    setSelectedImage(null);
    setImagePreview(null);
    setImageError(null);
  };

  // Handle form field changes
  const handleFieldChange = (
    field: keyof RecipeFormData,
    value: string | number,
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error for this field when user starts typing
    if (formErrors[field as keyof RecipeFormErrors]) {
      setFormErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const filteredRecipes = recipes.filter(
    (recipe) =>
      recipe.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (recipe.cuisine_type &&
        recipe.cuisine_type.toLowerCase().includes(searchTerm.toLowerCase())),
  );

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Nutrition Management
            </h1>
            <p className="text-muted-foreground">
              Manage recipes and meal plans for the AI to use in creating
              personalized nutrition plans
            </p>
          </div>
          <Dialog
            open={isAddDialogOpen}
            onOpenChange={(open) => {
              setIsAddDialogOpen(open);
              if (!open) resetForm();
            }}
          >
            <DialogTrigger asChild>
              <Button>Add New Recipe</Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Add New Recipe</DialogTitle>
              </DialogHeader>
              <div className="space-y-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Basic Information</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">
                        Recipe Name *
                      </label>
                      <Input
                        placeholder="Enter recipe name"
                        value={formData.name}
                        onChange={(e) =>
                          handleFieldChange("name", e.target.value)
                        }
                        className={formErrors.name ? "border-red-500" : ""}
                      />
                      {formErrors.name && (
                        <p className="text-red-500 text-xs mt-1">
                          {formErrors.name}
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="text-sm font-medium">Category *</label>
                      <Input
                        placeholder="e.g., Main Course, Breakfast"
                        value={formData.cuisine_type}
                        onChange={(e) =>
                          handleFieldChange("cuisine_type", e.target.value)
                        }
                        className={
                          formErrors.cuisine_type ? "border-red-500" : ""
                        }
                      />
                      {formErrors.cuisine_type && (
                        <p className="text-red-500 text-xs mt-1">
                          {formErrors.cuisine_type}
                        </p>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Description</label>
                    <Textarea
                      placeholder="Brief description of the recipe..."
                      rows={2}
                      value={formData.description}
                      onChange={(e) =>
                        handleFieldChange("description", e.target.value)
                      }
                    />
                  </div>
                </div>

                {/* Nutritional Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">
                    Nutritional Information (per serving)
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium">Calories</label>
                        <Input
                          type="number"
                          placeholder="350"
                          step="1"
                          min="0"
                          value={formData.calories_per_serving}
                          onChange={(e) =>
                            handleFieldChange(
                              "calories_per_serving",
                              e.target.value,
                            )
                          }
                          className={
                            formErrors.calories_per_serving
                              ? "border-red-500"
                              : ""
                          }
                        />
                        {formErrors.calories_per_serving && (
                          <p className="text-red-500 text-xs mt-1">
                            {formErrors.calories_per_serving}
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium">Servings</label>
                        <Input
                          type="number"
                          placeholder="4"
                          step="1"
                          min="1"
                          value={formData.servings}
                          onChange={(e) =>
                            handleFieldChange("servings", e.target.value)
                          }
                          className={
                            formErrors.servings ? "border-red-500" : ""
                          }
                        />
                        {formErrors.servings && (
                          <p className="text-red-500 text-xs mt-1">
                            {formErrors.servings}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium">
                          Protein (g)
                        </label>
                        <Input
                          type="number"
                          placeholder="25.5"
                          step="0.1"
                          min="0"
                          value={formData.protein_grams}
                          onChange={(e) =>
                            handleFieldChange("protein_grams", e.target.value)
                          }
                          className={
                            formErrors.protein_grams ? "border-red-500" : ""
                          }
                        />
                        {formErrors.protein_grams && (
                          <p className="text-red-500 text-xs mt-1">
                            {formErrors.protein_grams}
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium">Carbs (g)</label>
                        <Input
                          type="number"
                          placeholder="40.2"
                          step="0.1"
                          min="0"
                          value={formData.carbs_grams}
                          onChange={(e) =>
                            handleFieldChange("carbs_grams", e.target.value)
                          }
                          className={
                            formErrors.carbs_grams ? "border-red-500" : ""
                          }
                        />
                        {formErrors.carbs_grams && (
                          <p className="text-red-500 text-xs mt-1">
                            {formErrors.carbs_grams}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Fat (g)</label>
                      <Input
                        type="number"
                        placeholder="12.8"
                        step="0.1"
                        min="0"
                        value={formData.fat_grams}
                        onChange={(e) =>
                          handleFieldChange("fat_grams", e.target.value)
                        }
                        className={formErrors.fat_grams ? "border-red-500" : ""}
                      />
                      {formErrors.fat_grams && (
                        <p className="text-red-500 text-xs mt-1">
                          {formErrors.fat_grams}
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="text-sm font-medium">
                        Fiber (g){" "}
                        <span className="text-gray-500">(optional)</span>
                      </label>
                      <Input
                        type="number"
                        placeholder="5.2"
                        step="0.1"
                        min="0"
                        value={formData.fiber_grams}
                        onChange={(e) =>
                          handleFieldChange("fiber_grams", e.target.value)
                        }
                        className={
                          formErrors.fiber_grams ? "border-red-500" : ""
                        }
                      />
                      {formErrors.fiber_grams && (
                        <p className="text-red-500 text-xs mt-1">
                          {formErrors.fiber_grams}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Cooking Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Cooking Information</h3>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="text-sm font-medium">
                        Prep Time (min)
                      </label>
                      <Input
                        type="number"
                        placeholder="15"
                        step="1"
                        min="0"
                        value={formData.prep_time_minutes}
                        onChange={(e) =>
                          handleFieldChange("prep_time_minutes", e.target.value)
                        }
                        className={
                          formErrors.prep_time_minutes ? "border-red-500" : ""
                        }
                      />
                      {formErrors.prep_time_minutes && (
                        <p className="text-red-500 text-xs mt-1">
                          {formErrors.prep_time_minutes}
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="text-sm font-medium">
                        Cook Time (min)
                      </label>
                      <Input
                        type="number"
                        placeholder="30"
                        step="1"
                        min="0"
                        value={formData.cook_time_minutes}
                        onChange={(e) =>
                          handleFieldChange("cook_time_minutes", e.target.value)
                        }
                        className={
                          formErrors.cook_time_minutes ? "border-red-500" : ""
                        }
                      />
                      {formErrors.cook_time_minutes && (
                        <p className="text-red-500 text-xs mt-1">
                          {formErrors.cook_time_minutes}
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="text-sm font-medium">Difficulty</label>
                      <select
                        className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                        value={formData.difficulty_level}
                        onChange={(e) =>
                          handleFieldChange("difficulty_level", e.target.value)
                        }
                      >
                        <option value="">Select difficulty</option>
                        <option value="beginner">Beginner</option>
                        <option value="intermediate">Intermediate</option>
                        <option value="advanced">Advanced</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Recipe Image */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Recipe Image</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">
                        Upload Image
                      </label>
                      <div className="mt-2">
                        <input
                          type="file"
                          accept="image/jpeg,image/jpg,image/png,image/webp"
                          onChange={handleImageSelect}
                          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Supported formats: JPG, PNG, WebP. Max size: 5MB
                        </p>
                      </div>
                      {imageError && (
                        <p className="text-red-500 text-xs mt-1">
                          {imageError}
                        </p>
                      )}
                    </div>

                    {/* Image Preview */}
                    {imagePreview && (
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Preview</label>
                        <div className="relative inline-block">
                          <Image
                            src={imagePreview}
                            alt="Recipe preview"
                            width={128}
                            height={128}
                            className="w-32 h-32 object-cover rounded-lg border"
                          />
                          <button
                            type="button"
                            onClick={removeImage}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Recipe Content */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Recipe Content</h3>
                  <div>
                    <label className="text-sm font-medium">Ingredients *</label>
                    <Textarea
                      placeholder="List ingredients with quantities..."
                      rows={4}
                      value={formData.ingredients}
                      onChange={(e) =>
                        handleFieldChange("ingredients", e.target.value)
                      }
                      className={formErrors.ingredients ? "border-red-500" : ""}
                    />
                    {formErrors.ingredients && (
                      <p className="text-red-500 text-xs mt-1">
                        {formErrors.ingredients}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium">
                      Instructions *
                    </label>
                    <Textarea
                      placeholder="Step-by-step cooking instructions..."
                      rows={6}
                      value={formData.instructions}
                      onChange={(e) =>
                        handleFieldChange("instructions", e.target.value)
                      }
                      className={
                        formErrors.instructions ? "border-red-500" : ""
                      }
                    />
                    {formErrors.instructions && (
                      <p className="text-red-500 text-xs mt-1">
                        {formErrors.instructions}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium">Tags</label>
                    <Input
                      placeholder="High Protein, Low Carb, Vegetarian (comma separated)"
                      value={formData.dietary_tags}
                      onChange={(e) =>
                        handleFieldChange("dietary_tags", e.target.value)
                      }
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-2 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => setIsAddDialogOpen(false)}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleSubmit} disabled={isSubmitting}>
                    {isSubmitting ? "Saving..." : "Save Recipe"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        {loading ? (
          <div className="grid gap-4 md:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Loading...
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">--</div>
                  <p className="text-xs text-muted-foreground">
                    Loading data...
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : error ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-red-600">
                <p>{error}</p>
                <Button onClick={loadData} className="mt-2">
                  Retry
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Recipes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total_recipes}</div>
                <p className="text-xs text-muted-foreground">
                  Available for AI meal planning
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Active Recipes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.active_recipes}</div>
                <p className="text-xs text-muted-foreground">
                  Currently in use
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Categories
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.categories}</div>
                <p className="text-xs text-muted-foreground">
                  Different cuisine types
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Avg Calories
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.avg_calories}</div>
                <p className="text-xs text-muted-foreground">Per recipe</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Recipe Database</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 mb-4">
              <Input
                placeholder="Search recipes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
              <Button variant="outline">Filter</Button>
            </div>

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Recipe</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Nutrition</TableHead>
                  <TableHead>Macros (P/C/F)</TableHead>
                  <TableHead>Prep Time</TableHead>
                  <TableHead>Difficulty</TableHead>
                  <TableHead>Tags</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      Loading recipes...
                    </TableCell>
                  </TableRow>
                ) : filteredRecipes.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={9} className="text-center py-8">
                      {searchTerm
                        ? "No recipes found matching your search."
                        : "No recipes available."}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRecipes.map((recipe) => (
                    <TableRow key={recipe.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-3">
                          {recipe.image_url ? (
                            <Image
                              src={recipe.image_url}
                              alt={recipe.name}
                              width={48}
                              height={48}
                              className="w-12 h-12 object-cover rounded-lg border recipe-image"
                            />
                          ) : (
                            <div className="w-12 h-12 bg-gray-100 rounded-lg border flex items-center justify-center">
                              <span className="text-gray-400 text-xs">
                                No Image
                              </span>
                            </div>
                          )}
                          <div>
                            <div className="font-medium">{recipe.name}</div>
                            {recipe.description && (
                              <div className="text-xs text-muted-foreground line-clamp-1">
                                {recipe.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{recipe.cuisine_type || "N/A"}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="font-medium">
                            {recipe.calories_per_serving
                              ? `${recipe.calories_per_serving} cal`
                              : "N/A"}
                          </div>
                          {recipe.servings && (
                            <div className="text-xs text-muted-foreground">
                              {recipe.servings} serving
                              {recipe.servings !== 1 ? "s" : ""}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {recipe.protein_grams ||
                          recipe.carbs_grams ||
                          recipe.fat_grams ? (
                            <div className="space-y-1">
                              <div className="text-xs text-muted-foreground">
                                P:{" "}
                                {recipe.protein_grams
                                  ? `${recipe.protein_grams}g`
                                  : "-"}{" "}
                                | C:{" "}
                                {recipe.carbs_grams
                                  ? `${recipe.carbs_grams}g`
                                  : "-"}{" "}
                                | F:{" "}
                                {recipe.fat_grams
                                  ? `${recipe.fat_grams}g`
                                  : "-"}
                              </div>
                              {recipe.fiber_grams && (
                                <div className="text-xs text-muted-foreground">
                                  Fiber: {recipe.fiber_grams}g
                                </div>
                              )}
                            </div>
                          ) : (
                            <span className="text-muted-foreground">N/A</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {recipe.prep_time_minutes && (
                            <div>Prep: {recipe.prep_time_minutes}m</div>
                          )}
                          {recipe.cook_time_minutes && (
                            <div className="text-xs text-muted-foreground">
                              Cook: {recipe.cook_time_minutes}m
                            </div>
                          )}
                          {!recipe.prep_time_minutes &&
                            !recipe.cook_time_minutes &&
                            "N/A"}
                        </div>
                      </TableCell>
                      <TableCell className="capitalize">
                        {recipe.difficulty_level || "N/A"}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {recipe.dietary_tags &&
                          recipe.dietary_tags.length > 0 ? (
                            <>
                              {recipe.dietary_tags.slice(0, 2).map((tag) => (
                                <Badge
                                  key={tag}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {tag}
                                </Badge>
                              ))}
                              {recipe.dietary_tags.length > 2 && (
                                <Badge variant="outline" className="text-xs">
                                  +{recipe.dietary_tags.length - 2}
                                </Badge>
                              )}
                            </>
                          ) : (
                            <span className="text-xs text-muted-foreground">
                              No tags
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={recipe.is_active ? "default" : "secondary"}
                        >
                          {recipe.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            View
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

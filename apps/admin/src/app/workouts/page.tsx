"use client";

import React, { useState } from "react";
import { AdminLayout } from "@/components/AdminLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";

// Mock data for workouts
const mockWorkouts = [
  {
    id: 1,
    name: "Full Body Strength",
    category: "Strength Training",
    duration: "45 min",
    difficulty: "Intermediate",
    equipment: ["Dumbbells", "Bench"],
    targetMuscles: ["Chest", "Back", "Legs", "Arms"],
    status: "Active",
  },
  {
    id: 2,
    name: "HIIT Cardio Blast",
    category: "Cardio",
    duration: "30 min",
    difficulty: "Advanced",
    equipment: ["None"],
    targetMuscles: ["Full Body"],
    status: "Active",
  },
  {
    id: 3,
    name: "Beginner Yoga Flow",
    category: "Flexibility",
    duration: "20 min",
    difficulty: "Beginner",
    equipment: ["Yoga Mat"],
    targetMuscles: ["Core", "Flexibility"],
    status: "Draft",
  },
  {
    id: 4,
    name: "Push Day Upper Body",
    category: "Strength Training",
    duration: "60 min",
    difficulty: "Advanced",
    equipment: ["Barbell", "Dumbbells"],
    targetMuscles: ["Chest", "Shoulders", "Triceps"],
    status: "Active",
  },
];

export default function WorkoutsPage() {
  const [workouts] = useState(mockWorkouts);
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const filteredWorkouts = workouts.filter(
    (workout) =>
      workout.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      workout.category.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Workout Management
            </h1>
            <p className="text-muted-foreground">
              Manage workout routines and exercises for the AI to use in
              creating personalized fitness plans
            </p>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>Add New Workout</Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add New Workout</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Workout Name</label>
                    <Input placeholder="Enter workout name" />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Category</label>
                    <Input placeholder="e.g., Strength, Cardio, Flexibility" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Duration</label>
                    <Input placeholder="45 min" />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Difficulty</label>
                    <Input placeholder="Beginner, Intermediate, Advanced" />
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">
                    Equipment Required
                  </label>
                  <Input placeholder="Dumbbells, Bench, None (comma separated)" />
                </div>
                <div>
                  <label className="text-sm font-medium">
                    Target Muscle Groups
                  </label>
                  <Input placeholder="Chest, Back, Legs, Arms (comma separated)" />
                </div>
                <div>
                  <label className="text-sm font-medium">Exercise List</label>
                  <Textarea
                    placeholder="List exercises with sets, reps, and rest periods..."
                    rows={6}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Instructions</label>
                  <Textarea
                    placeholder="Workout instructions and tips..."
                    rows={4}
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setIsAddDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={() => setIsAddDialogOpen(false)}>
                    Save Workout
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Workouts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{workouts.length}</div>
              <p className="text-xs text-muted-foreground">
                Available for AI workout planning
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Workouts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {workouts.filter((w) => w.status === "Active").length}
              </div>
              <p className="text-xs text-muted-foreground">Currently in use</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Categories</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {new Set(workouts.map((w) => w.category)).size}
              </div>
              <p className="text-xs text-muted-foreground">
                Different workout types
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Avg Duration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(
                  workouts.reduce((sum, w) => sum + parseInt(w.duration), 0) /
                    workouts.length,
                )}{" "}
                min
              </div>
              <p className="text-xs text-muted-foreground">Per workout</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Workout Database</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 mb-4">
              <Input
                placeholder="Search workouts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
              <Button variant="outline">Filter</Button>
            </div>

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Workout Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Difficulty</TableHead>
                  <TableHead>Equipment</TableHead>
                  <TableHead>Target Muscles</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredWorkouts.map((workout) => (
                  <TableRow key={workout.id}>
                    <TableCell className="font-medium">
                      {workout.name}
                    </TableCell>
                    <TableCell>{workout.category}</TableCell>
                    <TableCell>{workout.duration}</TableCell>
                    <TableCell>{workout.difficulty}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {workout.equipment.slice(0, 2).map((item) => (
                          <Badge
                            key={item}
                            variant="outline"
                            className="text-xs"
                          >
                            {item}
                          </Badge>
                        ))}
                        {workout.equipment.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{workout.equipment.length - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {workout.targetMuscles.slice(0, 2).map((muscle) => (
                          <Badge
                            key={muscle}
                            variant="secondary"
                            className="text-xs"
                          >
                            {muscle}
                          </Badge>
                        ))}
                        {workout.targetMuscles.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{workout.targetMuscles.length - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          workout.status === "Active" ? "default" : "secondary"
                        }
                      >
                        {workout.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                        <Button variant="outline" size="sm">
                          View
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

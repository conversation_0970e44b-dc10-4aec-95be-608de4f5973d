import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Toaster } from "sonner";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "PlateMotion Admin Panel",
  description:
    "Comprehensive administration dashboard for PlateMotion platform",
  keywords: ["admin", "dashboard", "platemotion", "management"],
  authors: [{ name: "PlateMotion Team" }],
  robots: "noindex, nofollow", // Prevent search engine indexing
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <div className="min-h-screen bg-background">{children}</div>
        <Toaster />
      </body>
    </html>
  );
}

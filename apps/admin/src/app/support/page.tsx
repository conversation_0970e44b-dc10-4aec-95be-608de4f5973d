"use client";

import React, { useState, useEffect } from "react";
import { AdminLayout } from "@/components/AdminLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Plus,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  MessageSquare,
  RefreshCw,
} from "lucide-react";

// Import our new components and services
import { TicketCard } from "@/components/support/TicketCard";

import {
  TicketFilters,
  TicketFilterState,
} from "@/components/support/TicketFilters";
import { TicketDetailModal } from "@/components/support/TicketDetailModal";
import {
  supportService,
  SupportTicket,
  TicketStats,
} from "@/services/supportService";

export default function SupportPage() {
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [stats, setStats] = useState<TicketStats>({
    total_tickets: 0,
    open_tickets: 0,
    in_progress_tickets: 0,
    waiting_user_tickets: 0,
    resolved_tickets: 0,
    closed_tickets: 0,
    resolved_today: 0,
    avg_response_time: 0,
    sla_breaches: 0,
    tickets_by_priority: { urgent: 0, high: 0, medium: 0, low: 0 },
    tickets_by_category: {},
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<SupportTicket | null>(
    null,
  );
  const [activeTab, setActiveTab] = useState("all");

  // Filter state
  const [filters, setFilters] = useState<TicketFilterState>({
    search: "",
    status: "all",
    priority: "all",
    category: "all",
    assigned_to: "all",
    sla_breach: null,
    date_from: null,
    date_to: null,
  });

  // Load tickets and stats
  const loadData = async () => {
    try {
      setLoading(true);
      // Map UI filters to service API filters
      const apiFilters = {
        search: filters.search || undefined,
        status: filters.status !== "all" ? filters.status : undefined,
        priority: filters.priority !== "all" ? filters.priority : undefined,
        category: filters.category !== "all" ? filters.category : undefined,
        assigned_to:
          filters.assigned_to !== "all" ? filters.assigned_to : undefined,
        sla_breach:
          filters.sla_breach === null ? undefined : filters.sla_breach,
        date_from: filters.date_from
          ? filters.date_from.toISOString()
          : undefined,
        date_to: filters.date_to ? filters.date_to.toISOString() : undefined,
      } satisfies import("@/services/supportService").TicketFilters;

      const [ticketsData, statsData] = await Promise.all([
        supportService.getTickets(apiFilters),
        supportService.getTicketStats(),
      ]);

      setTickets(ticketsData);
      setStats(statsData);
    } catch (error) {
      console.error("Error loading support data:", error);
      // Handle error - could show toast notification
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    void loadData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reload when filters change
  useEffect(() => {
    if (!loading) {
      void loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters]);

  // Refresh data
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Filter tickets based on active tab
  const getFilteredTickets = () => {
    switch (activeTab) {
      case "open":
        return tickets.filter((t) => t.status === "open");
      case "in_progress":
        return tickets.filter((t) => t.status === "in_progress");
      case "waiting":
        return tickets.filter((t) => t.status === "waiting_user");
      case "resolved":
        return tickets.filter((t) => t.status === "resolved");
      case "sla_breach":
        return tickets.filter((t) => t.sla_breach);
      default:
        return tickets;
    }
  };

  const filteredTickets = getFilteredTickets();

  // Event handlers
  const handleTicketView = (ticket: SupportTicket) => {
    setSelectedTicket(ticket);
  };

  const handleTicketAssign = (ticket: SupportTicket) => {
    // TODO: Open assignment modal
    console.log("Assign ticket:", ticket.id);
  };

  const handleStatusChange = async (
    ticket: SupportTicket,
    newStatus: string,
  ) => {
    try {
      await supportService.updateTicket(ticket.id, {
        status: newStatus as SupportTicket["status"],
      });
      await loadData(); // Refresh data
    } catch (error) {
      console.error("Error updating ticket status:", error);
    }
  };

  const handleFiltersChange = (newFilters: TicketFilterState) => {
    setFilters(newFilters);
  };

  const handleFiltersReset = () => {
    setFilters({
      search: "",
      status: "all",
      priority: "all",
      category: "all",
      assigned_to: "all",
      sla_breach: null,
      date_from: null,
      date_to: null,
    });
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading support tickets...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Support Center
            </h1>
            <p className="text-muted-foreground">
              Manage customer support tickets and provide assistance
            </p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw
                className={`w-4 h-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create Ticket
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Tickets
              </CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_tickets}</div>
              <p className="text-xs text-muted-foreground">All time</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Open Tickets
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {stats.open_tickets}
              </div>
              <p className="text-xs text-muted-foreground">Need attention</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">In Progress</CardTitle>
              <Clock className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {stats.in_progress_tickets}
              </div>
              <p className="text-xs text-muted-foreground">Being worked on</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Waiting User
              </CardTitle>
              <User className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {stats.waiting_user_tickets}
              </div>
              <p className="text-xs text-muted-foreground">
                User response needed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Resolved Today
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {stats.resolved_today}
              </div>
              <p className="text-xs text-muted-foreground">Great work!</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Avg Response
              </CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.avg_response_time}m
              </div>
              <p className="text-xs text-muted-foreground">Response time</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                SLA Breaches
              </CardTitle>
              <XCircle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {stats.sla_breaches}
              </div>
              <p className="text-xs text-muted-foreground">This week</p>
            </CardContent>
          </Card>
        </div>

        {/* Tickets Section */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Support Tickets</CardTitle>
              <div className="text-sm text-muted-foreground">
                {filteredTickets.length} ticket
                {filteredTickets.length !== 1 ? "s" : ""}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <TicketFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onReset={handleFiltersReset}
              categories={Object.keys(stats.tickets_by_category)}
              className="mb-6"
            />

            {/* Ticket Tabs */}
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="space-y-4"
            >
              <TabsList className="grid w-full grid-cols-6">
                <TabsTrigger value="all" className="flex items-center gap-2">
                  All
                  <Badge variant="secondary" className="text-xs">
                    {tickets.length}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger value="open" className="flex items-center gap-2">
                  Open
                  <Badge variant="destructive" className="text-xs">
                    {stats.open_tickets}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger
                  value="in_progress"
                  className="flex items-center gap-2"
                >
                  In Progress
                  <Badge variant="default" className="text-xs">
                    {stats.in_progress_tickets}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger
                  value="waiting"
                  className="flex items-center gap-2"
                >
                  Waiting
                  <Badge variant="secondary" className="text-xs">
                    {stats.waiting_user_tickets}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger
                  value="resolved"
                  className="flex items-center gap-2"
                >
                  Resolved
                  <Badge variant="default" className="text-xs">
                    {stats.resolved_tickets}
                  </Badge>
                </TabsTrigger>
                <TabsTrigger
                  value="sla_breach"
                  className="flex items-center gap-2"
                >
                  SLA Breach
                  <Badge variant="destructive" className="text-xs">
                    {tickets.filter((t) => t.sla_breach).length}
                  </Badge>
                </TabsTrigger>
              </TabsList>

              {/* Ticket Content */}
              <TabsContent value="all" className="space-y-4">
                {filteredTickets.length > 0 ? (
                  <div className="grid gap-4">
                    {filteredTickets.map((ticket) => (
                      <TicketCard
                        key={ticket.id}
                        ticket={ticket}
                        onView={handleTicketView}
                        onAssign={handleTicketAssign}
                        onStatusChange={handleStatusChange}
                        compact={false}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <MessageSquare className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-4 text-lg font-semibold">
                      No tickets found
                    </h3>
                    <p className="mt-2 text-muted-foreground">
                      No support tickets match your current filters.
                    </p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="open" className="space-y-4">
                {filteredTickets.length > 0 ? (
                  <div className="grid gap-4">
                    {filteredTickets.map((ticket) => (
                      <TicketCard
                        key={ticket.id}
                        ticket={ticket}
                        onView={handleTicketView}
                        onAssign={handleTicketAssign}
                        onStatusChange={handleStatusChange}
                        compact={false}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
                    <h3 className="mt-4 text-lg font-semibold">
                      No open tickets
                    </h3>
                    <p className="mt-2 text-muted-foreground">
                      Great! All tickets have been addressed.
                    </p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="in_progress" className="space-y-4">
                {filteredTickets.length > 0 ? (
                  <div className="grid gap-4">
                    {filteredTickets.map((ticket) => (
                      <TicketCard
                        key={ticket.id}
                        ticket={ticket}
                        onView={handleTicketView}
                        onAssign={handleTicketAssign}
                        onStatusChange={handleStatusChange}
                        compact={false}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Clock className="mx-auto h-12 w-12 text-blue-500" />
                    <h3 className="mt-4 text-lg font-semibold">
                      No tickets in progress
                    </h3>
                    <p className="mt-2 text-muted-foreground">
                      No tickets are currently being worked on.
                    </p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="waiting" className="space-y-4">
                {filteredTickets.length > 0 ? (
                  <div className="grid gap-4">
                    {filteredTickets.map((ticket) => (
                      <TicketCard
                        key={ticket.id}
                        ticket={ticket}
                        onView={handleTicketView}
                        onAssign={handleTicketAssign}
                        onStatusChange={handleStatusChange}
                        compact={false}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <User className="mx-auto h-12 w-12 text-yellow-500" />
                    <h3 className="mt-4 text-lg font-semibold">
                      No tickets waiting for users
                    </h3>
                    <p className="mt-2 text-muted-foreground">
                      No tickets are waiting for customer responses.
                    </p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="resolved" className="space-y-4">
                {filteredTickets.length > 0 ? (
                  <div className="grid gap-4">
                    {filteredTickets.map((ticket) => (
                      <TicketCard
                        key={ticket.id}
                        ticket={ticket}
                        onView={handleTicketView}
                        onAssign={handleTicketAssign}
                        onStatusChange={handleStatusChange}
                        compact={false}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
                    <h3 className="mt-4 text-lg font-semibold">
                      No resolved tickets
                    </h3>
                    <p className="mt-2 text-muted-foreground">
                      No tickets have been resolved yet.
                    </p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="sla_breach" className="space-y-4">
                {filteredTickets.length > 0 ? (
                  <div className="grid gap-4">
                    {filteredTickets.map((ticket) => (
                      <TicketCard
                        key={ticket.id}
                        ticket={ticket}
                        onView={handleTicketView}
                        onAssign={handleTicketAssign}
                        onStatusChange={handleStatusChange}
                        compact={false}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
                    <h3 className="mt-4 text-lg font-semibold">
                      No SLA breaches
                    </h3>
                    <p className="mt-2 text-muted-foreground">
                      All tickets are within SLA requirements.
                    </p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Ticket Detail Modal */}
        <TicketDetailModal
          ticket={selectedTicket}
          open={!!selectedTicket}
          onClose={() => setSelectedTicket(null)}
          onStatusChange={async (ticketId, newStatus) => {
            const t = tickets.find((ti) => ti.id === ticketId);
            if (t) await handleStatusChange(t, newStatus);
          }}
        />
      </div>
    </AdminLayout>
  );
}

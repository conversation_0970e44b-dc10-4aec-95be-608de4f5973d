"use client";

import React, { useState, useEffect } from "react";
import { AdminLayout } from "@/components/AdminLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

import { Input } from "@/components/ui/input";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Plus,
  Search,
  BookOpen,
  Eye,
  ThumbsUp,
  Settings,
  RefreshCw,
  Tag,
} from "lucide-react";

// Import services and components
import {
  knowledgeBaseService,
  KBArticle,
  KBCategory,
  KBStats,
  ArticleFilters,
} from "@/services/knowledgeBaseService";
import { ArticleCard } from "@/components/knowledge-base/ArticleCard";
import { CategoryCard } from "@/components/knowledge-base/CategoryCard";
import { CreateArticleModal } from "@/components/knowledge-base/CreateArticleModal";
import { CreateCategoryModal } from "@/components/knowledge-base/CreateCategoryModal";

export default function KnowledgeBasePage() {
  const [articles, setArticles] = useState<KBArticle[]>([]);
  const [categories, setCategories] = useState<KBCategory[]>([]);
  const [stats, setStats] = useState<KBStats>({
    total_articles: 0,
    published_articles: 0,
    draft_articles: 0,
    total_categories: 0,
    total_views: 0,
    total_feedback: 0,
    avg_helpfulness: 0,
    popular_articles: [],
    recent_articles: [],
  });

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState("articles");
  const [showCreateArticle, setShowCreateArticle] = useState(false);
  const [showCreateCategory, setShowCreateCategory] = useState(false);

  // Filters
  const [filters, setFilters] = useState<ArticleFilters>({
    search: "",
    category_id: "",
    status: "all",
    featured: undefined,
    limit: 20,
    offset: 0,
  });

  // Load data
  const loadData = async () => {
    try {
      setLoading(true);
      const [articlesData, categoriesData, statsData] = await Promise.all([
        knowledgeBaseService.getArticles(filters),
        knowledgeBaseService.getCategories(),
        knowledgeBaseService.getKBStats(),
      ]);

      setArticles(articlesData);
      setCategories(categoriesData);
      setStats(statsData);
    } catch (error) {
      console.error("Error loading knowledge base data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    void loadData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reload when filters change
  useEffect(() => {
    if (!loading) {
      void loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters]);

  // Refresh data
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  // Filter handlers
  const handleSearchChange = (search: string) => {
    setFilters((prev) => ({ ...prev, search, offset: 0 }));
  };

  const handleCategoryFilter = (category_id: string) => {
    setFilters((prev) => ({
      ...prev,
      category_id: category_id === "all" ? "" : category_id,
      offset: 0,
    }));
  };

  const handleStatusFilter = (status: string) => {
    setFilters((prev) => ({
      ...prev,
      status: status === "all" ? "" : status,
      offset: 0,
    }));
  };

  const handleFeaturedFilter = (featured: string) => {
    setFilters((prev) => ({
      ...prev,
      featured: featured === "all" ? undefined : featured === "true",
      offset: 0,
    }));
  };

  // Article actions
  const handleArticleCreated = () => {
    setShowCreateArticle(false);
    loadData();
  };

  const handleArticleEdit = (article: KBArticle) => {
    // TODO: Open edit modal
    console.log("Edit article:", article.id);
  };

  const handleArticleDelete = async (article: KBArticle) => {
    if (confirm(`Are you sure you want to delete "${article.title}"?`)) {
      try {
        await knowledgeBaseService.deleteArticle(article.id);
        loadData();
      } catch (error) {
        console.error("Error deleting article:", error);
      }
    }
  };

  // Category actions
  const handleCategoryCreated = () => {
    setShowCreateCategory(false);
    loadData();
  };

  const handleCategoryEdit = (category: KBCategory) => {
    // TODO: Open edit modal
    console.log("Edit category:", category.id);
  };

  const handleCategoryDelete = async (category: KBCategory) => {
    if (confirm(`Are you sure you want to delete "${category.name}"?`)) {
      try {
        await knowledgeBaseService.deleteCategory(category.id);
        loadData();
      } catch (error) {
        console.error("Error deleting category:", error);
      }
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading knowledge base...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Knowledge Base
            </h1>
            <p className="text-muted-foreground">
              Manage help articles and FAQs for customer self-service
            </p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw
                className={`w-4 h-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
            <Button
              onClick={() => setShowCreateCategory(true)}
              variant="outline"
            >
              <Settings className="w-4 h-4 mr-2" />
              Manage Categories
            </Button>
            <Button onClick={() => setShowCreateArticle(true)}>
              <Plus className="w-4 h-4 mr-2" />
              New Article
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Articles
              </CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_articles}</div>
              <p className="text-xs text-muted-foreground">
                {stats.published_articles} published, {stats.draft_articles}{" "}
                drafts
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Categories</CardTitle>
              <Tag className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_categories}</div>
              <p className="text-xs text-muted-foreground">Active categories</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Views</CardTitle>
              <Eye className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {stats.total_views.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">Article views</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Helpfulness</CardTitle>
              <ThumbsUp className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {Math.round(stats.avg_helpfulness * 100)}%
              </div>
              <p className="text-xs text-muted-foreground">
                {stats.total_feedback} total ratings
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Card>
          <CardHeader>
            <CardTitle>Knowledge Base Management</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="space-y-4"
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger
                  value="articles"
                  className="flex items-center gap-2"
                >
                  <BookOpen className="w-4 h-4" />
                  Articles ({stats.total_articles})
                </TabsTrigger>
                <TabsTrigger
                  value="categories"
                  className="flex items-center gap-2"
                >
                  <Tag className="w-4 h-4" />
                  Categories ({stats.total_categories})
                </TabsTrigger>
              </TabsList>

              {/* Articles Tab */}
              <TabsContent value="articles" className="space-y-4">
                {/* Article Filters */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search articles..."
                        value={filters.search}
                        onChange={(e) => handleSearchChange(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <Select
                    value={filters.category_id || "all"}
                    onValueChange={handleCategoryFilter}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select
                    value={filters.status || "all"}
                    onValueChange={handleStatusFilter}
                  >
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="All Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select
                    value={
                      filters.featured === undefined
                        ? "all"
                        : filters.featured.toString()
                    }
                    onValueChange={handleFeaturedFilter}
                  >
                    <SelectTrigger className="w-[130px]">
                      <SelectValue placeholder="Featured" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Articles</SelectItem>
                      <SelectItem value="true">Featured</SelectItem>
                      <SelectItem value="false">Not Featured</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Articles List */}
                <div className="grid gap-4">
                  {articles.length > 0 ? (
                    articles.map((article) => (
                      <ArticleCard
                        key={article.id}
                        article={article}
                        onEdit={handleArticleEdit}
                        onDelete={handleArticleDelete}
                      />
                    ))
                  ) : (
                    <div className="text-center py-12">
                      <BookOpen className="mx-auto h-12 w-12 text-muted-foreground" />
                      <h3 className="mt-4 text-lg font-semibold">
                        No articles found
                      </h3>
                      <p className="mt-2 text-muted-foreground">
                        {filters.search ||
                        filters.category_id ||
                        filters.status !== "all"
                          ? "Try adjusting your search or filters."
                          : "Get started by creating your first article."}
                      </p>
                      {!filters.search &&
                        !filters.category_id &&
                        filters.status === "all" && (
                          <Button
                            className="mt-4"
                            onClick={() => setShowCreateArticle(true)}
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Create First Article
                          </Button>
                        )}
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Categories Tab */}
              <TabsContent value="categories" className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {categories.length > 0 ? (
                    categories.map((category) => (
                      <CategoryCard
                        key={category.id}
                        category={category}
                        onEdit={handleCategoryEdit}
                        onDelete={handleCategoryDelete}
                      />
                    ))
                  ) : (
                    <div className="col-span-full text-center py-12">
                      <Tag className="mx-auto h-12 w-12 text-muted-foreground" />
                      <h3 className="mt-4 text-lg font-semibold">
                        No categories found
                      </h3>
                      <p className="mt-2 text-muted-foreground">
                        Create categories to organize your articles.
                      </p>
                      <Button
                        className="mt-4"
                        onClick={() => setShowCreateCategory(true)}
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Create First Category
                      </Button>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Modals */}
        <CreateArticleModal
          open={showCreateArticle}
          onClose={() => setShowCreateArticle(false)}
          onSuccess={handleArticleCreated}
          categories={categories}
        />

        <CreateCategoryModal
          open={showCreateCategory}
          onClose={() => setShowCreateCategory(false)}
          onSuccess={handleCategoryCreated}
          categories={categories}
        />
      </div>
    </AdminLayout>
  );
}

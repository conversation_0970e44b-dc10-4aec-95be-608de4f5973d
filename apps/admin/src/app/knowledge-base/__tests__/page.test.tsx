import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import KnowledgeBasePage from "../page";
import * as knowledgeBaseService from "@/services/knowledgeBaseService";

// Mock the service
jest.mock("@/services/knowledgeBaseService", () => ({
  knowledgeBaseService: {
    getArticles: jest.fn(),
    getCategories: jest.fn(),
    getKBStats: jest.fn(),
    deleteArticle: jest.fn(),
    deleteCategory: jest.fn(),
  },
}));

// Mock AdminLayout
jest.mock("@/components/AdminLayout", () => ({
  AdminLayout: ({ children }: any) => (
    <div data-testid="admin-layout">{children}</div>
  ),
}));

// Mock UI components
jest.mock("@/components/ui/card", () => ({
  Card: ({ children }: any) => <div>{children}</div>,
  CardContent: ({ children }: any) => <div>{children}</div>,
  CardHeader: ({ children }: any) => <div>{children}</div>,
  CardTitle: ({ children }: any) => <h3>{children}</h3>,
}));

jest.mock("@/components/ui/button", () => ({
  Button: ({ children, onClick, disabled, variant }: any) => (
    <button onClick={onClick} disabled={disabled} data-variant={variant}>
      {children}
    </button>
  ),
}));

jest.mock("@/components/ui/input", () => ({
  Input: ({ value, onChange, placeholder }: any) => (
    <input value={value} onChange={onChange} placeholder={placeholder} />
  ),
}));

jest.mock("@/components/ui/tabs", () => ({
  Tabs: ({ children, value, onValueChange }: any) => (
    <div data-testid="tabs" data-value={value}>
      <button onClick={() => onValueChange("articles")}>Articles</button>
      <button onClick={() => onValueChange("categories")}>Categories</button>
      {children}
    </div>
  ),
  TabsContent: ({ children, value }: any) => (
    <div data-value={value}>{children}</div>
  ),
  TabsList: ({ children }: any) => <div>{children}</div>,
  TabsTrigger: ({ children, value }: any) => (
    <button data-value={value}>{children}</button>
  ),
}));

jest.mock("@/components/ui/select", () => ({
  Select: ({ children, value, onValueChange }: any) => (
    <div data-testid="select" data-value={value}>
      <button onClick={() => onValueChange && onValueChange("test-value")}>
        {value || "Select..."}
      </button>
      {children}
    </div>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value }: any) => (
    <div data-value={value}>{children}</div>
  ),
  SelectTrigger: ({ children }: any) => <div>{children}</div>,
  SelectValue: ({ placeholder }: any) => <span>{placeholder}</span>,
}));

// Mock the child components
jest.mock("@/components/knowledge-base/ArticleCard", () => ({
  ArticleCard: ({ article, onEdit, onDelete }: any) => (
    <div data-testid="article-card" data-article-id={article.id}>
      <h4>{article.title}</h4>
      <button onClick={() => onEdit(article)}>Edit Article</button>
      <button onClick={() => onDelete(article)}>Delete Article</button>
    </div>
  ),
}));

jest.mock("@/components/knowledge-base/CategoryCard", () => ({
  CategoryCard: ({ category, onEdit, onDelete }: any) => (
    <div data-testid="category-card" data-category-id={category.id}>
      <h4>{category.name}</h4>
      <button onClick={() => onEdit(category)}>Edit Category</button>
      <button onClick={() => onDelete(category)}>Delete Category</button>
    </div>
  ),
}));

jest.mock("@/components/knowledge-base/CreateArticleModal", () => ({
  CreateArticleModal: ({ open, onClose, onSuccess }: any) =>
    open ? (
      <div data-testid="create-article-modal">
        <button onClick={onSuccess}>Create Article Success</button>
        <button onClick={onClose}>Close Article Modal</button>
      </div>
    ) : null,
}));

jest.mock("@/components/knowledge-base/CreateCategoryModal", () => ({
  CreateCategoryModal: ({ open, onClose, onSuccess }: any) =>
    open ? (
      <div data-testid="create-category-modal">
        <button onClick={onSuccess}>Create Category Success</button>
        <button onClick={onClose}>Close Category Modal</button>
      </div>
    ) : null,
}));

const mockArticles = [
  {
    id: "article-1",
    title: "How to Create Meal Plans",
    slug: "how-to-create-meal-plans",
    content: "Content here...",
    status: "published",
    category_id: "meal-planning",
    category_name: "Meal Planning",
    view_count: 100,
    helpful_count: 10,
    not_helpful_count: 2,
  },
  {
    id: "article-2",
    title: "Workout Tracking Guide",
    slug: "workout-tracking-guide",
    content: "Content here...",
    status: "draft",
    category_id: "workout-tracking",
    category_name: "Workout Tracking",
    view_count: 50,
    helpful_count: 5,
    not_helpful_count: 1,
  },
];

const mockCategories = [
  {
    id: "meal-planning",
    name: "Meal Planning",
    slug: "meal-planning",
    description: "Meal planning guides",
    is_active: true,
    article_count: 5,
  },
  {
    id: "workout-tracking",
    name: "Workout Tracking",
    slug: "workout-tracking",
    description: "Workout guides",
    is_active: true,
    article_count: 3,
  },
];

const mockStats = {
  total_articles: 25,
  published_articles: 20,
  draft_articles: 5,
  total_categories: 6,
  total_views: 1250,
  total_feedback: 85,
  avg_helpfulness: 0.82,
  popular_articles: [],
  recent_articles: [],
};

describe("KnowledgeBasePage", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mocks
    (
      knowledgeBaseService.knowledgeBaseService.getArticles as jest.Mock
    ).mockResolvedValue(mockArticles);
    (
      knowledgeBaseService.knowledgeBaseService.getCategories as jest.Mock
    ).mockResolvedValue(mockCategories);
    (
      knowledgeBaseService.knowledgeBaseService.getKBStats as jest.Mock
    ).mockResolvedValue(mockStats);
  });

  it("should render loading state initially", () => {
    render(<KnowledgeBasePage />);

    expect(screen.getByText("Loading knowledge base...")).toBeInTheDocument();
  });

  it("should render main content after loading", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      expect(screen.getByText("Knowledge Base")).toBeInTheDocument();
      expect(
        screen.getByText(
          "Manage help articles and FAQs for customer self-service",
        ),
      ).toBeInTheDocument();
    });
  });

  it("should display statistics correctly", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      expect(screen.getByText("25")).toBeInTheDocument(); // total articles
      expect(screen.getByText("6")).toBeInTheDocument(); // total categories
      expect(screen.getByText("1,250")).toBeInTheDocument(); // total views
      expect(screen.getByText("82%")).toBeInTheDocument(); // helpfulness
    });
  });

  it("should render articles in articles tab", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      expect(screen.getByTestId("article-card")).toBeInTheDocument();
      expect(screen.getByText("How to Create Meal Plans")).toBeInTheDocument();
      expect(screen.getByText("Workout Tracking Guide")).toBeInTheDocument();
    });
  });

  it("should switch to categories tab", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      const categoriesTab = screen.getByText("Categories (6)");
      fireEvent.click(categoriesTab);

      expect(screen.getByTestId("category-card")).toBeInTheDocument();
      expect(screen.getByText("Meal Planning")).toBeInTheDocument();
      expect(screen.getByText("Workout Tracking")).toBeInTheDocument();
    });
  });

  it("should handle search input", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText("Search articles...");
      fireEvent.change(searchInput, { target: { value: "meal planning" } });

      expect(searchInput.value).toBe("meal planning");
    });
  });

  it("should open create article modal", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      const newArticleButton = screen.getByText("New Article");
      fireEvent.click(newArticleButton);

      expect(screen.getByTestId("create-article-modal")).toBeInTheDocument();
    });
  });

  it("should open create category modal", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      const manageCategoriesButton = screen.getByText("Manage Categories");
      fireEvent.click(manageCategoriesButton);

      expect(screen.getByTestId("create-category-modal")).toBeInTheDocument();
    });
  });

  it("should handle article creation success", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      const newArticleButton = screen.getByText("New Article");
      fireEvent.click(newArticleButton);

      const successButton = screen.getByText("Create Article Success");
      fireEvent.click(successButton);

      expect(
        screen.queryByTestId("create-article-modal"),
      ).not.toBeInTheDocument();
    });
  });

  it("should handle category creation success", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      const manageCategoriesButton = screen.getByText("Manage Categories");
      fireEvent.click(manageCategoriesButton);

      const successButton = screen.getByText("Create Category Success");
      fireEvent.click(successButton);

      expect(
        screen.queryByTestId("create-category-modal"),
      ).not.toBeInTheDocument();
    });
  });

  it("should handle article deletion with confirmation", async () => {
    // Mock window.confirm
    const originalConfirm = window.confirm;
    window.confirm = jest.fn(() => true);

    const mockDeleteArticle = jest
      .spyOn(knowledgeBaseService.knowledgeBaseService, "deleteArticle")
      .mockResolvedValue();

    render(<KnowledgeBasePage />);

    await waitFor(() => {
      const deleteButton = screen.getByText("Delete Article");
      fireEvent.click(deleteButton);

      expect(window.confirm).toHaveBeenCalledWith(
        'Are you sure you want to delete "How to Create Meal Plans"?',
      );
      expect(mockDeleteArticle).toHaveBeenCalledWith("article-1");
    });

    window.confirm = originalConfirm;
    mockDeleteArticle.mockRestore();
  });

  it("should handle category deletion with confirmation", async () => {
    // Mock window.confirm
    const originalConfirm = window.confirm;
    window.confirm = jest.fn(() => true);

    const mockDeleteCategory = jest
      .spyOn(knowledgeBaseService.knowledgeBaseService, "deleteCategory")
      .mockResolvedValue();

    render(<KnowledgeBasePage />);

    await waitFor(() => {
      // Switch to categories tab
      const categoriesTab = screen.getByText("Categories");
      fireEvent.click(categoriesTab);

      const deleteButton = screen.getByText("Delete Category");
      fireEvent.click(deleteButton);

      expect(window.confirm).toHaveBeenCalledWith(
        'Are you sure you want to delete "Meal Planning"?',
      );
      expect(mockDeleteCategory).toHaveBeenCalledWith("meal-planning");
    });

    window.confirm = originalConfirm;
    mockDeleteCategory.mockRestore();
  });

  it("should handle refresh button", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      const refreshButton = screen.getByText("Refresh");
      fireEvent.click(refreshButton);

      // Should call service methods again
      expect(
        knowledgeBaseService.knowledgeBaseService.getArticles,
      ).toHaveBeenCalled();
      expect(
        knowledgeBaseService.knowledgeBaseService.getCategories,
      ).toHaveBeenCalled();
      expect(
        knowledgeBaseService.knowledgeBaseService.getKBStats,
      ).toHaveBeenCalled();
    });
  });

  it("should display empty state when no articles", async () => {
    (
      knowledgeBaseService.knowledgeBaseService.getArticles as jest.Mock
    ).mockResolvedValue([]);

    render(<KnowledgeBasePage />);

    await waitFor(() => {
      expect(screen.getByText("No articles found")).toBeInTheDocument();
      expect(
        screen.getByText("Get started by creating your first article."),
      ).toBeInTheDocument();
    });
  });

  it("should display empty state when no categories", async () => {
    (
      knowledgeBaseService.knowledgeBaseService.getCategories as jest.Mock
    ).mockResolvedValue([]);

    render(<KnowledgeBasePage />);

    await waitFor(() => {
      // Switch to categories tab
      const categoriesTab = screen.getByText("Categories");
      fireEvent.click(categoriesTab);

      expect(screen.getByText("No categories found")).toBeInTheDocument();
      expect(
        screen.getByText("Create categories to organize your articles."),
      ).toBeInTheDocument();
    });
  });

  it("should handle service errors gracefully", async () => {
    (
      knowledgeBaseService.knowledgeBaseService.getArticles as jest.Mock
    ).mockRejectedValue(new Error("Service error"));
    (
      knowledgeBaseService.knowledgeBaseService.getCategories as jest.Mock
    ).mockRejectedValue(new Error("Service error"));
    (
      knowledgeBaseService.knowledgeBaseService.getKBStats as jest.Mock
    ).mockRejectedValue(new Error("Service error"));

    // Mock console.error to avoid test output noise
    const originalConsoleError = console.error;
    console.error = jest.fn();

    render(<KnowledgeBasePage />);

    await waitFor(() => {
      // Should still render the page structure even with errors
      expect(screen.getByText("Knowledge Base")).toBeInTheDocument();
    });

    console.error = originalConsoleError;
  });

  it("should update filters when search changes", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText("Search articles...");
      fireEvent.change(searchInput, { target: { value: "meal planning" } });

      // Should trigger a new service call with filters
      expect(
        knowledgeBaseService.knowledgeBaseService.getArticles,
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          search: "meal planning",
        }),
      );
    });
  });

  it("should show filtered empty state", async () => {
    (
      knowledgeBaseService.knowledgeBaseService.getArticles as jest.Mock
    ).mockResolvedValue([]);

    render(<KnowledgeBasePage />);

    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText("Search articles...");
      fireEvent.change(searchInput, { target: { value: "nonexistent" } });
    });

    await waitFor(() => {
      expect(screen.getByText("No articles found")).toBeInTheDocument();
      expect(
        screen.getByText("Try adjusting your search or filters."),
      ).toBeInTheDocument();
    });
  });

  it("should display correct tab counts", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      expect(screen.getByText("Articles (25)")).toBeInTheDocument();
      expect(screen.getByText("Categories (6)")).toBeInTheDocument();
    });
  });

  it("should handle modal close events", async () => {
    render(<KnowledgeBasePage />);

    await waitFor(() => {
      // Open article modal
      const newArticleButton = screen.getByText("New Article");
      fireEvent.click(newArticleButton);

      // Close it
      const closeButton = screen.getByText("Close Article Modal");
      fireEvent.click(closeButton);

      expect(
        screen.queryByTestId("create-article-modal"),
      ).not.toBeInTheDocument();
    });
  });
});

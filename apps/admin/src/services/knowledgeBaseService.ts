import { supabaseAdmin } from "@/lib/supabase";

// Types
export interface KBCategory {
  id: string;
  name: string;
  description: string | null;
  slug: string;
  parent_id: string | null;
  sort_order: number;
  icon: string | null;
  color: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by: string | null;

  // Computed fields
  article_count?: number;
  parent_category?: KBCategory;
  subcategories?: KBCategory[];
}

export interface KBArticle {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string | null;
  category_id: string | null;
  author_id: string;
  status: "draft" | "published" | "archived";
  published_at: string | null;
  meta_title: string | null;
  meta_description: string | null;
  tags: string[];
  view_count: number;
  helpful_count: number;
  not_helpful_count: number;
  featured: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;

  // Joined fields
  category_name?: string;
  category_slug?: string;
  author_name?: string;
  author_email?: string;
  helpfulness_ratio?: number;
}

export interface KBFeedback {
  id: string;
  article_id: string;
  user_id: string | null;
  session_id: string | null;
  is_helpful: boolean;
  feedback_text: string | null;
  created_at: string;
}

export interface KBSearchResult {
  article: KBArticle;
  rank: number;
  headline: string;
}

export interface KBStats {
  total_articles: number;
  published_articles: number;
  draft_articles: number;
  total_categories: number;
  total_views: number;
  total_feedback: number;
  avg_helpfulness: number;
  popular_articles: KBArticle[];
  recent_articles: KBArticle[];
}

export interface CreateCategoryData {
  name: string;
  description?: string;
  slug: string;
  parent_id?: string;
  icon?: string;
  color?: string;
  sort_order?: number;
}

export interface UpdateCategoryData {
  name?: string;
  description?: string;
  slug?: string;
  parent_id?: string;
  icon?: string;
  color?: string;
  sort_order?: number;
  is_active?: boolean;
}

export interface CreateArticleData {
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  category_id?: string;
  meta_title?: string;
  meta_description?: string;
  tags?: string[];
  featured?: boolean;
  sort_order?: number;
  status?: "draft" | "published";
}

export interface UpdateArticleData {
  title?: string;
  slug?: string;
  content?: string;
  excerpt?: string;
  category_id?: string;
  meta_title?: string;
  meta_description?: string;
  tags?: string[];
  featured?: boolean;
  sort_order?: number;
  status?: "draft" | "published" | "archived";
}

export interface ArticleFilters {
  search?: string;
  category_id?: string;
  status?: string;
  featured?: boolean;
  author_id?: string;
  tags?: string[];
  limit?: number;
  offset?: number;
}

// Helper function to check if Supabase is properly configured
function isSupabaseConfigured(): boolean {
  return !!(
    process.env.NEXT_PUBLIC_SUPABASE_URL &&
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );
}

class KnowledgeBaseService {
  /**
   * Get all categories with optional hierarchy
   */
  async getCategories(includeInactive = false): Promise<KBCategory[]> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, returning mock categories");
      return this.getMockCategories();
    }

    try {
      let query = supabaseAdmin
        .from("support_kb_categories")
        .select(
          `
          id,
          name,
          description,
          slug,
          parent_id,
          sort_order,
          icon,
          color,
          is_active,
          created_at,
          updated_at,
          created_by
        `,
        )
        .order("sort_order", { ascending: true })
        .order("name", { ascending: true });

      if (!includeInactive) {
        query = query.eq("is_active", true);
      }

      const { data, error } = await query;

      if (error) {
        console.error("Error fetching categories:", error);
        throw error;
      }

      // Get article counts for each category
      const categoriesWithCounts = await Promise.all(
        (data || []).map(async (category) => {
          const { count } = await supabaseAdmin
            .from("support_kb_articles")
            .select("*", { count: "exact", head: true })
            .eq("category_id", category.id)
            .eq("status", "published");

          return {
            ...category,
            article_count: count || 0,
          };
        }),
      );

      return categoriesWithCounts;
    } catch (error) {
      console.error("Error in getCategories:", error);
      throw error;
    }
  }

  /**
   * Get a single category by ID or slug
   */
  async getCategoryById(id: string): Promise<KBCategory | null> {
    if (!isSupabaseConfigured()) {
      const mockCategories = this.getMockCategories();
      return mockCategories.find((c) => c.id === id) || null;
    }

    try {
      const { data, error } = await supabaseAdmin
        .from("support_kb_categories")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        console.error("Error fetching category:", error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error("Error in getCategoryById:", error);
      throw error;
    }
  }

  /**
   * Create a new category
   */
  async createCategory(categoryData: CreateCategoryData): Promise<string> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, returning mock ID");
      return "mock-category-id";
    }

    try {
      const { data, error } = await supabaseAdmin
        .from("support_kb_categories")
        .insert({
          ...categoryData,
          created_by: null, // TODO: Get from auth context
        })
        .select("id")
        .single();

      if (error) {
        console.error("Error creating category:", error);
        throw error;
      }

      return data.id;
    } catch (error) {
      console.error("Error in createCategory:", error);
      throw error;
    }
  }

  /**
   * Update an existing category
   */
  async updateCategory(id: string, updates: UpdateCategoryData): Promise<void> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, skipping update");
      return;
    }

    try {
      const { error } = await supabaseAdmin
        .from("support_kb_categories")
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id);

      if (error) {
        console.error("Error updating category:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error in updateCategory:", error);
      throw error;
    }
  }

  /**
   * Delete a category
   */
  async deleteCategory(id: string): Promise<void> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, skipping delete");
      return;
    }

    try {
      const { error } = await supabaseAdmin
        .from("support_kb_categories")
        .delete()
        .eq("id", id);

      if (error) {
        console.error("Error deleting category:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error in deleteCategory:", error);
      throw error;
    }
  }

  /**
   * Get articles with filtering and pagination
   */
  async getArticles(filters: ArticleFilters = {}): Promise<KBArticle[]> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, returning mock articles");
      return this.getMockArticles();
    }

    try {
      let query = supabaseAdmin
        .from("support_kb_articles")
        .select(
          `
          id,
          title,
          slug,
          content,
          excerpt,
          category_id,
          author_id,
          status,
          published_at,
          meta_title,
          meta_description,
          tags,
          view_count,
          helpful_count,
          not_helpful_count,
          featured,
          sort_order,
          created_at,
          updated_at,
          support_kb_categories!category_id (
            name,
            slug
          ),
          profiles!author_id (
            full_name,
            email
          )
        `,
        )
        .order("created_at", { ascending: false });

      // Apply filters
      if (filters.search) {
        query = query.textSearch("search_vector", filters.search);
      }

      if (filters.category_id) {
        query = query.eq("category_id", filters.category_id);
      }

      if (filters.status) {
        query = query.eq("status", filters.status);
      }

      if (filters.featured !== undefined) {
        query = query.eq("featured", filters.featured);
      }

      if (filters.author_id) {
        query = query.eq("author_id", filters.author_id);
      }

      if (filters.tags && filters.tags.length > 0) {
        query = query.overlaps("tags", filters.tags);
      }

      // Pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(
          filters.offset,
          filters.offset + (filters.limit || 50) - 1,
        );
      }

      const { data, error } = await query;

      if (error) {
        console.error("Error fetching articles:", error);
        throw error;
      }

      // Transform the data to include joined fields
      return (data || []).map(
        (
          article: KBArticle & {
            support_kb_categories?: {
              name?: string | null;
              slug?: string | null;
            } | null;
            profiles?: {
              full_name?: string | null;
              email?: string | null;
            } | null;
            helpful_count?: number;
            not_helpful_count?: number;
          },
        ) => ({
          ...article,
          category_name: article.support_kb_categories?.name || null,
          category_slug: article.support_kb_categories?.slug || null,
          author_name: article.profiles?.full_name || "Unknown Author",
          author_email: article.profiles?.email || "",
          helpfulness_ratio:
            article.helpful_count + article.not_helpful_count > 0
              ? article.helpful_count /
                (article.helpful_count + article.not_helpful_count)
              : 0,
        }),
      );
    } catch (error) {
      console.error("Error in getArticles:", error);
      throw error;
    }
  }

  /**
   * Get a single article by ID or slug
   */
  async getArticleById(id: string): Promise<KBArticle | null> {
    if (!isSupabaseConfigured()) {
      const mockArticles = this.getMockArticles();
      return mockArticles.find((a) => a.id === id) || null;
    }

    try {
      const { data, error } = await supabaseAdmin
        .from("support_kb_articles")
        .select(
          `
          *,
          support_kb_categories!category_id (
            name,
            slug
          ),
          profiles!author_id (
            full_name,
            email
          )
        `,
        )
        .eq("id", id)
        .single();

      if (error) {
        console.error("Error fetching article:", error);
        throw error;
      }

      if (!data) return null;

      return {
        ...data,
        category_name: data.support_kb_categories?.name || null,
        category_slug: data.support_kb_categories?.slug || null,
        author_name: data.profiles?.full_name || "Unknown Author",
        author_email: data.profiles?.email || "",
        helpfulness_ratio:
          data.helpful_count + data.not_helpful_count > 0
            ? data.helpful_count / (data.helpful_count + data.not_helpful_count)
            : 0,
      };
    } catch (error) {
      console.error("Error in getArticleById:", error);
      throw error;
    }
  }

  /**
   * Create a new article
   */
  async createArticle(articleData: CreateArticleData): Promise<string> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, returning mock ID");
      return "mock-article-id";
    }

    try {
      const { data, error } = await supabaseAdmin
        .from("support_kb_articles")
        .insert({
          ...articleData,
          author_id: null, // TODO: Get from auth context
          published_at:
            articleData.status === "published"
              ? new Date().toISOString()
              : null,
        })
        .select("id")
        .single();

      if (error) {
        console.error("Error creating article:", error);
        throw error;
      }

      return data.id;
    } catch (error) {
      console.error("Error in createArticle:", error);
      throw error;
    }
  }

  /**
   * Update an existing article
   */
  async updateArticle(id: string, updates: UpdateArticleData): Promise<void> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, skipping update");
      return;
    }

    try {
      const updateData: Partial<UpdateArticleData & { published_at?: string }> =
        {
          ...updates,
          updated_at: new Date().toISOString(),
        };

      // Set published_at when publishing
      if (updates.status === "published") {
        updateData.published_at = new Date().toISOString();
      }

      const { error } = await supabaseAdmin
        .from("support_kb_articles")
        .update(updateData)
        .eq("id", id);

      if (error) {
        console.error("Error updating article:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error in updateArticle:", error);
      throw error;
    }
  }

  /**
   * Delete an article
   */
  async deleteArticle(id: string): Promise<void> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, skipping delete");
      return;
    }

    try {
      const { error } = await supabaseAdmin
        .from("support_kb_articles")
        .delete()
        .eq("id", id);

      if (error) {
        console.error("Error deleting article:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error in deleteArticle:", error);
      throw error;
    }
  }

  /**
   * Search articles with full-text search
   */
  async searchArticles(query: string, limit = 10): Promise<KBSearchResult[]> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, returning mock search results");
      return [];
    }

    try {
      const { data, error } = await supabaseAdmin
        .from("support_kb_articles")
        .select(
          `
          *,
          support_kb_categories!category_id (
            name,
            slug
          )
        `,
        )
        .textSearch("search_vector", query)
        .eq("status", "published")
        .limit(limit);

      if (error) {
        console.error("Error searching articles:", error);
        throw error;
      }

      return (data || []).map(
        (
          article: KBArticle & {
            support_kb_categories?: {
              name?: string | null;
              slug?: string | null;
            } | null;
            content: string;
          },
          index: number,
        ) => ({
          article: {
            ...article,
            category_name: article.support_kb_categories?.name || null,
            category_slug: article.support_kb_categories?.slug || null,
          },
          rank: index + 1,
          headline: this.generateHeadline(article.content, query),
        }),
      );
    } catch (error) {
      console.error("Error in searchArticles:", error);
      throw error;
    }
  }

  /**
   * Get knowledge base statistics
   */
  async getKBStats(): Promise<KBStats> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, returning mock stats");
      return this.getMockStats();
    }

    try {
      const [articlesData, categoriesData, feedbackData] = await Promise.all([
        supabaseAdmin.from("support_kb_articles").select("status, view_count"),
        supabaseAdmin
          .from("support_kb_categories")
          .select("id", { count: "exact", head: true }),
        supabaseAdmin.from("support_kb_feedback").select("is_helpful"),
      ]);

      const articles = articlesData.data || [];
      const totalViews = articles.reduce(
        (sum, article) => sum + (article.view_count || 0),
        0,
      );

      const feedback =
        (feedbackData.data as Array<{ is_helpful: boolean }>) || [];
      const helpfulFeedback = feedback.filter((f) => f.is_helpful).length;
      const avgHelpfulness =
        feedback.length > 0 ? helpfulFeedback / feedback.length : 0;

      // Get popular and recent articles
      const [popularArticles, recentArticles] = await Promise.all([
        this.getArticles({ status: "published", limit: 5 }),
        this.getArticles({ status: "published", limit: 5 }),
      ]);

      return {
        total_articles: articles.length,
        published_articles: articles.filter((a) => a.status === "published")
          .length,
        draft_articles: articles.filter((a) => a.status === "draft").length,
        total_categories: categoriesData.count || 0,
        total_views: totalViews,
        total_feedback: feedback.length,
        avg_helpfulness: avgHelpfulness,
        popular_articles: popularArticles.slice(0, 5),
        recent_articles: recentArticles.slice(0, 5),
      };
    } catch (error) {
      console.error("Error in getKBStats:", error);
      throw error;
    }
  }

  /**
   * Record article feedback
   */
  async recordFeedback(
    articleId: string,
    isHelpful: boolean,
    feedbackText?: string,
    userId?: string,
    sessionId?: string,
  ): Promise<void> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, skipping feedback");
      return;
    }

    try {
      const { error } = await supabaseAdmin.from("support_kb_feedback").insert({
        article_id: articleId,
        user_id: userId || null,
        session_id: sessionId || null,
        is_helpful: isHelpful,
        feedback_text: feedbackText || null,
      });

      if (error) {
        console.error("Error recording feedback:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error in recordFeedback:", error);
      throw error;
    }
  }

  /**
   * Generate search headline with highlighted terms
   */
  private generateHeadline(content: string, query: string): string {
    const words = query.toLowerCase().split(" ");
    const sentences = content.split(". ");

    // Find sentence containing query terms
    const matchingSentence = sentences.find((sentence) =>
      words.some((word) => sentence.toLowerCase().includes(word)),
    );

    if (matchingSentence) {
      return matchingSentence.substring(0, 150) + "...";
    }

    return content.substring(0, 150) + "...";
  }

  /**
   * Mock data for development/testing
   */
  private getMockCategories(): KBCategory[] {
    return [
      {
        id: "1",
        name: "Getting Started",
        description: "New user guides and basic app navigation",
        slug: "getting-started",
        parent_id: null,
        sort_order: 0,
        icon: "play-circle",
        color: "#06B6D4",
        is_active: true,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        created_by: null,
        article_count: 5,
      },
      {
        id: "2",
        name: "Meal Planning",
        description:
          "AI meal planner, dietary restrictions, and nutrition guidance",
        slug: "meal-planning",
        parent_id: null,
        sort_order: 1,
        icon: "utensils",
        color: "#10B981",
        is_active: true,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        created_by: null,
        article_count: 8,
      },
    ];
  }

  private getMockArticles(): KBArticle[] {
    return [
      {
        id: "1",
        title: "How to Create Your First Meal Plan",
        slug: "how-to-create-first-meal-plan",
        content:
          "Learn how to use PlateMotion's AI meal planner to create personalized meal plans...",
        excerpt:
          "Step-by-step guide to creating your first AI-generated meal plan",
        category_id: "2",
        author_id: "admin1",
        status: "published",
        published_at: "2024-01-01T00:00:00Z",
        meta_title: null,
        meta_description: null,
        tags: ["meal-planning", "getting-started", "ai"],
        view_count: 150,
        helpful_count: 12,
        not_helpful_count: 2,
        featured: true,
        sort_order: 0,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        category_name: "Meal Planning",
        category_slug: "meal-planning",
        author_name: "Support Team",
        author_email: "<EMAIL>",
        helpfulness_ratio: 0.86,
      },
    ];
  }

  private getMockStats(): KBStats {
    return {
      total_articles: 25,
      published_articles: 20,
      draft_articles: 5,
      total_categories: 6,
      total_views: 1250,
      total_feedback: 85,
      avg_helpfulness: 0.82,
      popular_articles: [],
      recent_articles: [],
    };
  }
}

export const knowledgeBaseService = new KnowledgeBaseService();

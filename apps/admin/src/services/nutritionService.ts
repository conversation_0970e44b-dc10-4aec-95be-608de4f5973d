import { supabaseAdmin } from "@/lib/supabase";

// Helper function to check if Supabase is properly configured
function isSupabaseConfigured(): boolean {
  return !!(
    process.env.NEXT_PUBLIC_SUPABASE_URL &&
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );
}

// Image upload interface
export interface ImageUploadResult {
  url: string;
  path: string;
}

// Enhanced Recipe interface matching database schema
export interface Recipe {
  id: string;
  name: string;
  description?: string;
  instructions?: Record<string, unknown>; // JSONB
  ingredients?: Record<string, unknown>; // JSONB

  // Timing
  prep_time_minutes?: number;
  cook_time_minutes?: number;
  total_time_minutes?: number;

  // Basic nutrition (per serving)
  calories_per_serving?: number;
  protein_grams?: number;
  carbs_grams?: number;
  fat_grams?: number;
  fiber_grams?: number;
  sugar_grams?: number;
  sodium_mg?: number;

  // Recipe metadata
  cuisine_type?: string;
  difficulty_level?: "beginner" | "intermediate" | "advanced";
  servings?: number;
  serving_size?: string;

  // Dietary and tags
  dietary_tags?: string[];
  allergen_info?: string[];
  health_benefits?: string[];

  // Cost and budget
  estimated_cost_per_serving?: number;
  cost_category?: "budget" | "moderate" | "premium";

  // Media and Attribution
  image_url?: string;
  video_url?: string;
  animation_url?: string;

  // Status and metadata
  is_active?: boolean;
  is_featured?: boolean;
  is_human_reviewed?: boolean;

  // Timestamps
  created_at?: string;
  updated_at?: string;
}

export interface RecipeFilters {
  search?: string;
  cuisine_type?: string;
  difficulty_level?: string;
  dietary_tags?: string[];
  max_prep_time?: number;
  max_calories?: number;
  cost_category?: string;
  is_active?: boolean;
}

export interface RecipeStats {
  total_recipes: number;
  active_recipes: number;
  categories: number;
  avg_calories: number;
  avg_prep_time: number;
}

class NutritionService {
  /**
   * Get all recipes with optional filtering
   */
  async getRecipes(filters: RecipeFilters = {}): Promise<Recipe[]> {
    // Return empty array if Supabase is not configured (e.g., during build)
    if (!isSupabaseConfigured()) {
      return [];
    }

    try {
      let query = supabaseAdmin
        .from("recipes")
        .select(
          `
          id,
          name,
          description,
          instructions,
          ingredients,
          prep_time_minutes,
          cook_time_minutes,
          total_time_minutes,
          calories_per_serving,
          protein_grams,
          carbs_grams,
          fat_grams,
          fiber_grams,
          cuisine_type,
          difficulty_level,
          servings,
          dietary_tags,
          allergen_info,
          estimated_cost_per_serving,
          cost_category,
          image_url,
          video_url,
          animation_url,
          is_active,
          is_featured,
          is_human_reviewed,
          created_at,
          updated_at
        `,
        )
        .order("created_at", { ascending: false });

      // Apply filters
      if (filters.search) {
        query = query.or(
          `name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`,
        );
      }

      if (filters.cuisine_type) {
        query = query.eq("cuisine_type", filters.cuisine_type);
      }

      if (filters.difficulty_level) {
        query = query.eq("difficulty_level", filters.difficulty_level);
      }

      if (filters.max_prep_time) {
        query = query.lte("prep_time_minutes", filters.max_prep_time);
      }

      if (filters.max_calories) {
        query = query.lte("calories_per_serving", filters.max_calories);
      }

      if (filters.cost_category) {
        query = query.eq("cost_category", filters.cost_category);
      }

      if (filters.is_active !== undefined) {
        query = query.eq("is_active", filters.is_active);
      }

      if (filters.dietary_tags && filters.dietary_tags.length > 0) {
        query = query.overlaps("dietary_tags", filters.dietary_tags);
      }

      const { data, error } = await query;

      if (error) {
        console.error("Error fetching recipes:", error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error("Error in getRecipes:", error);
      throw error;
    }
  }

  /**
   * Get recipe statistics for dashboard
   */
  async getRecipeStats(): Promise<RecipeStats> {
    // Return default stats if Supabase is not configured (e.g., during build)
    if (!isSupabaseConfigured()) {
      return {
        total_recipes: 0,
        active_recipes: 0,
        categories: 0,
        avg_calories: 0,
        avg_prep_time: 0,
      };
    }

    try {
      const { data, error } = await supabaseAdmin.from("recipes").select(`
          id,
          calories_per_serving,
          prep_time_minutes,
          cuisine_type,
          is_active
        `);

      if (error) {
        console.error("Error fetching recipe stats:", error);
        throw error;
      }

      const recipes = data || [];
      const activeRecipes = recipes.filter((r) => r.is_active);
      const cuisineTypes = new Set(
        recipes.map((r) => r.cuisine_type).filter(Boolean),
      );

      const totalCalories = recipes
        .filter((r) => r.calories_per_serving)
        .reduce((sum, r) => sum + (r.calories_per_serving || 0), 0);

      const totalPrepTime = recipes
        .filter((r) => r.prep_time_minutes)
        .reduce((sum, r) => sum + (r.prep_time_minutes || 0), 0);

      const recipesWithCalories = recipes.filter(
        (r) => r.calories_per_serving,
      ).length;
      const recipesWithPrepTime = recipes.filter(
        (r) => r.prep_time_minutes,
      ).length;

      return {
        total_recipes: recipes.length,
        active_recipes: activeRecipes.length,
        categories: cuisineTypes.size,
        avg_calories:
          recipesWithCalories > 0
            ? Math.round(totalCalories / recipesWithCalories)
            : 0,
        avg_prep_time:
          recipesWithPrepTime > 0
            ? Math.round(totalPrepTime / recipesWithPrepTime)
            : 0,
      };
    } catch (error) {
      console.error("Error in getRecipeStats:", error);
      throw error;
    }
  }

  /**
   * Get a single recipe by ID
   */
  async getRecipe(id: string): Promise<Recipe | null> {
    try {
      const { data, error } = await supabaseAdmin
        .from("recipes")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return null; // Recipe not found
        }
        console.error("Error fetching recipe:", error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error("Error in getRecipe:", error);
      throw error;
    }
  }

  /**
   * Create a new recipe
   */
  async createRecipe(
    recipe: Omit<Recipe, "id" | "created_at" | "updated_at">,
  ): Promise<Recipe> {
    try {
      const { data, error } = await supabaseAdmin
        .from("recipes")
        .insert([
          {
            ...recipe,
            is_active: recipe.is_active ?? true,
            is_human_reviewed: recipe.is_human_reviewed ?? false,
          },
        ])
        .select()
        .single();

      if (error) {
        console.error("Error creating recipe:", error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error("Error in createRecipe:", error);
      throw error;
    }
  }

  /**
   * Update an existing recipe
   */
  async updateRecipe(id: string, updates: Partial<Recipe>): Promise<Recipe> {
    try {
      const { data, error } = await supabaseAdmin
        .from("recipes")
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .select()
        .single();

      if (error) {
        console.error("Error updating recipe:", error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error("Error in updateRecipe:", error);
      throw error;
    }
  }

  /**
   * Delete a recipe
   */
  async deleteRecipe(id: string): Promise<void> {
    try {
      const { error } = await supabaseAdmin
        .from("recipes")
        .delete()
        .eq("id", id);

      if (error) {
        console.error("Error deleting recipe:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error in deleteRecipe:", error);
      throw error;
    }
  }

  /**
   * Toggle recipe active status
   */
  async toggleRecipeStatus(id: string): Promise<Recipe> {
    try {
      // First get current status
      const recipe = await this.getRecipe(id);
      if (!recipe) {
        throw new Error("Recipe not found");
      }

      // Toggle the status
      return await this.updateRecipe(id, {
        is_active: !recipe.is_active,
      });
    } catch (error) {
      console.error("Error in toggleRecipeStatus:", error);
      throw error;
    }
  }

  /**
   * Get unique cuisine types for filtering
   */
  async getCuisineTypes(): Promise<string[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from("recipes")
        .select("cuisine_type")
        .not("cuisine_type", "is", null);

      if (error) {
        console.error("Error fetching cuisine types:", error);
        throw error;
      }

      const cuisineTypes = [...new Set(data.map((item) => item.cuisine_type))];
      return cuisineTypes.filter(Boolean).sort();
    } catch (error) {
      console.error("Error in getCuisineTypes:", error);
      throw error;
    }
  }

  /**
   * Get unique dietary tags for filtering
   */
  async getDietaryTags(): Promise<string[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from("recipes")
        .select("dietary_tags")
        .not("dietary_tags", "is", null);

      if (error) {
        console.error("Error fetching dietary tags:", error);
        throw error;
      }

      const allTags = data.flatMap((item) => item.dietary_tags || []);
      const uniqueTags = [...new Set(allTags)];
      return uniqueTags.sort();
    } catch (error) {
      console.error("Error in getDietaryTags:", error);
      throw error;
    }
  }

  /**
   * Upload recipe image to Supabase Storage
   */
  async uploadRecipeImage(
    file: File,
    recipeId?: string,
  ): Promise<ImageUploadResult> {
    if (!isSupabaseConfigured()) {
      throw new Error("Supabase is not configured");
    }

    try {
      // Generate unique filename
      const fileExt = file.name.split(".").pop();
      const fileName = `${recipeId || Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `recipe-images/${fileName}`;

      // Upload file to Supabase Storage
      const { error } = await supabaseAdmin.storage
        .from("recipe-images")
        .upload(filePath, file, {
          cacheControl: "3600",
          upsert: false,
        });

      if (error) {
        console.error("Error uploading image:", error);
        throw error;
      }

      // Get public URL
      const { data: urlData } = supabaseAdmin.storage
        .from("recipe-images")
        .getPublicUrl(filePath);

      return {
        url: urlData.publicUrl,
        path: filePath,
      };
    } catch (error) {
      console.error("Error in uploadRecipeImage:", error);
      throw error;
    }
  }

  /**
   * Delete recipe image from Supabase Storage
   */
  async deleteRecipeImage(imagePath: string): Promise<void> {
    if (!isSupabaseConfigured()) {
      return;
    }

    try {
      const { error } = await supabaseAdmin.storage
        .from("recipe-images")
        .remove([imagePath]);

      if (error) {
        console.error("Error deleting image:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error in deleteRecipeImage:", error);
      throw error;
    }
  }

  /**
   * Create recipe with image upload
   */
  async createRecipeWithImage(
    recipe: Omit<Recipe, "id" | "created_at" | "updated_at" | "image_url">,
    imageFile?: File,
  ): Promise<Recipe> {
    try {
      let imageUrl: string | undefined;

      // Upload image first if provided
      if (imageFile) {
        const uploadResult = await this.uploadRecipeImage(imageFile);
        imageUrl = uploadResult.url;
      }

      // Create recipe with image URL
      const recipeData = {
        ...recipe,
        image_url: imageUrl,
      };

      return await this.createRecipe(recipeData);
    } catch (error) {
      console.error("Error in createRecipeWithImage:", error);
      throw error;
    }
  }
}

export const nutritionService = new NutritionService();

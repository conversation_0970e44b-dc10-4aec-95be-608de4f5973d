import { nutritionService, Recipe, RecipeFilters } from "../nutritionService";

// Mock Supabase
jest.mock("@/lib/supabase", () => ({
  supabaseAdmin: {
    from: jest.fn(),
  },
}));

// Mock data
const mockRecipe: Recipe = {
  id: "1",
  name: "Test Recipe",
  description: "A test recipe",
  instructions: { steps: ["Test instructions"] },
  ingredients: { items: ["ingredient1", "ingredient2"] },
  prep_time_minutes: 15,
  cook_time_minutes: 30,
  total_time_minutes: 45,
  calories_per_serving: 350,
  protein_grams: 25,
  carbs_grams: 40,
  fat_grams: 10,
  cuisine_type: "italian",
  difficulty_level: "intermediate",
  servings: 4,
  dietary_tags: ["vegetarian", "high-protein"],
  is_active: true,
  is_featured: false,
  is_human_reviewed: true,
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
};

const mockRecipes: Recipe[] = [
  mockRecipe,
  {
    ...mockRecipe,
    id: "2",
    name: "Another Recipe",
    cuisine_type: "mexican",
    difficulty_level: "beginner",
    calories_per_serving: 450,
    is_active: false,
  },
];

// Import the mocked supabase
import { supabaseAdmin } from "@/lib/supabase";

const mockSupabaseAdmin = supabaseAdmin as jest.Mocked<typeof supabaseAdmin>;

describe("NutritionService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getRecipes", () => {
    it("should fetch all recipes without filters", async () => {
      const mockQuery = {
        url: "",
        headers: {},
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.order.mockResolvedValue({ data: mockRecipes, error: null });

      const result = await nutritionService.getRecipes();

      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith("recipes");
      expect(mockQuery.select).toHaveBeenCalledWith(
        expect.stringContaining("id,name,description"),
      );
      expect(mockQuery.order).toHaveBeenCalledWith("created_at", {
        ascending: false,
      });
      expect(result).toEqual(mockRecipes);
    });

    it("should apply search filter", async () => {
      const mockQuery = {
        url: "",
        headers: {},
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        or: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.or.mockResolvedValue({ data: [mockRecipe], error: null });

      const filters: RecipeFilters = { search: "test" };
      const result = await nutritionService.getRecipes(filters);

      expect(mockQuery.or).toHaveBeenCalledWith(
        "name.ilike.%test%,description.ilike.%test%",
      );
      expect(result).toEqual([mockRecipe]);
    });

    it("should apply cuisine type filter", async () => {
      const mockQuery = {
        url: "",
        headers: {},
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.eq.mockResolvedValue({ data: [mockRecipe], error: null });

      const filters: RecipeFilters = { cuisine_type: "italian" };
      await nutritionService.getRecipes(filters);

      expect(mockQuery.eq).toHaveBeenCalledWith("cuisine_type", "italian");
    });

    it("should apply multiple filters", async () => {
      const mockQuery = {
        url: "",
        headers: {},
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        or: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        lte: jest.fn().mockReturnThis(),
        overlaps: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.overlaps.mockResolvedValue({ data: [mockRecipe], error: null });

      const filters: RecipeFilters = {
        search: "test",
        cuisine_type: "italian",
        difficulty_level: "intermediate",
        max_prep_time: 30,
        max_calories: 400,
        dietary_tags: ["vegetarian"],
        is_active: true,
      };

      await nutritionService.getRecipes(filters);

      expect(mockQuery.or).toHaveBeenCalledWith(
        "name.ilike.%test%,description.ilike.%test%",
      );
      expect(mockQuery.eq).toHaveBeenCalledWith("cuisine_type", "italian");
      expect(mockQuery.eq).toHaveBeenCalledWith(
        "difficulty_level",
        "intermediate",
      );
      expect(mockQuery.lte).toHaveBeenCalledWith("prep_time_minutes", 30);
      expect(mockQuery.lte).toHaveBeenCalledWith("calories_per_serving", 400);
      expect(mockQuery.eq).toHaveBeenCalledWith("is_active", true);
      expect(mockQuery.overlaps).toHaveBeenCalledWith("dietary_tags", [
        "vegetarian",
      ]);
    });

    it("should handle database errors", async () => {
      const mockQuery = {
        url: "",
        headers: {},
        select: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.order.mockResolvedValue({
        data: null,
        error: { message: "Database error" },
      });

      await expect(nutritionService.getRecipes()).rejects.toThrow();
    });
  });

  describe("getRecipeStats", () => {
    it("should calculate recipe statistics correctly", async () => {
      const mockQuery = {
        url: "",
        headers: {},
        select: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.select.mockResolvedValue({ data: mockRecipes, error: null });

      const result = await nutritionService.getRecipeStats();

      expect(result).toEqual({
        total_recipes: 2,
        active_recipes: 1,
        categories: 2, // italian and mexican
        avg_calories: 400, // (350 + 450) / 2
        avg_prep_time: 15, // both have 15 min prep time
      });
    });

    it("should handle empty recipe data", async () => {
      const mockQuery = {
        url: "",
        headers: {},
        select: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.select.mockResolvedValue({ data: [], error: null });

      const result = await nutritionService.getRecipeStats();

      expect(result).toEqual({
        total_recipes: 0,
        active_recipes: 0,
        categories: 0,
        avg_calories: 0,
        avg_prep_time: 0,
      });
    });
  });

  describe("getRecipe", () => {
    it("should fetch a single recipe by ID", async () => {
      const mockQuery = {
        url: "",
        headers: {},
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.single.mockResolvedValue({ data: mockRecipe, error: null });

      const result = await nutritionService.getRecipe("1");

      expect(mockQuery.select).toHaveBeenCalledWith("*");
      expect(mockQuery.eq).toHaveBeenCalledWith("id", "1");
      expect(result).toEqual(mockRecipe);
    });

    it("should return null when recipe not found", async () => {
      const mockQuery = {
        url: "",
        headers: {},
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.single.mockResolvedValue({
        data: null,
        error: { code: "PGRST116" },
      });

      const result = await nutritionService.getRecipe("999");

      expect(result).toBeNull();
    });
  });

  describe("createRecipe", () => {
    it("should create a new recipe", async () => {
      const newRecipe = {
        name: "New Recipe",
        description: "A new recipe",
        prep_time_minutes: 20,
      };

      const mockQuery = {
        url: "",
        headers: {},
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.single.mockResolvedValue({
        data: { ...newRecipe, id: "3" },
        error: null,
      });

      const result = await nutritionService.createRecipe(newRecipe);

      expect(mockQuery.insert).toHaveBeenCalledWith([
        {
          ...newRecipe,
          is_active: true,
          is_human_reviewed: false,
        },
      ]);
      expect(result).toEqual({ ...newRecipe, id: "3" });
    });
  });

  describe("updateRecipe", () => {
    it("should update an existing recipe", async () => {
      const updates = { name: "Updated Recipe" };

      const mockQuery = {
        url: "",
        headers: {},
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.single.mockResolvedValue({
        data: { ...mockRecipe, ...updates },
        error: null,
      });

      const result = await nutritionService.updateRecipe("1", updates);

      expect(mockQuery.update).toHaveBeenCalledWith({
        ...updates,
        updated_at: expect.any(String),
      });
      expect(mockQuery.eq).toHaveBeenCalledWith("id", "1");
      expect(result.name).toBe("Updated Recipe");
    });
  });

  describe("deleteRecipe", () => {
    it("should delete a recipe", async () => {
      const mockQuery = {
        url: "",
        headers: {},
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.eq.mockResolvedValue({ error: null });

      await nutritionService.deleteRecipe("1");

      expect(mockQuery.delete).toHaveBeenCalled();
      expect(mockQuery.eq).toHaveBeenCalledWith("id", "1");
    });
  });

  describe("toggleRecipeStatus", () => {
    it("should toggle recipe active status", async () => {
      // Mock getRecipe
      const getRecipeSpy = jest
        .spyOn(nutritionService, "getRecipe")
        .mockResolvedValue(mockRecipe);

      // Mock updateRecipe
      const updateRecipeSpy = jest
        .spyOn(nutritionService, "updateRecipe")
        .mockResolvedValue({ ...mockRecipe, is_active: false });

      const result = await nutritionService.toggleRecipeStatus("1");

      expect(getRecipeSpy).toHaveBeenCalledWith("1");
      expect(updateRecipeSpy).toHaveBeenCalledWith("1", { is_active: false });
      expect(result.is_active).toBe(false);

      getRecipeSpy.mockRestore();
      updateRecipeSpy.mockRestore();
    });

    it("should throw error when recipe not found", async () => {
      const getRecipeSpy = jest
        .spyOn(nutritionService, "getRecipe")
        .mockResolvedValue(null);

      await expect(nutritionService.toggleRecipeStatus("999")).rejects.toThrow(
        "Recipe not found",
      );

      getRecipeSpy.mockRestore();
    });
  });

  describe("getCuisineTypes", () => {
    it("should return unique cuisine types", async () => {
      const mockQuery = {
        url: "",
        headers: {},
        select: jest.fn().mockReturnThis(),
        not: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.not.mockResolvedValue({
        data: [
          { cuisine_type: "italian" },
          { cuisine_type: "mexican" },
          { cuisine_type: "italian" }, // duplicate
        ],
        error: null,
      });

      const result = await nutritionService.getCuisineTypes();

      expect(result).toEqual(["italian", "mexican"]);
    });
  });

  describe("getDietaryTags", () => {
    it("should return unique dietary tags", async () => {
      const mockQuery = {
        url: "",
        headers: {},
        select: jest.fn().mockReturnThis(),
        not: jest.fn().mockReturnThis(),
      };

      mockSupabaseAdmin.from.mockReturnValue(mockQuery);
      mockQuery.not.mockResolvedValue({
        data: [
          { dietary_tags: ["vegetarian", "high-protein"] },
          { dietary_tags: ["vegan", "low-carb"] },
          { dietary_tags: ["vegetarian"] }, // duplicate
        ],
        error: null,
      });

      const result = await nutritionService.getDietaryTags();

      expect(result).toEqual([
        "high-protein",
        "low-carb",
        "vegan",
        "vegetarian",
      ]);
    });
  });
});

import {
  knowledgeBaseService,
  CreateCategoryData,
  CreateArticleData,
} from "../knowledgeBaseService";

// Mock Supabase
jest.mock("@/lib/supabase", () => ({
  supabaseAdmin: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
          order: jest.fn(() => ({
            order: jest.fn(() => ({
              eq: jest.fn(),
            })),
          })),
        })),
        order: jest.fn(() => ({
          order: jest.fn(),
        })),
        textSearch: jest.fn(() => ({
          eq: jest.fn(() => ({
            limit: jest.fn(),
          })),
        })),
        overlaps: jest.fn(),
        limit: jest.fn(),
        range: jest.fn(),
        count: jest.fn(),
        head: jest.fn(),
      })),
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
      update: jest.fn(() => ({
        eq: jest.fn(),
      })),
      delete: jest.fn(() => ({
        eq: jest.fn(),
      })),
    })),
  },
}));

// Mock environment variables
const originalEnv = process.env;
beforeEach(() => {
  jest.resetModules();
  process.env = { ...originalEnv };
});

afterEach(() => {
  process.env = originalEnv;
});

describe("KnowledgeBaseService", () => {
  describe("Categories", () => {
    describe("getCategories", () => {
      it("should return mock categories when Supabase is not configured", async () => {
        // Remove Supabase config
        delete process.env.NEXT_PUBLIC_SUPABASE_URL;
        delete process.env.SUPABASE_SERVICE_ROLE_KEY;

        const categories = await knowledgeBaseService.getCategories();

        expect(categories).toBeDefined();
        expect(Array.isArray(categories)).toBe(true);
        expect(categories.length).toBeGreaterThan(0);
        expect(categories[0]).toHaveProperty("id");
        expect(categories[0]).toHaveProperty("name");
        expect(categories[0]).toHaveProperty("slug");
      });

      it("should include inactive categories when requested", async () => {
        const categoriesWithInactive =
          await knowledgeBaseService.getCategories(true);
        const categoriesActiveOnly =
          await knowledgeBaseService.getCategories(false);

        expect(categoriesWithInactive).toBeDefined();
        expect(categoriesActiveOnly).toBeDefined();
      });

      it("should include article counts for each category", async () => {
        const categories = await knowledgeBaseService.getCategories();

        categories.forEach((category) => {
          expect(category).toHaveProperty("article_count");
          expect(typeof category.article_count).toBe("number");
        });
      });
    });

    describe("getCategoryById", () => {
      it("should return a category when found", async () => {
        const categories = await knowledgeBaseService.getCategories();
        const firstCategory = categories[0];

        const category = await knowledgeBaseService.getCategoryById(
          firstCategory.id,
        );

        expect(category).toBeDefined();
        expect(category?.id).toBe(firstCategory.id);
      });

      it("should return null when category not found", async () => {
        const category =
          await knowledgeBaseService.getCategoryById("non-existent-id");

        expect(category).toBeNull();
      });
    });

    describe("createCategory", () => {
      it("should create a category with valid data", async () => {
        const categoryData: CreateCategoryData = {
          name: "Test Category",
          description: "Test description",
          slug: "test-category",
          icon: "folder",
          color: "#6B7280",
          sort_order: 0,
        };

        const categoryId =
          await knowledgeBaseService.createCategory(categoryData);

        expect(categoryId).toBeDefined();
        expect(typeof categoryId).toBe("string");
      });

      it("should handle missing optional fields", async () => {
        const categoryData: CreateCategoryData = {
          name: "Minimal Category",
          slug: "minimal-category",
        };

        const categoryId =
          await knowledgeBaseService.createCategory(categoryData);

        expect(categoryId).toBeDefined();
      });
    });

    describe("updateCategory", () => {
      it("should update category successfully", async () => {
        const updateData = {
          name: "Updated Category Name",
          description: "Updated description",
        };

        await expect(
          knowledgeBaseService.updateCategory("test-id", updateData),
        ).resolves.not.toThrow();
      });
    });

    describe("deleteCategory", () => {
      it("should delete category successfully", async () => {
        await expect(
          knowledgeBaseService.deleteCategory("test-id"),
        ).resolves.not.toThrow();
      });
    });
  });

  describe("Articles", () => {
    describe("getArticles", () => {
      it("should return mock articles when Supabase is not configured", async () => {
        // Remove Supabase config
        delete process.env.NEXT_PUBLIC_SUPABASE_URL;
        delete process.env.SUPABASE_SERVICE_ROLE_KEY;

        const articles = await knowledgeBaseService.getArticles();

        expect(articles).toBeDefined();
        expect(Array.isArray(articles)).toBe(true);
        expect(articles.length).toBeGreaterThan(0);
        expect(articles[0]).toHaveProperty("id");
        expect(articles[0]).toHaveProperty("title");
        expect(articles[0]).toHaveProperty("content");
        expect(articles[0]).toHaveProperty("status");
      });

      it("should apply search filters correctly", async () => {
        const filters = {
          search: "meal planning",
          category_id: "test-category",
          status: "published",
          featured: true,
          limit: 10,
          offset: 0,
        };

        const articles = await knowledgeBaseService.getArticles(filters);

        expect(articles).toBeDefined();
        expect(Array.isArray(articles)).toBe(true);
      });

      it("should handle empty filters", async () => {
        const articles = await knowledgeBaseService.getArticles({});

        expect(articles).toBeDefined();
        expect(Array.isArray(articles)).toBe(true);
      });

      it("should include computed fields", async () => {
        const articles = await knowledgeBaseService.getArticles();

        articles.forEach((article) => {
          expect(article).toHaveProperty("helpfulness_ratio");
          expect(typeof article.helpfulness_ratio).toBe("number");
        });
      });
    });

    describe("getArticleById", () => {
      it("should return an article when found", async () => {
        const articles = await knowledgeBaseService.getArticles();
        const firstArticle = articles[0];

        const article = await knowledgeBaseService.getArticleById(
          firstArticle.id,
        );

        expect(article).toBeDefined();
        expect(article?.id).toBe(firstArticle.id);
      });

      it("should return null when article not found", async () => {
        const article =
          await knowledgeBaseService.getArticleById("non-existent-id");

        expect(article).toBeNull();
      });
    });

    describe("createArticle", () => {
      it("should create an article with valid data", async () => {
        const articleData: CreateArticleData = {
          title: "Test Article",
          slug: "test-article",
          content:
            "This is a test article with enough content to meet the minimum requirements for a knowledge base article.",
          excerpt: "Test excerpt",
          category_id: "test-category",
          tags: ["test", "article"],
          featured: false,
          status: "draft",
        };

        const articleId = await knowledgeBaseService.createArticle(articleData);

        expect(articleId).toBeDefined();
        expect(typeof articleId).toBe("string");
      });

      it("should handle missing optional fields", async () => {
        const articleData: CreateArticleData = {
          title: "Minimal Article",
          slug: "minimal-article",
          content:
            "This is a minimal article with just the required fields to test the creation process.",
        };

        const articleId = await knowledgeBaseService.createArticle(articleData);

        expect(articleId).toBeDefined();
      });

      it("should set published_at when status is published", async () => {
        const articleData: CreateArticleData = {
          title: "Published Article",
          slug: "published-article",
          content: "This article will be published immediately upon creation.",
          status: "published",
        };

        const articleId = await knowledgeBaseService.createArticle(articleData);

        expect(articleId).toBeDefined();
      });
    });

    describe("updateArticle", () => {
      it("should update article successfully", async () => {
        const updateData = {
          title: "Updated Article Title",
          content: "Updated content that is long enough to meet requirements.",
          status: "published" as const,
        };

        await expect(
          knowledgeBaseService.updateArticle("test-id", updateData),
        ).resolves.not.toThrow();
      });
    });

    describe("deleteArticle", () => {
      it("should delete article successfully", async () => {
        await expect(
          knowledgeBaseService.deleteArticle("test-id"),
        ).resolves.not.toThrow();
      });
    });
  });

  describe("Search", () => {
    describe("searchArticles", () => {
      it("should return search results with ranking", async () => {
        const results = await knowledgeBaseService.searchArticles(
          "meal planning",
          5,
        );

        expect(results).toBeDefined();
        expect(Array.isArray(results)).toBe(true);

        results.forEach((result) => {
          expect(result).toHaveProperty("article");
          expect(result).toHaveProperty("rank");
          expect(result).toHaveProperty("headline");
          expect(typeof result.rank).toBe("number");
        });
      });

      it("should limit results correctly", async () => {
        const limit = 3;
        const results = await knowledgeBaseService.searchArticles(
          "test",
          limit,
        );

        expect(results.length).toBeLessThanOrEqual(limit);
      });

      it("should handle empty search query", async () => {
        const results = await knowledgeBaseService.searchArticles("", 10);

        expect(results).toBeDefined();
        expect(Array.isArray(results)).toBe(true);
      });
    });
  });

  describe("Statistics", () => {
    describe("getKBStats", () => {
      it("should return comprehensive statistics", async () => {
        const stats = await knowledgeBaseService.getKBStats();

        expect(stats).toBeDefined();
        expect(stats).toHaveProperty("total_articles");
        expect(stats).toHaveProperty("published_articles");
        expect(stats).toHaveProperty("draft_articles");
        expect(stats).toHaveProperty("total_categories");
        expect(stats).toHaveProperty("total_views");
        expect(stats).toHaveProperty("total_feedback");
        expect(stats).toHaveProperty("avg_helpfulness");
        expect(stats).toHaveProperty("popular_articles");
        expect(stats).toHaveProperty("recent_articles");

        // Verify data types
        expect(typeof stats.total_articles).toBe("number");
        expect(typeof stats.avg_helpfulness).toBe("number");
        expect(Array.isArray(stats.popular_articles)).toBe(true);
        expect(Array.isArray(stats.recent_articles)).toBe(true);
      });

      it("should calculate helpfulness ratio correctly", async () => {
        const stats = await knowledgeBaseService.getKBStats();

        expect(stats.avg_helpfulness).toBeGreaterThanOrEqual(0);
        expect(stats.avg_helpfulness).toBeLessThanOrEqual(1);
      });
    });
  });

  describe("Feedback", () => {
    describe("recordFeedback", () => {
      it("should record helpful feedback", async () => {
        await expect(
          knowledgeBaseService.recordFeedback(
            "test-article-id",
            true,
            "This article was very helpful!",
            "test-user-id",
            "test-session-id",
          ),
        ).resolves.not.toThrow();
      });

      it("should record not helpful feedback", async () => {
        await expect(
          knowledgeBaseService.recordFeedback(
            "test-article-id",
            false,
            "This article needs more detail.",
            "test-user-id",
            "test-session-id",
          ),
        ).resolves.not.toThrow();
      });

      it("should handle anonymous feedback", async () => {
        await expect(
          knowledgeBaseService.recordFeedback(
            "test-article-id",
            true,
            undefined,
            undefined,
            "anonymous-session-id",
          ),
        ).resolves.not.toThrow();
      });

      it("should handle feedback without text", async () => {
        await expect(
          knowledgeBaseService.recordFeedback(
            "test-article-id",
            true,
            undefined,
            "test-user-id",
          ),
        ).resolves.not.toThrow();
      });
    });
  });

  describe("Error Handling", () => {
    it("should handle network errors gracefully", async () => {
      // This test verifies that the service doesn't crash on network errors
      // In mock mode, it should always return mock data
      const categories = await knowledgeBaseService.getCategories();
      const articles = await knowledgeBaseService.getArticles();
      const stats = await knowledgeBaseService.getKBStats();

      expect(categories).toBeDefined();
      expect(articles).toBeDefined();
      expect(stats).toBeDefined();
    });

    it("should validate required fields for category creation", async () => {
      const invalidCategoryData = {
        name: "",
        slug: "",
      } as CreateCategoryData;

      // In mock mode, this should still work, but in real mode it would validate
      const categoryId =
        await knowledgeBaseService.createCategory(invalidCategoryData);
      expect(categoryId).toBeDefined();
    });

    it("should validate required fields for article creation", async () => {
      const invalidArticleData = {
        title: "",
        slug: "",
        content: "",
      } as CreateArticleData;

      // In mock mode, this should still work, but in real mode it would validate
      const articleId =
        await knowledgeBaseService.createArticle(invalidArticleData);
      expect(articleId).toBeDefined();
    });
  });

  describe("Data Validation", () => {
    it("should handle special characters in slugs", async () => {
      const categoryData: CreateCategoryData = {
        name: "Test Category with Special Characters!@#",
        slug: "test-category-special-chars",
      };

      const categoryId =
        await knowledgeBaseService.createCategory(categoryData);
      expect(categoryId).toBeDefined();
    });

    it("should handle long content in articles", async () => {
      const longContent = "A".repeat(10000); // Very long content
      const articleData: CreateArticleData = {
        title: "Long Article",
        slug: "long-article",
        content: longContent,
      };

      const articleId = await knowledgeBaseService.createArticle(articleData);
      expect(articleId).toBeDefined();
    });

    it("should handle empty tag arrays", async () => {
      const articleData: CreateArticleData = {
        title: "Article with Empty Tags",
        slug: "article-empty-tags",
        content: "This article has an empty tags array.",
        tags: [],
      };

      const articleId = await knowledgeBaseService.createArticle(articleData);
      expect(articleId).toBeDefined();
    });

    it("should handle articles without categories", async () => {
      const articleData: CreateArticleData = {
        title: "Uncategorized Article",
        slug: "uncategorized-article",
        content: "This article has no category assigned.",
        category_id: undefined,
      };

      const articleId = await knowledgeBaseService.createArticle(articleData);
      expect(articleId).toBeDefined();
    });
  });

  describe("Mock Data Quality", () => {
    it("should provide realistic mock categories", async () => {
      const categories = await knowledgeBaseService.getCategories();

      // Verify PlateMotion-specific categories exist
      const categoryNames = categories.map((c) => c.name);
      expect(categoryNames).toContain("Getting Started");
      expect(categoryNames).toContain("Meal Planning");

      // Verify category structure
      categories.forEach((category) => {
        expect(category.slug).toMatch(/^[a-z0-9-]+$/);
        expect(category.color).toMatch(/^#[0-9A-Fa-f]{6}$/);
        expect(typeof category.sort_order).toBe("number");
        expect(typeof category.is_active).toBe("boolean");
      });
    });

    it("should provide realistic mock articles", async () => {
      const articles = await knowledgeBaseService.getArticles();

      // Verify article structure
      articles.forEach((article) => {
        expect(article.slug).toMatch(/^[a-z0-9-]+$/);
        expect(["draft", "published", "archived"]).toContain(article.status);
        expect(typeof article.view_count).toBe("number");
        expect(typeof article.helpful_count).toBe("number");
        expect(typeof article.not_helpful_count).toBe("number");
        expect(Array.isArray(article.tags)).toBe(true);
      });
    });

    it("should provide realistic mock statistics", async () => {
      const stats = await knowledgeBaseService.getKBStats();

      expect(stats.total_articles).toBeGreaterThanOrEqual(0);
      expect(stats.published_articles).toBeLessThanOrEqual(
        stats.total_articles,
      );
      expect(stats.draft_articles).toBeLessThanOrEqual(stats.total_articles);
      expect(stats.avg_helpfulness).toBeGreaterThanOrEqual(0);
      expect(stats.avg_helpfulness).toBeLessThanOrEqual(1);
    });
  });
});

import { supabaseAdmin } from "@/lib/supabase";

// Types
export interface SupportTicket {
  id: string;
  user_id: string;
  title: string;
  description: string | null;
  category: string;
  priority: "low" | "medium" | "high" | "urgent";
  status: "open" | "in_progress" | "waiting_user" | "resolved" | "closed";
  assigned_to: string | null;
  created_at: string;
  updated_at: string;
  resolved_at: string | null;
  closed_at: string | null;
  first_response_at: string | null;
  sla_breach: boolean;
  source: "web" | "mobile" | "email";
  tags: string[];
  internal_notes: string | null;

  // Joined fields
  user_name?: string;
  user_email?: string;
  assigned_agent_name?: string;
}

export interface SupportMessage {
  id: string;
  ticket_id: string;
  sender_id: string;
  message: string;
  message_type: "text" | "image" | "file" | "system";
  is_admin: boolean;
  is_internal: boolean;
  attachments: Array<{ url: string; name?: string; type?: string }>;
  created_at: string;
  updated_at: string;
  read_at: string | null;

  // Joined fields
  sender_name?: string;
  sender_email?: string;
}

export interface TicketStats {
  total_tickets: number;
  open_tickets: number;
  in_progress_tickets: number;
  waiting_user_tickets: number;
  resolved_tickets: number;
  closed_tickets: number;
  resolved_today: number;
  avg_response_time: number; // in minutes
  sla_breaches: number;
  tickets_by_priority: {
    urgent: number;
    high: number;
    medium: number;
    low: number;
  };
  tickets_by_category: {
    [key: string]: number;
  };
}

export interface TicketFilters {
  search?: string;
  status?: string;
  priority?: string;
  category?: string;
  assigned_to?: string;
  user_id?: string;
  sla_breach?: boolean;
  date_from?: string;
  date_to?: string;
  limit?: number;
  offset?: number;
}

export interface CreateTicketData {
  user_id: string;
  title: string;
  description: string;
  category?: string;
  priority?: "low" | "medium" | "high" | "urgent";
  source?: "web" | "mobile" | "email";
  tags?: string[];
}

export interface UpdateTicketData {
  title?: string;
  description?: string;
  category?: string;
  priority?: "low" | "medium" | "high" | "urgent";
  status?: "open" | "in_progress" | "waiting_user" | "resolved" | "closed";
  assigned_to?: string | null;
  tags?: string[];
  internal_notes?: string;
}

// Helper function to check if Supabase is properly configured
function isSupabaseConfigured(): boolean {
  return !!(
    process.env.NEXT_PUBLIC_SUPABASE_URL &&
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );
}

class SupportService {
  /**
   * Get support tickets with filtering and pagination
   */
  async getTickets(filters: TicketFilters = {}): Promise<SupportTicket[]> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, returning mock data");
      return this.getMockTickets();
    }

    try {
      let query = supabaseAdmin
        .from("support_tickets")
        .select(
          `
          id,
          user_id,
          title,
          description,
          category,
          priority,
          status,
          assigned_to,
          created_at,
          updated_at,
          resolved_at,
          closed_at,
          first_response_at,
          sla_breach,
          source,
          tags,
          internal_notes,
          profiles!support_tickets_user_id_fkey (
            full_name,
            email
          ),
          assigned_agent:profiles!support_tickets_assigned_to_fkey (
            full_name,
            email
          )
        `,
        )
        .order("created_at", { ascending: false });

      // Apply filters
      if (filters.search) {
        query = query.or(
          `title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`,
        );
      }

      if (filters.status && filters.status !== "all") {
        query = query.eq("status", filters.status);
      }

      if (filters.priority && filters.priority !== "all") {
        query = query.eq("priority", filters.priority);
      }

      if (filters.category) {
        query = query.eq("category", filters.category);
      }

      if (filters.assigned_to) {
        query = query.eq("assigned_to", filters.assigned_to);
      }

      if (filters.user_id) {
        query = query.eq("user_id", filters.user_id);
      }

      if (filters.sla_breach !== undefined) {
        query = query.eq("sla_breach", filters.sla_breach);
      }

      if (filters.date_from) {
        query = query.gte("created_at", filters.date_from);
      }

      if (filters.date_to) {
        query = query.lte("created_at", filters.date_to);
      }

      // Pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(
          filters.offset,
          filters.offset + (filters.limit || 50) - 1,
        );
      }

      const { data, error } = await query;

      if (error) {
        console.error("Error fetching tickets:", error);
        throw error;
      }

      // Transform the data to include joined fields
      return (data || []).map(
        (
          ticket: SupportTicket & {
            profiles?: {
              full_name?: string | null;
              email?: string | null;
            } | null;
            assigned_agent?: { full_name?: string | null } | null;
          },
        ) => ({
          ...ticket,
          user_name: ticket.profiles?.full_name || "Unknown User",
          user_email: ticket.profiles?.email || "",
          assigned_agent_name: ticket.assigned_agent?.full_name || null,
        }),
      );
    } catch (error) {
      console.error("Error in getTickets:", error);
      throw error;
    }
  }

  /**
   * Get a single ticket by ID
   */
  async getTicketById(ticketId: string): Promise<SupportTicket | null> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, returning mock data");
      const mockTickets = this.getMockTickets();
      return mockTickets.find((t) => t.id === ticketId) || null;
    }

    try {
      const { data, error } = await supabaseAdmin
        .from("support_tickets")
        .select(
          `
          id,
          user_id,
          title,
          description,
          category,
          priority,
          status,
          assigned_to,
          created_at,
          updated_at,
          resolved_at,
          closed_at,
          first_response_at,
          sla_breach,
          source,
          tags,
          internal_notes,
          profiles!support_tickets_user_id_fkey (
            full_name,
            email
          ),
          assigned_agent:profiles!support_tickets_assigned_to_fkey (
            full_name,
            email
          )
        `,
        )
        .eq("id", ticketId)
        .single();

      if (error) {
        console.error("Error fetching ticket:", error);
        throw error;
      }

      if (!data) return null;

      return {
        ...data,
        user_name: data.profiles?.full_name || "Unknown User",
        user_email: data.profiles?.email || "",
        assigned_agent_name: data.assigned_agent?.full_name || null,
      };
    } catch (error) {
      console.error("Error in getTicketById:", error);
      throw error;
    }
  }

  /**
   * Create a new support ticket
   */
  async createTicket(ticketData: CreateTicketData): Promise<string> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, returning mock ID");
      return "mock-ticket-id";
    }

    try {
      const { data, error } = await supabaseAdmin.rpc("create_support_ticket", {
        p_user_id: ticketData.user_id,
        p_title: ticketData.title,
        p_description: ticketData.description,
        p_category: ticketData.category || "general",
        p_priority: ticketData.priority || "medium",
      });

      if (error) {
        console.error("Error creating ticket:", error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error("Error in createTicket:", error);
      throw error;
    }
  }

  /**
   * Update an existing ticket
   */
  async updateTicket(
    ticketId: string,
    updates: UpdateTicketData,
  ): Promise<void> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, skipping update");
      return;
    }

    try {
      const updateData: Record<string, unknown> = { ...updates };

      // Set timestamps based on status changes
      if (updates.status === "resolved") {
        updateData.resolved_at = new Date().toISOString();
      }
      if (updates.status === "closed") {
        updateData.closed_at = new Date().toISOString();
      }

      const { error } = await supabaseAdmin
        .from("support_tickets")
        .update(updateData)
        .eq("id", ticketId);

      if (error) {
        console.error("Error updating ticket:", error);
        throw error;
      }

      // Log the update in ticket history
      await this.logTicketHistory(ticketId, "updated", updates);
    } catch (error) {
      console.error("Error in updateTicket:", error);
      throw error;
    }
  }

  /**
   * Get ticket statistics
   */
  async getTicketStats(): Promise<TicketStats> {
    if (!isSupabaseConfigured()) {
      console.warn("Supabase not configured, returning mock stats");
      return this.getMockStats();
    }

    try {
      const { data, error } = await supabaseAdmin
        .from("support_tickets")
        .select("status, priority, category, created_at, first_response_at");

      if (error) {
        console.error("Error fetching ticket stats:", error);
        throw error;
      }

      const tickets = data || [];
      const today = new Date().toISOString().split("T")[0];

      const stats: TicketStats = {
        total_tickets: tickets.length,
        open_tickets: tickets.filter((t) => t.status === "open").length,
        in_progress_tickets: tickets.filter((t) => t.status === "in_progress")
          .length,
        waiting_user_tickets: tickets.filter((t) => t.status === "waiting_user")
          .length,
        resolved_tickets: tickets.filter((t) => t.status === "resolved").length,
        closed_tickets: tickets.filter((t) => t.status === "closed").length,
        resolved_today: tickets.filter(
          (t) => t.status === "resolved" && t.created_at.startsWith(today),
        ).length,
        avg_response_time: this.calculateAverageResponseTime(tickets),
        sla_breaches: tickets.filter(
          (t) => t.status === "open" || t.status === "in_progress",
        ).length, // Simplified
        tickets_by_priority: {
          urgent: tickets.filter((t) => t.priority === "urgent").length,
          high: tickets.filter((t) => t.priority === "high").length,
          medium: tickets.filter((t) => t.priority === "medium").length,
          low: tickets.filter((t) => t.priority === "low").length,
        },
        tickets_by_category: this.groupByCategory(tickets),
      };

      return stats;
    } catch (error) {
      console.error("Error in getTicketStats:", error);
      throw error;
    }
  }

  /**
   * Log ticket history for audit trail
   */
  private async logTicketHistory(
    ticketId: string,
    action: string,
    details: UpdateTicketData,
  ): Promise<void> {
    if (!isSupabaseConfigured()) return;

    try {
      await supabaseAdmin.from("support_ticket_history").insert({
        ticket_id: ticketId,
        action,
        new_value: JSON.stringify(details),
        // admin_user_id would be set from auth context
      });
    } catch (error) {
      console.error("Error logging ticket history:", error);
      // Don't throw here as it's not critical
    }
  }

  /**
   * Helper function to calculate average response time
   */
  private calculateAverageResponseTime(
    tickets: Array<Pick<SupportTicket, "created_at" | "first_response_at">>,
  ): number {
    const ticketsWithResponse = tickets.filter((t) => !!t.first_response_at);
    if (ticketsWithResponse.length === 0) return 0;

    const totalMinutes = ticketsWithResponse.reduce((sum, ticket) => {
      const created = new Date(ticket.created_at);
      const responded = new Date(ticket.first_response_at);
      const diffMinutes =
        (responded.getTime() - created.getTime()) / (1000 * 60);
      return sum + diffMinutes;
    }, 0);

    return Math.round(totalMinutes / ticketsWithResponse.length);
  }

  /**
   * Helper function to group tickets by category
   */
  private groupByCategory(
    tickets: Array<Pick<SupportTicket, "category">>,
  ): Record<string, number> {
    return tickets.reduce((acc, ticket) => {
      const category = ticket.category || "general";
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * Mock data for development/testing
   */
  private getMockTickets(): SupportTicket[] {
    return [
      {
        id: "1",
        user_id: "user1",
        title: "Unable to log into mobile app",
        description:
          "I'm getting an error when trying to log in with my email and password.",
        category: "technical",
        priority: "high",
        status: "open",
        assigned_to: null,
        created_at: "2024-01-15T10:30:00Z",
        updated_at: "2024-01-15T10:30:00Z",
        resolved_at: null,
        closed_at: null,
        first_response_at: null,
        sla_breach: false,
        source: "mobile",
        tags: ["login", "authentication"],
        internal_notes: null,
        user_name: "John Doe",
        user_email: "<EMAIL>",
      },
      {
        id: "2",
        user_id: "user2",
        title: "Meal plan not generating properly",
        description:
          "The AI meal planner is not creating balanced meal plans for my dietary restrictions.",
        category: "content",
        priority: "medium",
        status: "in_progress",
        assigned_to: "agent1",
        created_at: "2024-01-14T15:20:00Z",
        updated_at: "2024-01-15T09:15:00Z",
        resolved_at: null,
        closed_at: null,
        first_response_at: "2024-01-14T16:45:00Z",
        sla_breach: false,
        source: "web",
        tags: ["meal-planning", "ai"],
        internal_notes: "User has gluten intolerance and vegan preferences",
        user_name: "Jane Smith",
        user_email: "<EMAIL>",
        assigned_agent_name: "Support Agent",
      },
    ];
  }

  private getMockStats(): TicketStats {
    return {
      total_tickets: 156,
      open_tickets: 23,
      in_progress_tickets: 12,
      waiting_user_tickets: 5,
      resolved_tickets: 98,
      closed_tickets: 18,
      resolved_today: 8,
      avg_response_time: 45,
      sla_breaches: 3,
      tickets_by_priority: {
        urgent: 5,
        high: 18,
        medium: 89,
        low: 44,
      },
      tickets_by_category: {
        technical: 45,
        billing: 23,
        content: 34,
        general: 32,
        feature_request: 22,
      },
    };
  }
}

export const supportService = new SupportService();

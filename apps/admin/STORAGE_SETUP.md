# Supabase Storage Setup for Recipe Images

This guide explains how to set up Supabase Storage for recipe image uploads in the PlateMotion admin panel.

## Prerequisites

- Supabase project with admin access
- Environment variables configured:
  - `NEXT_PUBLIC_SUPABASE_URL`
  - `SUPABASE_SERVICE_ROLE_KEY`

## Setup Steps

### 1. Automatic Setup (Recommended)

Run the setup script to automatically create the storage bucket:

```bash
cd apps/admin
npx tsx scripts/setup-storage.ts
```

### 2. Manual Setup (Alternative)

If the automatic setup doesn't work, follow these manual steps:

#### A. Create Storage Bucket

1. Go to your Supabase dashboard
2. Navigate to Storage
3. Click "Create bucket"
4. Configure the bucket:
   - **Name**: `recipe-images`
   - **Public**: ✅ Enabled
   - **File size limit**: 5MB (5242880 bytes)
   - **Allowed MIME types**: `image/jpeg`, `image/jpg`, `image/png`, `image/webp`

#### B. Set Up RLS Policies

1. Go to SQL Editor in your Supabase dashboard
2. Run the SQL script from `scripts/setup-storage.sql`

Or manually create these policies:

```sql
-- Enable RLS
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Public read access
CREATE POLICY "Public read access for recipe images" ON storage.objects
FOR SELECT USING (bucket_id = 'recipe-images');

-- Authenticated upload
CREATE POLICY "Authenticated users can upload recipe images" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'recipe-images'
  AND auth.role() = 'authenticated'
);

-- Authenticated update
CREATE POLICY "Authenticated users can update recipe images" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'recipe-images'
  AND auth.role() = 'authenticated'
);

-- Authenticated delete
CREATE POLICY "Authenticated users can delete recipe images" ON storage.objects
FOR DELETE USING (
  bucket_id = 'recipe-images'
  AND auth.role() = 'authenticated'
);
```

## Configuration Details

### File Restrictions

- **Maximum file size**: 5MB
- **Allowed formats**: JPEG, JPG, PNG, WebP
- **Bucket**: `recipe-images`
- **Public access**: Enabled for read operations

### Security

- **Upload/Update/Delete**: Requires authentication
- **Read**: Public access (for displaying images)
- **RLS**: Enabled with appropriate policies

## Testing

After setup, test the functionality:

1. Start the admin panel: `npm run dev`
2. Navigate to the Nutrition section
3. Click "Add New Recipe"
4. Try uploading an image in the Recipe Image section
5. Verify the image appears in the recipe table

## Troubleshooting

### Common Issues

1. **Upload fails with "Bucket not found"**
   - Ensure the bucket name is exactly `recipe-images`
   - Check that the bucket was created successfully

2. **Permission denied errors**
   - Verify RLS policies are set up correctly
   - Check that your service role key has the necessary permissions

3. **File size errors**
   - Ensure files are under 5MB
   - Check that the file format is supported

4. **Images not displaying**
   - Verify the bucket is set to public
   - Check that the image URLs are being stored correctly in the database

### Debug Commands

```bash
# Test storage setup
npx tsx scripts/setup-storage.ts

# Check environment variables
echo $NEXT_PUBLIC_SUPABASE_URL
echo $SUPABASE_SERVICE_ROLE_KEY
```

## File Structure

```
apps/admin/
├── scripts/
│   ├── setup-storage.ts     # Automated setup script
│   └── setup-storage.sql    # Manual SQL setup
├── src/
│   └── services/
│       └── nutritionService.ts  # Image upload methods
└── STORAGE_SETUP.md         # This file
```

## Next Steps

Once storage is set up:

1. ✅ Recipe images can be uploaded via the admin panel
2. ✅ Images are automatically resized and optimized
3. ✅ Public URLs are generated for mobile app consumption
4. ✅ Images are properly integrated with the recipe database

The mobile app can now use these image URLs to display recipe photos in meal plans and nutrition tracking features.

{"name": "mobile", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "@supabase/supabase-js": "^2.53.0", "@tamagui/lucide-icons": "^1.132.15", "@tanstack/react-query": "^5.83.0", "date-fns": "^4.1.0", "expo": "~53.0.20", "expo-status-bar": "~2.2.3", "expo-video": "^2.2.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-haptic-feedback": "^2.3.3", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-svg-charts": "^5.4.0", "react-native-toast-message": "^2.3.3", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.3.0", "react-native-web": "~0.19.13", "tamagui": "^1.132.15", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli": "latest", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.2", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "callsites": "^3.1.0", "jest": "^30.0.5", "jest-expo": "^53.0.9", "typescript": "~5.8.3"}, "private": true}
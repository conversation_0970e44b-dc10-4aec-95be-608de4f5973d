# PlateMotion Mobile App

## Overview

PlateMotion is a comprehensive fitness and nutrition tracking application that
helps users achieve their health and wellness goals. This mobile app provides
features for workout planning, meal tracking, progress monitoring, and more.

## Features

- **Workout Tracking**: Plan and track your daily workouts with exercise videos
- **Nutrition Planning**: Create and follow meal plans with recipe details
- **Progress Monitoring**: Track your weight, measurements, and overall progress
- **Offline Support**: Continue using the app even without an internet
  connection
- **Cross-platform**: Works on both iOS and Android devices

## Offline Support

The PlateMotion mobile app includes robust offline support that allows users to
continue tracking their workouts and meals even when they don't have an internet
connection.

### How Offline Support Works

1. **Automatic Detection**: The app automatically detects when you're offline
2. **Local Storage**: All data is saved locally on your device when offline
3. **Automatic Sync**: When you regain connectivity, data is automatically
   synced to the cloud
4. **Seamless Experience**: You can continue using all app features without
   interruption

### Supported Offline Features

- Log workouts and exercises
- Track meals and nutrition
- Record progress measurements
- View previously loaded workout and meal plans

## Android Deployment

The app is configured for Android deployment with the following settings:

- Package name: `com.platemotion.app`
- Version code: `1`
- Required permissions: `INTERNET`, `ACCESS_NETWORK_STATE`

## Development Setup

1. Install dependencies:

   ```bash
   pnpm install
   ```

2. Start the development server:

   ```bash
   pnpm start
   ```

3. To run on Android:

   ```bash
   pnpm run android
   ```

4. To run on iOS:
   ```bash
   pnpm run ios
   ```

## Testing

The app should be tested on both physical devices and emulators to ensure proper
functionality across different platforms.

### Testing Checklist

- [ ] Offline data logging
- [ ] Data synchronization when coming back online
- [ ] Workout tracking functionality
- [ ] Meal planning and tracking
- [ ] Progress monitoring
- [ ] Navigation between screens
- [ ] Performance on different device sizes

### Testing Scripts

We've included several scripts to help with testing:

- `./test-offline.js` - Simple Node.js script to verify offline functionality
- `./build-android.sh` - Script to build the Android app
- `./run-android.sh` - Script to run the app on an Android emulator

For more detailed testing instructions, see the
[TESTING_PLAN.md](TESTING_PLAN.md) file.

## Architecture

The app follows a modular architecture with the following key components:

- **Screens**: Individual UI screens for different app features
- **Services**: Business logic and API interactions
- **Components**: Reusable UI components
- **Hooks**: Custom React hooks for state management
- **Contexts**: Global state management
- **Utils**: Utility functions and helpers

## Dependencies

Key dependencies include:

- React Native
- Expo
- React Navigation
- Supabase (for backend services)
- Tamagui (for UI components)
- React Native Async Storage (for offline storage)
- React Native NetInfo (for network detection)

## Additional Documentation

- [FINAL_SUMMARY.md](FINAL_SUMMARY.md) - Complete overview of the implementation
- [OFFLINE_IMPLEMENTATION_SUMMARY.md](OFFLINE_IMPLEMENTATION_SUMMARY.md) -
  Detailed technical documentation of offline support
- [TESTING_PLAN.md](TESTING_PLAN.md) - Comprehensive testing plan
- [NEXT_STEPS.md](NEXT_STEPS.md) - Guidance for future development and
  deployment

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

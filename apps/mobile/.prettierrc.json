{"semi": true, "singleQuote": true, "quoteProps": "as-needed", "trailingComma": "es5", "tabWidth": 2, "useTabs": false, "printWidth": 80, "proseWrap": "preserve", "jsxSingleQuote": false, "bracketSameLine": false, "bracketSpacing": true, "arrowParens": "avoid", "endOfLine": "lf", "overrides": [{"files": "*.json", "options": {"printWidth": 200}}, {"files": "*.md", "options": {"proseWrap": "always", "printWidth": 80}}]}
// Simple Node.js script to test offline functionality
const fs = require('fs');

console.log('Testing offline functionality...');

// Check that the offline utility file exists
const fileExists = fs.existsSync('./src/shared/utils/offlineTestUtils.ts');
console.log('Offline utility file exists:', fileExists);

if (fileExists) {
  // Read the file content
  const fileContent = fs.readFileSync(
    './src/shared/utils/offlineTestUtils.ts',
    'utf8'
  );

  // Check that it contains the expected functions
  const hasClearOfflineData = fileContent.includes('clearOfflineData');
  const hasGetOfflineQueue = fileContent.includes('getOfflineQueue');
  const hasSimulateOffline = fileContent.includes('simulateOffline');
  const hasPrintOfflineQueue = fileContent.includes('printOfflineQueue');

  console.log('Contains clearOfflineData function:', hasClearOfflineData);
  console.log('Contains getOfflineQueue function:', hasGetOfflineQueue);
  console.log('Contains simulateOffline function:', hasSimulateOffline);
  console.log('Contains printOfflineQueue function:', hasPrintOfflineQueue);

  if (
    hasClearOfflineData &&
    hasGetOfflineQueue &&
    hasSimulateOffline &&
    hasPrintOfflineQueue
  ) {
    console.log('✅ All offline utility functions are present!');
  } else {
    console.log('❌ Some offline utility functions are missing!');
  }
} else {
  console.log('❌ Offline utility file not found!');
}

console.log('Offline functionality test completed.');

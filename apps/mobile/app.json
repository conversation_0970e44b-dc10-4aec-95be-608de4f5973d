{"expo": {"name": "PlateMotion", "slug": "platemotion", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.platemotion.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.platemotion.app", "versionCode": 1, "permissions": ["INTERNET", "ACCESS_NETWORK_STATE"]}, "web": {"favicon": "./assets/favicon.png"}}}
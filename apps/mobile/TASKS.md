# Tasks

## Completed Tasks

- [x] Fix navigation typing in NutritionScreen
- [x] Add navigation to RecipeDetail screen
- [x] Fix navigation typing in ExerciseScreen
- [x] Add navigation to ExercisePlayer screen
- [x] Add navigation imports to HomeScreen
- [x] Add navigation to DailyWorkoutCard
- [x] Add navigation to DailyNutritionCard
- [x] Add navigation to Greeting component
- [x] Add navigation to WeeklyConsistencyTracker
- [x] Fix navigation errors with id props
- [x] Update React Navigation packages
- [x] Resolve tamagui import issues
- [x] Add video player functionality to ExercisePlayerScreen
- [x] Add grocery list FAB to NutritionScreen
- [x] Implement progress chart visualization in ProgressScreen
- [x] Add comprehensive feedback systems (toast notifications and haptic
      feedback)
- [x] Add loading states and empty states to all screens
- [x] Add offline support
- [x] Prepare Android deployment configurations
- [x] Implement multilingual onboarding flow
- [x] Create localization infrastructure with LanguageContext
- [x] Create English and Spanish translation files
- [x] Create localization utility functions
- [x] Create SplashScreen, LanguageSelectionScreen, IntroScreen, and TermsScreen
      components
- [x] Update navigation structure to include new onboarding screens
- [x] Update App.js to include LanguageProvider
- [x] Update existing onboarding screens to use LanguageContext
- [x] Ensure proper navigation flow between screens
- [x] Implement AI Coach feature with 8 integrated tools
- [x] Replace Progress tab with AI Coach in navigation
- [x] Migrate to feature-based architecture
- [x] Set up Supabase database with comprehensive schema
- [x] Comprehensive codebase analysis and fixes
- [x] Remove duplicate Supabase client definitions
- [x] Fix all service import path inconsistencies
- [x] Move contexts to proper shared/contexts location
- [x] Update all test files and documentation
- [x] Verify zero import/export conflicts
- [x] Confirm app builds successfully
- [x] Add Habits, Mood, and Workout logging to AI Coach quick action
- [x] Hydration unit preference (ml/fl oz) with locale default and normalization
- [x] Unit tests for hydration conversions and habit payload builder
- [x] Fix Jest config to run RN/Expo tests reliably (transform and expo mock)

- [x] Add EditProfileScreen and wire navigation from Profile & Settings
- [x] Update AI Edge Function to include latest profile in prompt
- [x] Fix invalid icons and missing components (bell-cog → bell-ring,
      cloud-off-outline, BugIcon, DownloadIcon, ShieldIcon)

## Remaining Tasks

- [ ] Test AI Coach functionality on device
- [ ] Test onboarding flow
- [ ] Verify language switching functionality
- [ ] Test localization accuracy in both languages
- [ ] Verify transition to main app after onboarding completion
- [ ] Test offline functionality on device
- [ ] Performance testing with new architecture
- [ ] User acceptance testing

## Notes

- Expo Go quick local testing enabled: AI Learning initialization is skipped in
  Expo Go, or when setting `EXPO_PUBLIC_DISABLE_AI_LEARNING=true`.

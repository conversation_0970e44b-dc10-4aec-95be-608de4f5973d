#!/bin/bash

echo "==========================================="
echo "PlateMotion Onboarding Implementation Setup"
echo "==========================================="

echo "\nThis script will help you set up a new task for implementing the onboarding flow."
echo "\nPlease open a new chat window and reference the ONBOARDING_IMPLEMENTATION_PLAN.md file"
echo "to begin working on the onboarding flow implementation."

echo "\nKey files to reference:"
echo "- ONBOARDING_IMPLEMENTATION_PLAN.md (full implementation plan)"
echo "- src/screens/onboarding/ (existing onboarding screens)"
echo "- src/navigation/index.tsx (navigation structure)"
echo "- package.json (dependencies)"

echo "\nWhen you're ready to start, run the following command in a new terminal:"
echo "cd /Users/<USER>/Desktop/platemotion/apps/mobile && pnpm start"

echo "\nHappy coding!"

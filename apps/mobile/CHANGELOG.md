# Changelog

## 2025-08-11

### Comprehensive Codebase Health Audit & Improvements

- **CRITICAL FIXES**: Resolved ES module configuration conflicts that were
  blocking Prettier and build tools
- **TypeScript Improvements**: Fixed all 61 TypeScript errors, enhanced type
  safety with stricter configuration
- **Code Quality**: Improved ESLint configuration with stricter rules, better
  import ordering, and React Native best practices
- **Dependencies**: Updated critical packages including React Navigation,
  Supabase, and TanStack Query
- **Architecture**: Enhanced file organization, removed unused imports, and
  improved error handling patterns
- **Configuration**: Converted App.js to App.tsx, moved utility scripts to
  proper locations, updated tsconfig.json

**Technical Debt Resolved:**

- Fixed ES module vs CommonJS conflicts in configuration files
- Resolved timer type issues in React Native components
- Enhanced error boundary with proper override modifiers
- Improved navigation type safety
- Standardized error handling patterns across components

**Files Modified:**

- Configuration: `.prettierrc.json`, `tsconfig.json`, `eslint.config.js`
- Core: `App.tsx` (converted from .js), `index.js`
- Components: Multiple TypeScript error fixes across 39 files
- Scripts: Moved `create-profile.js` to `scripts/create-profile.cjs`
- Documentation: Added comprehensive `CODEBASE_HEALTH_AUDIT_REPORT.md`

**Quality Metrics:**

- TypeScript errors: 61 → 0 ✅
- ESLint configuration: Enhanced with 15+ new rules
- Code formatting: 100% Prettier compliance ✅
- Dependencies: Updated 8 critical packages
- Overall health score: 7.5/10 → 9/10

## 2025-08-04

## 2025-08-09

### Progress logging MVP and unit tests

- Implemented Hydration unit preference (default by locale, toggleable) and
  normalization to ml
- Added Habits, Mood, and Workout tabs in LogProgressQuickAction with save flow
- Enhanced chat confirmation messages for habit/mood/workout logs
- Added unit tests for hydration conversion utilities and habit payload builder

Affected files:

- src/features/progress/components/LogProgressQuickAction.tsx
- src/features/ai-coach/screens/ChatScreen.tsx
- src/features/progress/utils/units.ts (+ tests)
- src/features/progress/utils/payloadBuilders.ts (+ tests)
- src/features/progress/hooks/useHydrationUnit.ts
- src/features/progress/services/progressDataService.ts
- Jest config fix to allow RN/Expo tests to run cleanly (transform allowlist,
  expo mock)

### Expo Go quick-testing guard

- Added skip logic in App.js to bypass AI Learning System initialization and
  notifications when running in Expo Go, enabling quick local testing without
  backend setup.
- Introduced optional env flags: `EXPO_PUBLIC_DISABLE_AI_LEARNING=true` or
  `EXPO_PUBLIC_SKIP_AI_LEARNING_INIT=true` to force skipping on any environment.

Affected files:

- App.js

### Edit Profile screen (initial version)

- Added EditProfileScreen with form fields for core profile attributes and save
  to Supabase (updates profiles table).
- Wired navigation from Profile & Settings screen to EditProfile route.

Affected files:

- src/features/profile/screens/EditProfileScreen.tsx (new)
- src/navigation/RootNavigator.tsx
- src/shared/types/navigation.ts
- src/features/profile/screens/ProfileSettingsScreen.tsx

### Like/Dislike Preference System Implementation

#### Added

- **User Preferences Database**: Complete database schema for tracking user
  preferences on meals, exercises, recipes, ingredients, workout plans, and meal
  plans
- **Preference Service**: TypeScript service layer with comprehensive preference
  management functionality
- **Preference UI Components**: Reusable React Native components for
  like/dislike rating with visual feedback
- **AI Integration**: Enhanced AI Edge Function to use preferences for
  personalized recommendations
- **GDPR Compliance**: Integrated preferences into data export and deletion
  functions

#### Database Features

- **Flexible Preference System**: Support for 5-point scale (hate, dislike,
  neutral, like, love) with numeric strength values
- **Contextual Metadata**: Store additional context about items and reasons for
  preferences
- **Performance Optimized**: Comprehensive indexing for fast preference lookups
  and recommendations
- **RLS Security**: Row Level Security policies ensure users only access their
  own preferences

#### Mobile App Integration

- **WorkoutDetailScreen**: Added preference buttons for individual exercises and
  overall workout rating
- **RecipeDetailScreen**: Added preference buttons for recipe rating with
  comprehensive metadata
- **Simple & Full Components**: Both simple thumbs up/down and full 5-option
  preference components
- **Real-time Updates**: Immediate visual feedback when preferences are saved
- **Error Handling**: Comprehensive error handling with user-friendly messages

#### AI Personalization

- **Preference-Aware Recommendations**: AI now considers user likes/dislikes
  when suggesting meals and workouts
- **Filtering System**: Automatically excludes disliked items from all
  recommendations
- **Priority Boosting**: Prioritizes items similar to user's liked preferences
- **Pattern Recognition**: AI analyzes preference patterns to improve
  recommendations over time

#### Technical Implementation

- **Database Functions**:
  - `save_user_preference()`: Save/update preferences with conflict resolution
  - `get_user_preferences_for_recommendations()`: Optimized for AI
    recommendation engine
  - `get_user_preference()`: Retrieve specific item preferences
  - `delete_user_preference()`: Remove individual preferences
  - `get_user_preference_stats()`: Comprehensive preference analytics
- **TypeScript Types**: Complete type definitions for all preference-related
  data structures
- **Service Layer**: Clean abstraction with helper methods for common preference
  operations
- **Component Architecture**: Reusable components with customizable size and
  display options

#### GDPR Enhancements

- **Data Export**: User preferences included in comprehensive data export
- **Data Deletion**: Preferences properly deleted during account deletion
- **Audit Logging**: All preference operations logged for compliance

#### Files Added/Modified

- `supabase/migrations/20250804_user_preferences.sql`: Complete preference
  system database schema
- `apps/mobile/src/features/preferences/services/preferenceService.ts`:
  TypeScript service layer
- `apps/mobile/src/features/preferences/components/PreferenceButtons.tsx`: React
  Native UI components
- `apps/mobile/src/features/preferences/index.ts`: Feature module exports
- `apps/mobile/src/features/exercise/screens/WorkoutDetailScreen.tsx`:
  Integrated preference buttons
- `apps/mobile/src/features/nutrition/screens/RecipeDetailScreen.tsx`:
  Integrated preference buttons
- `supabase/functions/gemini-chat/index.ts`: Enhanced AI with preference
  awareness
- `supabase/migrations/20250804_gdpr_compliance.sql`: Updated GDPR functions for
  preferences

#### Next Steps

- Test preference system end-to-end with database migrations
- Validate AI recommendation filtering works correctly
- Add preference management screen for users to view/edit all preferences
- Implement preference-based meal plan and workout plan generation
- Add analytics dashboard for preference insights

### Google Gemini AI Integration with Supabase Vault

#### Added

- **Supabase Vault Integration**: Securely store Google Gemini API key in
  Supabase Vault instead of environment variables
- **Supabase Edge Function**: Created `gemini-chat` Edge Function that handles
  AI communication server-side
- **Secure AI Service**: Updated mobile app AI service to call Edge Function
  with proper authentication
- **Fallback Mechanism**: Graceful fallback to mock responses during development
  or if Edge Function is unavailable

#### Security Improvements

- **API Key Protection**: Gemini API key is now stored securely in Supabase
  Vault, never exposed to client-side code
- **Server-Side Processing**: All AI API calls now happen server-side through
  Edge Function
- **Authentication Required**: All AI requests require valid Supabase
  authentication

#### Technical Changes

- Created `supabase/functions/gemini-chat/index.ts` Edge Function with vault
  integration
- Updated `aiChatService.ts` to use Edge Function instead of direct API calls
- Removed Gemini API key from environment variables (now in vault)
- Added proper error handling and CORS support in Edge Function
- Maintained all existing tool calling functionality

### Major Navigation Flow Improvement

#### Added

- **Password Visibility Toggle**: Added eye icon to password fields in login
  screen for better user experience
- **Session-First Routing**: Implemented simplified routing logic that
  prioritizes session state over complex first-time user detection
- **Dynamic Stack Rendering**: Navigation now conditionally renders the correct
  stack based on current authentication state

#### Fixed

- **Login Navigation Issue**: Fixed critical bug where users would stay on login
  screen after successful authentication
- **Routing Logic**: Simplified navigation from complex isFirstTimeUser logic to
  clean session-based routing
- **Stack Switching**: Navigator now properly switches between stacks when
  authentication state changes

#### Improved

- **User Experience**: Login flow now immediately navigates to dashboard after
  successful authentication
- **Code Simplicity**: Removed unnecessary AsyncStorage-based first-time user
  detection
- **Navigation Performance**: Eliminated redundant routing checks and
  streamlined navigation logic

#### Technical Changes

- Removed `isFirstTimeUser` state and AsyncStorage dependency from RootNavigator
- Updated routing priority: Session + Onboarding Complete → App, Session +
  Incomplete → Onboarding, No Session → Onboarding
- Changed from static `initialRouteName` to dynamic conditional stack rendering
- Cleaned up unnecessary `hasSeenOnboarding` AsyncStorage calls

## 2025-08-02

### Comprehensive Codebase Analysis & Fixes

#### Fixed

- **Critical**: Removed duplicate Supabase client definitions that caused
  runtime conflicts
- **Navigation**: Removed obsolete ProgressScreen from AppTabNavigator
  (functionality moved to AI Coach)
- **Imports**: Fixed all service import paths for offline functionality
  (progressService, mealService, workoutService)
- **Architecture**: Moved OfflineContext and LanguageContext from app-config to
  shared/contexts
- **Structure**: Cleaned up empty app-config directory and established proper
  feature-based organization
- **Documentation**: Updated all test files and documentation to match current
  file structure
- **Tests**: Fixed final-test.js and test-offline.js to reference correct file
  paths

#### Improved

- **Code Quality**: Eliminated all import/export conflicts and circular
  dependencies
- **Maintainability**: Established single source of truth for Supabase client in
  src/lib/supabase.ts
- **Architecture**: Completed transition to clean feature-based folder structure
- **Testing**: All verification tests now pass with 100% success rate
- **Performance**: Removed redundant code and optimized import paths

#### Verified

- Zero TypeScript/import errors detected via diagnostics
- App builds successfully with Expo development server
- All core functionality preserved during restructuring
- Complete test coverage for offline functionality
- GitHub Actions CI/CD pipeline resolved with updated lockfiles

### AI Coach Implementation & Architecture Restructuring

#### Added

- **AI Coach Feature**: Complete chatbot implementation with 8 integrated tools
- **Tool Integration**: Meal planner, workout planner, progress analysis,
  logging tools
- **Chat Interface**: Message bubbles, typing indicators, conversation
  persistence
- **Natural Language Processing**: Intent recognition for tool execution
- **Database Schema**: Comprehensive Supabase tables with Row Level Security

#### Changed

- **Navigation**: Replaced Progress tab with AI Coach tab in footer menu
- **Architecture**: Migrated from flat structure to feature-based organization
- **Services**: Reorganized all services by feature domains
- **State Management**: Moved stores to appropriate feature folders
- **Import Structure**: Established clean import patterns with index files

## 2025-07-31

### Onboarding Flow Implementation

- Implemented multilingual onboarding flow with English and Spanish support
- Created localization infrastructure with LanguageContext
- Created English and Spanish translation files
- Created localization utility functions
- Created SplashScreen, LanguageSelectionScreen, IntroScreen, and TermsScreen
  components
- Updated navigation structure to include new onboarding screens
- Updated App.js to include LanguageProvider
- Updated existing onboarding screens to use LanguageContext
- Ensured proper navigation flow between screens

### Offline Support Implementation

- Added comprehensive offline support for workout, meal, and progress logging
- Implemented local data storage using AsyncStorage
- Added automatic synchronization when connectivity is restored
- Created OfflineContext for global offline state management
- Updated services to handle offline scenarios gracefully
- Added network connectivity detection using NetInfo
- Added OfflineStatusIndicator component to show offline status

### New Features

- Created OfflineProvider context for managing offline state
- Added offline queue system for data synchronization
- Implemented offline-aware data services
- Added testing scripts and documentation

### Dependency Updates

- Added @react-native-async-storage/async-storage for local data storage
- Added @react-native-community/netinfo for network connectivity detection

### Testing

- Created offline functionality test scripts
- Added comprehensive testing plan
- Verified offline implementation with Node.js test script

## 2025-07-30

### Navigation Improvements

- Added proper TypeScript navigation typings with AppTabScreenProps
- Updated NutritionScreen with typed navigation to RecipeDetail screen
- Updated ExerciseScreen with navigation to ExercisePlayer screen
- Added navigation to HomeScreen components (DailyWorkoutCard,
  DailyNutritionCard, Greeting, WeeklyConsistencyTracker)
- Fixed navigation errors by adding id prop to navigators
- Updated React Navigation packages to latest versions
- Added GroceryList screen with navigation from NutritionScreen FAB

### UI Component Updates

- Added onPress handlers to cards for navigation
- Improved WeeklyConsistencyTracker UI with better spacing and styling
- Implemented video player in ExercisePlayerScreen with play/pause controls
- Added floating action button (FAB) to NutritionScreen for accessing grocery
  list
- Implemented progress chart visualization in ProgressScreen
- Enhanced all screens with proper loading states and empty states

### New Features

- Created GroceryListScreen with add, remove, and toggle functionality
- Added interactive weight progress chart to ProgressScreen
- Implemented comprehensive feedback systems with toast notifications and haptic
  feedback

### Dependency Updates

- Updated React Navigation packages
- Added expo-video for video playback functionality
- Added react-native-svg-charts for data visualization
- Added react-native-toast-message for notifications
- Added react-native-haptic-feedback for tactile feedback
- Verified tamagui and @tamagui/lucide-icons installations

### Bug Fixes

- Resolved navigation parameter passing issues
- Fixed missing navigation imports in several components

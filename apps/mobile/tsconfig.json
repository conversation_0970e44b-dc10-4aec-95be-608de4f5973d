{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "noImplicitOverride": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "strictFunctionTypes": false}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules", "scripts/**/*"]}
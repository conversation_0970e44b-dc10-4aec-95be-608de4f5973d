// Simple Node.js test for offline functionality
const fs = require('fs');

// Test that our offline utility functions exist
it('should have offline utility functions', () => {
  // Check that the file exists
  const fileExists = fs.existsSync('./src/shared/utils/offlineTestUtils.ts');
  expect(fileExists).toBe(true);

  // Read the file content
  const fileContent = fs.readFileSync(
    './src/shared/utils/offlineTestUtils.ts',
    'utf8'
  );

  // Check that it contains the expected functions
  expect(fileContent).toContain('clearOfflineData');
  expect(fileContent).toContain('getOfflineQueue');
  expect(fileContent).toContain('simulateOffline');
  expect(fileContent).toContain('printOfflineQueue');
});

import {
  getOfflineQueue,
  clearOfflineData,
} from '../src/shared/utils/offlineTestUtils';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

describe('Offline Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should clear offline data', async () => {
    await clearOfflineData();
    expect(AsyncStorage.removeItem).toHaveBeenCalledWith('offline_queue');
  });

  it('should get offline queue', async () => {
    const mockQueue = [
      { id: '1', type: 'progress_log', action: 'create', data: {} },
    ];
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(
      JSON.stringify(mockQueue)
    );

    const queue = await getOfflineQueue();
    expect(queue).toEqual(mockQueue);
    expect(AsyncStorage.getItem).toHaveBeenCalledWith('offline_queue');
  });

  it('should handle empty offline queue', async () => {
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);

    const queue = await getOfflineQueue();
    expect(queue).toEqual([]);
  });

  it('should handle JSON parse error', async () => {
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue('invalid json');

    const queue = await getOfflineQueue();
    expect(queue).toEqual([]);
  });
});

import {
  getOfflineQueue,
  clearOfflineData,
} from '../src/shared/utils/offlineTestUtils';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
}));

const AsyncStorage = require('@react-native-async-storage/async-storage');

describe('Offline Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should clear offline data', async () => {
    await clearOfflineData();
    expect(AsyncStorage.removeItem).toHaveBeenCalledWith('offline_queue');
  });

  it('should get offline queue', async () => {
    const mockQueue = JSON.stringify([
      { id: '1', type: 'progress_log', action: 'create', data: {} },
    ]);
    AsyncStorage.getItem.mockResolvedValue(mockQueue);

    const queue = await getOfflineQueue();
    expect(queue).toEqual([
      { id: '1', type: 'progress_log', action: 'create', data: {} },
    ]);
    expect(AsyncStorage.getItem).toHaveBeenCalledWith('offline_queue');
  });

  it('should handle empty offline queue', async () => {
    AsyncStorage.getItem.mockResolvedValue(null);

    const queue = await getOfflineQueue();
    expect(queue).toEqual([]);
  });

  it('should handle JSON parse error', async () => {
    AsyncStorage.getItem.mockResolvedValue('invalid json');

    const queue = await getOfflineQueue();
    expect(queue).toEqual([]);
  });
});

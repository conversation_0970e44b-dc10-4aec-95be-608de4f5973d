{"name": "mobile", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:strict": "eslint src --ext .ts,.tsx --max-warnings=0", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx}\"", "prepare": "husky || true"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@google/generative-ai": "^0.24.1", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-google-signin/google-signin": "^15.0.0", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "@supabase/supabase-js": "^2.53.0", "@tanstack/react-query": "^5.83.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "expo": "~53.0.20", "expo-auth-session": "^6.2.1", "expo-constants": "^17.1.7", "expo-crypto": "^14.1.5", "expo-image-picker": "^16.1.4", "expo-notifications": "^0.31.4", "expo-status-bar": "~2.2.3", "expo-video": "^2.2.2", "expo-web-browser": "^14.2.0", "posthog-react-native": "^4.4.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-haptic-feedback": "^2.3.3", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-toast-message": "^2.3.3", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.3.0", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@eslint/js": "^9", "@react-native-community/cli": "^19.1.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.2", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "@types/react-native-vector-icons": "^6.4.18", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "callsites": "^3.1.0", "eslint": "^9.32.0", "eslint-config-prettier": "^9.1.2", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "globals": "^15", "husky": "^9.1.7", "jest": "^30.0.5", "jest-expo": "^53.0.9", "lint-staged": "^16.1.4", "typescript": "~5.8.3"}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix --max-warnings=500", "prettier --write"]}, "private": true}
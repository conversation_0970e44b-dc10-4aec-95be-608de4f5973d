import React, { useEffect } from 'react';
import { PaperProvider } from 'react-native-paper';
import RootNavigator from './src/navigation';
import Toast from 'react-native-toast-message';
import { LanguageProvider } from './src/shared/contexts/LanguageContext';
import { ErrorBoundary } from './src/shared/components/ErrorBoundary';
import { AppState, AppStateStatus } from 'react-native';
import Constants from 'expo-constants';
import { syncController } from './src/features/progress/services/syncController';
import { showInfoToast } from './src/shared/utils/toast';
import { aiLearningInitService } from './src/features/progress/services/aiLearningInitService';

export default function App(): React.ReactElement {
  console.log('PlateMotion App starting...');

  // Initialize AI Learning System on app startup (skip in Expo Go or when disabled via env)
  useEffect(() => {
    const isExpoGo =
      Constants?.executionEnvironment === 'storeClient' ||
      Constants?.appOwnership === 'expo';

    const disableAI =
      process.env.EXPO_PUBLIC_DISABLE_AI_LEARNING === 'true' ||
      process.env.EXPO_PUBLIC_SKIP_AI_LEARNING_INIT === 'true';

    if (isExpoGo || disableAI) {
      console.log(
        `⏭️ Skipping AI Learning initialization (isExpoGo=${isExpoGo}, disableAI=${disableAI})`
      );
      return;
    }

    const initializeAI = async () => {
      try {
        console.log('🤖 Initializing AI Learning System...');
        const status = await aiLearningInitService.initializeAILearningSystem();
        console.log('🎉 AI Learning System Status:', status);
      } catch (error) {
        console.error('❌ AI Learning System initialization failed:', error);
      }
    };

    initializeAI();
  }, []);

  // Attempt sync when app returns to foreground
  useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      async (state: AppStateStatus) => {
        if (state === 'active') {
          const result = await syncController.trySync();
          if (!result.synced) {
            showInfoToast('Offline mode', 'Will sync when online');
          }
        }
      }
    );
    return () => subscription.remove();
  }, []);

  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    console.error('App Error Boundary caught an error:', error, errorInfo);
  };

  return (
    <ErrorBoundary onError={handleError}>
      <LanguageProvider>
        <PaperProvider>
          <RootNavigator />
          <Toast />
        </PaperProvider>
      </LanguageProvider>
    </ErrorBoundary>
  );
}

# PlateMotion Mobile App Environment Variables
# Copy this file to .env and fill in your actual values

# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL="YOUR_SUPABASE_URL_HERE"
EXPO_PUBLIC_SUPABASE_ANON_KEY="YOUR_SUPABASE_ANON_KEY_HERE"

# OAuth Configuration (for Supabase local development)
GOOGLE_CLIENT_ID="YOUR_GOOGLE_CLIENT_ID_HERE"
GOOGLE_CLIENT_SECRET="YOUR_GOOGLE_CLIENT_SECRET_HERE"
FACEBOOK_APP_ID="YOUR_FACEBOOK_APP_ID_HERE"
FACEBOOK_APP_SECRET="YOUR_FACEBOOK_APP_SECRET_HERE"

# OAuth Configuration (for Expo app)
EXPO_PUBLIC_GOOGLE_CLIENT_ID="YOUR_GOOGLE_CLIENT_ID_HERE"
EXPO_PUBLIC_FACEBOOK_APP_ID="YOUR_FACEBOOK_APP_ID_HERE"

# Note: Gemini AI API key is now stored securely in Supabase Vault
# No need to configure EXPO_PUBLIC_GEMINI_API_KEY anymore

# Note: SendGrid has been replaced with Supabase built-in email confirmation
# No need to configure SendGrid keys anymore

# Development Configuration
EXPO_PUBLIC_DEV_MODE="true"
EXPO_PUBLIC_API_BASE_URL="https://your-api-domain.com"

# Optional: Analytics and Monitoring
# EXPO_PUBLIC_ANALYTICS_KEY="YOUR_ANALYTICS_KEY_HERE"
# EXPO_PUBLIC_SENTRY_DSN="YOUR_SENTRY_DSN_HERE"
# PostHog Analytics (optional)
# EXPO_PUBLIC_ENABLE_ANALYTICS="false"
# EXPO_PUBLIC_POSTHOG_KEY="YOUR_POSTHOG_API_KEY"
# EXPO_PUBLIC_POSTHOG_HOST="https://us.i.posthog.com" # or your EU/self-hosted domain


# Vision (optional)
# EXPO_PUBLIC_ENABLE_VISION="false"
# EXPO_PUBLIC_GEMINI_API_KEY="YOUR_GEMINI_KEY"

# Note: All environment variables for Expo must be prefixed with EXPO_PUBLIC_
# to be accessible in the client-side code.

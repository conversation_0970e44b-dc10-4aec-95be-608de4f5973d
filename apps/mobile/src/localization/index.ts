import AsyncStorage from '@react-native-async-storage/async-storage';
import en from './en.json';
import es from './es.json';

// Define the structure of our translations
interface Translations {
  [key: string]: string | Translations;
}

// Available languages
const languages = {
  en,
  es,
};

// Default language
const DEFAULT_LANGUAGE = 'en';

// Get nested property from object using dot notation
const getNestedProperty = (
  obj: Translations,
  path: string
): string | Translations | undefined => {
  return path.split('.').reduce((current: any, key) => current?.[key], obj);
};

// Translation function
export const t = (key: string, language: string = DEFAULT_LANGUAGE): string => {
  const translations: Translations =
    languages[language as keyof typeof languages] ?? languages[DEFAULT_LANGUAGE];
  const translation = getNestedProperty(translations, key);
  // Return key if translation not found or is not a string
  return typeof translation === 'string' ? translation : key;
};

// Save language preference
export const saveLanguage = async (language: string): Promise<void> => {
  try {
    await AsyncStorage.setItem('user-language', language);
  } catch (error) {
    console.error('Error saving language preference:', error);
  }
};

// Load saved language preference
export const loadLanguage = async (): Promise<string> => {
  try {
    const savedLanguage = await AsyncStorage.getItem('user-language');
    return savedLanguage ?? DEFAULT_LANGUAGE;
  } catch (error) {
    console.error('Error loading language preference:', error);
    return DEFAULT_LANGUAGE;
  }
};

// Get available languages
export const getAvailableLanguages = (): { [key: string]: string } => ({
  en: 'English',
  es: 'Español',
});

export default {
  t,
  saveLanguage,
  loadLanguage,
  getAvailableLanguages,
};

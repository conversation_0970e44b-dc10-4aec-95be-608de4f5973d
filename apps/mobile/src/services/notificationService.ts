import Constants from 'expo-constants';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

// Check if we're running in Expo Go
const isExpoGo = Constants.appOwnership === 'expo';

// Configure notification behavior only if not in Expo Go
if (!isExpoGo) {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
      shouldShowBanner: true,
      shouldShowList: true,
    }),
  });
}

export interface NotificationData {
  id: string;
  title: string;
  body: string;
  data?: Record<string, unknown>;
  categoryId?: string;
  timestamp: number;
  read: boolean;
}

export interface ScheduledNotification {
  id: string;
  title: string;
  body: string;
  trigger: Notifications.NotificationTriggerInput;
  data?: Record<string, unknown>;
  categoryId?: string;
}

class NotificationService {
  private notifications: NotificationData[] = [];
  private listeners: ((notifications: NotificationData[]) => void)[] = [];

  async requestPermissions(): Promise<boolean> {
    try {
      // Return false if running in Expo Go
      if (isExpoGo) {
        console.warn('Notifications not supported in Expo Go');
        return false;
      }

      const { status: existingStatus } =
        await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Notification permissions not granted');
        return false;
      }

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'Default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#007AFF',
        });

        await Notifications.setNotificationChannelAsync('habits', {
          name: 'Habit Reminders',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#34C759',
        });

        await Notifications.setNotificationChannelAsync('workouts', {
          name: 'Workout Reminders',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF9500',
        });
      }

      return true;
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  async scheduleNotification(
    notification: ScheduledNotification
  ): Promise<string | null> {
    try {
      // Return null if running in Expo Go
      if (isExpoGo) {
        console.warn('Cannot schedule notification: not supported in Expo Go');
        return null;
      }

      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        console.warn('Cannot schedule notification: permissions not granted');
        return null;
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data ?? {},
          categoryIdentifier: notification.categoryId ?? 'default',
        },
        trigger: notification.trigger,
      });

      console.log('Notification scheduled:', notificationId);
      return notificationId;
    } catch (error) {
      console.error('Error scheduling notification:', error);
      return null;
    }
  }

  async cancelNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
      console.log('Notification cancelled:', notificationId);
    } catch (error) {
      console.error('Error cancelling notification:', error);
    }
  }

  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('All notifications cancelled');
    } catch (error) {
      console.error('Error cancelling all notifications:', error);
    }
  }

  async getScheduledNotifications(): Promise<
    Notifications.NotificationRequest[]
  > {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      return [];
    }
  }

  // In-app notification management
  addNotification(
    notification: Omit<NotificationData, 'id' | 'timestamp' | 'read'>
  ): void {
    const newNotification: NotificationData = {
      ...notification,
      id: Date.now().toString(),
      timestamp: Date.now(),
      read: false,
    };

    this.notifications.unshift(newNotification);
    this.notifyListeners();
  }

  markAsRead(notificationId: string): void {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      this.notifyListeners();
    }
  }

  markAllAsRead(): void {
    this.notifications.forEach(n => (n.read = true));
    this.notifyListeners();
  }

  removeNotification(notificationId: string): void {
    this.notifications = this.notifications.filter(
      n => n.id !== notificationId
    );
    this.notifyListeners();
  }

  clearAllNotifications(): void {
    this.notifications = [];
    this.notifyListeners();
  }

  getNotifications(): NotificationData[] {
    return [...this.notifications];
  }

  getUnreadCount(): number {
    return this.notifications.filter(n => !n.read).length;
  }

  subscribe(listener: (notifications: NotificationData[]) => void): () => void {
    this.listeners.push(listener);

    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener([...this.notifications]));
  }

  // Habit-specific notification helpers
  async scheduleHabitReminder(
    habitId: string,
    habitName: string,
    time: Date
  ): Promise<string | null> {
    return this.scheduleNotification({
      id: `habit-${habitId}`,
      title: 'Habit Reminder',
      body: `Time for your ${habitName}!`,
      trigger: {
        type: Notifications.SchedulableTriggerInputTypes.CALENDAR,
        hour: time.getHours(),
        minute: time.getMinutes(),
        repeats: true,
      },
      data: {
        type: 'habit_reminder',
        habitId,
        categoryId: 'habits',
      },
    });
  }

  async scheduleWorkoutReminder(
    workoutName: string,
    time: Date
  ): Promise<string | null> {
    return this.scheduleNotification({
      id: `workout-${Date.now()}`,
      title: 'Workout Time!',
      body: `Ready for your ${workoutName}?`,
      trigger: {
        type: Notifications.SchedulableTriggerInputTypes.CALENDAR,
        hour: time.getHours(),
        minute: time.getMinutes(),
        repeats: true,
      },
      data: {
        type: 'workout_reminder',
        categoryId: 'workouts',
      },
    });
  }

  async scheduleMealReminder(
    mealType: string,
    time: Date
  ): Promise<string | null> {
    return this.scheduleNotification({
      id: `meal-${mealType}-${Date.now()}`,
      title: 'Meal Time!',
      body: `Don't forget your ${mealType}!`,
      trigger: {
        type: Notifications.SchedulableTriggerInputTypes.CALENDAR,
        hour: time.getHours(),
        minute: time.getMinutes(),
        repeats: true,
      },
      data: {
        type: 'meal_reminder',
        mealType,
        categoryId: 'default',
      },
    });
  }
}

// Export singleton instance
export const notificationService = new NotificationService();

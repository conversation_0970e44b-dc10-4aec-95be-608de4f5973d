import { supabase } from '../lib/supabase';

export interface EmailVerificationResult {
  success: boolean;
  message: string;
  error?: string;
}

class EmailVerificationService {
  /**
   * Resend confirmation email using Supabase's built-in email confirmation
   */
  async resendConfirmationEmail(
    email: string
  ): Promise<EmailVerificationResult> {
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email.trim().toLowerCase(),
      });

      if (error) {
        console.error('Error resending confirmation email:', error);
        return {
          success: false,
          message: 'Failed to resend confirmation email. Please try again.',
          error: error.message,
        };
      }

      return {
        success: true,
        message:
          'Confirmation email sent successfully! Please check your inbox.',
      };
    } catch (error) {
      console.error('Error resending confirmation email:', error);
      return {
        success: false,
        message: 'An unexpected error occurred. Please try again.',
        error: 'UNEXPECTED_ERROR',
      };
    }
  }

  /**
   * Check if user's email is confirmed
   */
  async isEmailConfirmed(_userId: string): Promise<boolean> {
    try {
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();

      if (error || !user) {
        return false;
      }

      return user.email_confirmed_at !== null;
    } catch (error) {
      console.error('Error checking email confirmation:', error);
      return false;
    }
  }
}

export const emailVerificationService = new EmailVerificationService();

import { supabase } from './lib/supabase';

// Simple test to verify Supabase connection
async function testSupabaseConnection() {
  console.log('Testing Supabase connection...');

  try {
    // Test basic connection by querying a simple table
    const { data, error } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);

    if (error) {
      console.log('Error querying profiles table:', error);
    } else {
      console.log(
        'Successfully connected to Supabase. Profiles table accessible.'
      );
      console.log('Sample data:', data);
    }

    // Test auth status
    const { data: sessionData, error: sessionError } =
      await supabase.auth.getSession();
    if (sessionError) {
      console.log('Auth session error:', sessionError);
    } else {
      console.log('Auth session:', sessionData.session ? 'Active' : 'None');
      if (sessionData.session) {
        console.log('User ID:', sessionData.session.user.id);
        // Create profile for the user
        await createProfileForUser(sessionData.session.user.id);
      }
    }
  } catch (e) {
    console.error('Unexpected error testing Supabase connection:', e);
  }
}

// Function to create a profile for a user
async function createProfileForUser(userId: string) {
  console.log('Creating profile for user:', userId);

  try {
    // Check if profile already exists
    const { data: existingProfile, error: checkError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.log('Error checking profile:', checkError);
      return;
    }

    if (existingProfile) {
      console.log('Profile already exists for user:', userId);
      // Update the profile to mark onboarding as complete
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ onboarding_complete: true })
        .eq('id', userId);

      if (updateError) {
        console.log('Error updating profile:', updateError);
      } else {
        console.log('Profile updated successfully');
      }
      return;
    }

    // Create new profile
    const { data, error } = await supabase
      .from('profiles')
      .insert([
        {
          id: userId,
          onboarding_complete: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ])
      .select();

    if (error) {
      console.error('Error creating profile:', error);
    } else {
      console.log('Profile created successfully:', data);
    }
  } catch (e) {
    console.error('Unexpected error creating profile:', e);
  }
}

testSupabaseConnection();


import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import React, { useEffect } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { ChatScreen } from '../features/ai-coach';
import { ExerciseStackNavigator } from '../features/exercise/navigation/ExerciseStackNavigator';
import { HabitsStackNavigator } from '../features/habits/navigation/HabitsStackNavigator';
import { HomeScreen } from '../features/home';
import { NutritionStackNavigator } from '../features/nutrition/navigation/NutritionStackNavigator';
import { ProgressScreen } from '../features/progress/screens/ProgressScreen';
import { analytics } from '../shared/services';
import { AppTabParamList } from '../shared/types/navigation';

const AppTabNav = createBottomTabNavigator<AppTabParamList>();

// Icon components to avoid nested component warnings
const HomeIcon = ({ color, size }: { color: string; size: number }) => (
  <Icon name="home" color={color} size={size} />
);
const NutritionIcon = ({ color, size }: { color: string; size: number }) => (
  <Icon name="food" color={color} size={size} />
);
const ExerciseIcon = ({ color, size }: { color: string; size: number }) => (
  <Icon name="dumbbell" color={color} size={size} />
);
const HabitsIcon = ({ color, size }: { color: string; size: number }) => (
  <Icon name="calendar-check" color={color} size={size} />
);
const ProgressIcon = ({ color, size }: { color: string; size: number }) => (
  <Icon name="chart-line" color={color} size={size} />
);
const AICoachIcon = ({ color, size }: { color: string; size: number }) => (
  <Icon name="robot" color={color} size={size} />
);

export function AppTabNavigator() {
  const insets = useSafeAreaInsets();

  // Basic tab open tracking (screen-level)
  useEffect(() => {
    analytics.trackScreen('AppTabs');
  }, []);

  return (
    <AppTabNav.Navigator
      id={undefined}
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: '#8E8E93',
        tabBarStyle: {
          backgroundColor: '#1C1C1E',
          borderTopColor: '#2C2C2E',
          paddingBottom: Math.max(insets.bottom, 8), // Ensure minimum 8px padding, more if safe area requires it
          paddingTop: 8,
          height: 60 + Math.max(insets.bottom, 8), // Dynamic height based on safe area
        },
      }}
    >
      <AppTabNav.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarIcon: HomeIcon,
        }}
      />
      <AppTabNav.Screen
        name="AICoach"
        component={ChatScreen}
        options={{
          tabBarIcon: AICoachIcon,
          tabBarLabel: 'AI Coach',
        }}
        listeners={{
          focus: () =>
            analytics.trackEvent({
              name: 'tab_open',
              properties: { tab: 'AICoach' },
            }),
        }}
      />
      <AppTabNav.Screen
        name="Nutrition"
        component={NutritionStackNavigator}
        options={{
          tabBarIcon: NutritionIcon,
        }}
      />
      <AppTabNav.Screen
        name="Exercise"
        component={ExerciseStackNavigator}
        options={{
          tabBarIcon: ExerciseIcon,
        }}
      />
      <AppTabNav.Screen
        name="Habits"
        component={HabitsStackNavigator}
        options={{
          tabBarIcon: HabitsIcon,
        }}
      />
      <AppTabNav.Screen
        name="Progress"
        component={ProgressScreen}
        options={{
          tabBarIcon: ProgressIcon,
        }}
      />
    </AppTabNav.Navigator>
  );
}

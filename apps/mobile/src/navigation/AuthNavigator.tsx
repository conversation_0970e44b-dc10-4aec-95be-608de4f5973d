import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import { LoginScreen, SignUpScreen } from '../features/auth';
import { AuthStackParamList } from '../shared/types/navigation';

// Auth Screens

const AuthStackNav = createStackNavigator<AuthStackParamList>();

export function AuthNavigator() {
  return (
    <AuthStackNav.Navigator
      id={undefined}
      screenOptions={{ headerShown: false }}
    >
      <AuthStackNav.Screen name="Login" component={LoginScreen} />
      <AuthStackNav.Screen name="SignUp" component={SignUpScreen} />
    </AuthStackNav.Navigator>
  );
}

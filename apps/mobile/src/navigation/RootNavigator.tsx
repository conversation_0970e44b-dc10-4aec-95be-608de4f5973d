import { DefaultTheme, NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Session } from '@supabase/supabase-js';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { NotificationsScreen } from '../features/notifications';
import { OnboardingNavigator } from '../features/onboarding/navigation/OnboardingNavigator';
import { PrivacySettingsScreen } from '../features/privacy/screens/PrivacySettingsScreen';
import AppPreferencesScreen from '../features/profile/screens/AppPreferencesScreen';
import DataPrivacyScreen from '../features/profile/screens/DataPrivacyScreen';
import EditProfileScreen from '../features/profile/screens/EditProfileScreen';
import HelpSupportScreen from '../features/profile/screens/HelpSupportScreen';
import NotificationSettingsScreen from '../features/profile/screens/NotificationSettingsScreen';
import ProfileSettingsScreen from '../features/profile/screens/ProfileSettingsScreen';
import { testSupabaseConnection } from '../features/profile/services/profileService';
import { supabase } from '../lib/supabase';
import { NavigationProvider } from '../shared/contexts/NavigationContext';
import { NotificationProvider } from '../shared/contexts/NotificationContext';
import { RootStackParamList } from '../shared/types/navigation';
import { AppTabNavigator } from './AppTabNavigator';
import { linking } from './linking';

const RootStackNav = createStackNavigator<RootStackParamList>();
const queryClient = new QueryClient();

export default function RootNavigator() {
  const [session, setSession] = useState<Session | null>(null);
  const [onboardingComplete, setOnboardingComplete] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSessionAndProfile = async () => {
      try {
        // Test Supabase connection
        await testSupabaseConnection();

        console.log('Fetching session...');
        const { data, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('Error fetching session:', sessionError);
          setSession(null);
          setOnboardingComplete(false);
          setLoading(false);
          return;
        }

        const currentSession = data?.session;
        console.log('Session:', currentSession ? 'Found' : 'None');
        setSession(currentSession ?? null);

        if (currentSession?.user) {
          console.log('Fetching profile for user:', currentSession.user.id);
          const { data: profileData, error } = await supabase
            .from('profiles')
            .select('onboarding_complete')
            .eq('id', currentSession.user.id)
            .single();

          if (error) {
            console.log(
              'Profile not found or error fetching profile, assuming onboarding not complete',
              error
            );
            setOnboardingComplete(false);
          } else if (profileData) {
            console.log('Profile data:', profileData);
            setOnboardingComplete(profileData.onboarding_complete ?? false);
          } else {
            setOnboardingComplete(false);
          }
        } else {
          setOnboardingComplete(false);
        }
      } catch (e) {
        console.error('Error fetching session or profile:', e);
        setOnboardingComplete(false);
      } finally {
        console.log('Setting loading to false');
        setLoading(false);
      }
    };

    const initializeApp = async () => {
      await fetchSessionAndProfile();
    };

    initializeApp();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (_event, newSession) => {
      console.log(
        'Auth state changed:',
        _event,
        newSession ? 'Session exists' : 'No session'
      );

      // Only update if session actually changed
      if (newSession?.user?.id !== session?.user?.id) {
        setSession(newSession);
        setOnboardingComplete(false);

        if (newSession?.user) {
          // Fetch profile for new session
          try {
            const { data, error } = await supabase
              .from('profiles')
              .select('onboarding_complete')
              .eq('id', newSession.user.id)
              .single();

            if (error) {
              console.log(
                'Profile not found, assuming onboarding not complete'
              );
              setOnboardingComplete(false);
            } else if (data) {
              setOnboardingComplete(data.onboarding_complete ?? false);
            }
          } catch (e) {
            console.error('Error fetching profile in auth change:', e);
            setOnboardingComplete(false);
          }
        }
        setLoading(false);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading...</Text>
      </View>
    );
  }

  // Determine which stack to show based on current state
  const getCurrentStack = (): keyof RootStackParamList => {
    console.log('Determining current stack:', {
      session: session ? 'exists' : 'none',
      onboardingComplete,
      loading,
    });

    // Priority 1: If user has session and completed onboarding, show App
    if (session && onboardingComplete) {
      console.log('User has session and completed onboarding → App');
      return 'App';
    }

    // Priority 2: If user has session but hasn't completed onboarding, continue onboarding
    if (session && !onboardingComplete) {
      console.log('User has session but onboarding incomplete → Onboarding');
      return 'Onboarding';
    }

    // Priority 3: No session, start onboarding process
    console.log('No session → Onboarding');
    return 'Onboarding';
  };

  const currentStack = getCurrentStack();

  return (
    <QueryClientProvider client={queryClient}>
      <NotificationProvider>
        <NavigationProvider>
          <NavigationContainer linking={linking} theme={DefaultTheme}>
            <RootStackNav.Navigator
              id={undefined}
              screenOptions={{ headerShown: false }}
            >
              {currentStack === 'App' && (
                <>
                  <RootStackNav.Screen name="App" component={AppTabNavigator} />
                  <RootStackNav.Screen
                    name="ProfileSettings"
                    component={ProfileSettingsScreen}
                  />
                  <RootStackNav.Screen
                    name="AppPreferences"
                    component={AppPreferencesScreen}
                  />
                  <RootStackNav.Screen
                    name="NotificationSettings"
                    component={NotificationSettingsScreen}
                  />
                  <RootStackNav.Screen
                    name="HelpSupport"
                    component={HelpSupportScreen}
                  />
                  <RootStackNav.Screen
                    name="DataPrivacy"
                    component={DataPrivacyScreen}
                  />
                  <RootStackNav.Screen
                    name="PrivacySettings"
                    component={PrivacySettingsScreen}
                  />
                  <RootStackNav.Screen
                    name="Notifications"
                    component={NotificationsScreen}
                  />
                  <RootStackNav.Screen
                    name="EditProfile"
                    component={EditProfileScreen}
                  />
                </>
              )}
              {currentStack === 'Onboarding' && (
                <RootStackNav.Screen
                  name="Onboarding"
                  component={OnboardingNavigator}
                />
              )}
            </RootStackNav.Navigator>
          </NavigationContainer>
        </NavigationProvider>
      </NotificationProvider>
    </QueryClientProvider>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

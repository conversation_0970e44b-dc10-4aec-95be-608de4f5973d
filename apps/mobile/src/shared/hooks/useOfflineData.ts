import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect, useState } from 'react';

// Generic hook for managing offline data
export const useOfflineData = <T>(key: string, initialValue: T) => {
  const [data, setData] = useState<T>(initialValue);
  const [loading, setLoading] = useState(true);

  // Load data from AsyncStorage on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const storedData = await AsyncStorage.getItem(key);
        if (storedData) {
          setData(JSON.parse(storedData));
        }
      } catch (error) {
        console.error(`Error loading ${key} from AsyncStorage:`, error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [key]);

  // Save data to AsyncStorage whenever it changes
  const saveData = async (newData: T) => {
    try {
      setData(newData);
      await AsyncStorage.setItem(key, JSON.stringify(newData));
    } catch (error) {
      console.error(`Error saving ${key} to AsyncStorage:`, error);
    }
  };

  // Clear data from AsyncStorage
  const clearData = async () => {
    try {
      await AsyncStorage.removeItem(key);
      setData(initialValue);
    } catch (error) {
      console.error(`Error clearing ${key} from AsyncStorage:`, error);
    }
  };

  return { data, loading, saveData, clearData };
};

// Specific hook for managing offline progress logs
export const useOfflineProgressLogs = () => {
  return useOfflineData<ProgressLogWithLocalId[]>('progressLogs', []);
};

// Specific hook for managing offline workouts
export const useOfflineWorkouts = () => {
  return useOfflineData<WorkoutWithLocalId[]>('workouts', []);
};

// Specific hook for managing offline meals
export const useOfflineMeals = () => {
  return useOfflineData<MealWithLocalId[]>('meals', []);
};

// Types for offline data with local IDs
export interface ProgressLogWithLocalId extends ProgressLog {
  localId?: string;
  isLocal?: boolean;
  synced?: boolean;
}

export interface WorkoutWithLocalId extends Workout {
  localId?: string;
  isLocal?: boolean;
  synced?: boolean;
}

export interface MealWithLocalId extends Meal {
  localId?: string;
  isLocal?: boolean;
  synced?: boolean;
}

// Import types from services
export interface BodyMeasurements {
  [key: string]: number;
}

export interface WorkoutPerformance {
  [key: string]: string | number;
}

export interface Exercise {
  id: string;
  name: string;
  reps?: number;
  sets?: number;
  weight_kg?: number;
}

export interface Ingredient {
  id: string;
  name: string;
  quantity: number;
  unit: string;
}

export interface ProgressLog {
  id: string;
  log_date: string;
  weight_kg?: number;
  body_measurements?: BodyMeasurements;
  workout_performance?: WorkoutPerformance;
  subjective_feeling?: string;
}

export interface Workout {
  id: string;
  name: string;
  description?: string;
  exercises: Exercise[];
  // Add other workout properties as needed
}

export interface Meal {
  id: string;
  name: string;
  ingredients: Ingredient[];
  calories?: number;
  // Add other meal properties as needed
}

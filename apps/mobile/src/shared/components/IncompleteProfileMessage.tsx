import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { AppTabScreenProps } from '../types/navigation';

interface IncompleteProfileMessageProps {
  title: string;
  message: string;
  icon: React.ComponentProps<typeof MaterialCommunityIcons>['name'];
  buttonText?: string;
}

export default function IncompleteProfileMessage({
  title,
  message,
  icon,
  buttonText = 'Complete Profile',
}: IncompleteProfileMessageProps) {
  const navigation =
    useNavigation<AppTabScreenProps<'AICoach'>['navigation']>();

  const handleCompleteProfile = () => {
    navigation.navigate('AICoach');
  };

  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <MaterialCommunityIcons name={icon} size={64} color="#666" />
      </View>

      <Text style={styles.title}>{title}</Text>
      <Text style={styles.message}>{message}</Text>

      <TouchableOpacity style={styles.button} onPress={handleCompleteProfile}>
        <MaterialCommunityIcons name="account-plus" size={20} color="#fff" />
        <Text style={styles.buttonText}>{buttonText}</Text>
      </TouchableOpacity>

      <Text style={styles.helpText}>
        Chat with our AI Coach to set up your personalized plan
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    backgroundColor: '#f8f9fa',
  },
  iconContainer: {
    marginBottom: 24,
    opacity: 0.7,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    maxWidth: 300,
  },
  button: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
    marginBottom: 16,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  helpText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

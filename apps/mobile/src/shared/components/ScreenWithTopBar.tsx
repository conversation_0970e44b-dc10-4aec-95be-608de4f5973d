import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useNavigationMenu } from '../contexts/NavigationContext';
import { useNotifications } from '../contexts/NotificationContext';
import { HamburgerMenu } from './HamburgerMenu';
import { TopBar } from './TopBar';

interface ScreenWithTopBarProps {
  title: string;
  children: React.ReactNode;
  showMenu?: boolean;
  showNotifications?: boolean;
  backgroundColor?: string;
  textColor?: string;
}

export const ScreenWithTopBar: React.FC<ScreenWithTopBarProps> = ({
  title,
  children,
  showMenu = true,
  showNotifications = true,
  backgroundColor = '#1C1C1E',
  textColor = '#FFFFFF',
}) => {
  const { isMenuOpen, openMenu, closeMenu } = useNavigationMenu();
  const { unreadCount } = useNotifications();
  const navigation = useNavigation();

  const handleNotificationPress = () => {
    navigation.navigate('Notifications' as never);
  };

  const handleMenuNavigate = (screen: string) => {
    // Navigate to the specified profile screen
    // Since Profile is no longer a tab, we need to navigate directly to the screen
    navigation.navigate(screen as never);
  };

  return (
    <View style={styles.container}>
      <TopBar
        title={title}
        onMenuPress={openMenu}
        onNotificationPress={handleNotificationPress}
        showMenu={showMenu}
        showNotifications={showNotifications}
        backgroundColor={backgroundColor}
        textColor={textColor}
        notificationCount={unreadCount}
      />
      <View style={styles.content}>{children}</View>
      <HamburgerMenu
        visible={isMenuOpen}
        onClose={closeMenu}
        onNavigate={handleMenuNavigate}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  content: {
    flex: 1,
  },
});

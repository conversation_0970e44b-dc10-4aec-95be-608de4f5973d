import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useOffline } from '../../contexts';

const OfflineStatusIndicator = () => {
  const { isOnline, isSyncing } = useOffline();

  if (isOnline && !isSyncing) {
    return null; // Don't show anything when online and not syncing
  }

  return (
    <View style={styles.container}>
      {!isOnline && <Text style={styles.offlineText}>Offline Mode</Text>}
      {isSyncing && <Text style={styles.syncingText}>Syncing data...</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 10,
    alignItems: 'center',
    zIndex: 1000,
  },
  offlineText: {
    color: 'white',
    fontWeight: 'bold',
  },
  syncingText: {
    color: 'yellow',
    fontWeight: 'bold',
  },
});

export default OfflineStatusIndicator;

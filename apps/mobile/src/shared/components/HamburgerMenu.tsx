import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  ScrollView,
  Alert,
} from 'react-native';
import { Text, Card, List, But<PERSON>, Divider } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { supabase } from '../../lib/supabase';

const { width: SCREEN_WIDTH } = Dimensions.get('window');
const MENU_WIDTH = SCREEN_WIDTH * 0.8;

// Icon components to avoid nested component warnings
const DynamicIcon = ({ iconName, ...props }: { iconName: string } & { color?: string; style?: React.ComponentProps<typeof Icon>['style'] }) => (
  <Icon {...props} name={iconName} size={24} />
);
const ChevronRightIcon = (props: { color?: string; style?: React.ComponentProps<typeof Icon>['style'] }) => (
  <Icon {...props} name="chevron-right" size={20} />
);

// Factory function to create icon components
const createDynamicIconComponent = (iconName: string) => (props: { color?: string; style?: React.ComponentProps<typeof Icon>['style'] }) => (
  <DynamicIcon {...props} iconName={iconName} />
);

interface HamburgerMenuProps {
  visible: boolean;
  onClose: () => void;
  onNavigate?: (screen: string) => void;
}

export const HamburgerMenu: React.FC<HamburgerMenuProps> = ({
  visible,
  onClose,
  onNavigate,
}) => {
  const insets = useSafeAreaInsets();
  const slideAnim = React.useRef(new Animated.Value(-MENU_WIDTH)).current;

  React.useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: -MENU_WIDTH,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, slideAnim]);

  const handleSignOut = async () => {
    Alert.alert('Sign Out', 'Are you sure you want to sign out?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Sign Out',
        style: 'destructive',
        onPress: async () => {
          const { error } = await supabase.auth.signOut();
          if (error) {
            Alert.alert('Error', error.message);
          }
          onClose();
        },
      },
    ]);
  };

  const menuItems = [
    {
      title: 'Profile & Settings',
      description: 'View and edit your profile information',
      icon: 'account-circle',
      onPress: () => {
        onNavigate?.('ProfileSettings');
        onClose();
      },
    },
    {
      title: 'App Preferences',
      description: 'Customize your app experience',
      icon: 'cog',
      onPress: () => {
        onNavigate?.('AppPreferences');
        onClose();
      },
    },
    {
      title: 'Notification Settings',
      description: 'Manage your notification preferences',
      icon: 'bell-ring',
      onPress: () => {
        onNavigate?.('NotificationSettings');
        onClose();
      },
    },
    {
      title: 'Help & Support',
      description: 'Get help and contact support',
      icon: 'help-circle',
      onPress: () => {
        onNavigate?.('HelpSupport');
        onClose();
      },
    },
    {
      title: 'Data & Privacy',
      description: 'Manage your privacy and data settings',
      icon: 'shield-account',
      onPress: () => {
        onNavigate?.('DataPrivacy');
        onClose();
      },
    },
  ];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableOpacity
          style={styles.backdrop}
          activeOpacity={1}
          onPress={onClose}
        />
        <Animated.View
          style={[
            styles.menuContainer,
            {
              transform: [{ translateX: slideAnim }],
              paddingTop: insets.top,
              paddingBottom: insets.bottom,
            },
          ]}
        >
          <ScrollView style={styles.scrollView}>
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.headerTitle}>Menu</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Icon name="close" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </View>

            {/* Menu Items */}
            <View style={styles.menuContent}>
              {menuItems.map((item, index) => (
                <Card key={index} style={styles.menuCard}>
                  <List.Item
                    title={item.title}
                    description={item.description}
                    left={createDynamicIconComponent(item.icon)}
                    right={ChevronRightIcon}
                    onPress={item.onPress}
                    titleStyle={styles.menuItemTitle}
                    descriptionStyle={styles.menuItemDescription}
                  />
                </Card>
              ))}

              <Divider style={styles.divider} />

              {/* Sign Out Button */}
              <Button
                mode="contained"
                onPress={handleSignOut}
                style={styles.signOutButton}
                buttonColor="#FF3B30"
                textColor="#FFFFFF"
                icon="logout"
              >
                Sign Out
              </Button>
            </View>
          </ScrollView>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    flexDirection: 'row',
  },
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  menuContainer: {
    width: MENU_WIDTH,
    backgroundColor: '#1C1C1E',
    elevation: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 2,
      height: 0,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#2C2C2E',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  closeButton: {
    padding: 4,
  },
  menuContent: {
    padding: 16,
  },
  menuCard: {
    marginBottom: 8,
    backgroundColor: '#2C2C2E',
  },
  menuItemTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  menuItemDescription: {
    color: '#8E8E93',
    fontSize: 14,
  },
  divider: {
    marginVertical: 16,
    backgroundColor: '#2C2C2E',
  },
  signOutButton: {
    marginTop: 8,
  },
});

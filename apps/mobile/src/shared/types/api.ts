// Placeholder types for logs - ideally these would be more specific
export interface WorkoutLog {
  workout_id: string;
  completed_at: string;
  exercises: { exercise_id: string; sets: { reps: number; weight: number }[] }[];
}

export interface ProgressEntry {
  date: string;
  weight_kg?: number;
  body_fat_percentage?: number;
}

export interface MealLog {
  meal_id: string;
  consumed_at: string;
  servings: number;
}

// Common API response types
export interface ApiResponse<T = unknown> {
  data: T;
  error?: string;
  message?: string;
}

export interface ApiResponseWorkoutLog extends ApiResponse<WorkoutLog> {}
export interface ApiResponseProgressEntry extends ApiResponse<ProgressEntry> {}
export interface ApiResponseMealLog extends ApiResponse<MealLog> {}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  totalPages: number;
}

// User and Profile types
export interface User {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
}

export interface Profile {
  id: string;
  full_name?: string;
  age?: number;
  height_cm?: number;
  weight_kg?: number;
  goals?: string[];
  experience_level?: string;
  known_injuries?: string[];
  preferred_exercise_types?: string[];
  available_equipment?: string[];
  dietary_restrictions?: string[];
  food_allergies?: string[];
  budget_preference?: string;
  cuisine_types?: string[];
  ai_persona_preference?: string;
  onboarding_complete: boolean;
  created_at: string;
  updated_at: string;
}

// Exercise and Workout types
export interface Exercise {
  id: string;
  name: string;
  description?: string;
  video_url?: string;
  sets?: number;
  reps?: number;
  rest_seconds?: number;
  muscle_groups?: string[];
  equipment_needed?: string[];
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
}

export interface WorkoutPlan {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  plan_date: string;
  exercises: Exercise[];
  estimated_duration_minutes?: number;
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
  created_at: string;
  updated_at: string;
}

export interface WorkoutLog {
  id?: string;
  user_id: string;
  workout_plan_id: string;
  exercise_id: string;
  sets_completed: number;
  reps_completed: number;
  weight_used?: number;
  notes?: string;
  completed_at: string;
  created_at?: string;
}

// Nutrition and Recipe types
export interface Recipe {
  id: string;
  name: string;
  description?: string;
  instructions: string[];
  prep_time_minutes?: number;
  cook_time_minutes?: number;
  servings?: number;
  calories_per_serving?: number;
  difficulty_level?: 'easy' | 'medium' | 'hard';
  cuisine_type?: string;
  dietary_tags?: string[];
  image_url?: string;
  ingredients: Ingredient[];
  nutritional_info?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
    sugar?: number;
    sodium?: number;
  };
}

export interface Ingredient {
  id: string;
  name: string;
  amount: number;
  unit: string;
  calories_per_unit?: number;
  macros?: {
    protein: number;
    carbs: number;
    fat: number;
  };
}

export interface MealPlan {
  id: string;
  user_id: string;
  plan_date: string;
  recipes: Recipe[];
  total_calories?: number;
  total_macros?: {
    protein: number;
    carbs: number;
    fat: number;
  };
  created_at: string;
  updated_at: string;
}

// Progress tracking types
export interface ProgressEntry {
  id: string;
  user_id: string;
  date: string;
  weight_kg?: number;
  body_fat_percentage?: number;
  muscle_mass_kg?: number;
  measurements?: {
    chest?: number;
    waist?: number;
    hips?: number;
    arms?: number;
    thighs?: number;
  };
  notes?: string;
  created_at: string;
}

// Offline queue types
export interface OfflineQueueItem {
  id: string;
  type: 'workout_log' | 'progress_entry' | 'meal_log';
  action: 'create' | 'update' | 'delete';
  data: WorkoutLog | ProgressEntry | MealLog;
  timestamp: string;
  synced: boolean;
}

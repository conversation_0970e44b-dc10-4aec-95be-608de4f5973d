import { StyleProp, ViewStyle, TextStyle } from 'react-native';

// Re-export all types for easy importing
export * from './navigation';
export * from './api';

// Common utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Language types
export type SupportedLanguage = 'en' | 'es';

// Theme types
export type ThemeMode = 'light' | 'dark' | 'system';

// Common component props
export interface BaseComponentProps {
  testID?: string;
  style?: StyleProp<ViewStyle | TextStyle>;
}

// Loading states
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// Error types
export interface AppError {
  message: string;
  code?: string;
  details?: Record<string, unknown>;
}

import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { StackScreenProps } from '@react-navigation/stack';

// Auth Stack Types
export type AuthStackParamList = {
  Login: undefined;
  SignUp: undefined;
};

// App Tab Types
export type AppTabParamList = {
  Home: undefined;
  Nutrition: undefined;
  Exercise: undefined;
  AICoach: undefined;
  Habits: undefined;
  Progress: undefined;
};

// Nutrition Stack Types
export type NutritionStackParamList = {
  NutritionMain: undefined;
  RecipeDetail: { recipeId: string };
  GroceryList: undefined;
  MealPlan: { mealPlanId: string };
};

// Exercise Stack Types
export type ExerciseStackParamList = {
  ExerciseMain: undefined;
  WorkoutDetail: { workoutId: string };
  ExercisePlayer: { exerciseId: string };
};

// Profile Stack Types
export type ProfileStackParamList = {
  ProfileMain: undefined;
  PrivacySettings: undefined;
  ProfileSettings: undefined;
  AppPreferences: undefined;
  NotificationSettings: undefined;
  HelpSupport: undefined;
  DataPrivacy: undefined;
};

// Habits Stack Types
export type HabitsStackParamList = {
  HabitsList: undefined;
  AddHabit: undefined;
};

// Onboarding Stack Types
export type OnboardingStackParamList = {
  Splash: undefined;
  LanguageSelection: undefined;
  Intro: undefined;
  AuthSelection: undefined;
  AccountCreation: undefined;
  VerifyEmail: { email: string; userId: string };
  Terms: undefined;
  CreateProfile: undefined;
  GeneralQuestionnaire: undefined;
  PlanSelection: undefined;
  WorkoutQuestionnaire: undefined;
  NutritionQuestionnaire: undefined;
  AIPersonaSelection: undefined;
  OnboardingComplete: undefined;
};

// Root Stack Types
export type RootStackParamList = {
  Auth: { screen?: keyof AuthStackParamList };
  App: { screen?: keyof AppTabParamList };
  Onboarding: { screen?: keyof OnboardingStackParamList };
  ProfileSettings: undefined;
  AppPreferences: undefined;
  NotificationSettings: undefined;
  HelpSupport: undefined;
  DataPrivacy: undefined;
  PrivacySettings: undefined;
  Notifications: undefined;
  EditProfile: undefined;
};

// Screen Props Types
export type AuthStackScreenProps<T extends keyof AuthStackParamList> =
  StackScreenProps<AuthStackParamList, T>;

export type AppTabScreenProps<T extends keyof AppTabParamList> =
  BottomTabScreenProps<AppTabParamList, T>;

export type OnboardingStackScreenProps<
  T extends keyof OnboardingStackParamList,
> = StackScreenProps<OnboardingStackParamList, T>;

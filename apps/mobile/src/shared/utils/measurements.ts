export type UnitSystem = 'metric' | 'imperial';

export function kgToLb(kg: number): number {
  return +(kg * 2.20462).toFixed(1);
}
export function lbToKg(lb: number): number {
  return +(lb / 2.20462).toFixed(1);
}

export function cmToFtIn(cm: number): { ft: number; in: number } {
  const totalInches = cm / 2.54;
  const ft = Math.floor(totalInches / 12);
  const inches = Math.round(totalInches - ft * 12);
  return { ft, in: inches };
}
export function ftInToCm(ft: number, inches: number): number {
  return Math.round((ft * 12 + inches) * 2.54);
}

function toNumber(val: unknown): number | null {
  const n =
    typeof val === 'number' ? val : Number(String(val).replace(/[^0-9.]/g, ''));
  return isFinite(n) ? n : null;
}

// Heuristic parsers that can use unitSystem hint
export function parseHeightToCm(
  input: string | number,
  unitSystem?: UnitSystem
): number | null {
  if (input == null) return null;
  const raw = String(input).trim().toLowerCase();

  // Try patterns: 5'10", 5' 10, 5 ft 10 in
  const ftInMatch = raw.match(
    /(\d+)\s*(?:ft|'|feet)\s*(\d{1,2})?\s*(?:in|"|inches)?/
  );
  if (ftInMatch) {
    const ft = parseInt(ftInMatch[1], 10) || 0;
    const inches = parseInt(ftInMatch[2] || '0', 10) || 0;
    return ftInToCm(ft, inches);
  }

  // Pattern: inches only
  const inchesMatch = raw.match(/(\d+(?:\.\d+)?)\s*(?:in|"|inches)/);
  if (inchesMatch) {
    const inches = parseFloat(inchesMatch[1]);
    return Math.round(inches * 2.54);
  }

  // Pattern: cm
  const cmMatch = raw.match(/(\d+(?:\.\d+)?)\s*cm/);
  if (cmMatch) {
    return Math.round(parseFloat(cmMatch[1]));
  }

  // Bare number: decide by unitSystem hint or magnitude
  const num = toNumber(raw);
  if (num == null) return null;
  if (unitSystem === 'metric') {
    return Math.round(num);
  }
  if (unitSystem === 'imperial') {
    // If user typed feet only, convert; if looks like inches, treat as inches
    if (num <= 8) {
      return ftInToCm(
        Math.floor(num),
        Math.round((num - Math.floor(num)) * 12)
      );
    }
    return Math.round(num * 2.54);
  }
  // Fallback by magnitude
  if (num >= 100) return Math.round(num); // assume cm
  if (num <= 8) return ftInToCm(num, 0); // feet
  return Math.round(num * 2.54); // inches
}

export function parseWeightToKg(
  input: string | number,
  unitSystem?: UnitSystem
): number | null {
  if (input == null) return null;
  const raw = String(input).trim().toLowerCase();

  const kgMatch = raw.match(/(\d+(?:\.\d+)?)\s*kg/);
  if (kgMatch) return +parseFloat(kgMatch[1]).toFixed(1);

  const lbMatch = raw.match(/(\d+(?:\.\d+)?)\s*(?:lb|lbs|pounds)/);
  if (lbMatch) return lbToKg(parseFloat(lbMatch[1]));

  const num = toNumber(raw);
  if (num == null) return null;

  if (unitSystem === 'metric') return +num.toFixed(1);
  if (unitSystem === 'imperial') return lbToKg(num);

  // Fallback by magnitude (heuristic)
  if (num > 140) return lbToKg(num);
  return +num.toFixed(1);
}

export function defaultUnitSystemFromLocale(): UnitSystem {
  try {
    const locale = Intl.DateTimeFormat().resolvedOptions().locale || '';
    return /(^|[-_])US($|[-_])/i.test(locale) ? 'imperial' : 'metric';
  } catch {
    return 'metric';
  }
}

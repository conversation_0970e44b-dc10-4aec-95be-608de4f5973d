import { AppError } from '../types';

/**
 * Standard error handling utilities for consistent error management
 */

export class AppErrorHandler {
  /**
   * Creates a standardized AppError from any error
   */
  static createAppError(error: unknown, context?: string): AppError {
    if (error instanceof Error) {
      return {
        message: error.message,
        code: error.name,
        details: {
          context,
          stack: __DEV__ ? error.stack : undefined,
        },
      };
    }

    if (typeof error === 'string') {
      return {
        message: error,
        code: 'UNKNOWN_ERROR',
        details: { context },
      };
    }

    return {
      message: 'An unexpected error occurred',
      code: 'UNKNOWN_ERROR',
      details: { context, originalError: error },
    };
  }

  /**
   * Logs error with consistent format
   */
  static logError(error: AppError, level: 'error' | 'warn' = 'error') {
    const logMessage = `[${error.code}] ${error.message}`;

    if (level === 'error') {
      console.error(logMessage, error.details);
    } else {
      console.warn(logMessage, error.details);
    }
  }

  /**
   * Gets user-friendly error message
   */
  static getUserFriendlyMessage(error: AppError): string {
    // Map specific error codes to user-friendly messages
    const errorMessages: Record<string, string> = {
      NETWORK_ERROR: 'Please check your internet connection and try again.',
      UNAUTHORIZED: 'Please log in to continue.',
      FORBIDDEN: "You don't have permission to perform this action.",
      NOT_FOUND: 'The requested resource was not found.',
      VALIDATION_ERROR: 'Please check your input and try again.',
      SERVER_ERROR:
        "We're experiencing technical difficulties. Please try again later.",
    };

    return (
      (error.code && errorMessages[error.code]) ??
      error.message ??
      'Something went wrong. Please try again.'
    );
  }

  /**
   * Handles Supabase errors specifically
   */
  static handleSupabaseError(error: unknown, context?: string): AppError {
    if (isErrorWithCode(error)) {
      // Map Supabase error codes to our error codes
      const supabaseErrorMap: Record<string, string> = {
        PGRST116: 'NOT_FOUND', // No rows found
        '23505': 'DUPLICATE_ENTRY', // Unique constraint violation
        '42501': 'FORBIDDEN', // Insufficient privileges
        '08006': 'NETWORK_ERROR', // Connection failure
      };

      const mappedCode = supabaseErrorMap[error.code] ?? 'DATABASE_ERROR';

      return {
        message: error.message ?? 'Database operation failed',
        code: mappedCode,
        details: {
          context,
          supabaseCode: error.code,
          hint: error.hint,
        },
      };
    }

    return this.createAppError(error, context);
  }

  /**
   * Handles network/fetch errors
   */
  static handleNetworkError(error: any, context?: string): AppError {
    if (error?.name === 'TypeError' && error?.message?.includes('fetch')) {
      return {
        message: 'Network connection failed',
        code: 'NETWORK_ERROR',
        details: { context },
      };
    }

    if (error?.status) {
      const statusErrorMap: Record<number, string> = {
        400: 'VALIDATION_ERROR',
        401: 'UNAUTHORIZED',
        403: 'FORBIDDEN',
        404: 'NOT_FOUND',
        500: 'SERVER_ERROR',
        502: 'SERVER_ERROR',
        503: 'SERVER_ERROR',
      };

      const code = statusErrorMap[error.status] ?? 'HTTP_ERROR';

      return {
        message: error.statusText ?? `HTTP ${error.status} error`,
        code,
        details: {
          context,
          status: error.status,
          statusText: error.statusText,
        },
      };
    }

    return this.createAppError(error, context);
  }
}

/**
 * Hook for consistent error handling in components
 */
export const useErrorHandler = () => {
  const handleError = (error: unknown, context?: string): AppError => {
    const appError = AppErrorHandler.createAppError(error, context);
    AppErrorHandler.logError(appError);
    return appError;
  };

  const handleSupabaseError = (error: any, context?: string): AppError => {
    const appError = AppErrorHandler.handleSupabaseError(error, context);
    AppErrorHandler.logError(appError);
    return appError;
  };

  const handleNetworkError = (error: any, context?: string): AppError => {
    const appError = AppErrorHandler.handleNetworkError(error, context);
    AppErrorHandler.logError(appError);
    return appError;
  };

  const getUserMessage = (error: AppError): string => {
    return AppErrorHandler.getUserFriendlyMessage(error);
  };

  return {
    handleError,
    handleSupabaseError,
    handleNetworkError,
    getUserMessage,
  };
};

/**
 * Async wrapper that handles errors consistently
 */
export const withErrorHandling = async <T>(
  operation: () => Promise<T>,
  context?: string
): Promise<{ data?: T; error?: AppError }> => {
  try {
    const data = await operation();
    return { data };
  } catch (error) {
    const appError = AppErrorHandler.createAppError(error, context);
    AppErrorHandler.logError(appError);
    return { error: appError };
  }
};

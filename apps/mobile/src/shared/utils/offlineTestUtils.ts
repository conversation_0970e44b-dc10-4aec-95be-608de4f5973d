import AsyncStorage from '@react-native-async-storage/async-storage';

// Utility functions for testing offline functionality

export const clearOfflineData = async () => {
  try {
    await AsyncStorage.removeItem('offline_queue');
    console.log('Offline data cleared successfully');
  } catch (error) {
    console.error('Error clearing offline data:', error);
  }
};

export const getOfflineQueue = async () => {
  try {
    const queueStr = await AsyncStorage.getItem('offline_queue');
    return queueStr ? JSON.parse(queueStr) : [];
  } catch (error) {
    console.error('Error getting offline queue:', error);
    return [];
  }
};

export const printOfflineQueue = async () => {
  try {
    const queue = await getOfflineQueue();
    console.log('Offline Queue Contents:', JSON.stringify(queue, null, 2));
  } catch (error) {
    console.error('Error printing offline queue:', error);
  }
};

export const simulateOfflineScenario = async () => {
  // This function would be used in testing to simulate offline scenarios
  console.log('Simulating offline scenario...');
  // In a real test, you would mock network connectivity here
};

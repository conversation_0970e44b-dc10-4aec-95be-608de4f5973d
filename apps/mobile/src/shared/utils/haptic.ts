// Define the shape of the haptic feedback module for type safety
interface HapticFeedbackModule {
  trigger: (type: string, options: object) => void;
}

// Conditional import for haptic feedback to work with Expo Go
let ReactNativeHapticFeedback: HapticFeedbackModule | null;
let hapticModuleAvailable = false;

try {
  // Try to import the module - this will fail in Expo Go
  ReactNativeHapticFeedback = require('react-native-haptic-feedback').default;
  hapticModuleAvailable = true;
} catch (error) {
  // Module not available (e.g., in Expo Go)
  ReactNativeHapticFeedback = null;
}

const hapticOptions = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false,
};

export const triggerHaptic = (
  type:
    | 'selection'
    | 'impactLight'
    | 'impactMedium'
    | 'impactHeavy'
    | 'notificationSuccess'
    | 'notificationWarning'
    | 'notificationError' = 'impactLight'
) => {
  if (hapticModuleAvailable && ReactNativeHapticFeedback) {
    try {
      ReactNativeHapticFeedback.trigger(type, hapticOptions);
    } catch (error) {
      // Silently fail if haptic feedback fails for any reason
      console.log('Haptic feedback failed:', error);
    }
  } else {
    // In Expo Go, haptic feedback is not available
    console.log('Haptic feedback not available in Expo Go');
  }
};

export const triggerSelectionHaptic = () => {
  triggerHaptic('selection');
};

export const triggerSuccessHaptic = () => {
  triggerHaptic('notificationSuccess');
};

export const triggerErrorHaptic = () => {
  triggerHaptic('notificationError');
};

export const triggerImpactHaptic = (
  intensity: 'light' | 'medium' | 'heavy' = 'light'
) => {
  const type =
    intensity === 'light'
      ? 'impactLight'
      : intensity === 'medium'
        ? 'impactMedium'
        : 'impactHeavy';
  triggerHaptic(type);
};

import NetInfo from '@react-native-community/netinfo';
import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { syncOfflineQueue } from '../services/offline/offlineService';

interface OfflineContextType {
  isOnline: boolean;
  isSyncing: boolean;
  setIsOnline: (online: boolean) => void;
  setIsSyncing: (syncing: boolean) => void;
}

const OfflineContext = createContext<OfflineContextType | undefined>(undefined);

export const OfflineProvider = ({ children }: { children: ReactNode }) => {
  const [isOnline, setIsOnline] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const online = state.isConnected && state.isInternetReachable;
      setIsOnline(online === true);

      // If we just came back online, sync any pending data
      if (online === true && !isOnline) {
        syncPendingData();
      }
    });

    return () => {
      unsubscribe();
    };
  }, [isOnline]);

  const syncPendingData = async () => {
    setIsSyncing(true);
    try {
      await syncOfflineQueue();
    } catch (error) {
      console.error('Error syncing offline data:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <OfflineContext.Provider
      value={{ isOnline, isSyncing, setIsOnline, setIsSyncing }}
    >
      {children}
    </OfflineContext.Provider>
  );
};

export const useOffline = () => {
  const context = useContext(OfflineContext);
  if (context === undefined) {
    throw new Error('useOffline must be used within an OfflineProvider');
  }
  return context;
};

import React, { createContext, useContext, useState, useEffect } from 'react';
import * as Localization from '../../localization';

// Define the shape of our language context
interface LanguageContextType {
  language: string;
  setLanguage: (language: string) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined
);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [language, setLanguage] = useState<string>('en');

  // Load saved language preference on app start
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const savedLanguage = await Localization.loadLanguage();
        setLanguage(savedLanguage);
      } catch (error) {
        console.error('Error loading language preference:', error);
      }
    };

    loadLanguage();
  }, []);

  // Save language preference when it changes
  const updateLanguage = async (newLanguage: string) => {
    try {
      setLanguage(newLanguage);
      await Localization.saveLanguage(newLanguage);
    } catch (error) {
      console.error('Error saving language preference:', error);
    }
  };

  // Translation function
  const t = (key: string): string => {
    return Localization.t(key, language);
  };

  return (
    <LanguageContext.Provider
      value={{ language, setLanguage: updateLanguage, t }}
    >
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use the language context
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

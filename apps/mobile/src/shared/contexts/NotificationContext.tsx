import Constants from 'expo-constants';
import * as Notifications from 'expo-notifications';
import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import {
  notificationService,
  NotificationData,
} from '../../services/notificationService';

// Check if we're running in Expo Go
const isExpoGo = Constants.appOwnership === 'expo';

interface NotificationContextType {
  notifications: NotificationData[];
  unreadCount: number;
  hasPermission: boolean;
  isLoading: boolean;
  requestPermissions: () => Promise<boolean>;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  removeNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
  addNotification: (
    notification: Omit<NotificationData, 'id' | 'timestamp' | 'read'>
  ) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
}) => {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [hasPermission, setHasPermission] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    let isMounted = true;

    const initializeNotifications = async () => {
      try {
        // Skip notification setup if running in Expo Go
        if (isExpoGo) {
          console.warn('Notifications not supported in Expo Go');
          if (isMounted) {
            setHasPermission(false);
            setIsLoading(false);
          }
          return;
        }

        // Check current permissions
        const { status } = await Notifications.getPermissionsAsync();
        if (isMounted) {
          setHasPermission(status === 'granted');
        }

        // Load existing notifications
        const existingNotifications = notificationService.getNotifications();
        if (isMounted) {
          setNotifications(existingNotifications);
        }

        // Subscribe to notification updates
        const unsubscribe = notificationService.subscribe(
          updatedNotifications => {
            if (isMounted) {
              setNotifications(updatedNotifications);
            }
          }
        );

        // Set up notification listeners
        const notificationListener =
          Notifications.addNotificationReceivedListener(notification => {
            console.log('Notification received:', notification);

            // Add to in-app notifications
            notificationService.addNotification({
              title: notification.request.content.title ?? 'Notification',
              body: notification.request.content.body ?? '',
              data: notification.request.content.data,
              categoryId:
                notification.request.content.categoryIdentifier ?? undefined,
            });
          });

        const responseListener =
          Notifications.addNotificationResponseReceivedListener(response => {
            console.log('Notification response:', response);

            // Handle notification tap
            const notificationData = response.notification.request.content.data;
            if (notificationData?.type === 'habit_reminder') {
              // TODO: Navigate to habits screen
              console.log(
                'Navigate to habits for habit:',
                notificationData.habitId
              );
            } else if (notificationData?.type === 'workout_reminder') {
              // TODO: Navigate to exercise screen
              console.log('Navigate to exercise screen');
            }
          });

        if (isMounted) {
          setIsLoading(false);
        }

        // Cleanup function
        return () => {
          unsubscribe();
          notificationListener.remove();
          responseListener.remove();
        };
      } catch (error) {
        console.error('Error initializing notifications:', error);
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    const cleanup = initializeNotifications();

    return () => {
      isMounted = false;
      cleanup?.then(cleanupFn => cleanupFn?.());
    };
  }, []);

  const requestPermissions = async (): Promise<boolean> => {
    try {
      const granted = await notificationService.requestPermissions();
      setHasPermission(granted);
      return granted;
    } catch (error) {
      console.error('Error requesting permissions:', error);
      return false;
    }
  };

  const markAsRead = (notificationId: string) => {
    notificationService.markAsRead(notificationId);
  };

  const markAllAsRead = () => {
    notificationService.markAllAsRead();
  };

  const removeNotification = (notificationId: string) => {
    notificationService.removeNotification(notificationId);
  };

  const clearAllNotifications = () => {
    notificationService.clearAllNotifications();
  };

  const addNotification = (
    notification: Omit<NotificationData, 'id' | 'timestamp' | 'read'>
  ) => {
    notificationService.addNotification(notification);
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  const value: NotificationContextType = React.useMemo(
    () => ({
      notifications,
      unreadCount,
      hasPermission,
      isLoading,
      requestPermissions,
      markAsRead,
      markAllAsRead,
      removeNotification,
      clearAllNotifications,
      addNotification,
    }),
    [notifications, unreadCount, hasPermission, isLoading]
  );

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      'useNotifications must be used within a NotificationProvider'
    );
  }
  return context;
};

// Export the context for direct access if needed
export { NotificationContext };

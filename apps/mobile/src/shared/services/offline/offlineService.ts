import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { useEffect, useState } from 'react';
import { supabase } from '../../../lib/supabase';
import { Meal, ProgressLog, Workout } from '../../hooks/useOfflineData';

// Base type for offline actions
interface OfflineActionBase {
  id: string;
  action: 'create' | 'update' | 'delete';
  timestamp: number;
  synced?: boolean;
}

// Discriminated union for specific entity types
export type OfflineItem = OfflineActionBase & (
  | { type: 'progress_log'; data: ProgressLog }
  | { type: 'workout'; data: Workout }
  | { type: 'meal'; data: Meal }
  // TODO: Define specific types for these entities as well
  | { type: 'workout_log'; data: { [key: string]: unknown } }
  | { type: 'meal_log'; data: { [key: string]: unknown } }
  | { type: 'meal_plan'; data: { [key: string]: unknown } }
);

// Initialize offline queue
export const initOfflineService = async () => {
  try {
    const queue = await AsyncStorage.getItem('offline_queue');
    if (!queue) {
      await AsyncStorage.setItem('offline_queue', JSON.stringify([]));
    }
  } catch (error) {
    console.error('Error initializing offline service:', error);
  }
};

// Add item to offline queue
export const addToOfflineQueue = async (item: {
  type: OfflineItem['type'];
  data: OfflineItem['data'];
  action: OfflineItem['action'];
}) => {
  try {
    const queueStr = await AsyncStorage.getItem('offline_queue');
    const queue: OfflineItem[] = queueStr ? JSON.parse(queueStr) : [];

    const newItem: OfflineItem = {
      id: Math.random().toString(36).substr(2, 9),
      type: item.type,
      data: item.data,
      action: item.action,
      timestamp: Date.now(),
    } as OfflineItem; // Cast to assert the correlation between type and data

    queue.push(newItem);
    await AsyncStorage.setItem('offline_queue', JSON.stringify(queue));

    // Try to sync immediately
    syncOfflineQueue();

    return newItem.id;
  } catch (error) {
    console.error('Error adding to offline queue:', error);
    throw error;
  }
};

// Sync offline queue with server
export const syncOfflineQueue = async () => {
  try {
    // Check network connectivity
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      console.log('No internet connection. Skipping sync.');
      return;
    }

    const queueStr = await AsyncStorage.getItem('offline_queue');
    const queue: OfflineItem[] = queueStr ? JSON.parse(queueStr) : [];

    // Filter unsynced items
    const unsyncedItems = queue.filter(item => !item.synced);

    if (unsyncedItems.length === 0) {
      console.log('No items to sync.');
      return;
    }

    console.log(`Syncing ${unsyncedItems.length} items...`);

    // Process each item
    for (const item of unsyncedItems) {
      try {
        switch (item.type) {
          case 'progress_log':
            await syncProgressLog(item);
            break;
          case 'workout':
            await syncWorkout(item);
            break;
          case 'meal':
            await syncMeal(item);
            break;
          // Add more cases as needed
        }

        // Mark item as synced
        item.synced = true;
      } catch (error) {
        console.error(`Error syncing item ${item.id}:`, error);
        // Don't mark as synced if there was an error
      }
    }

    // Update queue in storage
    await AsyncStorage.setItem('offline_queue', JSON.stringify(queue));

    console.log('Sync completed.');
  } catch (error) {
    console.error('Error syncing offline queue:', error);
  }
};

// Sync progress log
const syncProgressLog = async (item: OfflineItem) => {
  const { action, data } = item;

  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      throw new Error('Not authenticated');
    }

    switch (action) {
      case 'create':
        const { error: createError } = await supabase
          .from('progress_logs')
          .insert({
            ...data,
            user_id: session.user.id,
          });

        if (createError) {
          throw createError;
        }
        break;

      case 'update':
        const { error: updateError } = await supabase
          .from('progress_logs')
          .update(data)
          .eq('id', data.id)
          .eq('user_id', session.user.id);

        if (updateError) {
          throw updateError;
        }
        break;

      case 'delete':
        const { error: deleteError } = await supabase
          .from('progress_logs')
          .delete()
          .eq('id', data.id)
          .eq('user_id', session.user.id);

        if (deleteError) {
          throw deleteError;
        }
        break;
    }
  } catch (error) {
    console.error('Error syncing progress log:', error);
    throw error;
  }
};

// Sync workout
const syncWorkout = async (item: OfflineItem) => {
  // Implement workout sync logic
  console.log('Syncing workout:', item);
};

// Sync meal
const syncMeal = async (item: OfflineItem) => {
  // Implement meal sync logic
  console.log('Syncing meal:', item);
};

// Get network status
export const useNetworkStatus = () => {
  const [isConnected, setIsConnected] = useState<boolean>(true);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsConnected(state.isConnected ?? false);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  return { isConnected };
};

import {
  analytics as noopAnalytics,
  IAnalyticsService,
} from './analyticsService';
import { createAnalytics } from './posthogAnalyticsService';

// Exposed singleton for app usage
export const analytics: IAnalyticsService = (() => {
  // Prefer provider behind feature flag; fallback to no-op
  try {
    const svc = createAnalytics();
    return svc;
  } catch {
    return noopAnalytics;
  }
})();

export type { IAnalyticsService } from './analyticsService';
export type { AnalyticsEvent } from './analyticsService';

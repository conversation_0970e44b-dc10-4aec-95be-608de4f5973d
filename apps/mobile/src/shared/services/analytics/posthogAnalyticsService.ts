import PostHog from 'posthog-react-native';
import { IAnalyticsService, AnalyticsEvent } from './analyticsService';

let posthog: PostHog | null = null;

// Initialize PostHog lazily to avoid issues in test or unsupported envs
function ensurePosthog() {
  if (posthog) return posthog;
  const apiKey = process.env.EXPO_PUBLIC_POSTHOG_KEY;
  const host =
    process.env.EXPO_PUBLIC_POSTHOG_HOST ?? 'https://us.i.posthog.com';
  const enabled = process.env.EXPO_PUBLIC_ENABLE_ANALYTICS === 'true';

  if (!enabled || !apiKey) return null;

  posthog = new PostHog(apiKey, {
    host,
    captureAppLifecycleEvents: true,
  });
  return posthog;
}

export class PostHogAnalyticsService implements IAnalyticsService {
  trackEvent(event: AnalyticsEvent): void {
    const ph = ensurePosthog();
    if (!ph) return;
    ph.capture(event.name, event.properties ?? {});
  }
  trackScreen(name: string, properties?: Record<string, string | number | boolean | null>): void {
    const ph = ensurePosthog();
    if (!ph) return;
    ph.screen(name, properties ?? {});
  }
  setUser(userId: string | null, traits?: Record<string, string | number | boolean | null>): void {
    const ph = ensurePosthog();
    if (!ph) return;
    if (userId) {
      ph.identify(userId, traits ?? {});
    } else {
      ph.reset();
    }
  }
}

export function createAnalytics(): IAnalyticsService {
  const enabled = process.env.EXPO_PUBLIC_ENABLE_ANALYTICS === 'true';
  const apiKey = process.env.EXPO_PUBLIC_POSTHOG_KEY;
  if (enabled && apiKey) {
    return new PostHogAnalyticsService();
  }
  // Fallback to no-op
  const { analytics } = require('./analyticsService');
  return analytics;
}

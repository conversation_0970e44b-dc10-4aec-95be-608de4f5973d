export type AnalyticsEvent = {
  name: string;
  properties?: Record<string, string | number | boolean | null>;
};

export interface IAnalyticsService {
  trackEvent(event: AnalyticsEvent): void;
  trackScreen(name: string, properties?: Record<string, string | number | boolean | null>): void;
  setUser(userId: string | null, traits?: Record<string, string | number | boolean | null>): void;
}

class NoopAnalyticsService implements IAnalyticsService {
  trackEvent(_event: AnalyticsEvent) {}
  trackScreen(_name: string, _properties?: Record<string, string | number | boolean | null>) {}
  setUser(_userId: string | null, _traits?: Record<string, string | number | boolean | null>) {}
}

export const analytics: IAnalyticsService = new NoopAnalyticsService();

import { format } from 'date-fns';

// This is a mock of the API service layer. It simulates fetching data.
// In a real application, this would use fetch or a library like Axios
// to call the Supabase Edge Function at `/api/v1/plans/daily`.

interface Recipe {
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  recipe_id: string;
  name: string;
}

interface MealPlan {
  date: string;
  recipes: Recipe[];
}

const mockMealPlan: MealPlan = {
  date: '2025-08-01',
  recipes: [
    {
      meal_type: 'breakfast',
      recipe_id: '1',
      name: 'Scrambled Eggs with Spinach',
    },
    {
      meal_type: 'lunch',
      recipe_id: '2',
      name: 'Quinoa Salad with Chickpeas',
    },
    {
      meal_type: 'dinner',
      recipe_id: '3',
      name: 'Baked Salmon with Asparagus',
    },
    {
      meal_type: 'snack',
      recipe_id: '4',
      name: 'Apple Slices with Peanut Butter',
    },
  ],
};

export const getDailyPlan = async (date: Date): Promise<MealPlan> => {
  console.log(`Fetching plan for: ${format(date, 'yyyy-MM-dd')}`);

  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));

  // In a real implementation, you would fetch from your API:
  // const response = await fetch(`/api/v1/plans/daily?date=${format(date, 'yyyy-MM-dd')}`);
  // if (!response.ok) {
  //   throw new Error('Failed to fetch daily plan');
  // }
  // const data = await response.json();
  // return data.meal_plan;

  // For now, we return the mock data.
  // To simulate different data for different days, you could modify the mock data based on the date.
  return mockMealPlan;
};

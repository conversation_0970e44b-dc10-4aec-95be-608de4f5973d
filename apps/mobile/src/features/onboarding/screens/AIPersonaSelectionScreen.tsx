import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { OnboardingStackScreenProps } from '../../../shared/types/navigation';
import { useOnboardingStore } from '../store/onboardingStore';

type Props = OnboardingStackScreenProps<'AIPersonaSelection'>;

export default function AIPersonaSelectionScreen({ navigation }: Props) {
  const { setAiPersona } = useOnboardingStore();

  const handleSelectPersona = (persona: string) => {
    setAiPersona(persona);
    navigation.navigate('OnboardingComplete');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Choose Your AI Coach</Text>
      <Text style={styles.description}>
        Select the persona for your AI coach.
      </Text>

      <TouchableOpacity
        style={styles.button}
        onPress={() => handleSelectPersona('Friendly & Encouraging')}
      >
        <Text style={styles.buttonText}>Friendly & Encouraging</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.button}
        onPress={() => handleSelectPersona('Strict & No-Nonsense')}
      >
        <Text style={styles.buttonText}>Strict & No-Nonsense</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.button}
        onPress={() => handleSelectPersona('Data-Driven & Scientific')}
      >
        <Text style={styles.buttonText}>Data-Driven & Scientific</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    paddingHorizontal: 20,
    lineHeight: 24,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useRoute } from '@react-navigation/native';
import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { supabase } from '../../../lib/supabase';
import { emailVerificationService } from '../../../services/emailVerificationService';
import { OnboardingStackScreenProps } from '../../../shared/types/navigation';

type Props = OnboardingStackScreenProps<'VerifyEmail'>;
type RouteParams = {
  email: string;
  userId: string;
};

export default function VerifyEmailScreen({ navigation }: Props) {
  const route = useRoute();
  const { email, userId } = route.params as RouteParams;

  const [_loading, _setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [isEmailConfirmed, setIsEmailConfirmed] = useState(false);

  // Check email confirmation status periodically
  useEffect(() => {
    const checkEmailConfirmation = async () => {
      try {
        const {
          data: { user },
          error: _error,
        } = await supabase.auth.getUser();

        if (user?.email_confirmed_at) {
          setIsEmailConfirmed(true);
          // Create basic profile and navigate to Terms
          await createProfileAndNavigate();
        }
      } catch (error) {
        console.error('Error checking email confirmation:', error);
      }
    };

    // Check immediately
    checkEmailConfirmation();

    // Check every 3 seconds
    const interval = setInterval(checkEmailConfirmation, 3000);

    return () => clearInterval(interval);
  }, []);

  // Resend cooldown timer
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const createProfileAndNavigate = async () => {
    try {
      _setLoading(true);

      // Create basic profile
      const { error: profileError } = await supabase.from('profiles').upsert([
        {
          id: userId,
          email: email,
          onboarding_complete: false,
        },
      ]);

      if (profileError) {
        console.error('Error creating profile:', profileError);
        Alert.alert('Error', 'Failed to create profile. Please try again.');
        return;
      }

      // Navigate to Terms screen
      navigation.navigate('Terms');
    } catch (error) {
      console.error('Error in createProfileAndNavigate:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      _setLoading(false);
    }
  };

  const handleResendEmail = async () => {
    if (resendCooldown > 0) {
      return;
    }

    setResendLoading(true);

    try {
      const result =
        await emailVerificationService.resendConfirmationEmail(email);

      if (result.success) {
        Alert.alert('Email Sent', result.message);
        setResendCooldown(60); // 60 second cooldown
      } else {
        Alert.alert('Error', result.message);
      }
    } catch (error) {
      console.error('Error resending email:', error);
      Alert.alert('Error', 'Failed to resend email. Please try again.');
    } finally {
      setResendLoading(false);
    }
  };

  if (isEmailConfirmed) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.content}>
          <View style={styles.iconContainer}>
            <MaterialCommunityIcons
              name="check-circle"
              size={80}
              color="#4CAF50"
            />
          </View>

          <Text style={styles.title}>Email Verified!</Text>
          <Text style={styles.subtitle}>
            Your email has been successfully verified. Setting up your
            account...
          </Text>

          <ActivityIndicator
            size="large"
            color="#007AFF"
            style={styles.loader}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <View style={styles.content}>
          <View style={styles.iconContainer}>
            <MaterialCommunityIcons
              name="email-outline"
              size={80}
              color="#007AFF"
            />
          </View>

          <Text style={styles.title}>Check Your Email</Text>
          <Text style={styles.subtitle}>
            We've sent a confirmation link to:
          </Text>
          <Text style={styles.email}>{email}</Text>

          <Text style={styles.instructions}>
            Click the link in your email to verify your account. This page will
            automatically update once your email is confirmed.
          </Text>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[
                styles.resendButton,
                (resendLoading || resendCooldown > 0) && styles.disabledButton,
              ]}
              onPress={handleResendEmail}
              disabled={resendLoading || resendCooldown > 0}
            >
              {resendLoading ? (
                <ActivityIndicator size="small" color="#007AFF" />
              ) : (
                <Text style={styles.resendButtonText}>
                  {resendCooldown > 0
                    ? `Resend in ${resendCooldown}s`
                    : 'Resend Email'}
                </Text>
              )}
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>
              ← Back to Account Creation
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 8,
  },
  email: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    textAlign: 'center',
    marginBottom: 24,
  },
  instructions: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 32,
    paddingHorizontal: 16,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 24,
  },
  resendButton: {
    backgroundColor: '#f8f9fa',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#007AFF',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.6,
  },
  resendButtonText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '600',
  },
  backButton: {
    paddingVertical: 12,
  },
  backButtonText: {
    color: '#666',
    fontSize: 16,
  },
  loader: {
    marginTop: 24,
  },
});

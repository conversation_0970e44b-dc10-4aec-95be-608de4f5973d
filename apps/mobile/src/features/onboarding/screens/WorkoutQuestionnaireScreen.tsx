import React from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { OnboardingStackScreenProps } from '../../../shared/types/navigation';
import { useOnboardingStore } from '../store/onboardingStore';

type Props = OnboardingStackScreenProps<'WorkoutQuestionnaire'>;

export default function WorkoutQuestionnaireScreen({ navigation }: Props) {
  const {
    experienceLevel,
    knownInjuries,
    preferredExerciseTypes,
    availableEquipment,
    setWorkoutInfo,
    planType,
  } = useOnboardingStore();

  const handleSubmit = () => {
    // If user selected both plans, go to nutrition next.
    // Otherwise, skip to AI persona selection.
    if (planType === 'both') {
      navigation.navigate('NutritionQuestionnaire');
    } else {
      navigation.navigate('AIPersonaSelection');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Workout Details</Text>
      <Text style={styles.description}>
        Tell us about your workout preferences.
      </Text>

      <TextInput
        style={styles.input}
        placeholder="Experience Level (e.g., Beginner, Intermediate)"
        value={experienceLevel}
        onChangeText={text => setWorkoutInfo({ experienceLevel: text })}
      />
      <TextInput
        style={styles.input}
        placeholder="Any known injuries?"
        value={knownInjuries}
        onChangeText={text => setWorkoutInfo({ knownInjuries: text })}
      />
      <TextInput
        style={styles.input}
        placeholder="Preferred Exercise Types (e.g., Cardio, Strength)"
        value={preferredExerciseTypes}
        onChangeText={text => setWorkoutInfo({ preferredExerciseTypes: text })}
      />
      <TextInput
        style={styles.input}
        placeholder="Available Equipment (e.g., Dumbbells, Treadmill)"
        value={availableEquipment}
        onChangeText={text => setWorkoutInfo({ availableEquipment: text })}
      />
      <TouchableOpacity style={styles.button} onPress={handleSubmit}>
        <Text style={styles.buttonText}>Continue</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    paddingHorizontal: 20,
    lineHeight: 24,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

import React from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { useLanguage } from '../../../shared/contexts/LanguageContext';
import { OnboardingStackScreenProps } from '../../../shared/types/navigation';
import { useOnboardingStore } from '../store/onboardingStore';

type Props = OnboardingStackScreenProps<'GeneralQuestionnaire'>;

export default function GeneralQuestionnaireScreen({ navigation }: Props) {
  const { primaryGoal, setGeneralInfo } = useOnboardingStore();
  const { t: _t } = useLanguage();

  const goalOptions = [
    'Lose Weight',
    'Build Muscle',
    'Improve Fitness',
    'Maintain Health',
    'Increase Strength',
    'Improve Endurance',
  ];

  const handleGoalSelect = (goal: string) => {
    setGeneralInfo({ primaryGoal: goal });
  };

  const canContinue = primaryGoal.length > 0;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>What's Your Primary Goal?</Text>
      <Text style={styles.description}>
        Help us personalize your fitness and nutrition plans by selecting your
        main objective.
      </Text>

      <View style={styles.optionsContainer}>
        {goalOptions.map(goal => (
          <TouchableOpacity
            key={goal}
            style={[
              styles.optionButton,
              primaryGoal === goal && styles.selectedOption,
            ]}
            onPress={() => handleGoalSelect(goal)}
          >
            <Text
              style={[
                styles.optionText,
                primaryGoal === goal && styles.selectedOptionText,
              ]}
            >
              {goal}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <TextInput
        style={styles.input}
        placeholder="Or describe your custom goal..."
        value={primaryGoal}
        onChangeText={text => setGeneralInfo({ primaryGoal: text })}
        multiline
      />

      <TouchableOpacity
        style={[styles.button, !canContinue && styles.buttonDisabled]}
        onPress={() => navigation.navigate('PlanSelection')}
        disabled={!canContinue}
      >
        <Text style={styles.buttonText}>Continue</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    paddingHorizontal: 20,
    lineHeight: 24,
  },
  optionsContainer: {
    marginBottom: 24,
  },
  optionButton: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    backgroundColor: '#f9f9f9',
  },
  selectedOption: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  optionText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#333',
  },
  selectedOptionText: {
    color: '#fff',
    fontWeight: '600',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    minHeight: 60,
    textAlignVertical: 'top',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

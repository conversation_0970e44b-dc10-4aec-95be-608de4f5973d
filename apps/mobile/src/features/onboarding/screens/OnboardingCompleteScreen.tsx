import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useEffect } from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { supabase } from '../../../lib/supabase';
import { useLanguage } from '../../../shared/contexts/LanguageContext';
import { RootStackParamList } from '../../../shared/types/navigation';
import { useOnboardingStore } from '../store/onboardingStore';

export default function OnboardingCompleteScreen() {
  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();
  const store = useOnboardingStore();
  const { t } = useLanguage();

  useEffect(() => {
    const saveOnboardingData = async () => {
      const { data, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !data?.session?.user) {
        console.error('Error getting session or no user found:', sessionError);
        navigation.replace('Auth', {});
        return;
      }

      const session = data.session;

      try {
        const {
          fullName,
          age,
          height,
          primaryGoal,
          planType: _planType,
          experienceLevel,
          knownInjuries: _knownInjuries,
          preferredExerciseTypes: _preferredExerciseTypes,
          availableEquipment: _availableEquipment,
          dietaryPreferences,
          allergies: _allergies,
          budget: _budget,
          cuisineTypes: _cuisineTypes,
          aiPersona: _aiPersona,
          reset: _reset,
        } = store;

        // Calculate date of birth from age
        const currentYear = new Date().getFullYear();
        const birthYear = currentYear - parseInt(age, 10);
        const dateOfBirth = `${birthYear}-01-01`; // Using January 1st as default

        const updates = {
          id: session.user.id,
          full_name: fullName,
          date_of_birth: dateOfBirth,
          height_cm: parseInt(height, 10),
          fitness_goals: [primaryGoal],
          activity_level: experienceLevel,
          dietary_preferences: [dietaryPreferences],
          onboarding_complete: true,
          updated_at: new Date().toISOString(),
        };

        const { error } = await supabase
          .from('profiles')
          .update(updates)
          .eq('id', session.user.id);

        if (error) {
          throw error;
        }

        // Onboarding complete - database flag is sufficient for routing

        store.reset();
        navigation.replace('App', { screen: 'Home' });
      } catch (error) {
        console.error('Error saving onboarding data:', error);
        navigation.replace('Auth', {});
      }
    };

    saveOnboardingData();
  }, [navigation, store]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('onboarding.complete.title')}</Text>
      <Text style={styles.description}>
        {t('onboarding.complete.description')}
      </Text>
      <ActivityIndicator size="large" color="#007AFF" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    paddingHorizontal: 20,
    lineHeight: 24,
  },
});

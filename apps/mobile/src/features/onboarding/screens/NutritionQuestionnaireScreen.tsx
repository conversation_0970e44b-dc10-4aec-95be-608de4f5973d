import React from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { OnboardingStackScreenProps } from '../../../shared/types/navigation';
import { useOnboardingStore } from '../store/onboardingStore';

type Props = OnboardingStackScreenProps<'NutritionQuestionnaire'>;

export default function NutritionQuestionnaireScreen({ navigation }: Props) {
  const {
    dietaryPreferences,
    allergies,
    budget,
    cuisineTypes,
    setNutritionInfo,
  } = useOnboardingStore();

  const handleSubmit = () => {
    navigation.navigate('AIPersonaSelection');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Nutrition Details</Text>
      <Text style={styles.description}>
        Tell us about your nutrition preferences.
      </Text>

      <TextInput
        style={styles.input}
        placeholder="Dietary Preferences (e.g., Vegan, Keto)"
        value={dietaryPreferences}
        onChangeText={text => setNutritionInfo({ dietaryPreferences: text })}
      />
      <TextInput
        style={styles.input}
        placeholder="Any allergies?"
        value={allergies}
        onChangeText={text => setNutritionInfo({ allergies: text })}
      />
      <TextInput
        style={styles.input}
        placeholder="Weekly Food Budget"
        value={budget}
        onChangeText={text => setNutritionInfo({ budget: text })}
        keyboardType="numeric"
      />
      <TextInput
        style={styles.input}
        placeholder="Favorite Cuisine Types"
        value={cuisineTypes}
        onChangeText={text => setNutritionInfo({ cuisineTypes: text })}
      />
      <TouchableOpacity style={styles.button} onPress={handleSubmit}>
        <Text style={styles.buttonText}>Continue</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    paddingHorizontal: 20,
    lineHeight: 24,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

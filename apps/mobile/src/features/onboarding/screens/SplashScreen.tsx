import React, { useEffect } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { useLanguage } from '../../../shared/contexts/LanguageContext';

type SplashScreenProps = {
  navigation: any;
};

export default function SplashScreen({ navigation }: SplashScreenProps) {
  const { t } = useLanguage();

  useEffect(() => {
    // Navigate to language selection after a short delay
    const timer = setTimeout(() => {
      navigation.navigate('LanguageSelection');
    }, 2000);

    return () => clearTimeout(timer);
  }, [navigation]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('splash.title')}</Text>
      <Text style={styles.version}>{t('splash.version')}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  version: {
    fontSize: 16,
    color: '#666',
  },
});

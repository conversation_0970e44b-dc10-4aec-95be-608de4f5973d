import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  HelperText,
  ActivityIndicator,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { supabase } from '../../../lib/supabase';
import { OnboardingStackScreenProps } from '../../../shared/types/navigation';
import { useOnboardingStore } from '../store/onboardingStore';

type Props = OnboardingStackScreenProps<'CreateProfile'>;

export default function CreateProfileScreen({ navigation }: Props) {
  const { fullName, age, height, setGeneralInfo } = useOnboardingStore();

  const [localFullName, setLocalFullName] = useState(fullName);
  const [localAge, setLocalAge] = useState(age);
  const [localHeight, setLocalHeight] = useState(height);
  const [loading, setLoading] = useState(false);
  const [sessionLoading, setSessionLoading] = useState(true);
  const [errors, setErrors] = useState<{
    fullName?: string;
    age?: string;
    height?: string;
  }>({});

  // Check for session on component mount
  React.useEffect(() => {
    const checkSession = async () => {
      try {
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession();

        if (error || !session?.user) {
          console.log('No valid session found, redirecting to auth');
          Alert.alert('Session Expired', 'Please log in again to continue.', [
            { text: 'OK', onPress: () => navigation.navigate('AuthSelection') },
          ]);
          return;
        }

        console.log('Valid session found for user:', session.user.id);
      } catch (error) {
        console.error('Error checking session:', error);
        Alert.alert(
          'Error',
          'Unable to verify authentication. Please try again.'
        );
      } finally {
        setSessionLoading(false);
      }
    };

    checkSession();
  }, [navigation]);

  const validateForm = () => {
    const newErrors: typeof errors = {};

    if (!localFullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }

    if (!localAge.trim()) {
      newErrors.age = 'Age is required';
    } else {
      const ageNum = parseInt(localAge, 10);
      if (isNaN(ageNum) || ageNum < 13 || ageNum > 120) {
        newErrors.age = 'Please enter a valid age between 13 and 120';
      }
    }

    if (!localHeight.trim()) {
      newErrors.height = 'Height is required';
    } else {
      const heightNum = parseInt(localHeight, 10);
      if (isNaN(heightNum) || heightNum < 100 || heightNum > 250) {
        newErrors.height = 'Please enter a valid height between 100-250 cm';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateProfile = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Get current session
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();

      if (sessionError || !session?.user) {
        Alert.alert(
          'Error',
          'Authentication session not found. Please log in again.'
        );
        return;
      }

      // Calculate date of birth from age
      const currentYear = new Date().getFullYear();
      const birthYear = currentYear - parseInt(localAge, 10);
      const dateOfBirth = `${birthYear}-01-01`; // Using January 1st as default

      // Create basic profile in database
      const { error: profileError } = await supabase.from('profiles').upsert([
        {
          id: session.user.id,
          full_name: localFullName.trim(),
          date_of_birth: dateOfBirth,
          height_cm: parseInt(localHeight, 10),
          onboarding_complete: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ]);

      if (profileError) {
        console.error('Error creating profile:', profileError);
        Alert.alert('Error', 'Failed to create profile. Please try again.');
        return;
      }

      // Save to onboarding store
      setGeneralInfo({
        fullName: localFullName.trim(),
        age: localAge,
        height: localHeight,
      });

      // Navigate to the next screen (General Questionnaire)
      navigation.navigate('GeneralQuestionnaire');
    } catch (error) {
      console.error('Profile creation error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSkip = () => {
    // Save current data to store even if incomplete
    setGeneralInfo({
      fullName: localFullName.trim(),
      age: localAge,
      height: localHeight,
    });

    // Navigate to next screen
    navigation.navigate('GeneralQuestionnaire');
  };

  // Show loading screen while checking session
  if (sessionLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#fff" />
          <Text style={styles.loadingText}>Verifying authentication...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.header}>
            <Text style={styles.title}>Create Your Profile</Text>
            <Text style={styles.subtitle}>
              Let's start with some basic information about you
            </Text>
          </View>

          <Card style={styles.card}>
            <Card.Content>
              <View style={styles.inputContainer}>
                <TextInput
                  label="Full Name"
                  value={localFullName}
                  onChangeText={setLocalFullName}
                  mode="outlined"
                  style={styles.input}
                  error={!!errors.fullName}
                  placeholder="Enter your full name"
                />
                <HelperText type="error" visible={!!errors.fullName}>
                  {errors.fullName}
                </HelperText>
              </View>

              <View style={styles.inputContainer}>
                <TextInput
                  label="Age"
                  value={localAge}
                  onChangeText={setLocalAge}
                  mode="outlined"
                  style={styles.input}
                  error={!!errors.age}
                  placeholder="Enter your age"
                  keyboardType="numeric"
                />
                <HelperText type="error" visible={!!errors.age}>
                  {errors.age}
                </HelperText>
              </View>

              <View style={styles.inputContainer}>
                <TextInput
                  label="Height (cm)"
                  value={localHeight}
                  onChangeText={setLocalHeight}
                  mode="outlined"
                  style={styles.input}
                  error={!!errors.height}
                  placeholder="Enter your height in centimeters"
                  keyboardType="numeric"
                />
                <HelperText type="error" visible={!!errors.height}>
                  {errors.height}
                </HelperText>
              </View>
            </Card.Content>
          </Card>

          <View style={styles.buttonContainer}>
            <Button
              mode="contained"
              onPress={handleCreateProfile}
              style={styles.primaryButton}
              disabled={loading}
              loading={loading}
            >
              {loading ? 'Creating Profile...' : 'Create Profile'}
            </Button>

            <Button
              mode="text"
              onPress={handleSkip}
              style={styles.secondaryButton}
              disabled={loading}
            >
              Skip for now
            </Button>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    marginBottom: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    lineHeight: 22,
  },
  card: {
    backgroundColor: '#1a1a1a',
    marginBottom: 30,
  },
  inputContainer: {
    marginBottom: 16,
  },
  input: {
    backgroundColor: '#2a2a2a',
  },
  buttonContainer: {
    gap: 12,
    marginTop: 20,
  },
  primaryButton: {
    paddingVertical: 8,
  },
  secondaryButton: {
    paddingVertical: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
    marginTop: 16,
    textAlign: 'center',
  },
});

import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { useLanguage } from '../../../shared/contexts/LanguageContext';

type IntroScreenProps = {
  navigation: any;
};

export default function IntroScreen({ navigation }: IntroScreenProps) {
  const { t } = useLanguage();

  const handleContinue = () => {
    navigation.navigate('AuthSelection');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('onboarding.intro.title')}</Text>

      <Text style={styles.description}>
        {t('onboarding.intro.description')}
      </Text>

      <View style={styles.imagePlaceholder} />

      <TouchableOpacity onPress={handleContinue} style={styles.continueButton}>
        <Text style={styles.continueButtonText}>{t('common.next')}</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginVertical: 16,
    paddingHorizontal: 20,
    lineHeight: 24,
  },
  imagePlaceholder: {
    width: 200,
    height: 200,
    backgroundColor: '#f0f0f0',
    borderRadius: 100,
    marginVertical: 32,
  },
  continueButton: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 8,
    minWidth: 200,
    alignItems: 'center',
    marginTop: 32,
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

import React from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { useLanguage } from '../../../shared/contexts/LanguageContext';

type LanguageSelectionScreenProps = {
  navigation: any;
};

export default function LanguageSelectionScreen({
  navigation,
}: LanguageSelectionScreenProps) {
  const { language, setLanguage, t } = useLanguage();

  const handleLanguageSelect = (selectedLanguage: string) => {
    setLanguage(selectedLanguage);
  };

  const handleContinue = () => {
    navigation.navigate('Intro');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('language_selection.title')}</Text>

      <View style={styles.languageContainer}>
        <TouchableOpacity
          style={[
            styles.languageButton,
            language === 'en' && styles.selectedButton,
          ]}
          onPress={() => handleLanguageSelect('en')}
        >
          <Text style={styles.languageText}>
            {t('language_selection.english')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.languageButton,
            language === 'es' && styles.selectedButton,
          ]}
          onPress={() => handleLanguageSelect('es')}
        >
          <Text style={styles.languageText}>
            {t('language_selection.spanish')}
          </Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        style={[styles.continueButton, !language && styles.disabledButton]}
        onPress={handleContinue}
        disabled={!language}
      >
        <Text style={styles.continueButtonText}>
          {t('language_selection.continue')}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 32,
  },
  languageContainer: {
    width: '100%',
    maxWidth: 300,
    marginBottom: 32,
  },
  languageButton: {
    padding: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    marginBottom: 16,
  },
  selectedButton: {
    borderColor: '#007AFF',
    backgroundColor: '#e6f0ff',
  },
  languageText: {
    fontSize: 18,
    fontWeight: '500',
  },
  continueButton: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 8,
    minWidth: 200,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

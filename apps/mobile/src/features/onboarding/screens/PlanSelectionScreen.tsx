import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { OnboardingStackScreenProps } from '../../../shared/types/navigation';
import { useOnboardingStore } from '../store/onboardingStore';

type Props = OnboardingStackScreenProps<'PlanSelection'>;

export default function PlanSelectionScreen({ navigation }: Props) {
  const { setPlanType } = useOnboardingStore();

  const handlePlanSelection = (plan: 'workout' | 'meal' | 'both') => {
    setPlanType(plan);

    if (plan === 'workout') {
      navigation.navigate('WorkoutQuestionnaire');
    } else if (plan === 'meal') {
      navigation.navigate('NutritionQuestionnaire');
    } else {
      // If both, start with workout questionnaire
      navigation.navigate('WorkoutQuestionnaire');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Choose Your Plan</Text>
      <Text style={styles.description}>
        Select the type of plan you're interested in.
      </Text>

      <TouchableOpacity
        style={styles.button}
        onPress={() => handlePlanSelection('workout')}
      >
        <Text style={styles.buttonText}>Workout Plans</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.button}
        onPress={() => handlePlanSelection('meal')}
      >
        <Text style={styles.buttonText}>Meal Plans</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.button}
        onPress={() => handlePlanSelection('both')}
      >
        <Text style={styles.buttonText}>Both</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    paddingHorizontal: 20,
    lineHeight: 24,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

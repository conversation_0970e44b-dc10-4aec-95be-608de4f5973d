import AsyncStorage from '@react-native-async-storage/async-storage';
import React from 'react';
import {
  ScrollView,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import { supabase } from '../../../lib/supabase';
import { useLanguage } from '../../../shared/contexts/LanguageContext';

type TermsScreenProps = {
  navigation: any;
};

export default function TermsScreen({ navigation }: TermsScreenProps) {
  const { t } = useLanguage();

  const handleAgree = async () => {
    // Mark that user has agreed to terms
    await AsyncStorage.setItem('hasAgreedToTerms', 'true');

    // Update user profile to mark onboarding as complete
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (session?.user) {
      await supabase.from('profiles').upsert([
        {
          id: session.user.id,
          onboarding_complete: true,
          updated_at: new Date().toISOString(),
        },
      ]);
    }

    // Navigate to main app (this will trigger the root navigator to show the App stack)
    navigation.reset({
      index: 0,
      routes: [{ name: 'OnboardingComplete' }],
    });
  };

  const handleDisagree = () => {
    // For now, we'll just go back to the previous screen
    // In a real app, you might want to log out the user or show a warning
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{t('onboarding.terms.title')}</Text>

      <ScrollView style={styles.content}>
        <Text style={styles.description}>
          {t('onboarding.terms.description')}
        </Text>

        <Text style={styles.paragraph}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do
          eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad
          minim veniam, quis nostrud exercitation ullamco laboris nisi ut
          aliquip ex ea commodo consequat.
        </Text>

        <Text style={styles.paragraph}>
          Duis aute irure dolor in reprehenderit in voluptate velit esse cillum
          dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non
          proident, sunt in culpa qui officia deserunt mollit anim id est
          laborum.
        </Text>

        <Text style={styles.paragraph}>
          Sed ut perspiciatis unde omnis iste natus error sit voluptatem
          accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae
          ab illo inventore veritatis et quasi architecto beatae vitae dicta
          sunt explicabo.
        </Text>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <TouchableOpacity onPress={handleAgree} style={styles.agreeButton}>
          <Text style={styles.agreeButtonText}>
            {t('onboarding.terms.agree')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleDisagree}
          style={styles.disagreeButton}
        >
          <Text style={styles.disagreeButtonText}>
            {t('onboarding.terms.disagree')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
  },
  content: {
    flex: 1,
    marginVertical: 24,
  },
  description: {
    fontSize: 18,
    marginVertical: 8,
    fontWeight: '600',
  },
  paragraph: {
    fontSize: 16,
    marginVertical: 8,
    lineHeight: 24,
  },
  buttonContainer: {
    marginVertical: 16,
  },
  agreeButton: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  agreeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  disagreeButton: {
    backgroundColor: '#FF3B30',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  disagreeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

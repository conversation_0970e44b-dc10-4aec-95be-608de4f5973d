import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, <PERSON>ton, Card, Switch, Divider } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

interface AILearningConsentProps {
  onComplete: (consent: boolean) => void;
  onSkip?: () => void;
}

export const AILearningConsent: React.FC<AILearningConsentProps> = ({
  onComplete,
  onSkip,
}) => {
  const [hasConsented, setHasConsented] = useState(true); // Default to true for better UX

  const handleContinue = () => {
    onComplete(hasConsented);
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <View style={styles.header}>
        <Icon name="robot" size={64} color="#4CAF50" />
        <Text variant="headlineMedium" style={styles.title}>
          Help Improve PlateMotion for Everyone! 🤝
        </Text>
        <Text variant="bodyLarge" style={styles.subtitle}>
          Your contributions make our AI smarter for all users
        </Text>
      </View>

      <Card style={styles.benefitsCard}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.benefitsTitle}>
            How Your Data Helps:
          </Text>

          <View style={styles.benefitItem}>
            <Icon name="camera" size={24} color="#4CAF50" />
            <View style={styles.benefitText}>
              <Text variant="bodyMedium" style={styles.benefitTitle}>
                Better Food Recognition
              </Text>
              <Text variant="bodySmall" style={styles.benefitDescription}>
                Your meal photo corrections help improve AI accuracy for
                everyone
              </Text>
            </View>
          </View>

          <View style={styles.benefitItem}>
            <Icon name="chart-line" size={24} color="#4CAF50" />
            <View style={styles.benefitText}>
              <Text variant="bodyMedium" style={styles.benefitTitle}>
                Smarter Recommendations
              </Text>
              <Text variant="bodySmall" style={styles.benefitDescription}>
                Anonymous progress patterns help create better meal and workout
                plans
              </Text>
            </View>
          </View>

          <View style={styles.benefitItem}>
            <Icon name="shield-check" size={24} color="#4CAF50" />
            <View style={styles.benefitText}>
              <Text variant="bodyMedium" style={styles.benefitTitle}>
                Privacy Protected
              </Text>
              <Text variant="bodySmall" style={styles.benefitDescription}>
                Only anonymous, non-personal data is shared - never your
                identity
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>

      <Card style={styles.privacyCard}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.privacyTitle}>
            Your Privacy Matters 🔒
          </Text>

          <View style={styles.privacyPoints}>
            <Text variant="bodyMedium" style={styles.privacyPoint}>
              ✓ No personal information is shared
            </Text>
            <Text variant="bodyMedium" style={styles.privacyPoint}>
              ✓ All data is anonymized before processing
            </Text>
            <Text variant="bodyMedium" style={styles.privacyPoint}>
              ✓ You can opt out anytime in settings
            </Text>
            <Text variant="bodyMedium" style={styles.privacyPoint}>
              ✓ Your data stays on your device by default
            </Text>
          </View>
        </Card.Content>
      </Card>

      <View style={styles.consentSection}>
        <View style={styles.switchContainer}>
          <View style={styles.switchText}>
            <Text variant="titleMedium">
              Help improve PlateMotion for everyone
            </Text>
            <Text variant="bodySmall" style={styles.switchDescription}>
              Share anonymous corrections and patterns to make our AI better
            </Text>
          </View>
          <Switch
            value={hasConsented}
            onValueChange={setHasConsented}
            color="#4CAF50"
          />
        </View>
      </View>

      <Divider style={styles.divider} />

      <View style={styles.actions}>
        <Button
          mode="contained"
          onPress={handleContinue}
          style={[
            styles.continueButton,
            hasConsented ? styles.consentedButton : styles.declinedButton,
          ]}
          buttonColor={hasConsented ? '#4CAF50' : '#666'}
        >
          {hasConsented ? 'Continue & Help Improve' : 'Continue Privately'}
        </Button>

        {onSkip && (
          <Button
            mode="text"
            onPress={onSkip}
            style={styles.skipButton}
            textColor="#666"
          >
            Skip for now
          </Button>
        )}
      </View>

      <Text variant="bodySmall" style={styles.disclaimer}>
        You can change this preference anytime in Settings → Privacy → Data
        Sharing
      </Text>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    padding: 24,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
    color: '#333',
  },
  subtitle: {
    textAlign: 'center',
    color: '#666',
  },
  benefitsCard: {
    marginBottom: 24,
    elevation: 2,
  },
  benefitsTitle: {
    marginBottom: 16,
    color: '#333',
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  benefitText: {
    flex: 1,
    marginLeft: 12,
  },
  benefitTitle: {
    fontWeight: '500',
    marginBottom: 4,
    color: '#333',
  },
  benefitDescription: {
    color: '#666',
    lineHeight: 18,
  },
  privacyCard: {
    marginBottom: 24,
    elevation: 2,
    backgroundColor: '#F8F9FA',
  },
  privacyTitle: {
    marginBottom: 16,
    color: '#333',
  },
  privacyPoints: {
    gap: 8,
  },
  privacyPoint: {
    color: '#4CAF50',
    fontWeight: '500',
  },
  consentSection: {
    marginBottom: 24,
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
  },
  switchText: {
    flex: 1,
    marginRight: 16,
  },
  switchDescription: {
    color: '#666',
    marginTop: 4,
  },
  divider: {
    marginBottom: 24,
  },
  actions: {
    gap: 12,
    marginBottom: 16,
  },
  continueButton: {
    borderRadius: 12,
    paddingVertical: 4,
  },
  consentedButton: {
    backgroundColor: '#4CAF50',
  },
  declinedButton: {
    backgroundColor: '#666',
  },
  skipButton: {
    borderRadius: 12,
  },
  disclaimer: {
    textAlign: 'center',
    color: '#999',
    fontStyle: 'italic',
  },
});

import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import { OnboardingStackParamList } from '../../../shared/types/navigation';

// Onboarding Screens
import AccountCreationScreen from '../screens/AccountCreationScreen';
import AIPersonaSelectionScreen from '../screens/AIPersonaSelectionScreen';
import AuthSelectionScreen from '../screens/AuthSelectionScreen';
import CreateProfileScreen from '../screens/CreateProfileScreen';
import GeneralQuestionnaireScreen from '../screens/GeneralQuestionnaireScreen';
import IntroScreen from '../screens/IntroScreen';
import LanguageSelectionScreen from '../screens/LanguageSelectionScreen';
import NutritionQuestionnaireScreen from '../screens/NutritionQuestionnaireScreen';
import OnboardingCompleteScreen from '../screens/OnboardingCompleteScreen';
import PlanSelectionScreen from '../screens/PlanSelectionScreen';
import SplashScreen from '../screens/SplashScreen';
import TermsScreen from '../screens/TermsScreen';
import VerifyEmailScreen from '../screens/VerifyEmailScreen';
import WorkoutQuestionnaireScreen from '../screens/WorkoutQuestionnaireScreen';

const OnboardingStackNav = createStackNavigator<OnboardingStackParamList>();

export function OnboardingNavigator() {
  return (
    <OnboardingStackNav.Navigator
      id={undefined}
      screenOptions={{ headerShown: false }}
    >
      <OnboardingStackNav.Screen name="Splash" component={SplashScreen} />
      <OnboardingStackNav.Screen
        name="LanguageSelection"
        component={LanguageSelectionScreen}
      />
      <OnboardingStackNav.Screen name="Intro" component={IntroScreen} />
      <OnboardingStackNav.Screen
        name="AuthSelection"
        component={AuthSelectionScreen}
      />
      <OnboardingStackNav.Screen
        name="AccountCreation"
        component={AccountCreationScreen}
      />
      <OnboardingStackNav.Screen
        name="VerifyEmail"
        component={VerifyEmailScreen}
      />
      <OnboardingStackNav.Screen name="Terms" component={TermsScreen} />
      <OnboardingStackNav.Screen
        name="CreateProfile"
        component={CreateProfileScreen}
      />
      <OnboardingStackNav.Screen
        name="GeneralQuestionnaire"
        component={GeneralQuestionnaireScreen}
      />
      <OnboardingStackNav.Screen
        name="PlanSelection"
        component={PlanSelectionScreen}
      />
      <OnboardingStackNav.Screen
        name="WorkoutQuestionnaire"
        component={WorkoutQuestionnaireScreen}
      />
      <OnboardingStackNav.Screen
        name="NutritionQuestionnaire"
        component={NutritionQuestionnaireScreen}
      />
      <OnboardingStackNav.Screen
        name="AIPersonaSelection"
        component={AIPersonaSelectionScreen}
      />
      <OnboardingStackNav.Screen
        name="OnboardingComplete"
        component={OnboardingCompleteScreen}
      />
    </OnboardingStackNav.Navigator>
  );
}

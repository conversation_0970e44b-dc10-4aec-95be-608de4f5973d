import { create } from 'zustand';

type OnboardingState = {
  // General
  fullName: string;
  age: string;
  height: string;
  primaryGoal: string;

  // Plan Selection
  planType: 'workout' | 'meal' | 'both' | null;

  // Workout
  experienceLevel: string;
  knownInjuries: string;
  preferredExerciseTypes: string;
  availableEquipment: string;

  // Nutrition
  dietaryPreferences: string;
  allergies: string;
  budget: string;
  cuisineTypes: string;

  // AI Persona
  aiPersona: string | null;

  // Actions
  setGeneralInfo: (info: {
    fullName?: string;
    age?: string;
    height?: string;
    primaryGoal?: string;
  }) => void;
  setPlanType: (type: 'workout' | 'meal' | 'both') => void;
  setWorkoutInfo: (info: {
    experienceLevel?: string;
    knownInjuries?: string;
    preferredExerciseTypes?: string;
    availableEquipment?: string;
  }) => void;
  setNutritionInfo: (info: {
    dietaryPreferences?: string;
    allergies?: string;
    budget?: string;
    cuisineTypes?: string;
  }) => void;
  setAiPersona: (persona: string) => void;
  reset: () => void;
};

const initialState = {
  fullName: '',
  age: '',
  height: '',
  primaryGoal: '',
  planType: null,
  experienceLevel: '',
  knownInjuries: '',
  preferredExerciseTypes: '',
  availableEquipment: '',
  dietaryPreferences: '',
  allergies: '',
  budget: '',
  cuisineTypes: '',
  aiPersona: null,
};

export const useOnboardingStore = create<OnboardingState>(set => ({
  ...initialState,

  // Actions
  setGeneralInfo: info => set(state => ({ ...state, ...info })),
  setPlanType: type => set({ planType: type }),
  setWorkoutInfo: info => set(state => ({ ...state, ...info })),
  setNutritionInfo: info => set(state => ({ ...state, ...info })),
  setAiPersona: persona => set({ aiPersona: persona }),
  reset: () => set(initialState),
}));

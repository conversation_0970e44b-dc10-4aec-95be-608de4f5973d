import { format, addDays } from 'date-fns';
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  Alert,
  StyleSheet,
  TextInput,
} from 'react-native';
import { useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  mealPlanManagementService,
  MealPlanSummary,
} from '../services/mealPlanManagementService';

interface MealPlanSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onCreateNew: (duration: number) => void;
  onViewExisting: (mealPlan: MealPlanSummary) => void;
  onUpdateExisting: (mealPlan: MealPlanSummary, duration: number) => void;
}

const PRESET_DURATIONS = [
  { days: 3, label: '3 days' },
  { days: 7, label: '7 days' },
  { days: 14, label: '14 days' },
  { days: 21, label: '21 days' },
];

const MIN_DURATION = 1;
const MAX_DURATION = 21;

export default function MealPlanSelectionModal({
  visible,
  onClose,
  onCreateNew,
  onViewExisting,
  onUpdateExisting,
}: MealPlanSelectionModalProps) {
  const theme = useTheme();
  const [existingPlans, setExistingPlans] = useState<MealPlanSummary[]>([]);
  const [loading, setLoading] = useState(false);
  const [durationInput, setDurationInput] = useState('7');
  const [durationError, setDurationError] = useState<string | null>(null);

  useEffect(() => {
    if (visible) {
      loadExistingPlans();
    }
  }, [visible]);

  const loadExistingPlans = async () => {
    try {
      setLoading(true);
      const plans = await mealPlanManagementService.getUserMealPlans();
      setExistingPlans(plans);
    } catch (error) {
      console.error('Error loading meal plans:', error);
      Alert.alert('Error', 'Failed to load existing meal plans');
    } finally {
      setLoading(false);
    }
  };

  const validateDuration = (
    input: string
  ): { isValid: boolean; duration?: number; error?: string } => {
    if (!input.trim()) {
      return { isValid: false, error: 'Please enter number of days' };
    }

    const duration = parseInt(input.trim(), 10);

    if (isNaN(duration)) {
      return { isValid: false, error: 'Please enter a valid number' };
    }

    if (duration < MIN_DURATION) {
      return { isValid: false, error: `Minimum ${MIN_DURATION} day required` };
    }

    if (duration > MAX_DURATION) {
      return { isValid: false, error: `Maximum ${MAX_DURATION} days allowed` };
    }

    return { isValid: true, duration };
  };

  const handleDurationInputChange = (text: string) => {
    setDurationInput(text);
    const validation = validateDuration(text);
    setDurationError(validation.error || null);
  };

  const handlePresetDuration = (days: number) => {
    setDurationInput(days.toString());
    setDurationError(null);
  };

  const handleCreateNew = () => {
    const validation = validateDuration(durationInput);
    if (!validation.isValid || !validation.duration) {
      setDurationError(validation.error || 'Invalid duration');
      return;
    }

    onCreateNew(validation.duration);
    onClose();
  };

  const handleViewExisting = (plan: MealPlanSummary) => {
    onViewExisting(plan);
    onClose();
  };

  const handleUpdateExisting = (plan: MealPlanSummary) => {
    const validation = validateDuration(durationInput);
    if (!validation.isValid || !validation.duration) {
      setDurationError(validation.error || 'Invalid duration');
      return;
    }

    Alert.alert(
      'Update Meal Plan',
      `Update "${plan.name}" with new ${validation.duration}-day plan?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Update',
          onPress: () => {
            onUpdateExisting(plan, validation.duration!);
            onClose();
          },
        },
      ]
    );
  };

  const formatDateRange = (startDate: string, duration: number) => {
    const start = new Date(startDate);
    const end = addDays(start, duration - 1);
    return `${format(start, 'MMM d')} - ${format(end, 'MMM d')}`;
  };

  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modal: {
      backgroundColor: theme.colors.surface,
      borderRadius: 16,
      padding: 24,
      margin: 20,
      maxHeight: '80%',
      width: '90%',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    title: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
    },
    closeButton: {
      padding: 8,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 12,
    },
    durationInputContainer: {
      marginBottom: 16,
    },
    durationInputLabel: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 8,
    },
    durationInput: {
      borderWidth: 1,
      borderColor: durationError ? theme.colors.error : theme.colors.outline,
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      color: theme.colors.onSurface,
      backgroundColor: theme.colors.surface,
      textAlign: 'center',
    },
    durationInputValid: {
      borderColor: theme.colors.primary,
    },
    durationError: {
      fontSize: 12,
      color: theme.colors.error,
      marginTop: 4,
      textAlign: 'center',
    },
    durationHint: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      marginTop: 4,
      textAlign: 'center',
    },
    presetButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 12,
      marginBottom: 16,
    },
    presetButton: {
      flex: 1,
      padding: 8,
      marginHorizontal: 2,
      borderRadius: 6,
      borderWidth: 1,
      borderColor: theme.colors.outline,
      alignItems: 'center',
      backgroundColor: theme.colors.surfaceVariant,
    },
    presetButtonText: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '500',
    },
    createButton: {
      backgroundColor: theme.colors.primary,
      padding: 16,
      borderRadius: 8,
      alignItems: 'center',
    },
    createButtonDisabled: {
      backgroundColor: theme.colors.surfaceVariant,
      opacity: 0.6,
    },
    createButtonText: {
      color: theme.colors.onPrimary,
      fontSize: 16,
      fontWeight: '600',
    },
    existingPlansContainer: {
      maxHeight: 200,
    },
    planItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      borderRadius: 8,
      backgroundColor: theme.colors.surfaceVariant,
      marginBottom: 8,
    },
    activePlanItem: {
      backgroundColor: theme.colors.primaryContainer,
      borderWidth: 1,
      borderColor: theme.colors.primary,
    },
    planInfo: {
      flex: 1,
      marginLeft: 12,
    },
    planName: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.onSurface,
    },
    planDetails: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      marginTop: 2,
    },
    planActions: {
      flexDirection: 'row',
    },
    actionButton: {
      padding: 8,
      marginLeft: 8,
    },
    emptyState: {
      alignItems: 'center',
      padding: 20,
    },
    emptyStateText: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
    },
    activeLabel: {
      fontSize: 10,
      color: theme.colors.primary,
      fontWeight: '600',
      marginTop: 2,
    },
  });

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <View style={styles.header}>
            <Text style={styles.title}>Meal Planning Options</Text>
            <TouchableOpacity
              onPress={onClose}
              style={styles.closeButton}
              testID="close-button"
            >
              <Icon name="close" size={24} color={theme.colors.onSurface} />
            </TouchableOpacity>
          </View>

          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Duration Selection */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Choose Duration</Text>

              <View style={styles.durationInputContainer}>
                <Text style={styles.durationInputLabel}>
                  Number of days (1-21):
                </Text>
                <TextInput
                  style={[
                    styles.durationInput,
                    !durationError &&
                      durationInput &&
                      styles.durationInputValid,
                  ]}
                  value={durationInput}
                  onChangeText={handleDurationInputChange}
                  placeholder="Enter number of days"
                  placeholderTextColor={theme.colors.onSurfaceVariant}
                  keyboardType="numeric"
                  maxLength={2}
                  selectTextOnFocus
                />
                {durationError ? (
                  <Text style={styles.durationError}>{durationError}</Text>
                ) : (
                  <Text style={styles.durationHint}>
                    Enter 1-21 days for your meal plan
                  </Text>
                )}
              </View>

              <View style={styles.presetButtons}>
                {PRESET_DURATIONS.map(preset => (
                  <TouchableOpacity
                    key={preset.days}
                    style={styles.presetButton}
                    onPress={() => handlePresetDuration(preset.days)}
                  >
                    <Text style={styles.presetButtonText}>{preset.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>

              <TouchableOpacity
                style={[
                  styles.createButton,
                  durationError && styles.createButtonDisabled,
                ]}
                onPress={handleCreateNew}
                disabled={!!durationError}
              >
                <Text style={styles.createButtonText}>
                  Create New {durationInput || '?'}-Day Plan
                </Text>
              </TouchableOpacity>
            </View>

            {/* Existing Plans */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Your Existing Plans</Text>

              {loading ? (
                <View style={styles.emptyState}>
                  <Text style={styles.emptyStateText}>Loading...</Text>
                </View>
              ) : existingPlans.length === 0 ? (
                <View style={styles.emptyState}>
                  <Text style={styles.emptyStateText}>
                    No existing meal plans found.{'\n'}Create your first plan
                    above!
                  </Text>
                </View>
              ) : (
                <ScrollView
                  style={styles.existingPlansContainer}
                  nestedScrollEnabled
                >
                  {existingPlans.map(plan => (
                    <View
                      key={plan.id}
                      style={[
                        styles.planItem,
                        plan.is_current && styles.activePlanItem,
                      ]}
                    >
                      <Icon
                        name={
                          plan.is_current
                            ? 'calendar-check'
                            : 'calendar-outline'
                        }
                        size={20}
                        color={
                          plan.is_current
                            ? theme.colors.primary
                            : theme.colors.onSurfaceVariant
                        }
                      />
                      <View style={styles.planInfo}>
                        <Text style={styles.planName}>{plan.name}</Text>
                        <Text style={styles.planDetails}>
                          {formatDateRange(
                            plan.week_start_date,
                            plan.duration_days
                          )}{' '}
                          • {plan.meal_count} meals
                          {plan.target_calories_per_day &&
                            ` • ${plan.target_calories_per_day} cal/day`}
                        </Text>
                        {plan.is_current && (
                          <Text style={styles.activeLabel}>ACTIVE</Text>
                        )}
                      </View>
                      <View style={styles.planActions}>
                        <TouchableOpacity
                          style={styles.actionButton}
                          onPress={() => handleViewExisting(plan)}
                          testID="view-plan-button"
                        >
                          <Icon
                            name="eye"
                            size={20}
                            color={theme.colors.primary}
                          />
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={styles.actionButton}
                          onPress={() => handleUpdateExisting(plan)}
                          testID="update-plan-button"
                        >
                          <Icon
                            name="refresh"
                            size={20}
                            color={theme.colors.primary}
                          />
                        </TouchableOpacity>
                      </View>
                    </View>
                  ))}
                </ScrollView>
              )}
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}

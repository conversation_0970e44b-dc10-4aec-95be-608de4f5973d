/**
 * Tests for meal plan duration validation logic
 * These tests verify the validation rules for meal plan duration input
 */

// Extract validation logic for testing
export const validateMealPlanDuration = (
  input: string
): { isValid: boolean; duration?: number; error?: string } => {
  const MIN_DURATION = 1;
  const MAX_DURATION = 21;

  if (!input.trim()) {
    return { isValid: false, error: 'Please enter number of days' };
  }

  const trimmedInput = input.trim();
  const duration = parseInt(trimmedInput, 10);

  // Check if it's a valid integer (no decimals, no non-numeric characters except negative sign)
  if (isNaN(duration) || !/^-?\d+$/.test(trimmedInput)) {
    return { isValid: false, error: 'Please enter a valid number' };
  }

  if (duration < MIN_DURATION) {
    return { isValid: false, error: `Minimum ${MIN_DURATION} day required` };
  }

  if (duration > MAX_DURATION) {
    return { isValid: false, error: `Maximum ${MAX_DURATION} days allowed` };
  }

  return { isValid: true, duration };
};

describe('validateMealPlanDuration', () => {
  describe('Valid inputs', () => {
    it('should accept minimum valid duration (1 day)', () => {
      const result = validateMealPlanDuration('1');
      expect(result).toEqual({
        isValid: true,
        duration: 1,
      });
    });

    it('should accept maximum valid duration (21 days)', () => {
      const result = validateMealPlanDuration('21');
      expect(result).toEqual({
        isValid: true,
        duration: 21,
      });
    });

    it('should accept common durations', () => {
      const commonDurations = [3, 7, 14, 10, 5, 15];

      commonDurations.forEach(days => {
        const result = validateMealPlanDuration(days.toString());
        expect(result).toEqual({
          isValid: true,
          duration: days,
        });
      });
    });

    it('should handle whitespace around valid numbers', () => {
      const inputs = ['  7  ', '\t14\t', ' 21 ', '1 '];
      const expected = [7, 14, 21, 1];

      inputs.forEach((input, index) => {
        const result = validateMealPlanDuration(input);
        expect(result).toEqual({
          isValid: true,
          duration: expected[index],
        });
      });
    });
  });

  describe('Invalid inputs - Empty/Whitespace', () => {
    it('should reject empty string', () => {
      const result = validateMealPlanDuration('');
      expect(result).toEqual({
        isValid: false,
        error: 'Please enter number of days',
      });
    });

    it('should reject whitespace-only strings', () => {
      const whitespaceInputs = ['   ', '\t\t', '\n', ' \t \n '];

      whitespaceInputs.forEach(input => {
        const result = validateMealPlanDuration(input);
        expect(result).toEqual({
          isValid: false,
          error: 'Please enter number of days',
        });
      });
    });
  });

  describe('Invalid inputs - Non-numeric', () => {
    it('should reject alphabetic characters', () => {
      const invalidInputs = ['abc', 'seven', 'XIV', 'days'];

      invalidInputs.forEach(input => {
        const result = validateMealPlanDuration(input);
        expect(result).toEqual({
          isValid: false,
          error: 'Please enter a valid number',
        });
      });
    });

    it('should reject special characters', () => {
      const invalidInputs = ['!@#', '***', '7.5', '7,5', '7-days'];

      invalidInputs.forEach(input => {
        const result = validateMealPlanDuration(input);
        expect(result).toEqual({
          isValid: false,
          error: 'Please enter a valid number',
        });
      });
    });

    it('should reject mixed alphanumeric', () => {
      const invalidInputs = ['7days', '14d', 'week7', '2weeks'];

      invalidInputs.forEach(input => {
        const result = validateMealPlanDuration(input);
        expect(result).toEqual({
          isValid: false,
          error: 'Please enter a valid number',
        });
      });
    });
  });

  describe('Invalid inputs - Out of range', () => {
    it('should reject zero', () => {
      const result = validateMealPlanDuration('0');
      expect(result).toEqual({
        isValid: false,
        error: 'Minimum 1 day required',
      });
    });

    it('should reject negative numbers', () => {
      const negativeInputs = ['-1', '-5', '-10'];

      negativeInputs.forEach(input => {
        const result = validateMealPlanDuration(input);
        expect(result).toEqual({
          isValid: false,
          error: 'Minimum 1 day required',
        });
      });
    });

    it('should reject numbers above maximum', () => {
      const tooLargeInputs = ['22', '25', '30', '100', '999'];

      tooLargeInputs.forEach(input => {
        const result = validateMealPlanDuration(input);
        expect(result).toEqual({
          isValid: false,
          error: 'Maximum 21 days allowed',
        });
      });
    });
  });

  describe('Edge cases', () => {
    it('should handle leading zeros correctly', () => {
      const result1 = validateMealPlanDuration('07');
      expect(result1).toEqual({
        isValid: true,
        duration: 7,
      });

      const result2 = validateMealPlanDuration('021');
      expect(result2).toEqual({
        isValid: true,
        duration: 21,
      });
    });

    it('should handle boundary values correctly', () => {
      // Just below minimum
      const belowMin = validateMealPlanDuration('0');
      expect(belowMin.isValid).toBe(false);
      expect(belowMin.error).toBe('Minimum 1 day required');

      // Minimum
      const atMin = validateMealPlanDuration('1');
      expect(atMin.isValid).toBe(true);
      expect(atMin.duration).toBe(1);

      // Maximum
      const atMax = validateMealPlanDuration('21');
      expect(atMax.isValid).toBe(true);
      expect(atMax.duration).toBe(21);

      // Just above maximum
      const aboveMax = validateMealPlanDuration('22');
      expect(aboveMax.isValid).toBe(false);
      expect(aboveMax.error).toBe('Maximum 21 days allowed');
    });

    it('should handle very large numbers', () => {
      const veryLargeInputs = ['999999', '1000000', '99'];

      veryLargeInputs.forEach(input => {
        const result = validateMealPlanDuration(input);
        expect(result).toEqual({
          isValid: false,
          error: 'Maximum 21 days allowed',
        });
      });
    });
  });

  describe('Return value structure', () => {
    it('should return correct structure for valid input', () => {
      const result = validateMealPlanDuration('7');

      expect(result).toHaveProperty('isValid');
      expect(result).toHaveProperty('duration');
      expect(result).not.toHaveProperty('error');
      expect(typeof result.isValid).toBe('boolean');
      expect(typeof result.duration).toBe('number');
    });

    it('should return correct structure for invalid input', () => {
      const result = validateMealPlanDuration('invalid');

      expect(result).toHaveProperty('isValid');
      expect(result).toHaveProperty('error');
      expect(result).not.toHaveProperty('duration');
      expect(typeof result.isValid).toBe('boolean');
      expect(typeof result.error).toBe('string');
    });
  });

  describe('Performance and consistency', () => {
    it('should be consistent across multiple calls', () => {
      const testCases = [
        { input: '7', expected: { isValid: true, duration: 7 } },
        {
          input: '25',
          expected: { isValid: false, error: 'Maximum 21 days allowed' },
        },
        {
          input: '',
          expected: { isValid: false, error: 'Please enter number of days' },
        },
      ];

      // Run each test case multiple times
      testCases.forEach(({ input, expected }) => {
        for (let i = 0; i < 5; i++) {
          const result = validateMealPlanDuration(input);
          expect(result).toEqual(expected);
        }
      });
    });

    it('should handle rapid successive calls', () => {
      const inputs = Array.from({ length: 100 }, (_, i) => (i % 25).toString());

      inputs.forEach(input => {
        const result = validateMealPlanDuration(input);
        expect(result).toHaveProperty('isValid');
        expect(typeof result.isValid).toBe('boolean');
      });
    });
  });
});

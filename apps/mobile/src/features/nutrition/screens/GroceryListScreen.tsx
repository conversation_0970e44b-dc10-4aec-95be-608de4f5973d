import * as React from 'react';
import { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Text,
  Button,
  TextInput,
  Card,
  Checkbox,
  ActivityIndicator,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { supabase } from '../../../lib/supabase';
import { ScreenWithTopBar } from '../../../shared/components/ScreenWithTopBar';
import {
  triggerSuccessHaptic,
  triggerImpactHaptic,
  triggerSelectionHaptic,
} from '../../../shared/utils/haptic';
import {
  showSuccessToast,
  showErrorToast,
  showInfoToast,
} from '../../../shared/utils/toast';
import { generateGroceryList } from '../../ai-coach/services/groceryListTools';

interface GroceryItem {
  id: string;
  name: string;
  completed: boolean;
  category?: string;
  amount?: number;
  unit?: string;
  estimated_cost?: number;
}

export default function GroceryListScreen() {
  const [groceryItems, setGroceryItems] = useState<GroceryItem[]>([]);
  const [newItem, setNewItem] = useState('');
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);

  // Load grocery items from database
  useEffect(() => {
    loadGroceryItems();
  }, []);

  const loadGroceryItems = async () => {
    try {
      setLoading(true);
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        setLoading(false);
        return;
      }

      // Get the most recent grocery list
      const { data: lists, error: listsError } = await supabase
        .from('grocery_lists')
        .select('id')
        .eq('user_id', user.id)
        .neq('status', 'completed')
        .order('created_at', { ascending: false })
        .limit(1);

      if (listsError) {
        console.error('Error loading grocery lists:', listsError);
        setLoading(false);
        return;
      }

      if (!lists || lists.length === 0) {
        // No grocery lists yet
        setGroceryItems([]);
        setLoading(false);
        return;
      }

      // Get items from the most recent list
      const { data: items, error: itemsError } = await supabase
        .from('grocery_list_items')
        .select('*')
        .eq('grocery_list_id', lists[0].id)
        .order('created_at', { ascending: false });

      if (itemsError) {
        console.error('Error loading grocery items:', itemsError);
        setLoading(false);
        return;
      }

      // Convert to our format
      const formattedItems: GroceryItem[] = (items || []).map(item => ({
        id: item.id,
        name: item.ingredient_name,
        completed: item.is_purchased || false,
        category: item.category,
        amount: item.quantity,
        unit: item.unit,
        estimated_cost: item.estimated_cost,
      }));

      setGroceryItems(formattedItems);
    } catch (error) {
      console.error('Error loading grocery items:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateSmartGroceryList = async () => {
    try {
      setGenerating(true);

      const result = await generateGroceryList({
        organize_by_category: true,
        exclude_pantry_staples: false,
        week_start_date: new Date().toISOString().split('T')[0],
      });

      if (result.success && result.data) {
        showSuccessToast(
          'Smart grocery list generated!',
          `${result.data.total_items} items added`
        );
        await loadGroceryItems(); // Reload to show the new items
      } else {
        showErrorToast(
          'Failed to generate grocery list',
          result.error || 'Unknown error'
        );
      }
    } catch (error) {
      console.error('Error generating grocery list:', error);
      showErrorToast('Failed to generate grocery list');
    } finally {
      setGenerating(false);
    }
  };

  const addItem = async () => {
    if (newItem.trim() !== '') {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) {
          return;
        }

        // Get or create a grocery list
        const { data: lists, error: listsError } = await supabase
          .from('grocery_lists')
          .select('id')
          .eq('user_id', user.id)
          .neq('status', 'completed')
          .order('created_at', { ascending: false })
          .limit(1);

        if (listsError) {
          console.error('Error getting grocery lists:', listsError);
          return;
        }

        let listId;
        if (!lists || lists.length === 0) {
          // Create a new grocery list
          const { data: newList, error: createError } = await supabase
            .from('grocery_lists')
            .insert({
              user_id: user.id,
              name: `Grocery List - ${new Date().toLocaleDateString()}`,
              status: 'active',
            })
            .select('id')
            .single();

          if (createError) {
            console.error('Error creating grocery list:', createError);
            showErrorToast('Failed to create grocery list');
            return;
          }
          listId = newList.id;
        } else {
          listId = lists[0].id;
        }

        // Add the item to the database
        const { data: newItemData, error: itemError } = await supabase
          .from('grocery_list_items')
          .insert({
            grocery_list_id: listId,
            ingredient_name: newItem.trim(),
            is_purchased: false,
          })
          .select('*')
          .single();

        if (itemError) {
          console.error('Error adding item:', itemError);
          showErrorToast('Failed to add item');
          return;
        }

        // Add to local state
        const formattedItem: GroceryItem = {
          id: newItemData.id,
          name: newItemData.ingredient_name,
          completed: false,
        };

        setGroceryItems([...groceryItems, formattedItem]);
        showSuccessToast('Item added!', newItemData.ingredient_name);
        triggerSuccessHaptic();
        setNewItem('');
      } catch (error) {
        console.error('Error adding item:', error);
        showErrorToast('Failed to add item');
      }
    }
  };

  const toggleItem = async (id: string) => {
    const item = groceryItems.find(groceryItem => groceryItem.id === id);
    if (item) {
      triggerSelectionHaptic();

      // Update in database
      try {
        const { error } = await supabase
          .from('grocery_list_items')
          .update({ is_purchased: !item.completed })
          .eq('id', id);

        if (error) {
          console.error('Error updating item:', error);
          showErrorToast('Failed to update item');
          return;
        }
      } catch (error) {
        console.error('Error updating item:', error);
        showErrorToast('Failed to update item');
        return;
      }

      // Update local state
      setGroceryItems(
        groceryItems.map(groceryItem =>
          groceryItem.id === id
            ? { ...groceryItem, completed: !groceryItem.completed }
            : groceryItem
        )
      );
    }
  };

  const removeItem = async (id: string) => {
    const itemToRemove = groceryItems.find(item => item.id === id);
    if (itemToRemove) {
      try {
        // Remove from database
        const { error } = await supabase
          .from('grocery_list_items')
          .delete()
          .eq('id', id);

        if (error) {
          console.error('Error removing item:', error);
          showErrorToast('Failed to remove item');
          return;
        }

        // Remove from local state
        setGroceryItems(groceryItems.filter(item => item.id !== id));
        showInfoToast('Item removed', itemToRemove.name);
        triggerImpactHaptic('light');
      } catch (error) {
        console.error('Error removing item:', error);
        showErrorToast('Failed to remove item');
      }
    }
  };

  const clearCompleted = async () => {
    const completedItems = groceryItems.filter(item => item.completed);
    const completedCount = completedItems.length;

    if (completedCount === 0) {
      return;
    }

    try {
      // Remove completed items from database
      const completedIds = completedItems.map(item => item.id);
      const { error } = await supabase
        .from('grocery_list_items')
        .delete()
        .in('id', completedIds);

      if (error) {
        console.error('Error clearing completed items:', error);
        showErrorToast('Failed to clear completed items');
        return;
      }

      // Update local state
      setGroceryItems(groceryItems.filter(item => !item.completed));
      showSuccessToast(
        'Items cleared',
        `${completedCount} completed item${completedCount > 1 ? 's' : ''} removed`
      );
      triggerSuccessHaptic();
    } catch (error) {
      console.error('Error clearing completed items:', error);
      showErrorToast('Failed to clear completed items');
    }
  };

  if (loading) {
    return (
      <ScreenWithTopBar title="Grocery List">
        <View style={[styles.container, styles.centerContent]}>
          <ActivityIndicator size="large" />
          <Text style={styles.loadingText}>Loading grocery list...</Text>
        </View>
      </ScreenWithTopBar>
    );
  }

  return (
    <ScreenWithTopBar title="Grocery List">
      <View style={styles.container}>
        {/* Smart Generation Button */}
        <View style={styles.headerContainer}>
          <Button
            mode="contained"
            onPress={generateSmartGroceryList}
            loading={generating}
            disabled={generating}
            style={styles.generateButton}
            icon="auto-fix"
          >
            {generating ? 'Generating...' : 'Generate Smart List'}
          </Button>
        </View>

        {/* Add Item Row */}
        <View style={styles.inputRow}>
          <TextInput
            style={styles.input}
            placeholder="Add new item..."
            value={newItem}
            onChangeText={setNewItem}
            onSubmitEditing={addItem}
          />
          <Button mode="contained" onPress={addItem} style={styles.addButton}>
            <Icon name="plus" size={20} color="white" />
          </Button>
        </View>

        {/* Clear Completed Button */}
        <Button
          onPress={clearCompleted}
          style={styles.clearButton}
          disabled={!groceryItems.some(item => item.completed)}
        >
          Clear Completed
        </Button>
        {/* Items List */}
        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.itemsContainer}>
            {groceryItems.map(item => (
              <Card key={item.id} style={styles.card}>
                <View style={styles.cardContent}>
                  <Checkbox
                    status={item.completed ? 'checked' : 'unchecked'}
                    onPress={() => toggleItem(item.id)}
                  />
                  <Text
                    style={[
                      styles.itemText,
                      item.completed && styles.completedItem,
                    ]}
                  >
                    {item.name}
                  </Text>
                  <Button
                    mode="outlined"
                    onPress={() => removeItem(item.id)}
                    style={styles.removeButton}
                  >
                    <Icon name="trash-can-outline" size={16} />
                  </Button>
                </View>
              </Card>
            ))}

            {groceryItems.length === 0 && (
              <View style={styles.emptyContainer}>
                <Icon name="cart-outline" size={64} color="#666" />
                <Text style={styles.emptyText}>Your grocery list is empty</Text>
                <Text style={styles.emptySubtext}>
                  Add items manually or generate from meal plans
                </Text>
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    </ScreenWithTopBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  headerContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  generateButton: {
    marginBottom: 16,
  },

  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  input: {
    flex: 1,
    height: 40,
    borderColor: 'gray',
    borderWidth: 1,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  addButton: {
    marginLeft: 8,
  },
  clearButton: {
    marginBottom: 16,
    marginHorizontal: 16,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  itemsContainer: {
    marginBottom: 16,
  },
  card: {
    marginBottom: 8,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  itemText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  completedItem: {
    textDecorationLine: 'line-through',
    color: 'gray',
  },
  removeButton: {
    marginLeft: 8,
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
});

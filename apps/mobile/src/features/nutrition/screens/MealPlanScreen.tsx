import { useNavigation, useRoute } from '@react-navigation/native';
import { format, addDays } from 'date-fns';
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  StyleSheet,
} from 'react-native';
import { useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  mealPlanManagementService,
  EnhancedMealPlan,
  MealPlanMeal,
} from '../services/mealPlanManagementService';

const MEAL_TYPE_ICONS = {
  breakfast: 'coffee',
  lunch: 'food-apple',
  dinner: 'silverware-fork-knife',
  snack: 'cookie',
};

const MEAL_TYPE_LABELS = {
  breakfast: 'Breakfast',
  lunch: 'Lunch',
  dinner: 'Dinner',
  snack: 'Snack',
};

const DAY_LABELS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export default function MealPlanScreen() {
  const theme = useTheme();
  const navigation = useNavigation();
  const route = useRoute();
  const { mealPlanId } = route.params as { mealPlanId: string };

  const [mealPlan, setMealPlan] = useState<EnhancedMealPlan | null>(null);
  const [meals, setMeals] = useState<MealPlanMeal[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadMealPlan();
  }, [mealPlanId]);

  const loadMealPlan = async () => {
    try {
      setLoading(true);
      const { plan, meals: planMeals } =
        await mealPlanManagementService.getMealPlanDetails(mealPlanId);
      setMealPlan(plan);
      setMeals(planMeals);
    } catch (error) {
      console.error('Error loading meal plan:', error);
      Alert.alert('Error', 'Failed to load meal plan');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadMealPlan();
    setRefreshing(false);
  };

  const handleDeletePlan = () => {
    Alert.alert(
      'Delete Meal Plan',
      `Are you sure you want to delete "${mealPlan?.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await mealPlanManagementService.deleteMealPlan(mealPlanId);
              Alert.alert('Success', 'Meal plan deleted successfully');
              navigation.goBack();
            } catch (_error) {
              Alert.alert('Error', 'Failed to delete meal plan');
            }
          },
        },
      ]
    );
  };

  const handleEditPlan = () => {
    Alert.alert('Edit Meal Plan', 'Choose an option:', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Edit Name',
        onPress: () => {
          // TODO: Implement name editing modal
          Alert.alert('Coming Soon', 'Name editing will be available soon');
        },
      },
      {
        text: 'Regenerate Plan',
        onPress: () => {
          // TODO: Implement plan regeneration
          Alert.alert(
            'Coming Soon',
            'Plan regeneration will be available soon'
          );
        },
      },
    ]);
  };

  const groupMealsByDay = () => {
    const groupedMeals: { [key: number]: MealPlanMeal[] } = {};

    meals.forEach(meal => {
      if (!groupedMeals[meal.day_of_week]) {
        groupedMeals[meal.day_of_week] = [];
      }
      groupedMeals[meal.day_of_week].push(meal);
    });

    // Sort meals within each day by meal type
    const mealTypeOrder = { breakfast: 0, lunch: 1, dinner: 2, snack: 3 };
    Object.keys(groupedMeals).forEach(day => {
      groupedMeals[parseInt(day)].sort(
        (a, b) =>
          (mealTypeOrder[a.meal_type] || 99) -
          (mealTypeOrder[b.meal_type] || 99)
      );
    });

    return groupedMeals;
  };

  const calculateDayNutrition = (dayMeals: MealPlanMeal[]) => {
    return dayMeals.reduce(
      (totals, meal) => ({
        calories:
          totals.calories +
          (meal.recipe?.calories_per_serving || 0) * (meal.servings || 1),
        protein:
          totals.protein +
          (meal.recipe?.protein_grams || 0) * (meal.servings || 1),
        carbs:
          totals.carbs + (meal.recipe?.carbs_grams || 0) * (meal.servings || 1),
        fat: totals.fat + (meal.recipe?.fat_grams || 0) * (meal.servings || 1),
      }),
      { calories: 0, protein: 0, carbs: 0, fat: 0 }
    );
  };

  const formatDateForDay = (dayOfWeek: number) => {
    if (!mealPlan?.week_start_date) return DAY_LABELS[dayOfWeek];

    const startDate = new Date(mealPlan.week_start_date);
    const dayDate = addDays(startDate, dayOfWeek);
    return format(dayDate, 'MMM d');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: 20,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    headerTop: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
      flex: 1,
    },
    headerActions: {
      flexDirection: 'row',
    },
    actionButton: {
      padding: 8,
      marginLeft: 8,
    },
    subtitle: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 8,
    },
    planStats: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    statItem: {
      alignItems: 'center',
    },
    statValue: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.primary,
    },
    statLabel: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
    },
    content: {
      flex: 1,
    },
    dayContainer: {
      marginBottom: 20,
    },
    dayHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.colors.primaryContainer,
      marginHorizontal: 16,
      marginTop: 16,
      borderRadius: 8,
    },
    dayTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.onPrimaryContainer,
      flex: 1,
    },
    dayNutrition: {
      fontSize: 12,
      color: theme.colors.onPrimaryContainer,
    },
    mealsList: {
      paddingHorizontal: 16,
    },
    mealItem: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.colors.surface,
      marginVertical: 4,
      borderRadius: 8,
      elevation: 1,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    mealIcon: {
      marginRight: 12,
    },
    mealInfo: {
      flex: 1,
    },
    mealName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.onSurface,
    },
    mealType: {
      fontSize: 12,
      color: theme.colors.primary,
      textTransform: 'capitalize',
      marginBottom: 4,
    },
    mealNutrition: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
    },
    servings: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      fontStyle: 'italic',
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 40,
    },
    emptyStateText: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginTop: 16,
    },
  });

  if (loading) {
    return (
      <View style={[styles.container, styles.emptyState]}>
        <Icon name="loading" size={48} color={theme.colors.primary} />
        <Text style={styles.emptyStateText}>Loading meal plan...</Text>
      </View>
    );
  }

  if (!mealPlan) {
    return (
      <View style={[styles.container, styles.emptyState]}>
        <Icon name="alert-circle" size={48} color={theme.colors.error} />
        <Text style={styles.emptyStateText}>Meal plan not found</Text>
      </View>
    );
  }

  const groupedMeals = groupMealsByDay();
  const totalMeals = meals.length;
  const avgCaloriesPerDay =
    totalMeals > 0
      ? Math.round(
          meals.reduce(
            (sum, meal) =>
              sum +
              (meal.recipe?.calories_per_serving || 0) * (meal.servings || 1),
            0
          ) / (mealPlan.duration_days || 7)
        )
      : 0;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Text style={styles.title}>{mealPlan.name}</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleEditPlan}
            >
              <Icon name="pencil" size={24} color={theme.colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleDeletePlan}
            >
              <Icon name="delete" size={24} color={theme.colors.error} />
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.subtitle}>
          {format(new Date(mealPlan.week_start_date), 'MMM d')} -{' '}
          {format(
            addDays(
              new Date(mealPlan.week_start_date),
              (mealPlan.duration_days || 7) - 1
            ),
            'MMM d, yyyy'
          )}
        </Text>

        <View style={styles.planStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{mealPlan.duration_days || 7}</Text>
            <Text style={styles.statLabel}>Days</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{totalMeals}</Text>
            <Text style={styles.statLabel}>Meals</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{avgCaloriesPerDay}</Text>
            <Text style={styles.statLabel}>Avg Cal/Day</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {mealPlan.target_calories_per_day || 'N/A'}
            </Text>
            <Text style={styles.statLabel}>Target Cal</Text>
          </View>
        </View>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {Array.from({ length: mealPlan.duration_days || 7 }, (_, dayIndex) => {
          const dayMeals = groupedMeals[dayIndex] || [];
          const dayNutrition = calculateDayNutrition(dayMeals);

          return (
            <View key={dayIndex} style={styles.dayContainer}>
              <View style={styles.dayHeader}>
                <Text style={styles.dayTitle}>
                  {DAY_LABELS[dayIndex]} - {formatDateForDay(dayIndex)}
                </Text>
                <Text style={styles.dayNutrition}>
                  {Math.round(dayNutrition.calories)} cal
                </Text>
              </View>

              <View style={styles.mealsList}>
                {dayMeals.length === 0 ? (
                  <View style={styles.mealItem}>
                    <Icon
                      name="information"
                      size={20}
                      color={theme.colors.onSurfaceVariant}
                      style={styles.mealIcon}
                    />
                    <Text
                      style={[
                        styles.mealName,
                        { color: theme.colors.onSurfaceVariant },
                      ]}
                    >
                      No meals planned for this day
                    </Text>
                  </View>
                ) : (
                  dayMeals.map(meal => (
                    <View key={meal.id} style={styles.mealItem}>
                      <Icon
                        name={MEAL_TYPE_ICONS[meal.meal_type] || 'food'}
                        size={20}
                        color={theme.colors.primary}
                        style={styles.mealIcon}
                      />
                      <View style={styles.mealInfo}>
                        <Text style={styles.mealType}>
                          {MEAL_TYPE_LABELS[meal.meal_type]}
                        </Text>
                        <Text style={styles.mealName}>
                          {meal.recipe?.name || 'Unknown Recipe'}
                        </Text>
                        <Text style={styles.mealNutrition}>
                          {Math.round(
                            (meal.recipe?.calories_per_serving || 0) *
                              (meal.servings || 1)
                          )}{' '}
                          cal •{' '}
                          {Math.round(
                            (meal.recipe?.protein_grams || 0) *
                              (meal.servings || 1)
                          )}
                          g protein
                        </Text>
                        {meal.servings !== 1 && (
                          <Text style={styles.servings}>
                            {meal.servings} servings
                          </Text>
                        )}
                      </View>
                    </View>
                  ))
                )}
              </View>
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
}

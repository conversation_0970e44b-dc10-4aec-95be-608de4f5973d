import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Text, Card } from 'react-native-paper';
import { supabase } from '../../../lib/supabase';
import { SimplePreferenceButtons } from '../../preferences';

interface Recipe {
  id: string;
  name: string;
  description?: string;
  instructions?: string;
  ingredients?: Record<string, string>;
  prep_time_minutes?: number;
  cook_time_minutes?: number;
  servings?: number;
  nutritional_info?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
    fiber?: number;
    sugar?: number;
    sodium?: number;
  };
  cuisine_type?: string;
}

const RecipeDetailScreen = ({ route }: any) => {
  const { recipeId } = route.params;

  const {
    data: recipe,
    isLoading,
    isError,
    error,
  } = useQuery<Recipe>({
    queryKey: ['recipe', recipeId],
    queryFn: async () => {
      const { data, error: fetchError } = await supabase
        .from('recipes')
        .select('*')
        .eq('id', recipeId)
        .single();

      if (fetchError) {
        throw fetchError;
      }
      return data;
    },
  });

  if (isLoading) {
    return (
      <View style={styles.centerContainer}>
        <Text>Loading recipe...</Text>
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.centerContainer}>
        <Text>Error loading recipe: {error.message}</Text>
      </View>
    );
  }

  if (!recipe) {
    return (
      <View style={styles.centerContainer}>
        <Text>Recipe not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        {isLoading && <Text>Loading...</Text>}
        {isError && <Text>Error: Failed to load recipe</Text>}
        {recipe && (
          <>
            <Text style={styles.titleText}>{recipe.name}</Text>

            {recipe.description && (
              <Text style={styles.descriptionText}>{recipe.description}</Text>
            )}

            <View style={styles.timeContainer}>
              {recipe.prep_time_minutes && (
                <Text>Prep: {recipe.prep_time_minutes} min</Text>
              )}
              {recipe.cook_time_minutes && (
                <Text>Cook: {recipe.cook_time_minutes} min</Text>
              )}
            </View>

            {recipe.cuisine_type && <Text>Cuisine: {recipe.cuisine_type}</Text>}

            {recipe.ingredients && (
              <Card style={styles.card}>
                <Text style={styles.cardTitle}>Ingredients</Text>
                {recipe.ingredients &&
                  Object.entries(recipe.ingredients).map(([item, amount]) => (
                    <Text key={item} style={styles.ingredientText}>
                      {amount} {item}
                    </Text>
                  ))}
              </Card>
            )}

            {recipe.instructions && (
              <Card style={styles.card}>
                <Text style={styles.cardTitle}>Instructions</Text>
                <Text style={styles.instructionsText}>
                  {recipe.instructions}
                </Text>
              </Card>
            )}

            {recipe.nutritional_info && (
              <Card style={styles.card}>
                <Text style={styles.cardTitle}>Nutrition</Text>
                <View style={styles.nutritionContainer}>
                  {recipe.nutritional_info &&
                    Object.entries(recipe.nutritional_info).map(
                      ([key, value]) => (
                        <Text key={key} style={styles.nutritionTag}>
                          {key}: {value}
                        </Text>
                      )
                    )}
                </View>
              </Card>
            )}

            <View style={styles.recipeActions}>
              <Text style={styles.rateRecipeText}>Rate this recipe:</Text>
              <SimplePreferenceButtons
                itemType="recipe"
                itemIdentifier={recipe.name}
                itemMetadata={{
                  description: recipe.description,
                  prep_time_minutes: recipe.prep_time_minutes,
                  cook_time_minutes: recipe.cook_time_minutes,
                  servings: recipe.servings || 1,
                  nutritional_info: recipe.nutritional_info,
                }}
                size="medium"
                showLabels={true}
              />
            </View>
          </>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  descriptionText: {
    fontSize: 16,
    color: '#666',
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 8,
  },
  card: {
    marginVertical: 8,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  ingredientText: {
    fontSize: 16,
  },
  instructionsText: {
    fontSize: 16,
  },
  nutritionContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  nutritionTag: {
    backgroundColor: '#f0f0f0',
    padding: 4,
    borderRadius: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  recipeActions: {
    alignItems: 'center',
    marginTop: 24,
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  rateRecipeText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
});

export default RecipeDetailScreen;

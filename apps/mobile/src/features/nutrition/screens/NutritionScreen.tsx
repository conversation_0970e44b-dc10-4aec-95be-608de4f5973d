import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useQuery } from '@tanstack/react-query';
import { format, addDays, subDays } from 'date-fns';
import React, { useState } from 'react';
import { View, StyleSheet, ActivityIndicator, ScrollView } from 'react-native';
import { Text, Card, IconButton, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import IncompleteProfileMessage from '../../../shared/components/IncompleteProfileMessage';
import { ScreenWithTopBar } from '../../../shared/components/ScreenWithTopBar';
import { NutritionStackParamList } from '../../../shared/types/navigation';
import { useProfileCompletion } from '../../home/<USER>/useProfileCompletion';
import { getDailyMealPlan } from '../services/mealService';

interface Recipe {
  id: string;
  name: string;
  description?: string;
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
}

interface MealPlan {
  id: string;
  date: string;
  recipes: Recipe[];
}

// Define props type for RecipeCard
interface RecipeCardProps {
  recipe: Recipe;
}

const RecipeCard: React.FC<RecipeCardProps> = ({ recipe }) => {
  const navigation =
    useNavigation<
      StackNavigationProp<NutritionStackParamList, 'RecipeDetail'>
    >();

  return (
    <Card
      style={styles.card}
      onPress={() =>
        navigation.navigate('RecipeDetail', { recipeId: recipe.id })
      }
    >
      <View style={styles.cardContent}>
        <View style={styles.cardTextContainer}>
          <Text style={styles.recipeName}>{recipe.name}</Text>
          <Text style={styles.mealType}>{recipe.meal_type}</Text>
        </View>
        {/* Checkbox placeholder */}
        <IconButton
          icon="checkbox-blank-outline"
          size={24}
          onPress={() => {}}
        />
      </View>
    </Card>
  );
};

// Define props type for DateNavigator
interface DateNavigatorProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

const DateNavigator = ({ currentDate, onDateChange }: DateNavigatorProps) => {
  const handlePrevDay = () => {
    onDateChange(subDays(currentDate, 1));
  };

  const handleNextDay = () => {
    onDateChange(addDays(currentDate, 1));
  };

  return (
    <View style={styles.dateNavigatorContainer}>
      <IconButton icon="chevron-left" onPress={handlePrevDay} size={24} />
      <View style={styles.dateDisplayContainer}>
        <Icon name="calendar" size={20} />
        <Text style={styles.dateText}>
          {format(currentDate, 'EEEE, MMM d')}
        </Text>
      </View>
      <IconButton icon="chevron-right" onPress={handleNextDay} size={24} />
    </View>
  );
};

export default function NutritionScreen() {
  const navigation =
    useNavigation<
      StackNavigationProp<NutritionStackParamList, 'GroceryList'>
    >();
  const [currentDate, setCurrentDate] = useState(new Date());
  const theme = useTheme();
  const { isComplete: isProfileComplete, loading: profileLoading } =
    useProfileCompletion();

  const {
    data: mealPlan,
    isLoading,
    isError,
    error,
  } = useQuery<MealPlan | null>({
    queryKey: ['dailyMealPlan', format(currentDate, 'yyyy-MM-dd')],
    queryFn: () => getDailyMealPlan(format(currentDate, 'yyyy-MM-dd')),
    enabled: isProfileComplete, // Only fetch meal plan if profile is complete
  });

  const renderContent = () => {
    // Show incomplete profile message if profile is not complete
    if (!profileLoading && !isProfileComplete) {
      return (
        <IncompleteProfileMessage
          title="Complete Your Profile First"
          message="We need to know your dietary preferences and goals to create personalized meal plans for you."
          icon="food-off"
          buttonText="Set Up Nutrition Profile"
        />
      );
    }

    if (isLoading || profileLoading) {
      return <ActivityIndicator size="large" color={theme.colors.primary} />;
    }

    if (isError) {
      return <Text>Error fetching data: {error.message}</Text>;
    }

    if (!mealPlan || mealPlan.recipes.length === 0) {
      return <Text>No meal plan available for this day.</Text>;
    }

    return (
      <View style={styles.recipeList}>
        {mealPlan.recipes.map(recipe => (
          <RecipeCard key={recipe.id} recipe={recipe} />
        ))}
      </View>
    );
  };

  return (
    <ScreenWithTopBar title="Nutrition">
      <View style={styles.container}>
        {/* Only show date navigator if profile is complete */}
        {!profileLoading && isProfileComplete && (
          <DateNavigator
            currentDate={currentDate}
            onDateChange={setCurrentDate}
          />
        )}
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.contentContainer}>{renderContent()}</View>
        </ScrollView>
        {/* Only show grocery list button if profile is complete */}
        {!profileLoading && isProfileComplete && (
          <IconButton
            icon="cart"
            size={24}
            onPress={() => navigation.navigate('GroceryList')}
            style={styles.fab}
            iconColor="white"
            containerColor={theme.colors.primary}
          />
        )}
      </View>
    </ScreenWithTopBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  dateNavigatorContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  dateDisplayContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  scrollContent: {
    padding: 16,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recipeList: {
    width: '100%',
  },
  card: {
    marginVertical: 8,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  cardTextContainer: {
    flex: 1,
  },
  recipeName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  mealType: {
    textTransform: 'capitalize',
    color: '#666',
  },

  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
});

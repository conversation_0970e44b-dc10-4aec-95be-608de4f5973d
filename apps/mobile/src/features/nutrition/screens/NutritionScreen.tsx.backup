import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>w,
  <PERSON><PERSON>,
  <PERSON>,
} from "tamagui";
import {
  ChevronLeft,
  ChevronRight,
  Calendar,
  ShoppingCart,
} from "@tamagui/lucide-icons";
import { format, addDays, subDays } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import { getDailyMealPlan } from "../services/mealService";
import { useNavigation } from "@react-navigation/native";
import { AppTabScreenProps } from "../../../shared/types/navigation";

interface Recipe {
  id: string;
  name: string;
  description?: string;
  meal_type: "breakfast" | "lunch" | "dinner" | "snack";
}

interface MealPlan {
  id: string;
  date: string;
  recipes: Recipe[];
}

// Define props type for RecipeCard
interface RecipeCardProps {
  recipe: Recipe;
}

const RecipeCard: React.FC<RecipeCardProps> = ({ recipe }) => {
  const navigation =
    useNavigation<AppTabScreenProps<"RecipeDetail">["navigation"]>();

  return (
    <Card
      bordered
      padding="$4"
      marginBottom="$4"
      onPress={() =>
        navigation.navigate("RecipeDetail", { recipeId: recipe.id })
      }
    >
      <XStack alignItems="center" justifyContent="space-between">
        <YStack>
          <Text fontSize="$6" fontWeight="bold">
            {recipe.name}
          </Text>
          <Text color="$gray10" textTransform="capitalize">
            {recipe.meal_type}
          </Text>
        </YStack>
        {/* Checkbox placeholder */}
        <Button size="$2" circular />
      </XStack>
    </Card>
  );
};

// Define props type for DateNavigator
interface DateNavigatorProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

const DateNavigator = ({ currentDate, onDateChange }: DateNavigatorProps) => {
  const handlePrevDay = () => {
    onDateChange(subDays(currentDate, 1));
  };

  const handleNextDay = () => {
    onDateChange(addDays(currentDate, 1));
  };

  return (
    <XStack
      justifyContent="space-between"
      alignItems="center"
      paddingHorizontal="$4"
      paddingVertical="$3"
      borderBottomWidth={1}
      borderColor="$gray4"
    >
      <Button icon={ChevronLeft} onPress={handlePrevDay} circular />
      <XStack alignItems="center" space="$2">
        <Calendar size="$1" />
        <Text fontSize="$5" fontWeight="bold">
          {format(currentDate, "EEEE, MMM d")}
        </Text>
      </XStack>
      <Button icon={ChevronRight} onPress={handleNextDay} circular />
    </XStack>
  );
};

export default function NutritionScreen() {
  const navigation =
    useNavigation<AppTabScreenProps<"GroceryList">["navigation"]>();
  const [currentDate, setCurrentDate] = useState(new Date());

  const {
    data: mealPlan,
    isLoading,
    isError,
    error,
  } = useQuery<MealPlan | null>({
    queryKey: ["dailyMealPlan", format(currentDate, "yyyy-MM-dd")],
    queryFn: () => getDailyMealPlan(format(currentDate, "yyyy-MM-dd")),
  });

  const renderContent = () => {
    if (isLoading) {
      return <Spinner size="large" color="$blue10" />;
    }

    if (isError) {
      return <Text>Error fetching data: {error.message}</Text>;
    }

    if (!mealPlan || mealPlan.recipes.length === 0) {
      return <Text>No meal plan available for this day.</Text>;
    }

    return (
      <YStack space="$4">
        {mealPlan.recipes.map((recipe) => (
          <RecipeCard key={recipe.id} recipe={recipe} />
        ))}
      </YStack>
    );
  };

  return (
    <YStack flex={1} backgroundColor="$background">
      <DateNavigator currentDate={currentDate} onDateChange={setCurrentDate} />
      <ScrollView padding="$4">
        <YStack flex={1} justifyContent="center" alignItems="center">
          {renderContent()}
        </YStack>
      </ScrollView>
      <Button
        position="absolute"
        bottom={20}
        right={20}
        size="$5"
        circular
        icon={ShoppingCart}
        onPress={() => navigation.navigate("GroceryList")}
        backgroundColor="$blue10"
        color="white"
      />
    </YStack>
  );
}

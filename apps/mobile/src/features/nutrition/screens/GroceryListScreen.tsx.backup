import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>ck,
  XStack,
  Text,
  Button,
  Input,
  ScrollView,
  Card,
  Checkbox,
} from "tamagui";
import { Plus, Trash2 } from "@tamagui/lucide-icons";
import {
  showSuccessToast,
  showErrorToast,
  showInfoToast,
} from "../../../shared/utils/toast";
import {
  triggerSuccessHaptic,
  triggerImpactHaptic,
  triggerSelectionHaptic,
} from "../../../shared/utils/haptic";

interface GroceryItem {
  id: string;
  name: string;
  completed: boolean;
}

export default function GroceryListScreen() {
  const [groceryItems, setGroceryItems] = useState<GroceryItem[]>([
    { id: "1", name: "Chicken breast", completed: false },
    { id: "2", name: "<PERSON><PERSON><PERSON><PERSON>", completed: true },
    { id: "3", name: "Brown rice", completed: false },
  ]);
  const [newItem, setNewItem] = useState("");

  const addItem = () => {
    if (newItem.trim() !== "") {
      setGroceryItems([
        ...groceryItems,
        {
          id: Date.now().toString(),
          name: newItem,
          completed: false,
        },
      ]);
      showSuccessToast("Item added!", newItem);
      triggerSuccessHaptic();
      setNewItem("");
    }
  };

  const toggleItem = (id: string) => {
    const item = groceryItems.find((item) => item.id === id);
    if (item) {
      triggerSelectionHaptic();
    }
    setGroceryItems(
      groceryItems.map((item) =>
        item.id === id ? { ...item, completed: !item.completed } : item,
      ),
    );
  };

  const removeItem = (id: string) => {
    const itemToRemove = groceryItems.find((item) => item.id === id);
    if (itemToRemove) {
      showInfoToast("Item removed", itemToRemove.name);
      triggerImpactHaptic("light");
    }
    setGroceryItems(groceryItems.filter((item) => item.id !== id));
  };

  const clearCompleted = () => {
    const completedCount = groceryItems.filter((item) => item.completed).length;
    setGroceryItems(groceryItems.filter((item) => !item.completed));
    if (completedCount > 0) {
      showSuccessToast(
        "Items cleared",
        `${completedCount} completed item${completedCount > 1 ? "s" : ""} removed`,
      );
      triggerSuccessHaptic();
    }
  };

  return (
    <YStack flex={1} backgroundColor="$background">
      <YStack padding="$4" space="$4">
        <Text fontSize="$8" fontWeight="bold">
          Grocery List
        </Text>

        <XStack space="$3">
          <Input
            flex={1}
            placeholder="Add new item..."
            value={newItem}
            onChangeText={setNewItem}
            onSubmitEditing={addItem}
          />
          <Button icon={Plus} onPress={addItem} />
        </XStack>

        <Button
          onPress={clearCompleted}
          backgroundColor="$red10"
          color="white"
          disabled={!groceryItems.some((item) => item.completed)}
        >
          Clear Completed
        </Button>
      </YStack>

      <ScrollView padding="$4" flex={1}>
        <YStack space="$3">
          {groceryItems.map((item) => (
            <Card key={item.id} bordered padding="$3">
              <XStack alignItems="center" space="$3">
                <Checkbox
                  checked={item.completed}
                  onCheckedChange={() => toggleItem(item.id)}
                />
                <Text
                  flex={1}
                  textDecorationLine={item.completed ? "line-through" : "none"}
                  color={item.completed ? "$gray10" : "$color"}
                >
                  {item.name}
                </Text>
                <Button
                  icon={Trash2}
                  size="$2"
                  circular
                  onPress={() => removeItem(item.id)}
                />
              </XStack>
            </Card>
          ))}

          {groceryItems.length === 0 && (
            <YStack justifyContent="center" alignItems="center" padding="$8">
              <Text fontSize="$5" color="$gray10">
                Your grocery list is empty
              </Text>
            </YStack>
          )}
        </YStack>
      </ScrollView>
    </YStack>
  );
}

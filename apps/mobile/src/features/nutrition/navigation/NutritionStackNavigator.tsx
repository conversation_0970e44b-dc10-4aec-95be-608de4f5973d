import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import GroceryListScreen from '../screens/GroceryListScreen';
import MealPlanScreen from '../screens/MealPlanScreen';
import NutritionScreen from '../screens/NutritionScreen';
import RecipeDetailScreen from '../screens/RecipeDetailScreen';

const NutritionStack = createStackNavigator();

export function NutritionStackNavigator() {
  return (
    <NutritionStack.Navigator
      id={undefined}
      screenOptions={{ headerShown: false }}
    >
      <NutritionStack.Screen name="NutritionMain" component={NutritionScreen} />
      <NutritionStack.Screen
        name="RecipeDetail"
        component={RecipeDetailScreen}
      />
      <NutritionStack.Screen name="GroceryList" component={GroceryListScreen} />
      <NutritionStack.Screen name="MealPlan" component={MealPlanScreen} />
    </NutritionStack.Navigator>
  );
}

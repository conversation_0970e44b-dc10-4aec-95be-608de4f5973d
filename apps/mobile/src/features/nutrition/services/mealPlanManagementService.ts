import NetInfo from '@react-native-community/netinfo';
import { supabase } from '../../../lib/supabase';
import { addToOfflineQueue } from '../../../shared/services/offline/offlineService';

export interface EnhancedMealPlan {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  week_start_date: string;
  duration_days: number;
  target_calories_per_day?: number;
  target_protein_grams?: number;
  target_carbs_grams?: number;
  target_fat_grams?: number;
  dietary_preferences?: string[];
  excluded_ingredients?: string[];
  preferred_cuisines?: string[];
  max_prep_time_minutes?: number;
  cooking_skill_level?: 'beginner' | 'intermediate' | 'advanced';
  ai_generated: boolean;
  created_at: string;
  updated_at?: string;
}

export interface MealPlanMeal {
  id: string;
  meal_plan_id: string;
  recipe_id: string;
  day_of_week: number; // 0 = Sunday, 1 = Monday, etc.
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  servings: number;
  notes?: string;
  recipe?: {
    id: string;
    name: string;
    description?: string;
    calories_per_serving?: number;
    protein_grams?: number;
    carbs_grams?: number;
    fat_grams?: number;
    prep_time_minutes?: number;
    cook_time_minutes?: number;
    image_url?: string;
  };
}

export interface MealPlanSummary {
  id: string;
  name: string;
  week_start_date: string;
  duration_days: number;
  target_calories_per_day?: number;
  meal_count: number;
  created_at: string;
  is_current: boolean; // If this plan covers current date
}

class MealPlanManagementService {
  /**
   * Get all meal plans for the current user
   */
  async getUserMealPlans(): Promise<MealPlanSummary[]> {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) throw new Error('Not authenticated');

      const { data: mealPlans, error } = await supabase
        .from('meal_plans')
        .select(
          `
          id,
          name,
          week_start_date,
          duration_days,
          target_calories_per_day,
          created_at,
          meal_plan_meals(count)
        `
        )
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];

      return (mealPlans || []).map(plan => ({
        id: plan.id,
        name: plan.name,
        week_start_date: plan.week_start_date,
        duration_days: plan.duration_days || 7,
        target_calories_per_day: plan.target_calories_per_day,
        meal_count: plan.meal_plan_meals?.[0]?.count || 0,
        created_at: plan.created_at,
        is_current: this.isPlanCurrent(
          plan.week_start_date,
          plan.duration_days || 7,
          todayStr
        ),
      }));
    } catch (error) {
      console.error('Error fetching user meal plans:', error);
      throw error;
    }
  }

  /**
   * Check if user has an active meal plan for current date
   */
  async hasActiveMealPlan(): Promise<{
    hasActive: boolean;
    activePlan?: MealPlanSummary;
  }> {
    try {
      const mealPlans = await this.getUserMealPlans();
      const activePlan = mealPlans.find(plan => plan.is_current);

      return {
        hasActive: !!activePlan,
        activePlan,
      };
    } catch (error) {
      console.error('Error checking active meal plan:', error);
      return { hasActive: false };
    }
  }

  /**
   * Get detailed meal plan with all meals
   */
  async getMealPlanDetails(mealPlanId: string): Promise<{
    plan: EnhancedMealPlan;
    meals: MealPlanMeal[];
  }> {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) throw new Error('Not authenticated');

      // Get meal plan details
      const { data: plan, error: planError } = await supabase
        .from('meal_plans')
        .select('*')
        .eq('id', mealPlanId)
        .eq('user_id', session.user.id)
        .single();

      if (planError) throw planError;

      // Get meals with recipe details
      const { data: meals, error: mealsError } = await supabase
        .from('meal_plan_meals')
        .select(
          `
          *,
          recipes (
            id,
            name,
            description,
            calories_per_serving,
            protein_grams,
            carbs_grams,
            fat_grams,
            prep_time_minutes,
            cook_time_minutes,
            image_url
          )
        `
        )
        .eq('meal_plan_id', mealPlanId)
        .order('day_of_week')
        .order('meal_type');

      if (mealsError) throw mealsError;

      return {
        plan: plan as EnhancedMealPlan,
        meals: (meals || []).map(meal => ({
          ...meal,
          recipe: meal.recipes,
        })) as MealPlanMeal[],
      };
    } catch (error) {
      console.error('Error fetching meal plan details:', error);
      throw error;
    }
  }

  /**
   * Delete a meal plan
   */
  async deleteMealPlan(mealPlanId: string): Promise<void> {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) throw new Error('Not authenticated');

      // Check network connectivity
      const netInfo = await NetInfo.fetch();
      if (!netInfo.isConnected) {
        await addToOfflineQueue({
          type: 'meal_plan',
          action: 'delete',
          data: { id: mealPlanId },
        });
        throw new Error('No internet connection. Action queued for later.');
      }

      const { error } = await supabase
        .from('meal_plans')
        .delete()
        .eq('id', mealPlanId)
        .eq('user_id', session.user.id);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting meal plan:', error);
      throw error;
    }
  }

  /**
   * Update meal plan name and description
   */
  async updateMealPlan(
    mealPlanId: string,
    updates: { name?: string; description?: string }
  ): Promise<void> {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) throw new Error('Not authenticated');

      const { error } = await supabase
        .from('meal_plans')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', mealPlanId)
        .eq('user_id', session.user.id);

      if (error) throw error;
    } catch (error) {
      console.error('Error updating meal plan:', error);
      throw error;
    }
  }

  /**
   * Check if a meal plan covers the current date
   */
  private isPlanCurrent(
    weekStartDate: string,
    durationDays: number,
    todayStr: string
  ): boolean {
    const startDate = new Date(weekStartDate);
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + durationDays - 1);

    const today = new Date(todayStr);
    return today >= startDate && today <= endDate;
  }

  /**
   * Get meal plan for a specific date
   */
  async getMealPlanForDate(date: string): Promise<{
    plan: EnhancedMealPlan;
    meals: MealPlanMeal[];
  } | null> {
    try {
      const mealPlans = await this.getUserMealPlans();
      const activePlan = mealPlans.find(plan =>
        this.isPlanCurrent(plan.week_start_date, plan.duration_days, date)
      );

      if (!activePlan) return null;

      return await this.getMealPlanDetails(activePlan.id);
    } catch (error) {
      console.error('Error fetching meal plan for date:', error);
      throw error;
    }
  }
}

export const mealPlanManagementService = new MealPlanManagementService();

import NetInfo from '@react-native-community/netinfo';
import { supabase } from '../../../lib/supabase';
import { addToOfflineQueue } from '../../../shared/services/offline/offlineService';

export interface Recipe {
  id: string;
  name: string;
  description?: string;
  meal_type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  ingredients?: any;
  instructions?: string;
}

export interface MealLog {
  id?: string;
  meal_plan_id: string;
  recipe_id: string;
  consumed_at?: string;
  notes?: string;
  created_at?: string;
}

export interface MealPlan {
  id: string;
  date: string;
  recipes: Recipe[];
}

export const logMeal = async (
  mealLog: Omit<MealLog, 'id' | 'created_at'>
): Promise<MealLog> => {
  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      throw new Error('Not authenticated');
    }

    // Check network connectivity
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      // Add to offline queue
      await addToOfflineQueue({
        type: 'meal_log',
        action: 'create',
        data: mealLog,
      });
      throw new Error('No internet connection. Data saved locally.');
    }

    const { data, error } = await supabase
      .from('meal_logs')
      .insert([
        {
          ...mealLog,
          user_id: session.user.id,
        },
      ])
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error logging meal:', error);
    throw error;
  }
};

export const getDailyMealPlan = async (
  date: string
): Promise<MealPlan | null> => {
  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      throw new Error('Not authenticated');
    }

    // Check network connectivity
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      throw new Error('No internet connection');
    }

    // Get the meal plan for the specified date
    const { data: mealPlan, error: mealPlanError } = await supabase
      .from('meal_plans')
      .select('id')
      .eq('user_id', session.user.id)
      .eq('date', date)
      .single();

    if (mealPlanError) {
      if (mealPlanError.code === 'PGRST116') {
        // No meal plan found for this date
        return null;
      }
      throw mealPlanError;
    }

    // Get the recipes for this meal plan
    const { data: mealPlanRecipes, error: recipesError } = await supabase
      .from('meal_plan_recipes')
      .select(
        `
        meal_type,
        recipes (
          id,
          name,
          description
        )
      `
      )
      .eq('meal_plan_id', mealPlan.id);

    if (recipesError) {
      throw recipesError;
    }

    // Transform the data to match our MealPlan interface
    const recipes: Recipe[] = mealPlanRecipes
      .map(item => ({
        id: (item.recipes as any)?.id || '',
        name: (item.recipes as any)?.name || '',
        description: (item.recipes as any)?.description || '',
        meal_type: item.meal_type as 'breakfast' | 'lunch' | 'dinner' | 'snack',
      }))
      .filter(recipe => recipe.id && recipe.name);

    return {
      id: mealPlan.id,
      date,
      recipes,
    };
  } catch (error) {
    console.error('Error fetching daily meal plan:', error);
    throw error;
  }
};

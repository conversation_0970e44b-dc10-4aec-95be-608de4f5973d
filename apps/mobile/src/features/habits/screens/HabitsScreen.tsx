import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { format, addDays, subDays } from 'date-fns';
import React, { useState } from 'react';
import {
  ScrollView,
  View,
  StyleSheet,
  Animated,
  RefreshControl,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  FAB,
  Chip,
  ActivityIndicator,
  IconButton,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { ScreenWithTopBar } from '../../../shared/components';
import { useNotifications } from '../../../shared/contexts/NotificationContext';
import { HabitsStackParamList } from '../../../shared/types/navigation';
import { ProgressIndicator } from '../../progress/components/ProgressIndicator';
import { useHabits } from '../hooks/useHabits';
import { Habit } from '../services/habitsService';

type HabitsScreenNavigationProp = StackNavigationProp<
  HabitsStackParamList,
  'HabitsList'
>;

// Define props type for DateNavigator
interface DateNavigatorProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

const DateNavigator = ({ currentDate, onDateChange }: DateNavigatorProps) => {
  const handlePrevDay = () => {
    onDateChange(subDays(currentDate, 1));
  };

  const handleNextDay = () => {
    onDateChange(addDays(currentDate, 1));
  };

  return (
    <View style={styles.dateNavigatorContainer}>
      <IconButton icon="chevron-left" onPress={handlePrevDay} size={24} />
      <View style={styles.dateDisplayContainer}>
        <Icon name="calendar" size={20} />
        <Text style={styles.dateText}>
          {format(currentDate, 'EEEE, MMM d')}
        </Text>
      </View>
      <IconButton icon="chevron-right" onPress={handleNextDay} size={24} />
    </View>
  );
};

const HabitCard = ({
  habit,
  onToggle,
  onDelete,
  currentDate,
}: {
  habit: Habit;
  onToggle: (id: string, date: Date) => void;
  onDelete: (id: string) => void;
  currentDate: Date;
}) => {
  const scaleAnim = React.useRef(new Animated.Value(1)).current;
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'supplement':
        return 'pill';
      case 'activity':
        return 'run';
      case 'hydration':
        return 'water';
      case 'sleep':
        return 'sleep';
      default:
        return 'check-circle';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'supplement':
        return '#FF9500';
      case 'activity':
        return '#34C759';
      case 'hydration':
        return '#007AFF';
      case 'sleep':
        return '#5856D6';
      default:
        return '#8E8E93';
    }
  };

  const handleToggle = () => {
    // Animate the card when toggling
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    onToggle(habit.id, currentDate);
  };

  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <Card
        style={[
          styles.habitCard,
          habit.completed_today && styles.completedCard,
        ]}
      >
        <View style={styles.habitHeader}>
          <View style={styles.habitInfo}>
            <View style={styles.habitTitleRow}>
              <Icon
                name={getCategoryIcon(habit.category)}
                size={20}
                color={getCategoryColor(habit.category)}
                style={styles.categoryIcon}
              />
              <Text style={styles.habitTitle}>{habit.title}</Text>
            </View>
            <Text style={styles.habitDescription}>{habit.description}</Text>
            {habit.target_amount && (
              <Text style={styles.habitTarget}>
                Target: {habit.target_amount} {habit.target_unit}
              </Text>
            )}
          </View>
          <View style={styles.habitActions}>
            <Button
              mode="text"
              onPress={() => onDelete(habit.id)}
              icon="delete"
              compact
              style={styles.deleteButton}
              textColor="#FF3B30"
            >
              Delete
            </Button>
            <Button
              mode={habit.completed_today ? 'contained' : 'outlined'}
              onPress={handleToggle}
              icon={habit.completed_today ? 'check' : 'circle-outline'}
              compact
              style={styles.completeButton}
            >
              {habit.completed_today ? 'Done' : 'Mark'}
            </Button>
          </View>
        </View>
        <View style={styles.habitFooter}>
          <Chip
            icon="fire"
            style={styles.streakChip}
            textStyle={styles.streakText}
          >
            {habit.current_streak} day streak
          </Chip>
          <Chip
            style={[
              styles.categoryChip,
              { backgroundColor: getCategoryColor(habit.category) + '20' },
            ]}
            textStyle={[
              styles.categoryChipText,
              { color: getCategoryColor(habit.category) },
            ]}
          >
            {habit.category}
          </Chip>
        </View>
      </Card>
    </Animated.View>
  );
};

export default function HabitsScreen() {
  const navigation = useNavigation<HabitsScreenNavigationProp>();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const { addNotification } = useNotifications();
  const progressAnim = React.useRef(new Animated.Value(0)).current;

  const {
    habits,
    loading,
    error,
    currentDate,
    setCurrentDate,
    refreshHabits,
    refreshHabitsForDate,
    deleteHabit,
    toggleHabitCompletion,
    toggleHabitCompletionForDate,
    getHabitsByCategory,
  } = useHabits();

  const categories = [
    { key: 'all', label: 'All', icon: 'view-grid' },
    { key: 'supplement', label: 'Supplements', icon: 'pill' },
    { key: 'activity', label: 'Activity', icon: 'run' },
    { key: 'hydration', label: 'Hydration', icon: 'water' },
    { key: 'sleep', label: 'Sleep', icon: 'sleep' },
    { key: 'custom', label: 'Custom', icon: 'plus-circle' },
  ];

  const filteredHabits = getHabitsByCategory(selectedCategory);

  const completedToday = habits.filter(habit => habit.completed_today).length;
  const totalHabits = habits.length;
  const progressPercentage =
    totalHabits > 0 ? (completedToday / totalHabits) * 100 : 0;

  // Refresh habits when screen comes into focus (e.g., after adding a new habit)
  useFocusEffect(
    React.useCallback(() => {
      const today = new Date();
      const isToday = currentDate.toDateString() === today.toDateString();

      if (isToday) {
        refreshHabits();
      } else {
        refreshHabitsForDate(currentDate);
      }
    }, [currentDate, refreshHabits, refreshHabitsForDate])
  );

  // Animate progress bar when habits change
  React.useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: progressPercentage,
      duration: 500,
      useNativeDriver: false,
    }).start();
  }, [progressPercentage, progressAnim]);

  const handleToggleHabit = async (habitId: string, date: Date) => {
    try {
      const habit = habits.find(h => h.id === habitId);
      const wasCompleted = habit?.completed_today;
      const today = new Date();
      const isToday = date.toDateString() === today.toDateString();

      if (isToday) {
        await toggleHabitCompletion(habitId);
      } else {
        await toggleHabitCompletionForDate(habitId, date);
      }

      // Add a notification when habit is completed (only for today)
      if (habit && !wasCompleted && isToday) {
        addNotification({
          title: 'Habit Completed! 🎉',
          body: `Great job completing "${habit.title}"! Keep up the streak!`,
          categoryId: 'habits',
        });
      }
    } catch (toggleError) {
      console.error('Error toggling habit:', toggleError);
    }
  };

  const handleDeleteHabit = async (habitId: string) => {
    try {
      await deleteHabit(habitId);
      addNotification({
        title: 'Habit Deleted',
        body: 'Habit has been successfully removed',
        categoryId: 'habits',
      });
    } catch (deleteError) {
      if (
        deleteError instanceof Error &&
        deleteError.message !== 'User cancelled'
      ) {
        console.error('Error deleting habit:', deleteError);
      }
    }
  };

  return (
    <ScreenWithTopBar title="Habits">
      {loading && habits.length === 0 ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading your habits...</Text>
        </View>
      ) : (
        <View style={styles.container}>
          {/* Date Navigator */}
          <DateNavigator
            currentDate={currentDate}
            onDateChange={setCurrentDate}
          />

          <ScrollView
            style={styles.scrollContainer}
            contentContainerStyle={styles.contentContainer}
            refreshControl={
              <RefreshControl
                refreshing={loading && habits.length > 0}
                onRefresh={() => {
                  const today = new Date();
                  const isToday =
                    currentDate.toDateString() === today.toDateString();

                  if (isToday) {
                    refreshHabits();
                  } else {
                    refreshHabitsForDate(currentDate);
                  }
                }}
                colors={['#007AFF']}
                tintColor="#007AFF"
              />
            }
          >
            {/* Progress Summary */}
            <ProgressIndicator
              type="habits"
              completed={completedToday}
              total={totalHabits}
              streak={habits.reduce(
                (max, habit) => Math.max(max, habit.current_streak),
                0
              )}
              onPress={() => {
                // Navigate to Progress tab through root navigator
                // @ts-ignore
                (navigation as any).navigate('Progress');
              }}
            />

            {/* Category Filter */}
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.categoryScroll}
              contentContainerStyle={styles.categoryContainer}
            >
              {categories.map(category => (
                <Chip
                  key={category.key}
                  icon={category.icon}
                  selected={selectedCategory === category.key}
                  onPress={() => setSelectedCategory(category.key)}
                  style={[
                    styles.categoryFilterChip,
                    selectedCategory === category.key &&
                      styles.selectedCategoryChip,
                  ]}
                >
                  {category.label}
                </Chip>
              ))}
            </ScrollView>

            {/* Habits List */}
            <View style={styles.habitsSection}>
              <Text style={styles.sectionTitle}>
                {selectedCategory === 'all'
                  ? 'All Habits'
                  : `${categories.find(c => c.key === selectedCategory)?.label} Habits`}
              </Text>
              {filteredHabits.map(habit => (
                <HabitCard
                  key={habit.id}
                  habit={habit}
                  onToggle={handleToggleHabit}
                  onDelete={handleDeleteHabit}
                  currentDate={currentDate}
                />
              ))}
            </View>

            {/* Empty State */}
            {filteredHabits.length === 0 && (
              <Card style={styles.emptyCard}>
                <Icon
                  name="plus-circle-outline"
                  size={48}
                  color="#8E8E93"
                  style={styles.emptyIcon}
                />
                <Text style={styles.emptyTitle}>No habits yet</Text>
                <Text style={styles.emptyDescription}>
                  Create your first habit to start building healthy routines
                </Text>
              </Card>
            )}
          </ScrollView>

          {/* Error Message */}
          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
              <Button
                mode="outlined"
                onPress={() => {
                  const today = new Date();
                  const isToday =
                    currentDate.toDateString() === today.toDateString();

                  if (isToday) {
                    refreshHabits();
                  } else {
                    refreshHabitsForDate(currentDate);
                  }
                }}
              >
                Retry
              </Button>
            </View>
          )}
        </View>
      )}

      {/* Floating Action Button */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => navigation.navigate('AddHabit')}
      />
    </ScreenWithTopBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 80, // Space for FAB
  },
  dateNavigatorContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
    backgroundColor: '#FFFFFF',
  },
  dateDisplayContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },

  categoryScroll: {
    marginBottom: 16,
  },
  categoryContainer: {
    paddingHorizontal: 4,
  },
  categoryFilterChip: {
    marginRight: 8,
  },
  selectedCategoryChip: {
    backgroundColor: '#007AFF',
  },
  habitsSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  habitCard: {
    marginBottom: 12,
    padding: 16,
  },
  completedCard: {
    opacity: 0.7,
  },
  habitHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  habitInfo: {
    flex: 1,
    marginRight: 12,
  },
  habitTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  categoryIcon: {
    marginRight: 8,
  },
  habitTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  habitDescription: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 4,
  },
  habitTarget: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '500',
  },
  habitActions: {
    alignItems: 'flex-end',
    gap: 8,
  },
  deleteButton: {
    minWidth: 70,
  },
  completeButton: {
    minWidth: 80,
  },
  habitFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  streakChip: {
    backgroundColor: '#FF950020',
  },
  streakText: {
    color: '#FF9500',
    fontSize: 12,
  },
  categoryChip: {
    // View styles only - text styles should go in textStyle prop
  },
  emptyCard: {
    padding: 32,
    alignItems: 'center',
    marginTop: 32,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#8E8E93',
  },
  errorContainer: {
    padding: 16,
    alignItems: 'center',
    backgroundColor: '#FF3B3020',
    margin: 16,
    borderRadius: 8,
  },
  errorText: {
    color: '#FF3B30',
    marginBottom: 12,
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#007AFF',
  },
  categoryChipText: {
    fontSize: 12,
  },
});

import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import React, { useState } from 'react';
import { ScrollView, View, StyleSheet, Alert } from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Chip,
  SegmentedButtons,
} from 'react-native-paper';
import { ScreenWithTopBar } from '../../../shared/components';
import { useNotifications } from '../../../shared/contexts/NotificationContext';
import { HabitsStackParamList } from '../../../shared/types/navigation';
import { useHabits } from '../hooks/useHabits';

type AddHabitScreenNavigationProp = StackNavigationProp<
  HabitsStackParamList,
  'AddHabit'
>;

interface HabitFormData {
  title: string;
  description: string;
  category: 'supplement' | 'activity' | 'hydration' | 'sleep' | 'custom';
  customCategory?: string;
  frequency: 'daily' | 'weekly' | 'custom';
  target?: number;
  unit?: string;
}

const categories = [
  { key: 'supplement', label: 'Supplement', icon: 'pill', color: '#FF9500' },
  { key: 'activity', label: 'Activity', icon: 'run', color: '#34C759' },
  { key: 'hydration', label: 'Hydration', icon: 'water', color: '#007AFF' },
  { key: 'sleep', label: 'Sleep', icon: 'sleep', color: '#5856D6' },
  { key: 'custom', label: 'Custom', icon: 'plus-circle', color: '#8E8E93' },
];

export default function AddHabitScreen() {
  const navigation = useNavigation<AddHabitScreenNavigationProp>();
  const { addNotification } = useNotifications();
  const { createHabit } = useHabits();

  const [formData, setFormData] = useState<HabitFormData>({
    title: '',
    description: '',
    category: 'custom',
    customCategory: '',
    frequency: 'daily',
    target: undefined,
    unit: '',
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    // Validate form
    if (!formData.title.trim()) {
      Alert.alert('Error', 'Please enter a habit title');
      return;
    }

    if (!formData.description.trim()) {
      Alert.alert('Error', 'Please enter a habit description');
      return;
    }

    if (formData.category === 'custom' && !formData.customCategory?.trim()) {
      Alert.alert('Error', 'Please enter a custom category name');
      return;
    }

    setIsLoading(true);

    try {
      // Save to database
      await createHabit(formData);

      // Add success notification
      addNotification({
        title: 'Habit Created! 🎉',
        body: `"${formData.title}" has been added to your habits`,
        categoryId: 'habits',
      });

      // Navigate back
      navigation.goBack();
    } catch (error) {
      console.error('Error creating habit:', error);
      Alert.alert('Error', 'Failed to create habit. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScreenWithTopBar title="Add New Habit">
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        {/* Basic Information */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Basic Information</Text>

          <TextInput
            label="Habit Title"
            value={formData.title}
            onChangeText={text => setFormData({ ...formData, title: text })}
            mode="outlined"
            style={styles.input}
            placeholder="e.g., Morning Protein Shake"
          />

          <TextInput
            label="Description"
            value={formData.description}
            onChangeText={text =>
              setFormData({ ...formData, description: text })
            }
            mode="outlined"
            multiline
            numberOfLines={3}
            style={styles.input}
            placeholder="e.g., 25g whey protein within 30 minutes of waking"
          />
        </Card>

        {/* Category Selection */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Category</Text>
          <View style={styles.categoryGrid}>
            {categories.map(category => (
              <Chip
                key={category.key}
                icon={category.icon}
                selected={formData.category === category.key}
                onPress={() =>
                  setFormData({ ...formData, category: category.key as any })
                }
                style={[
                  styles.categoryChip,
                  formData.category === category.key && {
                    backgroundColor: category.color + '20',
                  },
                ]}
                textStyle={
                  formData.category === category.key
                    ? { color: category.color }
                    : undefined
                }
              >
                {category.label}
              </Chip>
            ))}
          </View>

          {/* Custom Category Input */}
          {formData.category === 'custom' && (
            <View style={styles.customCategoryContainer}>
              <Text style={styles.customCategoryLabel}>
                Custom Category Name
              </Text>
              <TextInput
                mode="outlined"
                value={formData.customCategory || ''}
                onChangeText={text =>
                  setFormData({ ...formData, customCategory: text })
                }
                style={styles.customCategoryInput}
                placeholder="e.g., Mindfulness, Study, Work"
              />
            </View>
          )}
        </Card>

        {/* Frequency */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Frequency</Text>
          <SegmentedButtons
            value={formData.frequency}
            onValueChange={value =>
              setFormData({ ...formData, frequency: value as any })
            }
            buttons={[
              { value: 'daily', label: 'Daily' },
              { value: 'weekly', label: 'Weekly' },
              { value: 'custom', label: 'Custom' },
            ]}
          />

          {/* Custom Frequency Options */}
          {formData.frequency === 'custom' && (
            <View style={styles.customFrequencyContainer}>
              <Text style={styles.customFrequencyTitle}>Custom Schedule</Text>
              <Text style={styles.customFrequencyNote}>
                📝 Custom frequency selected. For now, this will be treated as
                daily. Advanced scheduling options coming soon!
              </Text>
            </View>
          )}
        </Card>

        {/* Target (Optional) */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Target (Optional)</Text>
          <View style={styles.targetRow}>
            <TextInput
              label="Target Amount"
              value={formData.target?.toString() || ''}
              onChangeText={text =>
                setFormData({
                  ...formData,
                  target: text ? parseFloat(text) : undefined,
                })
              }
              mode="outlined"
              keyboardType="numeric"
              style={styles.targetInput}
              placeholder="e.g., 25"
            />
            <TextInput
              label="Unit"
              value={formData.unit || ''}
              onChangeText={text => setFormData({ ...formData, unit: text })}
              mode="outlined"
              style={styles.unitInput}
              placeholder="e.g., g, ml, steps"
            />
          </View>
        </Card>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={styles.button}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={handleSave}
            style={styles.button}
            loading={isLoading}
            disabled={isLoading}
          >
            Create Habit
          </Button>
        </View>
      </ScrollView>
    </ScreenWithTopBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  section: {
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  input: {
    marginBottom: 12,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryChip: {
    marginBottom: 8,
  },
  customCategoryContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E5E7',
  },
  customCategoryLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 8,
  },
  customCategoryInput: {
    marginBottom: 0,
  },
  targetRow: {
    flexDirection: 'row',
    gap: 12,
  },
  targetInput: {
    flex: 2,
  },
  unitInput: {
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  button: {
    flex: 1,
  },
  customFrequencyContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#FFF3CD',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FFEAA7',
  },
  customFrequencyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#856404',
    marginBottom: 8,
  },
  customFrequencyNote: {
    fontSize: 14,
    color: '#856404',
    textAlign: 'center',
    lineHeight: 20,
  },
});

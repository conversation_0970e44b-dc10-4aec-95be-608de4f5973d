import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import { HabitsStackParamList } from '../../../shared/types/navigation';
import AddHabitScreen from '../screens/AddHabitScreen';
import HabitsScreen from '../screens/HabitsScreen';

const HabitsStack = createStackNavigator<HabitsStackParamList>();

export function HabitsStackNavigator() {
  return (
    <HabitsStack.Navigator
      id={undefined}
      screenOptions={{
        headerShown: false,
      }}
    >
      <HabitsStack.Screen name="HabitsList" component={HabitsScreen} />
      <HabitsStack.Screen name="AddHabit" component={AddHabitScreen} />
    </HabitsStack.Navigator>
  );
}

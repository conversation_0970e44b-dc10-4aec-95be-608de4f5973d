import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { habitsService, Habit, HabitFormData } from '../services/habitsService';

export interface UseHabitsReturn {
  habits: Habit[];
  loading: boolean;
  error: string | null;
  currentDate: Date;
  setCurrentDate: (date: Date) => void;
  refreshHabits: () => Promise<void>;
  refreshHabitsForDate: (date: Date) => Promise<void>;
  createHabit: (habitData: HabitFormData) => Promise<Habit>;
  updateHabit: (
    habitId: string,
    updates: Partial<HabitFormData>
  ) => Promise<Habit>;
  deleteHabit: (habitId: string) => Promise<void>;
  toggleHabitCompletion: (habitId: string) => Promise<void>;
  toggleHabitCompletionForDate: (habitId: string, date: Date) => Promise<void>;
  getHabitsByCategory: (category: string) => Habit[];
}

export function useHabits(): UseHabitsReturn {
  const [habits, setHabits] = useState<Habit[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentDate, setCurrentDate] = useState(new Date());

  const refreshHabits = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const fetchedHabits = await habitsService.getUserHabits();
      setHabits(fetchedHabits);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to fetch habits';
      setError(errorMessage);
      console.error('Error fetching habits:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshHabitsForDate = useCallback(async (date: Date) => {
    try {
      setLoading(true);
      setError(null);
      const dateString = date.toISOString().split('T')[0];
      const fetchedHabits =
        await habitsService.getUserHabitsForDate(dateString);
      setHabits(fetchedHabits);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to fetch habits';
      setError(errorMessage);
      console.error('Error fetching habits for date:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const createHabit = useCallback(
    async (habitData: HabitFormData): Promise<Habit> => {
      try {
        setError(null);
        const newHabit = await habitsService.createHabit(habitData);

        // Add the new habit to the local state
        setHabits(prev => [newHabit, ...prev]);

        return newHabit;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to create habit';
        setError(errorMessage);
        throw err;
      }
    },
    []
  );

  const updateHabit = useCallback(
    async (
      habitId: string,
      updates: Partial<HabitFormData>
    ): Promise<Habit> => {
      try {
        setError(null);
        const updatedHabit = await habitsService.updateHabit(habitId, updates);

        // Update the habit in local state
        setHabits(prev =>
          prev.map(habit => (habit.id === habitId ? updatedHabit : habit))
        );

        return updatedHabit;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to update habit';
        setError(errorMessage);
        throw err;
      }
    },
    []
  );

  const deleteHabit = useCallback(async (habitId: string): Promise<void> => {
    try {
      setError(null);

      // Show confirmation dialog
      return new Promise((resolve, reject) => {
        Alert.alert(
          'Delete Habit',
          'Are you sure you want to delete this habit? This action cannot be undone.',
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => reject(new Error('User cancelled')),
            },
            {
              text: 'Delete',
              style: 'destructive',
              onPress: async () => {
                try {
                  await habitsService.deleteHabit(habitId);

                  // Remove the habit from local state
                  setHabits(prev => prev.filter(habit => habit.id !== habitId));

                  resolve();
                } catch (err) {
                  const errorMessage =
                    err instanceof Error
                      ? err.message
                      : 'Failed to delete habit';
                  setError(errorMessage);
                  reject(err);
                }
              },
            },
          ]
        );
      });
    } catch (err) {
      if (err instanceof Error && err.message !== 'User cancelled') {
        const errorMessage = err.message;
        setError(errorMessage);
      }
      throw err;
    }
  }, []);

  const toggleHabitCompletion = useCallback(
    async (habitId: string): Promise<void> => {
      try {
        setError(null);
        const result = await habitsService.toggleHabitCompletion(habitId);

        // Update the habit in local state with new completion status and streak
        setHabits(prev =>
          prev.map(habit =>
            habit.id === habitId
              ? {
                  ...result.habit,
                  completed_today: result.completed,
                }
              : habit
          )
        );
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : 'Failed to toggle habit completion';
        setError(errorMessage);
        console.error('Error toggling habit completion:', err);
      }
    },
    []
  );

  const toggleHabitCompletionForDate = useCallback(
    async (habitId: string, date: Date): Promise<void> => {
      try {
        setError(null);
        const dateString = date.toISOString().split('T')[0];
        const result = await habitsService.toggleHabitCompletionForDate(
          habitId,
          dateString
        );

        // Update the habit in local state with new completion status
        setHabits(prev =>
          prev.map(habit =>
            habit.id === habitId
              ? {
                  ...result.habit,
                  completed_today: result.completed,
                }
              : habit
          )
        );
      } catch (err) {
        const errorMessage =
          err instanceof Error
            ? err.message
            : 'Failed to toggle habit completion';
        setError(errorMessage);
        console.error('Error toggling habit completion for date:', err);
      }
    },
    []
  );

  const getHabitsByCategory = useCallback(
    (category: string): Habit[] => {
      if (category === 'all') {
        return habits;
      }
      return habits.filter(habit => habit.category === category);
    },
    [habits]
  );

  // Load habits when date changes
  useEffect(() => {
    const today = new Date();
    const isToday = currentDate.toDateString() === today.toDateString();

    if (isToday) {
      refreshHabits();
    } else {
      refreshHabitsForDate(currentDate);
    }
  }, [currentDate, refreshHabits, refreshHabitsForDate]);

  return {
    habits,
    loading,
    error,
    currentDate,
    setCurrentDate,
    refreshHabits,
    refreshHabitsForDate,
    createHabit,
    updateHabit,
    deleteHabit,
    toggleHabitCompletion,
    toggleHabitCompletionForDate,
    getHabitsByCategory,
  };
}

import { supabase } from '../../../lib/supabase';
import { achievementTriggerService } from '../../progress/services/achievementTriggerService';
import { progressIntegrationService } from '../../progress/services/progressIntegrationService';

export interface Habit {
  id: string;
  user_id: string;
  title: string;
  description: string;
  category: string; // Can be predefined categories or custom category names
  frequency: 'daily' | 'weekly' | 'custom';
  target_amount?: number;
  target_unit?: string;
  current_streak: number;
  best_streak: number;
  total_completions: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  completed_today?: boolean;
}

export interface HabitFormData {
  title: string;
  description: string;
  category: 'supplement' | 'activity' | 'hydration' | 'sleep' | 'custom';
  customCategory?: string;
  frequency: 'daily' | 'weekly' | 'custom';
  target_amount?: number;
  target_unit?: string;
}

export interface HabitCompletion {
  id: string;
  habit_id: string;
  user_id: string;
  completed_at: string;
  completion_date: string;
  notes?: string;
}

class HabitsService {
  /**
   * Get all habits for the current user with today's completion status
   */
  async getUserHabits(): Promise<Habit[]> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase.rpc('get_habits_with_today_status', {
      user_uuid: user.id,
    });

    if (error) {
      console.error('Error fetching habits:', error);
      throw new Error('Failed to fetch habits');
    }

    return data || [];
  }

  /**
   * Get all habits for the current user with completion status for a specific date
   */
  async getUserHabitsForDate(date: string): Promise<Habit[]> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Get all habits
    const { data: habitsData, error: habitsError } = await supabase
      .from('habits')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (habitsError) {
      console.error('Error fetching habits:', habitsError);
      throw new Error('Failed to fetch habits');
    }

    // Get completions for the specific date
    const { data: completionsData, error: completionsError } = await supabase
      .from('habit_completions')
      .select('habit_id')
      .eq('user_id', user.id)
      .eq('completion_date', date);

    if (completionsError) {
      console.error('Error fetching completions:', completionsError);
      throw new Error('Failed to fetch habit completions');
    }

    // Create a set of completed habit IDs for quick lookup
    const completedHabitIds = new Set(
      completionsData?.map(c => c.habit_id) || []
    );

    // Map habits with completion status for the specific date
    const habitsWithStatus: Habit[] = (habitsData || []).map(habit => ({
      ...habit,
      completed_today: completedHabitIds.has(habit.id),
    }));

    return habitsWithStatus;
  }

  /**
   * Create a new habit
   */
  async createHabit(habitData: HabitFormData): Promise<Habit> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Use custom category name if category is "custom", otherwise use the category
    const categoryToSave =
      habitData.category === 'custom' && habitData.customCategory
        ? habitData.customCategory
        : habitData.category;

    const { data, error } = await supabase
      .from('habits')
      .insert({
        user_id: user.id,
        title: habitData.title,
        description: habitData.description,
        category: categoryToSave,
        frequency: habitData.frequency,
        target_amount: habitData.target_amount,
        target_unit: habitData.target_unit,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating habit:', error);
      throw new Error('Failed to create habit');
    }

    // Trigger achievement check for habit creation
    try {
      await achievementTriggerService.onHabitCreated(data.id);
    } catch (achievementError) {
      console.error(
        'Error checking achievements after habit creation:',
        achievementError
      );
      // Don't throw error here as habit creation was successful
    }

    return data;
  }

  /**
   * Update an existing habit
   */
  async updateHabit(
    habitId: string,
    updates: Partial<HabitFormData>
  ): Promise<Habit> {
    const { data, error } = await supabase
      .from('habits')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', habitId)
      .select()
      .single();

    if (error) {
      console.error('Error updating habit:', error);
      throw new Error('Failed to update habit');
    }

    return data;
  }

  /**
   * Delete a habit (soft delete by setting is_active to false)
   */
  async deleteHabit(habitId: string): Promise<void> {
    const { error } = await supabase
      .from('habits')
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq('id', habitId);

    if (error) {
      console.error('Error deleting habit:', error);
      throw new Error('Failed to delete habit');
    }
  }

  /**
   * Permanently delete a habit and all its completions
   */
  async permanentlyDeleteHabit(habitId: string): Promise<void> {
    const { error } = await supabase.from('habits').delete().eq('id', habitId);

    if (error) {
      console.error('Error permanently deleting habit:', error);
      throw new Error('Failed to permanently delete habit');
    }
  }

  /**
   * Toggle habit completion for today
   */
  async toggleHabitCompletion(
    habitId: string
  ): Promise<{ completed: boolean; habit: Habit }> {
    const today = new Date().toISOString().split('T')[0];
    return this.toggleHabitCompletionForDate(habitId, today);
  }

  /**
   * Toggle habit completion for a specific date
   */
  async toggleHabitCompletionForDate(
    habitId: string,
    date: string
  ): Promise<{ completed: boolean; habit: Habit }> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Check if already completed on this date
    const { data: existingCompletion } = await supabase
      .from('habit_completions')
      .select('id')
      .eq('habit_id', habitId)
      .eq('completion_date', date)
      .single();

    if (existingCompletion) {
      // Remove completion
      const { error } = await supabase
        .from('habit_completions')
        .delete()
        .eq('id', existingCompletion.id);

      if (error) {
        console.error('Error removing habit completion:', error);
        throw new Error('Failed to remove habit completion');
      }

      // Get updated habit data
      const updatedHabit = await this.getHabitById(habitId);
      return { completed: false, habit: updatedHabit };
    } else {
      // Add completion
      const { error } = await supabase.from('habit_completions').insert({
        habit_id: habitId,
        user_id: user.id,
        completion_date: date,
      });

      if (error) {
        console.error('Error adding habit completion:', error);
        throw new Error('Failed to add habit completion');
      }

      // Get updated habit data
      const updatedHabit = await this.getHabitById(habitId);

      // Trigger integrated progress update
      try {
        await progressIntegrationService.onHabitCompleted(
          habitId,
          user.id,
          new Date(date)
        );
      } catch (progressError) {
        console.error(
          'Error updating integrated progress after habit completion:',
          progressError
        );
        // Don't throw error here as habit completion was successful
      }

      return { completed: true, habit: updatedHabit };
    }
  }

  /**
   * Get a single habit by ID with today's completion status
   */
  async getHabitById(habitId: string): Promise<Habit> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase.rpc('get_habits_with_today_status', {
      user_uuid: user.id,
    });

    if (error) {
      console.error('Error fetching habit:', error);
      throw new Error('Failed to fetch habit');
    }

    const habit = data?.find((h: Habit) => h.id === habitId);
    if (!habit) {
      throw new Error('Habit not found');
    }

    return habit;
  }

  /**
   * Get habit completions for a specific date range
   */
  async getHabitCompletions(
    habitId: string,
    startDate: string,
    endDate: string
  ): Promise<HabitCompletion[]> {
    const { data, error } = await supabase
      .from('habit_completions')
      .select('*')
      .eq('habit_id', habitId)
      .gte('completion_date', startDate)
      .lte('completion_date', endDate)
      .order('completion_date', { ascending: false });

    if (error) {
      console.error('Error fetching habit completions:', error);
      throw new Error('Failed to fetch habit completions');
    }

    return data || [];
  }

  /**
   * Get habits by category
   */
  async getHabitsByCategory(category: string): Promise<Habit[]> {
    const habits = await this.getUserHabits();
    if (category === 'all') {
      return habits;
    }
    return habits.filter(habit => habit.category === category);
  }
}

export const habitsService = new HabitsService();

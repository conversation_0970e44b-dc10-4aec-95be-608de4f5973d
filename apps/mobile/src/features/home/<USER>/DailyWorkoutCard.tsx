import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import {
  Text,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { supabase } from '../../../lib/supabase';
import { AppTabScreenProps } from '../../../shared/types/navigation';

export default function DailyWorkoutCard() {
  const [workoutName, setWorkoutName] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [workoutPlanId, setWorkoutPlanId] = useState<string | null>(null);
  const navigation =
    useNavigation<AppTabScreenProps<'Exercise'>['navigation']>();

  useEffect(() => {
    const fetchTodaysWorkout = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        if (!session) {
          throw new Error('Not authenticated');
        }

        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

        // 1. Find today's workout plan
        const { data: planData, error: planError } = await supabase
          .from('workout_plans')
          .select('id')
          .eq('user_id', session.user.id)
          .eq('date', today)
          .single();

        if (planError) {
          throw planError;
        }

        // Store the workout plan ID for navigation
        setWorkoutPlanId(planData.id);

        if (!planData) {
          setWorkoutName('Rest Day');
          return;
        }

        // 2. Fetch the first exercise of the plan to use as the name
        // TODO: Add a 'name' field to workout_plans table for a proper workout name.
        const { data: exerciseData, error: exerciseError } = await supabase
          .from('workout_plan_exercises')
          .select('exercises ( name )')
          .eq('workout_plan_id', planData.id)
          .order('order', { ascending: true })
          .limit(1)
          .single();

        if (exerciseError) {
          throw exerciseError;
        }

        if (exerciseData && (exerciseData.exercises as any)?.name) {
          setWorkoutName((exerciseData.exercises as any).name);
        } else {
          setWorkoutName('General Workout'); // Fallback name
        }
      } catch (e: any) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    };

    fetchTodaysWorkout();
  }, []);

  const handlePress = () => {
    if (workoutPlanId) {
      // @ts-ignore - Navigate to nested screen in Exercise tab
      navigation.navigate('Exercise', {
        screen: 'WorkoutDetail',
        params: { workoutId: workoutPlanId },
      });
    }
  };

  return (
    <TouchableOpacity style={styles.card} onPress={handlePress}>
      <Text style={styles.title}>Today's Workout</Text>
      {loading ? (
        <ActivityIndicator size="small" color="#007AFF" />
      ) : error ? (
        <Text style={styles.errorText}>{error}</Text>
      ) : workoutName ? (
        <Text style={styles.description}>{workoutName}</Text>
      ) : (
        <Text style={styles.placeholderText}>No workout planned for today</Text>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#333',
    padding: 16,
    borderRadius: 12,
    marginVertical: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: '#fff',
  },
  errorText: {
    fontSize: 16,
    color: '#ff6b6b',
  },
  placeholderText: {
    fontSize: 16,
    color: '#999',
  },
});

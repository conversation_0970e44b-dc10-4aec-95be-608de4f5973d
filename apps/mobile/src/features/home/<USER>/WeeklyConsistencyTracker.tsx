import {
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  format,
  isSameDay,
} from 'date-fns';
import React, { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { supabase } from '../../../lib/supabase';

interface WorkoutPlan {
  date: string;
}

export default function WeeklyConsistencyTracker() {
  const [completedDays, setCompletedDays] = useState<Date[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const today = new Date();
  const weekStart = startOfWeek(today, { weekStartsOn: 1 }); // Monday
  const weekEnd = endOfWeek(today, { weekStartsOn: 1 });
  const weekDays = eachDayOfInterval({ start: weekStart, end: weekEnd });

  useEffect(() => {
    const fetchWorkoutData = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        if (!session) {
          throw new Error('Not authenticated');
        }

        const { data, error: fetchError } = await supabase
          .from('workout_plans')
          .select('date')
          .eq('user_id', session.user.id)
          .gte('date', format(weekStart, 'yyyy-MM-dd'))
          .lte('date', format(weekEnd, 'yyyy-MM-dd'));

        if (fetchError) {
          throw fetchError;
        }

        if (data) {
          const dates = data.map((plan: WorkoutPlan) => new Date(plan.date));
          setCompletedDays(dates);
        }
      } catch (e: any) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    };

    fetchWorkoutData();
  }, []);

  return (
    <View style={styles.card}>
      <Text style={styles.title}>Weekly Consistency</Text>
      {loading ? (
        <ActivityIndicator size="small" color="#007AFF" />
      ) : error ? (
        <Text style={styles.errorText}>{error}</Text>
      ) : (
        <View style={styles.daysContainer}>
          {weekDays.map(day => {
            const isCompleted = completedDays.some(completedDay =>
              isSameDay(completedDay, day)
            );
            return (
              <View key={day.toString()} style={styles.dayColumn}>
                <Text style={styles.dayLabel}>{format(day, 'EEE')}</Text>
                <View
                  style={[
                    styles.circle,
                    isCompleted
                      ? styles.circleCompleted
                      : styles.circleIncomplete,
                  ]}
                />
              </View>
            );
          })}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#333',
    padding: 16,
    borderRadius: 12,
    marginVertical: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 16,
  },
  daysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dayColumn: {
    alignItems: 'center',
    gap: 8,
  },
  dayLabel: {
    fontSize: 12,
    color: '#999',
  },
  circle: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  circleCompleted: {
    backgroundColor: '#4CAF50',
  },
  circleIncomplete: {
    backgroundColor: '#666',
  },
  errorText: {
    fontSize: 16,
    color: '#ff6b6b',
  },
});

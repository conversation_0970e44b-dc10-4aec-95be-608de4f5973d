import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import {
  Text,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { supabase } from '../../../lib/supabase';
import { AppTabScreenProps } from '../../../shared/types/navigation';

export default function Greeting() {
  const [fullName, setFullName] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigation = useNavigation<AppTabScreenProps<'Habits'>['navigation']>();

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        if (!session) {
          throw new Error('Not authenticated');
        }

        const { data, error: profileError } = await supabase
          .from('profiles')
          .select('full_name')
          .eq('id', session.user.id)
          .single();

        if (profileError) {
          throw profileError;
        }

        if (data) {
          setFullName(data.full_name);
        }
      } catch (e: any) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  if (loading) {
    return <ActivityIndicator size="small" color="#007AFF" />;
  }

  if (error) {
    return <Text style={styles.errorText}>Error: {error}</Text>;
  }

  const getGreeting = () => {
    const hours = new Date().getHours();
    if (hours < 12) {
      return 'Good morning';
    }
    if (hours < 18) {
      return 'Good afternoon';
    }
    return 'Good evening';
  };

  // Display the first name if available
  const displayName = fullName ? fullName.split(' ')[0] : 'User';

  const handlePress = () => {
    navigation.navigate('Habits');
  };

  return (
    <TouchableOpacity onPress={handlePress}>
      <Text style={styles.greeting}>{`${getGreeting()}, ${displayName}!`}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  errorText: {
    color: '#ff0000',
    fontSize: 16,
  },
});

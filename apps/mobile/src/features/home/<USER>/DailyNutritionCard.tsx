import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import { supabase } from '../../../lib/supabase';
import { AppTabScreenProps } from '../../../shared/types/navigation';

interface Meal {
  id: string;
  meal_type: string;
  recipes: {
    name: string;
  } | null; // Supabase returns an object for a to-one relationship
}

export default function DailyNutritionCard() {
  const [meals, setMeals] = useState<Meal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigation =
    useNavigation<AppTabScreenProps<'Nutrition'>['navigation']>();

  useEffect(() => {
    const fetchTodaysMeals = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        if (!session) {
          throw new Error('Not authenticated');
        }

        const today = new Date().toISOString().split('T')[0];

        const { data: planData, error: planError } = await supabase
          .from('meal_plans')
          .select('id')
          .eq('user_id', session.user.id)
          .eq('date', today)
          .single();

        if (planError && planError.code !== 'PGRST116') {
          throw planError;
        }

        if (!planData) {
          return; // No meals planned for today
        }

        const { data: mealData, error: mealError } = await supabase
          .from('meal_plan_recipes')
          .select('id, meal_type, recipes ( name )')
          .eq('meal_plan_id', planData.id);

        if (mealError) {
          throw mealError;
        }

        if (mealData) {
          // The type from Supabase can be broad, so we cast to 'any' first.
          setMeals(mealData as any as Meal[]);
        }
      } catch (e: any) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    };

    fetchTodaysMeals();
  }, []);

  const handlePress = () => {
    navigation.navigate('Nutrition');
  };

  return (
    <TouchableOpacity style={styles.card} onPress={handlePress}>
      <Text style={styles.title}>Today's Nutrition</Text>
      {loading ? (
        <ActivityIndicator size="small" color="#007AFF" />
      ) : error ? (
        <Text style={styles.errorText}>{error}</Text>
      ) : meals.length > 0 ? (
        <View style={styles.mealsContainer}>
          {meals.map(meal => (
            <Text key={meal.id} style={styles.mealText}>
              {meal.meal_type}: {meal.recipes?.name ?? 'N/A'}
            </Text>
          ))}
        </View>
      ) : (
        <Text style={styles.placeholderText}>No meals planned for today.</Text>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#333',
    padding: 16,
    borderRadius: 12,
    marginVertical: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  mealsContainer: {
    gap: 8,
  },
  mealText: {
    fontSize: 16,
    color: '#fff',
  },
  errorText: {
    fontSize: 16,
    color: '#ff6b6b',
  },
  placeholderText: {
    fontSize: 16,
    color: '#999',
  },
});

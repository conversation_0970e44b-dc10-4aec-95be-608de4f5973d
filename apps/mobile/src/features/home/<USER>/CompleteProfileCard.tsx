import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { AppTabScreenProps } from '../../../shared/types/navigation';

interface CompleteProfileCardProps {
  onCompleteProfile: () => void;
}

export default function CompleteProfileCard({
  onCompleteProfile,
}: CompleteProfileCardProps) {
  const navigation =
    useNavigation<AppTabScreenProps<'AICoach'>['navigation']>();

  const handleCompleteProfile = () => {
    // Navigate to AI Coach to complete profile through questionnaire
    navigation.navigate('AICoach');
    onCompleteProfile();
  };

  return (
    <View style={styles.card}>
      <View style={styles.header}>
        <MaterialCommunityIcons name="account-plus" size={32} color="#007AFF" />
        <View style={styles.headerText}>
          <Text style={styles.title}>Complete Your Profile</Text>
          <Text style={styles.subtitle}>
            Get personalized workouts and meal plans
          </Text>
        </View>
      </View>

      <Text style={styles.description}>
        Tell us about your fitness goals, dietary preferences, and experience
        level to unlock personalized recommendations tailored just for you.
      </Text>

      <View style={styles.benefits}>
        <View style={styles.benefit}>
          <MaterialCommunityIcons name="target" size={20} color="#4CAF50" />
          <Text style={styles.benefitText}>Personalized workout plans</Text>
        </View>
        <View style={styles.benefit}>
          <MaterialCommunityIcons name="food" size={20} color="#4CAF50" />
          <Text style={styles.benefitText}>Custom meal recommendations</Text>
        </View>
        <View style={styles.benefit}>
          <MaterialCommunityIcons name="chart-line" size={20} color="#4CAF50" />
          <Text style={styles.benefitText}>Progress tracking & insights</Text>
        </View>
      </View>

      <TouchableOpacity style={styles.button} onPress={handleCompleteProfile}>
        <Text style={styles.buttonText}>Complete Profile</Text>
        <MaterialCommunityIcons name="arrow-right" size={20} color="#fff" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#1a1a1a',
    borderRadius: 16,
    padding: 20,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: '#007AFF20',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    marginLeft: 12,
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#999',
  },
  description: {
    fontSize: 16,
    color: '#ccc',
    lineHeight: 22,
    marginBottom: 20,
  },
  benefits: {
    marginBottom: 24,
  },
  benefit: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  benefitText: {
    fontSize: 14,
    color: '#ccc',
    marginLeft: 8,
  },
  button: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

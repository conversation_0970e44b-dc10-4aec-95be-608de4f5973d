import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { ScrollView, View, StyleSheet, Text } from 'react-native';
import { ScreenWithTopBar } from '../../../shared/components';
import { TodaysProgressWidget } from '../../progress/components/TodaysProgressWidget';
import Ai<PERSON>oachCorner from '../components/AiCoachCorner';
import CompleteProfileCard from '../components/CompleteProfileCard';
import DailyNutritionCard from '../components/DailyNutritionCard';
import DailyWorkoutCard from '../components/DailyWorkoutCard';
import Greeting from '../components/Greeting';
import WeeklyConsistencyTracker from '../components/WeeklyConsistencyTracker';
import { useProfileCompletion } from '../hooks/useProfileCompletion';

export default function HomeScreen() {
  const navigation = useNavigation();
  const isDevelopment = __DEV__ || process.env.EXPO_PUBLIC_DEV_MODE === 'true';
  const { isComplete: isProfileComplete, loading: profileLoading } =
    useProfileCompletion();

  const handleCompleteProfile = () => {
    // This will be called when user taps the complete profile button
    // The CompleteProfileCard already handles navigation to AI Coach
  };

  const handleProgressPress = () => {
    // @ts-ignore navigate to Progress tab (root-level)
    (navigation as any).navigate('Progress');
  };

  return (
    <ScreenWithTopBar title="Home">
      {isDevelopment && (
        <View style={styles.developmentBanner}>
          <Text style={styles.developmentText}>
            DEVELOPMENT MODE - AUTH DISABLED
          </Text>
        </View>
      )}
      <ScrollView style={styles.scrollView}>
        <View style={styles.container}>
          <Greeting />

          {/* Show Complete Profile card if profile is not complete */}
          {!profileLoading && !isProfileComplete && (
            <CompleteProfileCard onCompleteProfile={handleCompleteProfile} />
          )}

          {/* Today's Progress Widget */}
          <TodaysProgressWidget onPress={handleProgressPress} />

          <DailyWorkoutCard />
          <DailyNutritionCard />
          <WeeklyConsistencyTracker />
          <AiCoachCorner />
        </View>
      </ScrollView>
    </ScreenWithTopBar>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  container: {
    flex: 1,
    padding: 16,
    gap: 16,
  },
  developmentBanner: {
    backgroundColor: '#ffeb3b',
    padding: 8,
    alignItems: 'center',
  },
  developmentText: {
    color: '#000',
    fontWeight: 'bold',
  },
});

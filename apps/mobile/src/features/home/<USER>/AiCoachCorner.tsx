import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const AiCoachCorner = React.memo(() => {
  // TODO: Fetch dynamic daily tips from the AI coach
  const dailyTip = '"The only bad workout is the one that didn\'t happen."';

  return (
    <View style={styles.card}>
      <Text style={styles.title}>AI Coach's Corner</Text>
      <Text style={styles.tip}>{dailyTip}</Text>
    </View>
  );
});

AiCoachCorner.displayName = 'AiCoachCorner';

export default AiCoachCorner;

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#333',
    padding: 16,
    borderRadius: 12,
    marginVertical: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  tip: {
    fontSize: 16,
    color: '#fff',
    fontStyle: 'italic',
  },
});

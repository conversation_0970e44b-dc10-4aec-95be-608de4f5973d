import { useState, useEffect } from 'react';
import { supabase } from '../../../lib/supabase';

interface ProfileCompletionStatus {
  isComplete: boolean;
  loading: boolean;
  error: string | null;
  missingFields: string[];
}

export function useProfileCompletion(): ProfileCompletionStatus {
  const [status, setStatus] = useState<ProfileCompletionStatus>({
    isComplete: false,
    loading: true,
    error: null,
    missingFields: [],
  });

  useEffect(() => {
    const checkProfileCompletion = async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (!session?.user) {
          setStatus({
            isComplete: false,
            loading: false,
            error: 'Not authenticated',
            missingFields: [],
          });
          return;
        }

        // Fetch profile data
        const { data: profile, error } = await supabase
          .from('profiles')
          .select(
            `
            full_name,
            date_of_birth,
            height_cm,
            fitness_goals,
            activity_level,
            dietary_preferences,
            onboarding_complete
          `
          )
          .eq('id', session.user.id)
          .single();

        if (error) {
          setStatus({
            isComplete: false,
            loading: false,
            error: error.message,
            missingFields: [],
          });
          return;
        }

        // Check which fields are missing for a complete profile
        const missingFields: string[] = [];

        if (!profile.full_name) {
          missingFields.push('full_name');
        }
        if (!profile.date_of_birth) {
          missingFields.push('date_of_birth');
        }
        if (!profile.height_cm) {
          missingFields.push('height_cm');
        }
        if (!profile.fitness_goals || profile.fitness_goals.length === 0) {
          missingFields.push('fitness_goals');
        }
        if (!profile.activity_level) {
          missingFields.push('activity_level');
        }
        if (
          !profile.dietary_preferences ||
          profile.dietary_preferences.length === 0
        ) {
          missingFields.push('dietary_preferences');
        }

        // Profile is complete if onboarding is complete AND all questionnaire fields are filled
        const isComplete =
          profile.onboarding_complete && missingFields.length === 0;

        setStatus({
          isComplete,
          loading: false,
          error: null,
          missingFields,
        });
      } catch (error: any) {
        setStatus({
          isComplete: false,
          loading: false,
          error: error.message,
          missingFields: [],
        });
      }
    };

    checkProfileCompletion();
  }, []);

  return status;
}

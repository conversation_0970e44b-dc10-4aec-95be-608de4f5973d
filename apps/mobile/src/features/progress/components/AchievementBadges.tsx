import React from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Card, Text, Chip, ProgressBar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { AchievementProgress } from '../services/achievementService';

interface AchievementBadgeProps {
  achievement: AchievementProgress;
  onPress?: () => void;
}

const AchievementBadge: React.FC<AchievementBadgeProps> = ({
  achievement,
  onPress,
}) => {
  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return '#4CAF50';
      case 'rare':
        return '#2196F3';
      case 'epic':
        return '#9C27B0';
      case 'legendary':
        return '#FF9800';
      default:
        return '#666';
    }
  };

  return (
    <TouchableOpacity onPress={onPress} style={styles.badgeContainer}>
      <Card
        style={[
          styles.badgeCard,
          achievement.is_unlocked ? styles.unlockedCard : styles.lockedCard,
        ]}
      >
        <Card.Content style={styles.badgeContent}>
          <View
            style={[
              styles.iconContainer,
              achievement.is_unlocked
                ? {
                    backgroundColor: `${getRarityColor(achievement.achievement.rarity)}20`,
                  }
                : styles.iconContainerUnlocked,
            ]}
          >
            <Text
              style={[
                styles.iconEmoji,
                achievement.is_unlocked
                  ? styles.iconEmojiUnlocked
                  : styles.iconEmojiLocked,
              ]}
            >
              {achievement.achievement.icon}
            </Text>
          </View>

          <Text
            variant="bodyMedium"
            style={[
              styles.badgeName,
              achievement.is_unlocked
                ? styles.titleUnlocked
                : styles.titleLocked,
            ]}
            numberOfLines={2}
          >
            {achievement.achievement.name}
          </Text>

          <Chip
            mode="outlined"
            compact
            style={[
              styles.rarityChip,
              { borderColor: getRarityColor(achievement.achievement.rarity) },
            ]}
            textStyle={[
              styles.rarityText,
              { color: getRarityColor(achievement.achievement.rarity) },
            ]}
          >
            {achievement.achievement.rarity}
          </Chip>

          {!achievement.is_unlocked && (
            <View style={styles.progressContainer}>
              <ProgressBar
                progress={achievement.percentage / 100}
                color={getRarityColor(achievement.achievement.rarity)}
                style={styles.progressBar}
              />
              <Text variant="bodySmall" style={styles.progressText}>
                {achievement.current_progress}/{achievement.target_progress}
              </Text>
            </View>
          )}

          {achievement.is_unlocked && (
            <View style={styles.unlockedContainer}>
              <Icon name="check-circle" size={16} color="#4CAF50" />
              <Text variant="bodySmall" style={styles.pointsText}>
                +{achievement.achievement.points} pts
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );
};

interface AchievementBadgesProps {
  achievements: AchievementProgress[];
  title?: string;
  maxVisible?: number;
  onAchievementPress?: (achievement: AchievementProgress) => void;
  onViewAllPress?: () => void;
}

export const AchievementBadges: React.FC<AchievementBadgesProps> = ({
  achievements,
  title = 'Achievements',
  maxVisible = 6,
  onAchievementPress,
  onViewAllPress,
}) => {
  const unlockedAchievements = achievements.filter(a => a.is_unlocked);
  const inProgressAchievements = achievements
    .filter(a => !a.is_unlocked && a.percentage > 0)
    .sort((a, b) => b.percentage - a.percentage);

  const displayAchievements = [
    ...unlockedAchievements.slice(0, Math.floor(maxVisible / 2)),
    ...inProgressAchievements.slice(0, Math.ceil(maxVisible / 2)),
  ].slice(0, maxVisible);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text variant="titleLarge" style={styles.sectionTitle}>
          {title}
        </Text>
        <View style={styles.headerStats}>
          <Text variant="bodyMedium" style={styles.statsText}>
            {unlockedAchievements.length}/{achievements.length}
          </Text>
          {onViewAllPress && (
            <TouchableOpacity onPress={onViewAllPress}>
              <Text variant="bodyMedium" style={styles.viewAllText}>
                View All
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {displayAchievements.length > 0 ? (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {displayAchievements.map((achievement, index) => (
            <AchievementBadge
              key={`${achievement.achievement.id}-${index}`}
              achievement={achievement}
              onPress={() => onAchievementPress?.(achievement)}
            />
          ))}
        </ScrollView>
      ) : (
        <View style={styles.emptyState}>
          <Icon name="emoji-events" size={48} color="#ccc" />
          <Text variant="bodyLarge" style={styles.emptyText}>
            No achievements yet
          </Text>
          <Text variant="bodyMedium" style={styles.emptySubtext}>
            Complete habits to unlock your first achievement!
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    color: '#333',
  },
  headerStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statsText: {
    color: '#666',
    fontWeight: '500',
  },
  viewAllText: {
    color: '#2196F3',
    fontWeight: '500',
  },
  scrollContent: {
    paddingRight: 16,
  },
  badgeContainer: {
    marginRight: 12,
  },
  badgeCard: {
    width: 120,
    backgroundColor: '#fff',
    elevation: 2,
  },
  unlockedCard: {
    borderWidth: 2,
    borderColor: '#4CAF50',
  },
  lockedCard: {
    opacity: 0.8,
  },
  badgeContent: {
    padding: 12,
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconEmoji: {
    fontSize: 24,
  },
  badgeName: {
    textAlign: 'center',
    fontWeight: '500',
    marginBottom: 8,
    minHeight: 32,
  },
  rarityChip: {
    marginBottom: 8,
  },
  rarityText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 4,
    borderRadius: 2,
    marginBottom: 4,
  },
  progressText: {
    color: '#666',
    fontSize: 10,
  },
  unlockedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  pointsText: {
    color: '#4CAF50',
    fontWeight: '500',
    fontSize: 10,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    color: '#999',
    textAlign: 'center',
  },
  iconContainerUnlocked: {
    backgroundColor: '#f5f5f5',
  },
  iconEmojiUnlocked: {
    opacity: 1,
  },
  iconEmojiLocked: {
    opacity: 0.5,
  },
  titleUnlocked: {
    color: '#333',
  },
  titleLocked: {
    color: '#999',
  },
});

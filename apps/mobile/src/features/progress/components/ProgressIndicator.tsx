import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, ProgressBar, Chip } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface ProgressIndicatorProps {
  type: 'habits' | 'nutrition' | 'exercise' | 'overall';
  completed: number;
  total: number;
  streak?: number;
  points?: number;
  onPress?: () => void;
  compact?: boolean;
  showDetails?: boolean;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  type,
  completed,
  total,
  streak = 0,
  points = 0,
  onPress,
  compact = false,
  showDetails = true,
}) => {
  const completionRate = total > 0 ? (completed / total) * 100 : 0;

  const getTypeConfig = () => {
    switch (type) {
      case 'habits':
        return {
          icon: 'check-circle',
          color: '#4CAF50',
          label: 'Habits',
          unit: 'habits',
        };
      case 'nutrition':
        return {
          icon: 'restaurant',
          color: '#FF9800',
          label: 'Nutrition',
          unit: 'meals',
        };
      case 'exercise':
        return {
          icon: 'fitness-center',
          color: '#2196F3',
          label: 'Exercise',
          unit: 'workouts',
        };
      case 'overall':
        return {
          icon: 'trending-up',
          color: '#9C27B0',
          label: 'Overall',
          unit: 'goals',
        };
      default:
        return {
          icon: 'star',
          color: '#757575',
          label: 'Progress',
          unit: 'items',
        };
    }
  };

  const config = getTypeConfig();

  const getProgressColor = (): string => {
    if (completionRate >= 80) {
      return '#4CAF50';
    }
    if (completionRate >= 60) {
      return '#FF9800';
    }
    if (completionRate >= 40) {
      return '#2196F3';
    }
    return '#F44336';
  };

  const getMotivationalMessage = (): string => {
    if (completionRate === 100) {
      return 'Perfect! 🌟';
    } else if (completionRate >= 80) {
      return 'Excellent! 💪';
    } else if (completionRate >= 60) {
      return 'Good progress! 🚀';
    } else if (completionRate >= 40) {
      return 'Keep going! 💫';
    } else if (completed > 0) {
      return 'Great start! ✨';
    } else {
      return "Let's begin! 🌱";
    }
  };

  const Container = onPress ? TouchableOpacity : View;

  return (
    <Container
      onPress={onPress}
      activeOpacity={0.7}
      style={[
        styles.container,
        compact && styles.compactContainer,
        onPress && styles.pressableContainer,
      ]}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <View
            style={[
              styles.iconContainer,
              { backgroundColor: `${config.color}20` },
            ]}
          >
            <Icon
              name={config.icon}
              size={compact ? 16 : 20}
              color={config.color}
            />
          </View>
          <Text
            variant={compact ? 'bodyMedium' : 'titleSmall'}
            style={styles.title}
          >
            {config.label}
          </Text>
        </View>

        {onPress && <Icon name="chevron-right" size={20} color="#666" />}
      </View>

      {/* Progress Section */}
      <View style={styles.progressSection}>
        <View style={styles.progressHeader}>
          <Text
            variant={compact ? 'titleMedium' : 'titleLarge'}
            style={[styles.percentage, { color: getProgressColor() }]}
          >
            {Math.round(completionRate)}%
          </Text>
          {!compact && (
            <Text variant="bodySmall" style={styles.motivationText}>
              {getMotivationalMessage()}
            </Text>
          )}
        </View>

        <ProgressBar
          progress={completionRate / 100}
          color={getProgressColor()}
          style={[styles.progressBar, compact && styles.compactProgressBar]}
        />

        <Text variant="bodySmall" style={styles.progressText}>
          {completed} of {total} {config.unit} completed
        </Text>
      </View>

      {/* Details Section */}
      {showDetails && !compact && (
        <View style={styles.detailsSection}>
          {streak > 0 && (
            <Chip
              icon="local-fire-department"
              compact
              style={[styles.detailChip, styles.streakChip]}
              textStyle={styles.streakText}
            >
              {streak} day streak
            </Chip>
          )}

          {points > 0 && (
            <Chip
              icon="star"
              compact
              style={[styles.detailChip, styles.pointsChip]}
              textStyle={styles.pointsText}
            >
              +{points} points
            </Chip>
          )}
        </View>
      )}

      {/* Compact Details */}
      {showDetails && compact && (streak > 0 || points > 0) && (
        <View style={styles.compactDetails}>
          {streak > 0 && (
            <View style={styles.compactStat}>
              <Icon name="local-fire-department" size={14} color="#FF5722" />
              <Text variant="bodySmall" style={styles.compactStatText}>
                {streak}
              </Text>
            </View>
          )}

          {points > 0 && (
            <View style={styles.compactStat}>
              <Icon name="star" size={14} color="#FF9800" />
              <Text variant="bodySmall" style={styles.compactStatText}>
                +{points}
              </Text>
            </View>
          )}
        </View>
      )}
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginVertical: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  compactContainer: {
    padding: 12,
    marginVertical: 2,
  },
  pressableContainer: {
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontWeight: '600',
    color: '#333',
  },
  progressSection: {
    marginBottom: 8,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  percentage: {
    fontWeight: '700',
  },
  motivationText: {
    color: '#666',
    fontStyle: 'italic',
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    backgroundColor: '#f0f0f0',
    marginBottom: 6,
  },
  compactProgressBar: {
    height: 4,
  },
  progressText: {
    color: '#666',
    textAlign: 'center',
  },
  detailsSection: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 4,
  },
  detailChip: {
    backgroundColor: '#f8f9fa',
  },
  streakChip: {
    borderColor: '#FF5722',
    borderWidth: 1,
  },
  streakText: {
    color: '#FF5722',
    fontWeight: '600',
  },
  pointsChip: {
    borderColor: '#FF9800',
    borderWidth: 1,
  },
  pointsText: {
    color: '#FF9800',
    fontWeight: '600',
  },
  compactDetails: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 4,
  },
  compactStat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  compactStatText: {
    color: '#666',
    fontWeight: '500',
  },
});

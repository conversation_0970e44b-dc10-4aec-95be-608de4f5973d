import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Text } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color,
  trend,
}) => (
  <Card style={styles.statCard}>
    <Card.Content style={styles.statContent}>
      <View style={styles.statHeader}>
        <View style={[styles.iconContainer, { backgroundColor: `${color}20` }]}>
          <Icon name={icon} size={24} color={color} />
        </View>
        {trend && (
          <View style={styles.trendContainer}>
            <Icon
              name={trend.isPositive ? 'trending-up' : 'trending-down'}
              size={16}
              color={trend.isPositive ? '#4CAF50' : '#F44336'}
            />
            <Text
              variant="bodySmall"
              style={[
                styles.trendText,
                trend.isPositive
                  ? styles.trendTextPositive
                  : styles.trendTextNegative,
              ]}
            >
              {Math.abs(trend.value)}%
            </Text>
          </View>
        )}
      </View>

      <Text variant="headlineMedium" style={styles.statValue}>
        {value}
      </Text>

      <Text variant="bodyMedium" style={styles.statTitle}>
        {title}
      </Text>

      {subtitle && (
        <Text variant="bodySmall" style={styles.statSubtitle}>
          {subtitle}
        </Text>
      )}
    </Card.Content>
  </Card>
);

interface ProgressStatsProps {
  stats: {
    currentStreak: number;
    longestStreak: number;
    completionRate: number;
    totalPoints: number;
    activeHabits: number;
    weeklyGoal: number;
  };
  trends?: {
    streakTrend: number;
    completionTrend: number;
    pointsTrend: number;
  };
}

export const ProgressStats: React.FC<ProgressStatsProps> = ({
  stats,
  trends,
}) => {
  const statCards = [
    {
      title: 'Current Streak',
      value: `${stats.currentStreak}`,
      subtitle: 'days in a row',
      icon: 'local-fire-department',
      color: '#FF5722',
      trend: trends
        ? {
            value: trends.streakTrend,
            isPositive: trends.streakTrend >= 0,
          }
        : undefined,
    },
    {
      title: 'Completion Rate',
      value: `${stats.completionRate}%`,
      subtitle: 'this week',
      icon: 'check-circle',
      color: '#4CAF50',
      trend: trends
        ? {
            value: trends.completionTrend,
            isPositive: trends.completionTrend >= 0,
          }
        : undefined,
    },
    {
      title: 'Total Points',
      value: stats.totalPoints.toLocaleString(),
      subtitle: 'earned',
      icon: 'stars',
      color: '#FF9800',
      trend: trends
        ? {
            value: trends.pointsTrend,
            isPositive: trends.pointsTrend >= 0,
          }
        : undefined,
    },
    {
      title: 'Longest Streak',
      value: `${stats.longestStreak}`,
      subtitle: 'personal best',
      icon: 'emoji-events',
      color: '#9C27B0',
    },
    {
      title: 'Active Habits',
      value: `${stats.activeHabits}`,
      subtitle: 'tracking',
      icon: 'list-alt',
      color: '#2196F3',
    },
    {
      title: 'Weekly Goal',
      value: `${stats.weeklyGoal}%`,
      subtitle: 'target',
      icon: 'flag',
      color: '#607D8B',
    },
  ];

  return (
    <View style={styles.container}>
      <Text variant="titleLarge" style={styles.sectionTitle}>
        Your Progress
      </Text>

      <View style={styles.statsGrid}>
        {statCards.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    marginBottom: 12,
    backgroundColor: '#fff',
    elevation: 2,
  },
  statContent: {
    padding: 12,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 2,
  },
  statValue: {
    fontWeight: '700',
    color: '#333',
    marginBottom: 4,
  },
  statTitle: {
    color: '#666',
    fontWeight: '500',
  },
  statSubtitle: {
    color: '#999',
    marginTop: 2,
  },
  trendTextPositive: {
    color: '#4CAF50',
  },
  trendTextNegative: {
    color: '#F44336',
  },
});

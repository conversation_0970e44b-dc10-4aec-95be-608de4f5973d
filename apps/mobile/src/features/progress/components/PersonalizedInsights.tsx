import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Card, Text, Button, Chip } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { insightsService, UserInsight } from '../services/insightsService';

interface PersonalizedInsightsProps {
  maxInsights?: number;
  showActions?: boolean;
}

export const PersonalizedInsights: React.FC<PersonalizedInsightsProps> = ({
  maxInsights = 3,
  showActions = true,
}) => {
  const [insights, setInsights] = useState<UserInsight[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadInsights = async () => {
    try {
      const personalizedInsights =
        await insightsService.generatePersonalizedInsights();
      setInsights(personalizedInsights.slice(0, maxInsights));
    } catch (error) {
      console.error('Error loading insights:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadInsights();
  };

  const handleInsightAction = (insight: UserInsight) => {
    if (!insight.action) {
      return;
    }

    switch (insight.action.type) {
      case 'navigate':
        // TODO: Navigate to specified screen
        console.log('Navigate to:', insight.action.data?.screen);
        break;
      case 'create_habit':
        // TODO: Open habit creation flow
        console.log('Create habit action');
        break;
      case 'set_reminder':
        // TODO: Open reminder settings
        console.log('Set reminder action');
        break;
      case 'view_progress':
        // TODO: Navigate to progress details
        console.log('View progress action');
        break;
    }
  };

  const getPriorityBadgeColor = (priority: string): string => {
    switch (priority) {
      case 'high':
        return '#F44336';
      case 'medium':
        return '#FF9800';
      case 'low':
        return '#4CAF50';
      default:
        return '#757575';
    }
  };

  useEffect(() => {
    loadInsights();
  }, []);

  if (loading) {
    return (
      <Card style={styles.container}>
        <Card.Content style={styles.loadingContent}>
          <Text variant="bodyMedium">Loading insights...</Text>
        </Card.Content>
      </Card>
    );
  }

  if (insights.length === 0) {
    return (
      <Card style={styles.container}>
        <Card.Content style={styles.emptyContent}>
          <Icon name="lightbulb" size={48} color="#ccc" />
          <Text variant="titleMedium" style={styles.emptyTitle}>
            No Insights Yet
          </Text>
          <Text variant="bodyMedium" style={styles.emptyText}>
            Keep tracking your habits to get personalized insights!
          </Text>
        </Card.Content>
      </Card>
    );
  }

  return (
    <Card style={styles.container}>
      <Card.Content style={styles.content}>
        <View style={styles.header}>
          <Text variant="titleMedium" style={styles.title}>
            💡 Personal Insights
          </Text>
          <TouchableOpacity onPress={handleRefresh} disabled={refreshing}>
            <Icon
              name="refresh"
              size={24}
              color={refreshing ? '#ccc' : '#666'}
            />
          </TouchableOpacity>
        </View>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.insightsScroll}
          contentContainerStyle={styles.insightsContainer}
        >
          {insights.map((insight, _index) => (
            <View key={insight.id} style={styles.insightCard}>
              <View style={styles.insightHeader}>
                <View
                  style={[
                    styles.insightIconContainer,
                    { backgroundColor: `${insight.color}20` },
                  ]}
                >
                  <Icon name={insight.icon} size={20} color={insight.color} />
                </View>
                <Chip
                  mode="outlined"
                  compact
                  style={[
                    styles.priorityChip,
                    { borderColor: getPriorityBadgeColor(insight.priority) },
                  ]}
                  textStyle={[
                    styles.priorityText,
                    { color: getPriorityBadgeColor(insight.priority) },
                  ]}
                >
                  {insight.priority}
                </Chip>
              </View>

              <Text variant="titleSmall" style={styles.insightTitle}>
                {insight.title}
              </Text>

              <Text variant="bodySmall" style={styles.insightMessage}>
                {insight.message}
              </Text>

              {insight.actionable && insight.action && showActions && (
                <Button
                  mode="outlined"
                  compact
                  onPress={() => handleInsightAction(insight)}
                  style={[styles.actionButton, { borderColor: insight.color }]}
                  labelStyle={[
                    styles.actionButtonText,
                    { color: insight.color },
                  ]}
                >
                  {insight.action.label}
                </Button>
              )}
            </View>
          ))}
        </ScrollView>

        {insights.length >= maxInsights && (
          <Button
            mode="text"
            onPress={() => {
              // TODO: Navigate to full insights screen
              console.log('View all insights');
            }}
            style={styles.viewAllButton}
          >
            View All Insights
          </Button>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    backgroundColor: '#fff',
    elevation: 4,
  },
  content: {
    padding: 16,
  },
  loadingContent: {
    padding: 24,
    alignItems: 'center',
  },
  emptyContent: {
    padding: 24,
    alignItems: 'center',
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
    color: '#666',
  },
  emptyText: {
    color: '#999',
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontWeight: '600',
    color: '#333',
  },
  insightsScroll: {
    marginHorizontal: -8,
  },
  insightsContainer: {
    paddingHorizontal: 8,
    gap: 12,
  },
  insightCard: {
    width: 280,
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  insightHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  insightIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  priorityChip: {
    height: 24,
    backgroundColor: 'transparent',
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  insightTitle: {
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  insightMessage: {
    color: '#666',
    lineHeight: 18,
    marginBottom: 12,
  },
  actionButton: {
    borderWidth: 1,
    borderRadius: 8,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  viewAllButton: {
    marginTop: 8,
    alignSelf: 'center',
  },
});

import { format } from 'date-fns';
import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Text, Button, ProgressBar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { insightsService, WeeklySummary } from '../services/insightsService';

interface WeeklySummaryCardProps {
  weekStart?: Date;
  onViewDetails?: () => void;
}

export const WeeklySummaryCard: React.FC<WeeklySummaryCardProps> = ({
  weekStart,
  onViewDetails,
}) => {
  const [summary, setSummary] = useState<WeeklySummary | null>(null);
  const [loading, setLoading] = useState(true);

  const loadWeeklySummary = async () => {
    try {
      const weeklySummary =
        await insightsService.generateWeeklySummary(weekStart);
      setSummary(weeklySummary);
    } catch (error) {
      console.error('Error loading weekly summary:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadWeeklySummary();
  }, [weekStart]);

  if (loading) {
    return (
      <Card style={styles.container}>
        <Card.Content style={styles.loadingContent}>
          <Text variant="bodyMedium">Loading weekly summary...</Text>
        </Card.Content>
      </Card>
    );
  }

  if (!summary) {
    return null;
  }

  const weekRange = `${format(summary.weekStart, 'MMM d')} - ${format(summary.weekEnd, 'MMM d')}`;
  const completionProgress =
    summary.totalHabits > 0 ? summary.completionRate / 100 : 0;

  return (
    <Card style={styles.container}>
      <Card.Content style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text variant="titleMedium" style={styles.title}>
              📊 Weekly Summary
            </Text>
            <Text variant="bodyMedium" style={styles.dateRange}>
              {weekRange}
            </Text>
          </View>
          <View style={styles.completionBadge}>
            <Text variant="headlineSmall" style={styles.completionRate}>
              {summary.completionRate}%
            </Text>
            <Text variant="bodySmall" style={styles.completionLabel}>
              Complete
            </Text>
          </View>
        </View>

        {/* Progress Bar */}
        <View style={styles.progressSection}>
          <ProgressBar
            progress={completionProgress}
            color={
              completionProgress >= 0.8
                ? '#4CAF50'
                : completionProgress >= 0.6
                  ? '#FF9800'
                  : '#F44336'
            }
            style={styles.progressBar}
          />
          <Text variant="bodySmall" style={styles.progressText}>
            {summary.completedHabits} of {summary.totalHabits * 7} habit
            completions
          </Text>
        </View>

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <View style={styles.statIconContainer}>
              <Icon name="star" size={20} color="#FF9800" />
            </View>
            <Text variant="bodySmall" style={styles.statLabel}>
              Points
            </Text>
            <Text variant="titleMedium" style={styles.statValue}>
              {summary.pointsEarned}
            </Text>
          </View>

          <View style={styles.statItem}>
            <View style={styles.statIconContainer}>
              <Icon name="emoji-events" size={20} color="#4CAF50" />
            </View>
            <Text variant="bodySmall" style={styles.statLabel}>
              Achievements
            </Text>
            <Text variant="titleMedium" style={styles.statValue}>
              {summary.achievements}
            </Text>
          </View>

          <View style={styles.statItem}>
            <View style={styles.statIconContainer}>
              <Icon name="trending-up" size={20} color="#2196F3" />
            </View>
            <Text variant="bodySmall" style={styles.statLabel}>
              Streak Change
            </Text>
            <Text
              variant="titleMedium"
              style={[
                styles.statValue,
                summary.streakChanges >= 0
                  ? styles.statValuePositive
                  : styles.statValueNegative,
              ]}
            >
              {summary.streakChanges >= 0 ? '+' : ''}
              {summary.streakChanges}
            </Text>
          </View>
        </View>

        {/* Motivational Message */}
        <View style={styles.motivationSection}>
          <Text variant="bodyMedium" style={styles.motivationText}>
            {summary.motivationalMessage}
          </Text>
        </View>

        {/* Top Insights */}
        {summary.insights.length > 0 && (
          <View style={styles.insightsSection}>
            <Text variant="titleSmall" style={styles.insightsTitle}>
              Key Insights
            </Text>
            {summary.insights.slice(0, 2).map((insight, _index) => (
              <View key={insight.id} style={styles.insightItem}>
                <Icon
                  name={insight.icon}
                  size={16}
                  color={insight.color}
                  style={styles.insightIcon}
                />
                <Text variant="bodySmall" style={styles.insightText}>
                  {insight.message}
                </Text>
              </View>
            ))}
          </View>
        )}

        {/* Performance Indicators */}
        <View style={styles.performanceSection}>
          {summary.topPerformingHabits.length > 0 && (
            <View style={styles.performanceItem}>
              <Text variant="bodySmall" style={styles.performanceLabel}>
                🌟 Top Habits
              </Text>
              <Text variant="bodySmall" style={styles.performanceValue}>
                {summary.topPerformingHabits.slice(0, 2).join(', ')}
              </Text>
            </View>
          )}

          {summary.strugglingHabits.length > 0 && (
            <View style={styles.performanceItem}>
              <Text variant="bodySmall" style={styles.performanceLabel}>
                💪 Focus Areas
              </Text>
              <Text variant="bodySmall" style={styles.performanceValue}>
                {summary.strugglingHabits.slice(0, 2).join(', ')}
              </Text>
            </View>
          )}
        </View>

        {/* Action Button */}
        {onViewDetails && (
          <Button
            mode="outlined"
            onPress={onViewDetails}
            style={styles.detailsButton}
          >
            View Detailed Report
          </Button>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    backgroundColor: '#fff',
    elevation: 4,
  },
  content: {
    padding: 16,
  },
  loadingContent: {
    padding: 24,
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  title: {
    fontWeight: '600',
    color: '#333',
  },
  dateRange: {
    color: '#666',
    marginTop: 4,
  },
  completionBadge: {
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
  },
  completionRate: {
    fontWeight: '700',
    color: '#2196F3',
  },
  completionLabel: {
    color: '#666',
    marginTop: 2,
  },
  progressSection: {
    marginBottom: 20,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
    marginBottom: 8,
  },
  progressText: {
    color: '#666',
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statLabel: {
    color: '#666',
    marginBottom: 4,
  },
  statValue: {
    fontWeight: '700',
    color: '#333',
  },
  motivationSection: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  motivationText: {
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  insightsSection: {
    marginBottom: 16,
  },
  insightsTitle: {
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
  },
  insightIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  insightText: {
    flex: 1,
    color: '#666',
    lineHeight: 18,
  },
  performanceSection: {
    marginBottom: 16,
  },
  performanceItem: {
    marginBottom: 8,
  },
  performanceLabel: {
    color: '#666',
    marginBottom: 2,
  },
  performanceValue: {
    color: '#333',
    fontWeight: '500',
  },
  detailsButton: {
    borderRadius: 8,
  },
  statValuePositive: {
    color: '#4CAF50',
  },
  statValueNegative: {
    color: '#F44336',
  },
});

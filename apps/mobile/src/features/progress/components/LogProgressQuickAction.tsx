import AsyncStorage from '@react-native-async-storage/async-storage';
import * as ImagePicker from 'expo-image-picker';
import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
} from 'react-native';
import {
  Text,
  Button,
  Card,
  Chip,
  IconButton,
  TextInput,
  Portal,
  Dialog,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useHydrationUnit } from '../hooks/useHydrationUnit';
import { analyzeMealPhoto as analyzeMealPhotoMVP } from '../services/mealPhotoAnalyzer';
import {
  PhotoAnalysisResult,
  FoodItem,
} from '../services/photoAnalysisService';
import {
  toMl,
  formatNumberWithSeparators,
  parseNumberFromFormatted,
} from '../utils/units';

interface LogProgressQuickActionProps {
  onComplete: (data: ProgressLogData) => void;
  onClose: () => void;
}

export interface ProgressLogData {
  type: 'meal' | 'habit' | 'mood' | 'workout' | 'weight';
  data: any;
  timestamp: string;
}

export const LogProgressQuickAction: React.FC<LogProgressQuickActionProps> = ({
  onComplete,
  onClose,
}) => {
  const [activeTab, setActiveTab] = useState<
    'meal' | 'habits' | 'mood' | 'workout'
  >('meal');
  const [photoAnalysis, setPhotoAnalysis] =
    useState<PhotoAnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [editDialog, setEditDialog] = useState<{
    id: string | null;
    name: string;
    estimatedPortion: string;
    calories: string;
    isAdd: boolean;
  } | null>(null);

  const handleTakePhoto = async () => {
    try {
      const permissionResult =
        await ImagePicker.requestCameraPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert(
          'Permission needed',
          'Camera permission is required to take photos'
        );
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: (ImagePicker as any).MediaTypeOptions?.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setSelectedImage(result.assets[0].uri);
        await analyzeMealPhoto(result.assets[0].uri);
      }
    } catch (_error) {
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const handleSelectPhoto = async () => {
    try {
      const permissionResult =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        Alert.alert(
          'Permission needed',
          'Photo library permission is required'
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: (ImagePicker as any).MediaTypeOptions?.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setSelectedImage(result.assets[0].uri);
        await analyzeMealPhoto(result.assets[0].uri);
      }
    } catch (_error) {
      Alert.alert('Error', 'Failed to select photo');
    }
  };

  const analyzeMealPhoto = async (imageUri: string) => {
    setIsAnalyzing(true);
    try {
      const analysis = await analyzeMealPhotoMVP(imageUri);
      setPhotoAnalysis(analysis);
    } catch (_error) {
      Alert.alert(
        'Analysis Failed',
        'Could not analyze the photo. Please try again.'
      );
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleFoodItemToggle = (foodId: string, confirmed: boolean) => {
    if (!photoAnalysis) {
      return;
    }

    setPhotoAnalysis({
      ...photoAnalysis,
      foods: photoAnalysis.foods.map(food =>
        food.id === foodId ? { ...food, confirmed } : food
      ),
    });
  };

  const handleConfirmAll = () => {
    if (!photoAnalysis) {
      return;
    }

    setPhotoAnalysis({
      ...photoAnalysis,
      foods: photoAnalysis.foods.map(food => ({ ...food, confirmed: true })),
    });
  };

  const handleLogMeal = async () => {
    if (!photoAnalysis) {
      return;
    }

    const confirmedFoods = photoAnalysis.foods.filter(
      (food: any) => food.confirmed !== false
    );

    const mealData: ProgressLogData = {
      type: 'meal',
      data: {
        foods: confirmedFoods,
        totalCalories: confirmedFoods.reduce(
          (sum, food) => sum + (food.calories || 0),
          0
        ),
        photo: selectedImage,
        analysisId: photoAnalysis.analysisId,
      },
      timestamp: new Date().toISOString(),
    };

    onComplete(mealData);
  };

  const renderMealTab = () => (
    <View style={styles.tabContent}>
      {!selectedImage ? (
        <View style={styles.photoOptions}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Log Your Meal 📸
          </Text>
          <Text variant="bodyMedium" style={styles.description}>
            Take or select a photo of your meal for AI analysis
          </Text>

          <View style={styles.photoButtons}>
            <Button
              mode="contained"
              onPress={handleTakePhoto}
              icon="camera"
              style={styles.photoButton}
            >
              Take Photo
            </Button>
            <Button
              mode="outlined"
              onPress={handleSelectPhoto}
              icon="image"
              style={styles.photoButton}
            >
              Select Photo
            </Button>
          </View>
        </View>
      ) : (
        <ScrollView style={styles.analysisContainer}>
          <Image source={{ uri: selectedImage }} style={styles.mealImage} />

          {isAnalyzing ? (
            <View style={styles.analyzingContainer}>
              <Text variant="titleMedium">Analyzing your meal... 🤖</Text>
              <Text variant="bodySmall" style={styles.analyzingText}>
                This may take a few seconds
              </Text>
            </View>
          ) : photoAnalysis ? (
            <View style={styles.analysisResults}>
              <View style={styles.analysisHeader}>
                <Text variant="titleMedium">I found these foods:</Text>
                <Button
                  mode="outlined"
                  onPress={handleConfirmAll}
                  compact
                  style={styles.confirmAllButton}
                >
                  Confirm All
                </Button>
              </View>

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                  marginBottom: 8,
                }}
              >
                <Button
                  mode="text"
                  compact
                  onPress={() =>
                    setEditDialog({
                      id: null,
                      name: '',
                      estimatedPortion: '',
                      calories: '',
                      isAdd: true,
                    })
                  }
                >
                  + Add Item
                </Button>
              </View>

              {photoAnalysis.foods.map(food => (
                <FoodItemCard
                  key={food.id}
                  food={food}
                  onToggle={confirmed =>
                    handleFoodItemToggle(food.id, confirmed)
                  }
                />
              ))}

              <View style={styles.totalCalories}>
                <Text variant="titleMedium">
                  Total:{' '}
                  {photoAnalysis.foods
                    .filter((food: any) => food.confirmed !== false)
                    .reduce((sum, food) => sum + (food.calories || 0), 0)}{' '}
                  calories
                  {/* Edit/Add Item Dialog */}
                  <Portal>
                    <Dialog
                      visible={!!editDialog}
                      onDismiss={() => setEditDialog(null)}
                    >
                      <Dialog.Title>
                        {editDialog?.isAdd ? 'Add Item' : 'Edit Item'}
                      </Dialog.Title>
                      <Dialog.Content>
                        <TextInput
                          mode="outlined"
                          label="Food name"
                          value={editDialog?.name || ''}
                          onChangeText={t =>
                            setEditDialog(ed => (ed ? { ...ed, name: t } : ed))
                          }
                          style={{ marginBottom: 8 }}
                        />
                        <TextInput
                          mode="outlined"
                          label="Portion"
                          value={editDialog?.estimatedPortion || ''}
                          onChangeText={t =>
                            setEditDialog(ed =>
                              ed ? { ...ed, estimatedPortion: t } : ed
                            )
                          }
                          style={{ marginBottom: 8 }}
                        />
                        <TextInput
                          mode="outlined"
                          label="Calories"
                          keyboardType="numeric"
                          value={editDialog?.calories || ''}
                          onChangeText={t =>
                            setEditDialog(ed =>
                              ed
                                ? { ...ed, calories: t.replace(/[^0-9]/g, '') }
                                : ed
                            )
                          }
                        />
                      </Dialog.Content>
                      <Dialog.Actions>
                        <Button onPress={() => setEditDialog(null)}>
                          Cancel
                        </Button>
                        <Button
                          onPress={() => {
                            if (!photoAnalysis || !editDialog) return;
                            const caloriesNum =
                              parseInt(editDialog.calories || '0', 10) || 0;
                            if (editDialog.isAdd) {
                              const newFood: FoodItem & {
                                confirmed?: boolean;
                              } = {
                                id: `food_${Date.now()}`,
                                name: editDialog.name || 'custom item',
                                category: 'other',
                                estimatedPortion:
                                  editDialog.estimatedPortion || '1 serving',
                                confidence: 100,
                                calories: caloriesNum,
                                macros: { protein: 0, carbs: 0, fat: 0 },
                                confirmed: true,
                              };
                              setPhotoAnalysis({
                                ...photoAnalysis,
                                foods: [...photoAnalysis.foods, newFood],
                              });
                            } else {
                              setPhotoAnalysis({
                                ...photoAnalysis,
                                foods: photoAnalysis.foods.map(f =>
                                  f.id === editDialog.id
                                    ? {
                                        ...f,
                                        name: editDialog.name,
                                        estimatedPortion:
                                          editDialog.estimatedPortion,
                                        calories: caloriesNum,
                                      }
                                    : f
                                ),
                              });
                            }
                            setEditDialog(null);
                          }}
                        >
                          Save
                        </Button>
                      </Dialog.Actions>
                    </Dialog>
                  </Portal>
                </Text>
              </View>

              <Button
                mode="contained"
                onPress={handleLogMeal}
                style={styles.logButton}
                disabled={
                  !photoAnalysis.foods.some(
                    (food: any) => food.confirmed !== false
                  )
                }
              >
                Log This Meal
              </Button>
            </View>
          ) : null}
        </ScrollView>
      )}
    </View>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'meal':
        return renderMealTab();
      case 'habits':
        return <HabitsTab onComplete={onComplete} />;
      case 'mood':
        return <MoodTab onComplete={onComplete} />;
      case 'workout':
        return <WorkoutTab onComplete={onComplete} />;
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text variant="headlineSmall">Log Progress</Text>
        <IconButton icon="close" onPress={onClose} />
      </View>

      <View style={styles.tabs}>
        {[
          { key: 'meal', label: 'Meal', icon: 'food' },
          { key: 'habits', label: 'Habits', icon: 'check-circle' },
          { key: 'mood', label: 'Mood', icon: 'emoticon' },
          { key: 'workout', label: 'Workout', icon: 'dumbbell' },
        ].map(tab => (
          <TouchableOpacity
            key={tab.key}
            style={[styles.tab, activeTab === tab.key && styles.activeTab]}
            onPress={() => setActiveTab(tab.key as any)}
          >
            <Icon
              name={tab.icon}
              size={20}
              color={activeTab === tab.key ? '#007AFF' : '#666'}
            />
            <Text
              variant="bodySmall"
              style={[
                styles.tabLabel,
                activeTab === tab.key && styles.activeTabLabel,
              ]}
            >
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {renderTabContent()}
    </View>
  );
};

// --- Subcomponents for new tabs ---
const HabitsTab: React.FC<{ onComplete: (data: ProgressLogData) => void }> = ({
  onComplete,
}) => {
  const { unit, setUnit } = useHydrationUnit();
  const [hydrationInput, setHydrationInput] = useState<string>('');
  const [stepsInput, setStepsInput] = useState<string>('');

  const [showUnitPrompt, setShowUnitPrompt] = useState(false);
  useEffect(() => {
    (async () => {
      const flag = await AsyncStorage.getItem('hydration_unit_prompt_shown');
      if (!flag) setShowUnitPrompt(true);
    })();
  }, []);

  const addPreset = (amount: number) => {
    const current = parseNumberFromFormatted(hydrationInput);
    const next = current + amount;
    setHydrationInput(formatNumberWithSeparators(next));
  };

  const handleSave = () => {
    const hydrationValue = parseNumberFromFormatted(hydrationInput);
    const stepsValue = parseNumberFromFormatted(stepsInput);

    const payload: ProgressLogData = {
      type: 'habit',
      data: {
        hydration: { value: toMl(hydrationValue, unit), unit: 'ml' },
        steps: stepsValue,
      },
      timestamp: new Date().toISOString(),
    };
    onComplete(payload);
  };

  const presetA = unit === 'ml' ? 250 : 8;
  const presetB = unit === 'ml' ? 500 : 16;

  return (
    <ScrollView style={{ flex: 1 }}>
      <Card style={{ marginBottom: 12 }}>
        <Card.Title
          title="Hydration"
          subtitle={`Units: ${unit === 'ml' ? 'milliliters' : 'fluid ounces'}`}
          right={() => (
            <View style={{ flexDirection: 'row', paddingRight: 8 }}>
              <Button
                compact
                onPress={() => setUnit(unit === 'ml' ? 'fl_oz' : 'ml')}
              >
                {unit === 'ml' ? 'Use fl oz' : 'Use ml'}
              </Button>
            </View>
          )}
        />
        <Card.Content>
          <Text variant="bodySmall" style={{ marginBottom: 8 }}>
            Quick add
          </Text>
          <View style={{ flexDirection: 'row', gap: 8, marginBottom: 8 }}>
            <Button mode="outlined" onPress={() => addPreset(presetA)}>
              +{presetA} {unit}
            </Button>
            <Button mode="outlined" onPress={() => addPreset(presetB)}>
              +{presetB} {unit}
            </Button>
          </View>
          <Text variant="bodySmall" style={{ marginBottom: 4 }}>
            Custom amount ({unit})
          </Text>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            <TextInput
              mode="outlined"
              value={hydrationInput}
              onChangeText={t =>
                setHydrationInput(formatNumberWithSeparators(t))
              }
              keyboardType="numeric"
              placeholder={`e.g., ${unit === 'ml' ? '250' : '8'}`}
              style={{ flex: 1 }}
            />
          </View>
        </Card.Content>
      </Card>

      <Card>
        <Card.Title title="Steps" />
        <Card.Content>
          <Text variant="bodySmall" style={{ marginBottom: 4 }}>
            Enter steps
            <Portal>
              <Dialog
                visible={showUnitPrompt}
                onDismiss={() => setShowUnitPrompt(false)}
              >
                <Dialog.Title>Choose hydration unit</Dialog.Title>
                <Dialog.Content>
                  <Text>Which unit do you prefer for hydration?</Text>
                </Dialog.Content>
                <Dialog.Actions>
                  <Button
                    onPress={async () => {
                      await setUnit('ml');
                      await AsyncStorage.setItem(
                        'hydration_unit_prompt_shown',
                        '1'
                      );
                      setShowUnitPrompt(false);
                    }}
                  >
                    Milliliters (ml)
                  </Button>
                  <Button
                    onPress={async () => {
                      await setUnit('fl_oz');
                      await AsyncStorage.setItem(
                        'hydration_unit_prompt_shown',
                        '1'
                      );
                      setShowUnitPrompt(false);
                    }}
                  >
                    Fluid ounces (fl oz)
                  </Button>
                </Dialog.Actions>
              </Dialog>
            </Portal>
          </Text>
          <TextInput
            mode="outlined"
            value={stepsInput}
            onChangeText={t => setStepsInput(formatNumberWithSeparators(t))}
            keyboardType="numeric"
            placeholder="e.g., 8,000"
          />
        </Card.Content>
      </Card>

      <Button mode="contained" style={{ marginTop: 16 }} onPress={handleSave}>
        Save Habits
      </Button>
    </ScrollView>
  );
};

const MoodTab: React.FC<{ onComplete: (data: ProgressLogData) => void }> = ({
  onComplete,
}) => {
  const [mood, setMood] = useState<'low' | 'ok' | 'great'>('ok');
  const [energy, setEnergy] = useState<'low' | 'ok' | 'high'>('ok');
  const [notes, setNotes] = useState<string>('');

  const handleSave = () => {
    const payload: ProgressLogData = {
      type: 'mood',
      data: { mood, energy, notes: notes.trim() || undefined },
      timestamp: new Date().toISOString(),
    };
    onComplete(payload);
  };

  const Option = ({ value, label, current, onPress }: any) => (
    <Chip
      selected={current === value}
      onPress={() => onPress(value)}
      style={{ marginRight: 8 }}
    >
      {label}
    </Chip>
  );

  return (
    <ScrollView style={{ flex: 1 }}>
      <Card style={{ marginBottom: 12 }}>
        <Card.Title title="Mood" />
        <Card.Content>
          <View style={{ flexDirection: 'row', marginBottom: 8 }}>
            <Option value="low" label="Low" current={mood} onPress={setMood} />
            <Option value="ok" label="OK" current={mood} onPress={setMood} />
            <Option
              value="great"
              label="Great"
              current={mood}
              onPress={setMood}
            />
          </View>
        </Card.Content>
      </Card>
      <Card style={{ marginBottom: 12 }}>
        <Card.Title title="Energy" />
        <Card.Content>
          <View style={{ flexDirection: 'row' }}>
            <Option
              value="low"
              label="Low"
              current={energy}
              onPress={setEnergy}
            />
            <Option
              value="ok"
              label="OK"
              current={energy}
              onPress={setEnergy}
            />
            <Option
              value="high"
              label="High"
              current={energy}
              onPress={setEnergy}
            />
          </View>
        </Card.Content>
      </Card>
      <Card>
        <Card.Title title="Notes" />
        <Card.Content>
          <TextInput
            mode="outlined"
            value={notes}
            onChangeText={setNotes}
            placeholder="Optional"
            multiline
          />
        </Card.Content>
      </Card>

      <Button mode="contained" style={{ marginTop: 16 }} onPress={handleSave}>
        Save Mood
      </Button>
    </ScrollView>
  );
};

const WorkoutTab: React.FC<{ onComplete: (data: ProgressLogData) => void }> = ({
  onComplete,
}) => {
  const [completed, setCompleted] = useState<boolean>(true);
  const [duration, setDuration] = useState<string>('');
  const [type, setType] = useState<'strength' | 'cardio' | 'mobility'>(
    'strength'
  );
  const [intensity, setIntensity] = useState<'low' | 'medium' | 'high'>(
    'medium'
  );

  const handleSave = () => {
    const durationMin = parseNumberFromFormatted(duration);
    const payload: ProgressLogData = {
      type: 'workout',
      data: { completed, durationMin, type, intensity },
      timestamp: new Date().toISOString(),
    };
    onComplete(payload);
  };

  return (
    <ScrollView style={{ flex: 1 }}>
      <Card style={{ marginBottom: 12 }}>
        <Card.Title title="Workout" />
        <Card.Content>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 12,
            }}
          >
            <Chip
              selected={completed}
              onPress={() => setCompleted(true)}
              style={{ marginRight: 8 }}
            >
              Completed
            </Chip>
            <Chip selected={!completed} onPress={() => setCompleted(false)}>
              Skipped
            </Chip>
          </View>

          <Text variant="bodySmall" style={{ marginBottom: 4 }}>
            Duration (min)
          </Text>
          <TextInput
            mode="outlined"
            value={duration}
            onChangeText={t => setDuration(formatNumberWithSeparators(t))}
            keyboardType="numeric"
            placeholder="e.g., 45"
          />

          <Text variant="bodySmall" style={{ marginTop: 12, marginBottom: 4 }}>
            Type
          </Text>
          <View style={{ flexDirection: 'row', marginBottom: 8 }}>
            {(['strength', 'cardio', 'mobility'] as const).map(item => (
              <Chip
                key={item}
                selected={type === item}
                onPress={() => setType(item)}
                style={{ marginRight: 8 }}
              >
                {item.charAt(0).toUpperCase() + item.slice(1)}
              </Chip>
            ))}
          </View>

          <Text variant="bodySmall" style={{ marginTop: 12, marginBottom: 4 }}>
            Intensity
          </Text>
          <View style={{ flexDirection: 'row' }}>
            {(['low', 'medium', 'high'] as const).map(item => (
              <Chip
                key={item}
                selected={intensity === item}
                onPress={() => setIntensity(item)}
                style={{ marginRight: 8 }}
              >
                {item.charAt(0).toUpperCase() + item.slice(1)}
              </Chip>
            ))}
          </View>
        </Card.Content>
      </Card>

      <Button mode="contained" style={{ marginTop: 16 }} onPress={handleSave}>
        Save Workout
      </Button>
    </ScrollView>
  );
};

interface FoodItemCardProps {
  food: FoodItem & { confirmed?: boolean };
  onToggle: (confirmed: boolean) => void;
}

const FoodItemCard: React.FC<FoodItemCardProps> = ({ food, onToggle }) => {
  const isConfirmed = food.confirmed !== false;

  return (
    <Card style={[styles.foodCard, !isConfirmed && styles.foodCardUnconfirmed]}>
      <View style={styles.foodCardContent}>
        <View style={styles.foodInfo}>
          <Text variant="titleSmall">{food.name}</Text>
          <Text variant="bodySmall" style={styles.foodDetails}>
            {food.estimatedPortion} • {food.calories} cal
          </Text>
          <View style={styles.macros}>
            <Chip compact style={styles.macroChip}>
              P: {food.macros?.protein || 0}g
            </Chip>
            <Chip compact style={styles.macroChip}>
              C: {food.macros?.carbs || 0}g
            </Chip>
            <Chip compact style={styles.macroChip}>
              F: {food.macros?.fat || 0}g
            </Chip>
          </View>
        </View>

        <TouchableOpacity
          style={[styles.confirmButton, isConfirmed && styles.confirmedButton]}
          onPress={() => onToggle(!isConfirmed)}
        >
          <Icon
            name={isConfirmed ? 'check-circle' : 'circle-outline'}
            size={24}
            color={isConfirmed ? '#4CAF50' : '#666'}
          />
        </TouchableOpacity>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tabs: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabLabel: {
    marginTop: 4,
    color: '#666',
  },
  activeTabLabel: {
    color: '#007AFF',
    fontWeight: '500',
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  photoOptions: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  sectionTitle: {
    marginBottom: 8,
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
    color: '#666',
    marginBottom: 32,
  },
  photoButtons: {
    flexDirection: 'row',
    gap: 16,
  },
  photoButton: {
    minWidth: 120,
  },
  analysisContainer: {
    flex: 1,
  },
  mealImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    marginBottom: 16,
  },
  analyzingContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  analyzingText: {
    color: '#666',
    marginTop: 8,
  },
  analysisResults: {
    flex: 1,
  },
  analysisHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  confirmAllButton: {
    borderRadius: 8,
  },
  foodCard: {
    marginBottom: 12,
  },
  foodCardUnconfirmed: {
    opacity: 0.6,
  },
  foodCardContent: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  foodInfo: {
    flex: 1,
  },
  foodDetails: {
    color: '#666',
    marginTop: 4,
  },
  macros: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  macroChip: {
    height: 24,
  },
  confirmButton: {
    padding: 8,
  },
  confirmedButton: {
    backgroundColor: '#E8F5E8',
    borderRadius: 20,
  },
  totalCalories: {
    alignItems: 'center',
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    marginTop: 16,
  },
  logButton: {
    marginTop: 16,
    borderRadius: 12,
  },
});

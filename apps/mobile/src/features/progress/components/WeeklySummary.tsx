import { format, startOfWeek, endOfWeek } from 'date-fns';
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Card, Text, ProgressBar } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

export interface WeeklyGoal {
  category: string;
  icon: string;
  color: string;
  current: number;
  target: number;
  unit: string;
}

interface WeeklySummaryProps {
  goals: WeeklyGoal[];
  weekStartDate?: Date;
  title?: string;
}

export const WeeklySummary: React.FC<WeeklySummaryProps> = ({
  goals,
  weekStartDate = new Date(),
  title = "This Week's Progress",
}) => {
  const weekStart = startOfWeek(weekStartDate, { weekStartsOn: 1 }); // Monday
  const weekEnd = endOfWeek(weekStartDate, { weekStartsOn: 1 }); // Sunday

  const weekRange = `${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d')}`;

  const overallProgress =
    goals.length > 0
      ? goals.reduce(
          (sum, goal) =>
            sum + Math.min(100, (goal.current / goal.target) * 100),
          0
        ) / goals.length
      : 0;

  const completedGoals = goals.filter(
    goal => goal.current >= goal.target
  ).length;

  return (
    <Card style={styles.container}>
      <Card.Content style={styles.content}>
        <View style={styles.header}>
          <View>
            <Text variant="titleMedium" style={styles.title}>
              {title}
            </Text>
            <Text variant="bodyMedium" style={styles.dateRange}>
              {weekRange}
            </Text>
          </View>
          <View style={styles.overallStats}>
            <Text variant="headlineSmall" style={styles.overallProgress}>
              {Math.round(overallProgress)}%
            </Text>
            <Text variant="bodySmall" style={styles.completedGoals}>
              {completedGoals}/{goals.length} goals
            </Text>
          </View>
        </View>

        <View style={styles.goalsContainer}>
          {goals.map((goal, index) => {
            const progress = Math.min(1, goal.current / goal.target);
            const percentage = Math.round(progress * 100);
            const isCompleted = goal.current >= goal.target;

            return (
              <View key={index} style={styles.goalItem}>
                <View style={styles.goalHeader}>
                  <View style={styles.goalInfo}>
                    <View
                      style={[
                        styles.iconContainer,
                        { backgroundColor: `${goal.color}20` },
                      ]}
                    >
                      <Icon name={goal.icon} size={20} color={goal.color} />
                    </View>
                    <View style={styles.goalText}>
                      <Text variant="bodyMedium" style={styles.goalCategory}>
                        {goal.category}
                      </Text>
                      <Text variant="bodySmall" style={styles.goalProgress}>
                        {goal.current} / {goal.target} {goal.unit}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.goalStats}>
                    <Text
                      variant="bodyMedium"
                      style={[
                        styles.percentage,
                        isCompleted
                          ? styles.percentageCompleted
                          : styles.percentageIncomplete,
                      ]}
                    >
                      {percentage}%
                    </Text>
                    {isCompleted && (
                      <Icon name="check-circle" size={16} color="#4CAF50" />
                    )}
                  </View>
                </View>

                <ProgressBar
                  progress={progress}
                  color={goal.color}
                  style={styles.progressBar}
                />
              </View>
            );
          })}
        </View>

        {goals.length === 0 && (
          <View style={styles.emptyState}>
            <Icon name="flag" size={48} color="#ccc" />
            <Text variant="bodyLarge" style={styles.emptyText}>
              No weekly goals set
            </Text>
            <Text variant="bodyMedium" style={styles.emptySubtext}>
              Set some goals to track your weekly progress!
            </Text>
          </View>
        )}

        {overallProgress >= 100 && goals.length > 0 && (
          <View style={styles.celebrationBanner}>
            <Icon name="celebration" size={24} color="#FF9800" />
            <Text variant="bodyMedium" style={styles.celebrationText}>
              🎉 Amazing! You've completed all your weekly goals!
            </Text>
          </View>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    backgroundColor: '#fff',
    elevation: 4,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  title: {
    fontWeight: '600',
    color: '#333',
  },
  dateRange: {
    color: '#666',
    marginTop: 4,
  },
  overallStats: {
    alignItems: 'flex-end',
  },
  overallProgress: {
    fontWeight: '700',
    color: '#333',
  },
  completedGoals: {
    color: '#666',
    marginTop: 2,
  },
  goalsContainer: {
    gap: 16,
  },
  goalItem: {
    paddingVertical: 8,
  },
  goalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  goalInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  goalText: {
    flex: 1,
  },
  goalCategory: {
    fontWeight: '500',
    color: '#333',
  },
  goalProgress: {
    color: '#666',
    marginTop: 2,
  },
  goalStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  percentage: {
    fontWeight: '600',
    fontSize: 16,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
    backgroundColor: '#f0f0f0',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    color: '#666',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    color: '#999',
    textAlign: 'center',
  },
  celebrationBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
    gap: 8,
  },
  celebrationText: {
    color: '#E65100',
    fontWeight: '500',
    flex: 1,
  },
  percentageCompleted: {
    color: '#4CAF50',
  },
  percentageIncomplete: {
    color: '#666',
  },
});

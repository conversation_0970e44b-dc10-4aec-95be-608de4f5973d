import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Text, ProgressBar } from 'react-native-paper';
import { CompletionRate } from '../../services/analyticsService';

interface HabitCompletionChartProps {
  data: CompletionRate[];
  title?: string;
  height?: number;
  showArea?: boolean;
  color?: string;
}

const { width: _screenWidth } = Dimensions.get('window');

export const HabitCompletionChart: React.FC<HabitCompletionChartProps> = ({
  data,
  title = 'Habit Completion Rate',
  height = 200, // eslint-disable-line @typescript-eslint/no-unused-vars
  showArea = true, // eslint-disable-line @typescript-eslint/no-unused-vars
  color = '#4CAF50',
}) => {
  // Calculate average completion rate
  const averageRate =
    data.length > 0
      ? Math.round(
          data.reduce((sum, item) => sum + item.completion_rate, 0) /
            data.length
        )
      : 0;

  return (
    <View style={styles.container}>
      {title && (
        <View style={styles.header}>
          <Text variant="titleMedium" style={styles.title}>
            {title}
          </Text>
          <Text variant="bodyMedium" style={styles.average}>
            Avg: {averageRate}%
          </Text>
        </View>
      )}

      <View style={styles.chartContainer}>
        {/* Simple progress bars for each day */}
        {data.map((item, index) => (
          <View key={index} style={styles.dayProgress}>
            <Text variant="bodySmall" style={styles.dayLabel}>
              Day {index + 1}
            </Text>
            <ProgressBar
              progress={item.completion_rate / 100}
              color={color}
              style={styles.progressBar}
            />
            <Text variant="bodySmall" style={styles.percentageText}>
              {item.completion_rate}%
            </Text>
          </View>
        ))}
      </View>

      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: color }]} />
          <Text variant="bodySmall" style={styles.legendText}>
            Completion Rate
          </Text>
        </View>
        {data.length > 0 && (
          <Text variant="bodySmall" style={styles.dataRange}>
            Last {data.length} days
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontWeight: '600',
    color: '#333',
  },
  average: {
    color: '#666',
    fontWeight: '500',
  },
  chartContainer: {
    marginVertical: 8,
  },
  dayProgress: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
    paddingHorizontal: 8,
  },
  dayLabel: {
    width: 50,
    color: '#666',
  },
  progressBar: {
    flex: 1,
    height: 6,
    marginHorizontal: 12,
    borderRadius: 3,
  },
  percentageText: {
    width: 40,
    textAlign: 'right',
    color: '#666',
    fontWeight: '500',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    color: '#666',
  },
  dataRange: {
    color: '#999',
    fontStyle: 'italic',
  },
});

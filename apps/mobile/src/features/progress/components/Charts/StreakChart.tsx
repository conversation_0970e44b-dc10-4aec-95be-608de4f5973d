import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Text } from 'react-native-paper';
import { StreakData } from '../../services/analyticsService';

interface StreakChartProps {
  data: StreakData[];
  title?: string;
  height?: number;
  color?: string;
  maxBars?: number;
}

const { width: screenWidth } = Dimensions.get('window');
const _chartWidth = screenWidth - 40;

export const StreakChart: React.FC<StreakChartProps> = ({
  data,
  title = 'Current Streaks',
  height = 200, // eslint-disable-line @typescript-eslint/no-unused-vars
  color = '#FF9800',
  maxBars = 5,
}) => {
  // Sort by current streak and take top items
  const sortedData = [...data]
    .filter(item => item.current_streak > 0)
    .sort((a, b) => b.current_streak - a.current_streak)
    .slice(0, maxBars);

  // Transform data for Victory charts
  const chartData = sortedData.map((item, index) => ({
    x: index + 1,
    y: item.current_streak,
    label: `${item.entity_name || item.streak_type}: ${item.current_streak} days`,
    name: item.entity_name || item.streak_type,
  }));

  const maxStreak = Math.max(...sortedData.map(s => s.current_streak), 0);

  return (
    <View style={styles.container}>
      {title && (
        <View style={styles.header}>
          <Text variant="titleMedium" style={styles.title}>
            {title}
          </Text>
          <Text variant="bodyMedium" style={styles.maxStreak}>
            Best: {maxStreak} days
          </Text>
        </View>
      )}

      {chartData.length > 0 ? (
        <>
          <View style={styles.chartContainer}>
            {/* Simple bars for each streak */}
            {sortedData.map((item, index) => (
              <View key={index} style={styles.streakBar}>
                <Text variant="bodySmall" style={styles.streakLabel}>
                  {item.entity_name || item.streak_type}
                </Text>
                <View style={styles.barContainer}>
                  <View
                    style={[
                      styles.bar,
                      {
                        width: `${(item.current_streak / maxStreak) * 100}%`,
                        backgroundColor: color,
                      },
                    ]}
                  />
                </View>
                <Text variant="bodySmall" style={styles.streakValue}>
                  {item.current_streak}d
                </Text>
              </View>
            ))}
          </View>

          <View style={styles.legend}>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: color }]} />
              <Text variant="bodySmall" style={styles.legendText}>
                Active Streaks
              </Text>
            </View>
            <Text variant="bodySmall" style={styles.dataCount}>
              {sortedData.length} active
            </Text>
          </View>
        </>
      ) : (
        <View style={styles.emptyState}>
          <Text variant="bodyLarge" style={styles.emptyText}>
            No active streaks yet
          </Text>
          <Text variant="bodyMedium" style={styles.emptySubtext}>
            Complete some habits to start building streaks!
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontWeight: '600',
    color: '#333',
  },
  maxStreak: {
    color: '#666',
    fontWeight: '500',
  },
  chartContainer: {
    marginVertical: 8,
  },
  streakBar: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 6,
    paddingHorizontal: 8,
  },
  streakLabel: {
    width: 80,
    color: '#666',
    fontSize: 10,
  },
  barContainer: {
    flex: 1,
    height: 20,
    backgroundColor: '#f0f0f0',
    borderRadius: 10,
    marginHorizontal: 12,
    overflow: 'hidden',
  },
  bar: {
    height: '100%',
    borderRadius: 10,
    minWidth: 4,
  },
  streakValue: {
    width: 30,
    textAlign: 'right',
    color: '#666',
    fontWeight: '500',
    fontSize: 10,
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    color: '#666',
  },
  dataCount: {
    color: '#999',
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    color: '#666',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    color: '#999',
    textAlign: 'center',
  },
});

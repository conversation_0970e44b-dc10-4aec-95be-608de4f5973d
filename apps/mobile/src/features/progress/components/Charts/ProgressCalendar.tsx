import { startOfWeek, addDays, isSameDay } from 'date-fns';
import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text } from 'react-native-paper';
import { StreakCalendarData } from '../../services/streakService';

interface ProgressCalendarProps {
  data: StreakCalendarData[];
  title?: string;
  onDatePress?: (date: string) => void;
}

export const ProgressCalendar: React.FC<ProgressCalendarProps> = ({
  data,
  title = 'Activity Calendar',
  onDatePress,
}) => {
  // Group data by weeks
  const weeks: StreakCalendarData[][] = [];
  let currentWeek: StreakCalendarData[] = [];

  data.forEach((dayData, index) => {
    const date = new Date(dayData.date);
    const dayOfWeek = date.getDay(); // 0 = Sunday, 6 = Saturday

    // Start new week on Sunday
    if (dayOfWeek === 0 && currentWeek.length > 0) {
      weeks.push(currentWeek);
      currentWeek = [];
    }

    currentWeek.push(dayData);

    // Add the last week
    if (index === data.length - 1) {
      weeks.push(currentWeek);
    }
  });

  const getActivityColor = (completionRate: number, hasActivity: boolean) => {
    if (!hasActivity) {
      return '#f5f5f5';
    }
    if (completionRate >= 80) {
      return '#4CAF50';
    }
    if (completionRate >= 60) {
      return '#8BC34A';
    }
    if (completionRate >= 40) {
      return '#FFC107';
    }
    if (completionRate >= 20) {
      return '#FF9800';
    }
    return '#FF5722';
  };

  const getActivityIntensity = (
    completionRate: number,
    hasActivity: boolean
  ) => {
    if (!hasActivity) {
      return 0.1;
    }
    return Math.max(0.3, completionRate / 100);
  };

  const weekDays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

  return (
    <View style={styles.container}>
      {title && (
        <Text variant="titleMedium" style={styles.title}>
          {title}
        </Text>
      )}

      {/* Week day headers */}
      <View style={styles.weekHeader}>
        {weekDays.map((day, index) => (
          <Text key={index} variant="bodySmall" style={styles.weekDayLabel}>
            {day}
          </Text>
        ))}
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.scrollContainer}
      >
        <View style={styles.calendarContainer}>
          {weeks.map((week, weekIndex) => (
            <View key={weekIndex} style={styles.week}>
              {Array.from({ length: 7 }, (_, dayIndex) => {
                const dayData = week.find(_dayItem => {
                  const date = new Date(week[0].date);
                  const startOfCurrentWeek = startOfWeek(date, {
                    weekStartsOn: 0,
                  });
                  const currentDay = addDays(startOfCurrentWeek, dayIndex);
                  return isSameDay(new Date(_dayItem.date), currentDay);
                });

                const isEmpty = !dayData;
                const date = isEmpty ? null : new Date(dayData.date);
                const dayNumber = date ? date.getDate() : '';

                return (
                  <View
                    key={dayIndex}
                    style={[
                      styles.dayCell,
                      isEmpty
                        ? styles.dayCellEmpty
                        : {
                            backgroundColor: getActivityColor(
                              dayData.completion_rate,
                              dayData.has_activity
                            ),
                            opacity: getActivityIntensity(
                              dayData.completion_rate,
                              dayData.has_activity
                            ),
                          },
                    ]}
                    onTouchEnd={() => {
                      if (dayData && onDatePress) {
                        onDatePress(dayData.date);
                      }
                    }}
                  >
                    {!isEmpty && (
                      <Text variant="bodySmall" style={styles.dayNumber}>
                        {dayNumber}
                      </Text>
                    )}
                  </View>
                );
              })}
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Legend */}
      <View style={styles.legend}>
        <Text variant="bodySmall" style={styles.legendTitle}>
          Activity Level:
        </Text>
        <View style={styles.legendItems}>
          <View style={styles.legendItem}>
            <View style={[styles.legendSquare, styles.legendSquareNone]} />
            <Text variant="bodySmall" style={styles.legendText}>
              None
            </Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendSquare, styles.legendSquareLow]} />
            <Text variant="bodySmall" style={styles.legendText}>
              Low
            </Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendSquare, styles.legendSquareMedium]} />
            <Text variant="bodySmall" style={styles.legendText}>
              Med
            </Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendSquare, styles.legendSquareHigh]} />
            <Text variant="bodySmall" style={styles.legendText}>
              High
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  weekHeader: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
    paddingHorizontal: 4,
  },
  weekDayLabel: {
    color: '#666',
    fontWeight: '500',
    textAlign: 'center',
    width: 32,
  },
  scrollContainer: {
    maxHeight: 200,
  },
  calendarContainer: {
    flexDirection: 'row',
  },
  week: {
    flexDirection: 'column',
    marginRight: 4,
  },
  dayCell: {
    width: 32,
    height: 32,
    borderRadius: 4,
    marginVertical: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  dayNumber: {
    fontSize: 10,
    fontWeight: '500',
    color: '#333',
  },
  legend: {
    marginTop: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  legendTitle: {
    color: '#666',
    marginBottom: 8,
    fontWeight: '500',
  },
  legendItems: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendSquare: {
    width: 12,
    height: 12,
    borderRadius: 2,
    marginRight: 4,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  legendText: {
    color: '#666',
    fontSize: 10,
  },
  dayCellEmpty: {
    backgroundColor: 'transparent',
    opacity: 0.3,
  },

  legendSquareNone: {
    backgroundColor: '#f5f5f5',
  },
  legendSquareLow: {
    backgroundColor: '#FF5722',
  },
  legendSquareMedium: {
    backgroundColor: '#FFC107',
  },
  legendSquareHigh: {
    backgroundColor: '#4CAF50',
  },
});

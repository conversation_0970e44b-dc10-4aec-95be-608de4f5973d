import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Card, Text, ProgressBar, Chip } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useIntegratedProgress } from '../hooks/useIntegratedProgress';

interface TodaysProgressData {
  habitsCompleted: number;
  totalHabits: number;
  completionRate: number;
  currentStreak: number;
  pointsToday: number;
  newAchievements: number;
}

interface TodaysProgressWidgetProps {
  onPress?: () => void;
  compact?: boolean;
}

export const TodaysProgressWidget: React.FC<TodaysProgressWidgetProps> = ({
  onPress,
  compact = false,
}) => {
  const { progressData: integratedData, loading } = useIntegratedProgress();

  // Transform integrated data to match component interface
  const progressData: TodaysProgressData = {
    habitsCompleted: integratedData?.habits.completed || 0,
    totalHabits: integratedData?.habits.total || 0,
    completionRate: integratedData?.overall.dailyScore || 0,
    currentStreak: integratedData?.overall.overallStreak || 0,
    pointsToday: 0, // Will be calculated from daily activities
    newAchievements: 0, // Will be calculated from today's achievements
  };

  const getMotivationalMessage = (): string => {
    const { completionRate, currentStreak } = progressData;

    if (completionRate === 100) {
      return 'Perfect day! All habits completed! 🌟';
    } else if (completionRate >= 80) {
      return 'Great progress today! Keep it up! 💪';
    } else if (completionRate >= 50) {
      return "Good start! You're halfway there! 🚀";
    } else if (currentStreak > 0) {
      return `${currentStreak} day streak! Don't break it! 🔥`;
    } else {
      return 'Every step counts! Start your journey! ✨';
    }
  };

  const getProgressColor = (): string => {
    const { completionRate } = progressData;
    if (completionRate >= 80) {
      return '#4CAF50';
    }
    if (completionRate >= 60) {
      return '#FF9800';
    }
    return '#F44336';
  };

  if (loading) {
    return (
      <Card style={[styles.container, compact && styles.compactContainer]}>
        <Card.Content style={styles.loadingContent}>
          <Text variant="bodyMedium">Loading today's progress...</Text>
        </Card.Content>
      </Card>
    );
  }

  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <CardComponent onPress={onPress} activeOpacity={0.7}>
      <Card style={[styles.container, compact && styles.compactContainer]}>
        <Card.Content
          style={[styles.content, compact && styles.compactContent]}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.titleContainer}>
              <Icon name="today" size={24} color="#2196F3" />
              <Text variant="titleMedium" style={styles.title}>
                Today's Progress
              </Text>
            </View>
            {onPress && <Icon name="chevron-right" size={24} color="#666" />}
          </View>

          {/* Main Progress */}
          <View style={styles.progressSection}>
            <View style={styles.progressHeader}>
              <Text
                variant="headlineMedium"
                style={[
                  styles.progressPercentage,
                  { color: getProgressColor() },
                ]}
              >
                {Math.round(progressData.completionRate)}%
              </Text>
              <Text variant="bodyMedium" style={styles.progressSubtext}>
                {progressData.habitsCompleted} of {progressData.totalHabits}{' '}
                habits
              </Text>
            </View>

            <ProgressBar
              progress={progressData.completionRate / 100}
              color={getProgressColor()}
              style={styles.progressBar}
            />
          </View>

          {/* Stats Row */}
          {!compact && (
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Icon name="local-fire-department" size={20} color="#FF5722" />
                <Text variant="bodySmall" style={styles.statLabel}>
                  Streak
                </Text>
                <Text variant="titleSmall" style={styles.statValue}>
                  {progressData.currentStreak}
                </Text>
              </View>

              <View style={styles.statItem}>
                <Icon name="star" size={20} color="#FF9800" />
                <Text variant="bodySmall" style={styles.statLabel}>
                  Points
                </Text>
                <Text variant="titleSmall" style={styles.statValue}>
                  +{progressData.pointsToday}
                </Text>
              </View>

              <View style={styles.statItem}>
                <Icon name="emoji-events" size={20} color="#4CAF50" />
                <Text variant="bodySmall" style={styles.statLabel}>
                  New
                </Text>
                <Text variant="titleSmall" style={styles.statValue}>
                  {progressData.newAchievements}
                </Text>
              </View>
            </View>
          )}

          {/* Motivational Message */}
          <View style={styles.motivationContainer}>
            <Text variant="bodyMedium" style={styles.motivationText}>
              {getMotivationalMessage()}
            </Text>
          </View>

          {/* Quick Stats Chips (Compact Mode) */}
          {compact && (
            <View style={styles.compactStats}>
              <Chip
                icon="local-fire-department"
                compact
                style={styles.compactChip}
              >
                {progressData.currentStreak} streak
              </Chip>
              {progressData.pointsToday > 0 && (
                <Chip icon="star" compact style={styles.compactChip}>
                  +{progressData.pointsToday} pts
                </Chip>
              )}
              {progressData.newAchievements > 0 && (
                <Chip icon="emoji-events" compact style={styles.compactChip}>
                  {progressData.newAchievements} new
                </Chip>
              )}
            </View>
          )}
        </Card.Content>
      </Card>
    </CardComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    backgroundColor: '#fff',
    elevation: 4,
  },
  compactContainer: {
    margin: 12,
  },
  content: {
    padding: 16,
  },
  compactContent: {
    padding: 12,
  },
  loadingContent: {
    padding: 24,
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontWeight: '600',
    color: '#333',
  },
  progressSection: {
    marginBottom: 16,
  },
  progressHeader: {
    alignItems: 'center',
    marginBottom: 12,
  },
  progressPercentage: {
    fontWeight: '700',
    fontSize: 32,
  },
  progressSubtext: {
    color: '#666',
    marginTop: 4,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
    paddingVertical: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statLabel: {
    color: '#666',
    marginTop: 4,
    marginBottom: 2,
  },
  statValue: {
    fontWeight: '600',
    color: '#333',
  },
  motivationContainer: {
    backgroundColor: '#e3f2fd',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  motivationText: {
    color: '#1976d2',
    textAlign: 'center',
    fontWeight: '500',
  },
  compactStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginTop: 8,
  },
  compactChip: {
    backgroundColor: '#f0f0f0',
  },
});

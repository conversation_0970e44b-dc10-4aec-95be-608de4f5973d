import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Text,
  Card,
  Switch,
  Button,
  Divider,
  ProgressBar,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { aiLearningBackendService } from '../services/aiLearningBackendService';
import { aiLearningInitService } from '../services/aiLearningInitService';
import { progressDataService } from '../services/progressDataService';

interface AILearningSettingsProps {
  onClose?: () => void;
}

export const AILearningSettings: React.FC<AILearningSettingsProps> = ({
  onClose,
}) => {
  const [settings, setSettings] = useState({
    shareCorrections: true,
    sharePatterns: true,
    enableSmartNotifications: true,
  });
  const [systemHealth, setSystemHealth] = useState<any>(null);
  const [learningInsights, setLearningInsights] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);

      // Load current settings
      const progressSettings = await progressDataService.getProgressSettings();
      setSettings({
        shareCorrections: progressSettings.shareWithAI,
        sharePatterns: progressSettings.shareWithAI,
        enableSmartNotifications:
          progressSettings.notificationPreference !== 'off',
      });

      // Load system health
      const health = await aiLearningInitService.checkSystemHealth();
      setSystemHealth(health);

      // Load learning insights
      const insights = await aiLearningInitService.getAILearningInsights();
      setLearningInsights(insights);
    } catch (error) {
      console.error('Failed to load AI learning settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSettingChange = async (setting: string, value: boolean) => {
    try {
      const newSettings = { ...settings, [setting]: value };
      setSettings(newSettings);

      // Update backend preferences
      await aiLearningBackendService.updateUserPreferences({
        shareCorrections: newSettings.shareCorrections,
        sharePatterns: newSettings.sharePatterns,
      });

      // Update local progress settings
      await progressDataService.updateProgressSettings({
        shareWithAI: newSettings.shareCorrections,
        notificationPreference: newSettings.enableSmartNotifications
          ? 'smart'
          : 'off',
      });

      // Show confirmation
      Alert.alert(
        'Settings Updated',
        'Your AI learning preferences have been saved.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Failed to update settings:', error);
      Alert.alert(
        'Update Failed',
        'Could not save your preferences. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleForceReinitialize = async () => {
    Alert.alert(
      'Reinitialize AI Learning',
      'This will reset the AI learning system. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reinitialize',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              await aiLearningInitService.forceReinitialization();
              await loadSettings();
              Alert.alert(
                'Success',
                'AI learning system has been reinitialized.'
              );
            } catch (error) {
              Alert.alert(
                'Error',
                'Failed to reinitialize AI learning system.'
              );
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return { name: 'check-circle', color: '#4CAF50' };
      case 'warning':
        return { name: 'alert-circle', color: '#FF9800' };
      case 'error':
        return { name: 'close-circle', color: '#F44336' };
      default:
        return { name: 'help-circle', color: '#666' };
    }
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text variant="titleMedium">Loading AI Learning Settings...</Text>
          <ProgressBar indeterminate style={styles.progressBar} />
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <View style={styles.header}>
        <Icon name="robot" size={48} color="#4CAF50" />
        <Text variant="headlineSmall" style={styles.title}>
          AI Learning Settings
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Control how your data helps improve PlateMotion for everyone
        </Text>
      </View>

      {/* System Health Status */}
      {systemHealth && (
        <Card style={styles.healthCard}>
          <Card.Content>
            <View style={styles.healthHeader}>
              <Text variant="titleMedium">System Health</Text>
              <View style={styles.healthStatus}>
                <Icon
                  name={getHealthStatusIcon(systemHealth.overall).name}
                  size={24}
                  color={getHealthStatusIcon(systemHealth.overall).color}
                />
                <Text
                  variant="bodyMedium"
                  style={{
                    color: getHealthStatusIcon(systemHealth.overall).color,
                  }}
                >
                  {systemHealth.overall.toUpperCase()}
                </Text>
              </View>
            </View>

            <View style={styles.healthDetails}>
              <View style={styles.healthItem}>
                <Text variant="bodySmall">Database</Text>
                <Icon
                  name={systemHealth.database ? 'check' : 'close'}
                  size={16}
                  color={systemHealth.database ? '#4CAF50' : '#F44336'}
                />
              </View>
              <View style={styles.healthItem}>
                <Text variant="bodySmall">Vector DB</Text>
                <Icon
                  name={systemHealth.vectorDatabase ? 'check' : 'close'}
                  size={16}
                  color={systemHealth.vectorDatabase ? '#4CAF50' : '#F44336'}
                />
              </View>
              <View style={styles.healthItem}>
                <Text variant="bodySmall">Notifications</Text>
                <Icon
                  name={systemHealth.notifications ? 'check' : 'close'}
                  size={16}
                  color={systemHealth.notifications ? '#4CAF50' : '#F44336'}
                />
              </View>
              <View style={styles.healthItem}>
                <Text variant="bodySmall">Learning Active</Text>
                <Icon
                  name={systemHealth.learningActive ? 'check' : 'close'}
                  size={16}
                  color={systemHealth.learningActive ? '#4CAF50' : '#F44336'}
                />
              </View>
            </View>

            {systemHealth.totalCorrections > 0 && (
              <Text variant="bodySmall" style={styles.statsText}>
                Total Corrections: {systemHealth.totalCorrections}
              </Text>
            )}
          </Card.Content>
        </Card>
      )}

      {/* Privacy Settings */}
      <Card style={styles.settingsCard}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Privacy & Data Sharing
          </Text>

          <View style={styles.settingItem}>
            <View style={styles.settingText}>
              <Text variant="bodyMedium">Share Food Corrections</Text>
              <Text variant="bodySmall" style={styles.settingDescription}>
                Help improve AI food recognition for everyone by sharing your
                corrections anonymously
              </Text>
            </View>
            <Switch
              value={settings.shareCorrections}
              onValueChange={value =>
                handleSettingChange('shareCorrections', value)
              }
              color="#4CAF50"
            />
          </View>

          <Divider style={styles.divider} />

          <View style={styles.settingItem}>
            <View style={styles.settingText}>
              <Text variant="bodyMedium">Share Usage Patterns</Text>
              <Text variant="bodySmall" style={styles.settingDescription}>
                Share anonymous usage patterns to improve meal recommendations
              </Text>
            </View>
            <Switch
              value={settings.sharePatterns}
              onValueChange={value =>
                handleSettingChange('sharePatterns', value)
              }
              color="#4CAF50"
            />
          </View>

          <Divider style={styles.divider} />

          <View style={styles.settingItem}>
            <View style={styles.settingText}>
              <Text variant="bodyMedium">Smart Notifications</Text>
              <Text variant="bodySmall" style={styles.settingDescription}>
                Enable AI-powered notification timing based on your habits
              </Text>
            </View>
            <Switch
              value={settings.enableSmartNotifications}
              onValueChange={value =>
                handleSettingChange('enableSmartNotifications', value)
              }
              color="#4CAF50"
            />
          </View>
        </Card.Content>
      </Card>

      {/* Learning Insights */}
      {learningInsights && (
        <Card style={styles.insightsCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Learning Insights
            </Text>

            <View style={styles.insightItem}>
              <Icon name="chart-line" size={20} color="#4CAF50" />
              <Text variant="bodyMedium">
                System Status:{' '}
                {learningInsights.insights?.systemStatus || 'Unknown'}
              </Text>
            </View>

            <View style={styles.insightItem}>
              <Icon name="database" size={20} color="#4CAF50" />
              <Text variant="bodyMedium">
                Total Corrections:{' '}
                {learningInsights.insights?.totalCorrections || 0}
              </Text>
            </View>

            {learningInsights.insights?.lastActivity && (
              <View style={styles.insightItem}>
                <Icon name="clock" size={20} color="#4CAF50" />
                <Text variant="bodyMedium">
                  Last Activity: {learningInsights.insights.lastActivity}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>
      )}

      {/* Advanced Actions */}
      <Card style={styles.actionsCard}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Advanced
          </Text>

          <Button
            mode="outlined"
            onPress={handleForceReinitialize}
            style={styles.actionButton}
            icon="refresh"
          >
            Reinitialize AI Learning
          </Button>

          <Text variant="bodySmall" style={styles.actionDescription}>
            Use this if you're experiencing issues with AI learning features
          </Text>
        </Card.Content>
      </Card>

      {onClose && (
        <Button mode="contained" onPress={onClose} style={styles.closeButton}>
          Close
        </Button>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    padding: 16,
    paddingBottom: 32,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  progressBar: {
    marginTop: 16,
    width: 200,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    marginTop: 12,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    textAlign: 'center',
    color: '#666',
  },
  healthCard: {
    marginBottom: 16,
    elevation: 2,
  },
  healthHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  healthStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  healthDetails: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
  },
  healthItem: {
    alignItems: 'center',
    gap: 4,
  },
  statsText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
  },
  settingsCard: {
    marginBottom: 16,
    elevation: 2,
  },
  sectionTitle: {
    marginBottom: 16,
    color: '#333',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  settingText: {
    flex: 1,
    marginRight: 16,
  },
  settingDescription: {
    color: '#666',
    marginTop: 4,
  },
  divider: {
    marginVertical: 12,
  },
  insightsCard: {
    marginBottom: 16,
    elevation: 2,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  actionsCard: {
    marginBottom: 16,
    elevation: 2,
  },
  actionButton: {
    marginBottom: 8,
  },
  actionDescription: {
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  closeButton: {
    marginTop: 16,
    borderRadius: 12,
  },
});

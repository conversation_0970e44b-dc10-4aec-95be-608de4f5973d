import { format } from 'date-fns';
import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Modal,
  Portal,
  Text,
  Button,
  Card,
  ProgressBar,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { AchievementProgress } from '../services/achievementService';

interface AchievementModalProps {
  visible: boolean;
  achievement: AchievementProgress | null;
  onDismiss: () => void;
}

export const AchievementModal: React.FC<AchievementModalProps> = ({
  visible,
  achievement,
  onDismiss,
}) => {
  if (!achievement) {
    return null;
  }

  const { achievement: achievementData, progress, unlocked_at } = achievement;
  const isUnlocked = !!unlocked_at;
  const progressPercentage = Math.round(
    (progress / achievementData.criteria_value) * 100
  );

  const getRarityColor = (rarity: string): string => {
    switch (rarity) {
      case 'common':
        return '#4CAF50';
      case 'rare':
        return '#2196F3';
      case 'epic':
        return '#9C27B0';
      case 'legendary':
        return '#FF9800';
      default:
        return '#757575';
    }
  };

  const getRarityLabel = (rarity: string): string => {
    return rarity.charAt(0).toUpperCase() + rarity.slice(1);
  };

  const getCategoryIcon = (category: string): string => {
    switch (category) {
      case 'habit':
        return 'check-circle';
      case 'nutrition':
        return 'restaurant';
      case 'exercise':
        return 'fitness-center';
      case 'milestone':
        return 'flag';
      default:
        return 'star';
    }
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={styles.modalContainer}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          <Card style={styles.card}>
            <Card.Content style={styles.content}>
              {/* Header */}
              <View style={styles.header}>
                <View
                  style={[
                    styles.iconContainer,
                    {
                      backgroundColor: `${getRarityColor(achievementData.rarity)}20`,
                    },
                  ]}
                >
                  <Icon
                    name={getCategoryIcon(achievementData.category)}
                    size={32}
                    color={getRarityColor(achievementData.rarity)}
                  />
                </View>
                <View style={styles.headerText}>
                  <Text variant="headlineSmall" style={styles.title}>
                    {achievementData.name}
                  </Text>
                  <View style={styles.rarityContainer}>
                    <Text
                      variant="bodyMedium"
                      style={[
                        styles.rarity,
                        { color: getRarityColor(achievementData.rarity) },
                      ]}
                    >
                      {getRarityLabel(achievementData.rarity)}
                    </Text>
                    <Text variant="bodyMedium" style={styles.points}>
                      {achievementData.points} pts
                    </Text>
                  </View>
                </View>
              </View>

              {/* Status Badge */}
              <View
                style={[
                  styles.statusBadge,
                  isUnlocked
                    ? styles.statusBadgeUnlocked
                    : styles.statusBadgeLocked,
                ]}
              >
                <Icon
                  name={isUnlocked ? 'check-circle' : 'lock'}
                  size={16}
                  color="#fff"
                />
                <Text variant="bodySmall" style={styles.statusText}>
                  {isUnlocked ? 'Unlocked' : 'Locked'}
                </Text>
              </View>

              {/* Description */}
              <Text variant="bodyLarge" style={styles.description}>
                {achievementData.description}
              </Text>

              {/* Progress Section */}
              <View style={styles.progressSection}>
                <View style={styles.progressHeader}>
                  <Text variant="titleMedium" style={styles.progressTitle}>
                    Progress
                  </Text>
                  <Text variant="bodyMedium" style={styles.progressText}>
                    {progress} / {achievementData.criteria_value}
                  </Text>
                </View>

                <ProgressBar
                  progress={Math.min(
                    1,
                    progress / achievementData.criteria_value
                  )}
                  color={getRarityColor(achievementData.rarity)}
                  style={styles.progressBar}
                />

                <Text variant="bodySmall" style={styles.progressPercentage}>
                  {progressPercentage}% Complete
                </Text>
              </View>

              {/* Criteria */}
              <View style={styles.criteriaSection}>
                <Text variant="titleMedium" style={styles.criteriaTitle}>
                  Requirements
                </Text>
                <Text variant="bodyMedium" style={styles.criteriaText}>
                  {achievementData.criteria_type}:{' '}
                  {achievementData.criteria_value}
                </Text>
              </View>

              {/* Unlock Date */}
              {isUnlocked && unlocked_at && (
                <View style={styles.unlockedSection}>
                  <Icon name="event" size={20} color="#666" />
                  <Text variant="bodyMedium" style={styles.unlockedText}>
                    Unlocked on {format(new Date(unlocked_at), 'MMMM d, yyyy')}
                  </Text>
                </View>
              )}

              {/* Tips for locked achievements */}
              {!isUnlocked && (
                <View style={styles.tipsSection}>
                  <Text variant="titleMedium" style={styles.tipsTitle}>
                    💡 Tips to Unlock
                  </Text>
                  <Text variant="bodyMedium" style={styles.tipsText}>
                    {getUnlockTips(
                      achievementData.category,
                      achievementData.criteria_type
                    )}
                  </Text>
                </View>
              )}

              {/* Action Button */}
              <Button
                mode="contained"
                onPress={onDismiss}
                style={[
                  styles.button,
                  { backgroundColor: getRarityColor(achievementData.rarity) },
                ]}
              >
                {isUnlocked ? 'Awesome!' : 'Got it!'}
              </Button>
            </Card.Content>
          </Card>
        </ScrollView>
      </Modal>
    </Portal>
  );
};

// Helper function for unlock tips
function getUnlockTips(category: string, criteriaType: string): string {
  switch (category) {
    case 'habit':
      if (criteriaType.includes('streak')) {
        return 'Complete your habits consistently every day to build longer streaks!';
      }
      if (criteriaType.includes('completion')) {
        return 'Focus on completing all your daily habits to improve your completion rate.';
      }
      return 'Keep building and maintaining your healthy habits!';

    case 'nutrition':
      return 'Log your meals and track your nutrition goals consistently.';

    case 'exercise':
      return 'Stay active and log your workouts regularly.';

    case 'milestone':
      return 'Keep using the app and tracking your progress to reach this milestone!';

    default:
      return 'Continue your wellness journey to unlock this achievement!';
  }
}

const styles = StyleSheet.create({
  modalContainer: {
    margin: 20,
    maxHeight: '80%',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 16,
  },
  content: {
    padding: 24,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    color: '#333',
    marginBottom: 4,
  },
  rarityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  rarity: {
    fontWeight: '600',
    textTransform: 'uppercase',
    fontSize: 12,
  },
  points: {
    color: '#666',
    fontWeight: '500',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 16,
    gap: 6,
  },
  statusText: {
    color: '#fff',
    fontWeight: '600',
  },
  description: {
    color: '#666',
    lineHeight: 24,
    marginBottom: 24,
  },
  progressSection: {
    marginBottom: 24,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressTitle: {
    fontWeight: '600',
    color: '#333',
  },
  progressText: {
    color: '#666',
    fontWeight: '500',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    backgroundColor: '#f0f0f0',
    marginBottom: 8,
  },
  progressPercentage: {
    color: '#666',
    textAlign: 'center',
  },
  criteriaSection: {
    marginBottom: 24,
  },
  criteriaTitle: {
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  criteriaText: {
    color: '#666',
    textTransform: 'capitalize',
  },
  unlockedSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    gap: 8,
  },
  unlockedText: {
    color: '#666',
  },
  tipsSection: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  tipsTitle: {
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  tipsText: {
    color: '#666',
    lineHeight: 20,
  },
  button: {
    borderRadius: 12,
    paddingVertical: 4,
  },
  statusBadgeUnlocked: {
    backgroundColor: '#4CAF50',
  },
  statusBadgeLocked: {
    backgroundColor: '#FF9800',
  },
});

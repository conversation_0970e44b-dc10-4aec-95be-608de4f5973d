import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import { Card, Text, Button } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Achievement } from '../services/achievementService';

interface AchievementNotificationProps {
  achievement: Achievement | null;
  visible: boolean;
  onDismiss: () => void;
  onViewDetails?: () => void;
}

const { width: screenWidth } = Dimensions.get('window');

export const AchievementNotification: React.FC<
  AchievementNotificationProps
> = ({ achievement, visible, onDismiss, onViewDetails }) => {
  const slideAnim = useRef(new Animated.Value(-screenWidth)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible && achievement) {
      // Slide in animation
      Animated.parallel([
        Animated.spring(slideAnim, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      // Auto dismiss after 5 seconds
      const timer = setTimeout(() => {
        handleDismiss();
      }, 5000);

      return () => clearTimeout(timer);
    } else {
      handleDismiss();
    }
  }, [visible, achievement]);

  const handleDismiss = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: -screenWidth,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onDismiss();
    });
  };

  const getRarityColor = (rarity: string): string => {
    switch (rarity) {
      case 'common':
        return '#4CAF50';
      case 'rare':
        return '#2196F3';
      case 'epic':
        return '#9C27B0';
      case 'legendary':
        return '#FF9800';
      default:
        return '#757575';
    }
  };

  const getRarityEmoji = (rarity: string): string => {
    switch (rarity) {
      case 'common':
        return '🌟';
      case 'rare':
        return '💎';
      case 'epic':
        return '🏆';
      case 'legendary':
        return '👑';
      default:
        return '⭐';
    }
  };

  const getCategoryIcon = (category: string): string => {
    switch (category) {
      case 'habit':
        return 'check-circle';
      case 'nutrition':
        return 'restaurant';
      case 'exercise':
        return 'fitness-center';
      case 'milestone':
        return 'flag';
      default:
        return 'star';
    }
  };

  if (!visible || !achievement) {
    return null;
  }

  return (
    <View style={styles.overlay}>
      <Animated.View
        style={[
          styles.container,
          {
            transform: [{ translateX: slideAnim }, { scale: scaleAnim }],
            opacity: opacityAnim,
          },
        ]}
      >
        <Card
          style={[
            styles.card,
            { borderLeftColor: getRarityColor(achievement.rarity) },
          ]}
        >
          <Card.Content style={styles.content}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.celebrationContainer}>
                <Text style={styles.celebrationText}>
                  🎉 Achievement Unlocked! {getRarityEmoji(achievement.rarity)}
                </Text>
              </View>
              <Button
                mode="text"
                onPress={handleDismiss}
                icon="close"
                compact
                style={styles.closeButton}
              >
                Close
              </Button>
            </View>

            {/* Achievement Info */}
            <View style={styles.achievementInfo}>
              <View
                style={[
                  styles.iconContainer,
                  {
                    backgroundColor: `${getRarityColor(achievement.rarity)}20`,
                  },
                ]}
              >
                <Icon
                  name={getCategoryIcon(achievement.category)}
                  size={32}
                  color={getRarityColor(achievement.rarity)}
                />
              </View>

              <View style={styles.textContainer}>
                <Text variant="titleMedium" style={styles.achievementName}>
                  {achievement.name}
                </Text>
                <Text
                  variant="bodyMedium"
                  style={styles.achievementDescription}
                >
                  {achievement.description}
                </Text>
                <View style={styles.rewardInfo}>
                  <Text
                    variant="bodySmall"
                    style={[
                      styles.rarity,
                      { color: getRarityColor(achievement.rarity) },
                    ]}
                  >
                    {achievement.rarity.toUpperCase()}
                  </Text>
                  <Text variant="bodySmall" style={styles.points}>
                    +{achievement.points} points
                  </Text>
                </View>
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.actions}>
              <Button
                mode="outlined"
                onPress={handleDismiss}
                style={styles.actionButton}
                compact
              >
                Dismiss
              </Button>
              {onViewDetails && (
                <Button
                  mode="contained"
                  onPress={() => {
                    onViewDetails();
                    handleDismiss();
                  }}
                  style={[
                    styles.actionButton,
                    { backgroundColor: getRarityColor(achievement.rarity) },
                  ]}
                  compact
                >
                  View Details
                </Button>
              )}
            </View>
          </Card.Content>
        </Card>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  container: {
    width: screenWidth - 32,
    maxWidth: 400,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 16,
    borderLeftWidth: 6,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  content: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  celebrationContainer: {
    flex: 1,
  },
  celebrationText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
  },
  closeButton: {
    margin: 0,
  },
  achievementInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
  },
  achievementName: {
    fontWeight: '700',
    color: '#333',
    marginBottom: 4,
  },
  achievementDescription: {
    color: '#666',
    lineHeight: 20,
    marginBottom: 8,
  },
  rewardInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  rarity: {
    fontWeight: '600',
    fontSize: 12,
    textTransform: 'uppercase',
  },
  points: {
    color: '#666',
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
  },
  actionButton: {
    borderRadius: 8,
  },
});

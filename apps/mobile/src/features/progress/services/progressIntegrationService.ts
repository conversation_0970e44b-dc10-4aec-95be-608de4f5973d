import { format } from 'date-fns';
import { supabase } from '../../../lib/supabase';
import { achievementService } from './achievementService';
import { achievementTriggerService } from './achievementTriggerService';
import { analyticsService } from './analyticsService';
import { streakService } from './streakService';

export interface IntegratedProgressData {
  habits: {
    completed: number;
    total: number;
    completionRate: number;
    currentStreak: number;
    longestStreak: number;
  };
  nutrition: {
    mealsLogged: number;
    targetMeals: number;
    completionRate: number;
    caloriesTracked: boolean;
    macrosOnTrack: boolean;
  };
  exercise: {
    workoutsCompleted: number;
    targetWorkouts: number;
    completionRate: number;
    minutesActive: number;
    targetMinutes: number;
  };
  overall: {
    totalPoints: number;
    dailyScore: number;
    weeklyScore: number;
    achievements: number;
    overallStreak: number;
  };
}

export interface ProgressSyncResult {
  success: boolean;
  updatedMetrics: string[];
  newAchievements: any[];
  errors: string[];
}

class ProgressIntegrationService {
  // ==================== REAL-TIME PROGRESS TRACKING ====================

  async getIntegratedProgressData(
    date: Date = new Date()
  ): Promise<IntegratedProgressData> {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) {
        throw new Error('User not authenticated');
      }

      const dateStr = format(date, 'yyyy-MM-dd');

      // Get habits data
      const habitsData = await this.getHabitsProgress(user.user.id, dateStr);

      // Get nutrition data
      const nutritionData = await this.getNutritionProgress(
        user.user.id,
        dateStr
      );

      // Get exercise data
      const exerciseData = await this.getExerciseProgress(
        user.user.id,
        dateStr
      );

      // Calculate overall metrics
      const overallData = await this.getOverallProgress(user.user.id, dateStr);

      return {
        habits: habitsData,
        nutrition: nutritionData,
        exercise: exerciseData,
        overall: overallData,
      };
    } catch (error) {
      console.error('Error getting integrated progress data:', error);
      throw error;
    }
  }

  // ==================== HABITS PROGRESS ====================

  private async getHabitsProgress(userId: string, date: string) {
    // Get total habits for the user
    const { data: habits } = await supabase
      .from('habits')
      .select('id, current_streak')
      .eq('user_id', userId)
      .eq('is_active', true);

    // Get completed habits for the date
    const { data: completions } = await supabase
      .from('habit_completions')
      .select('habit_id')
      .eq('user_id', userId)
      .eq('completion_date', date);

    const total = habits?.length || 0;
    const completed = completions?.length || 0;
    const completionRate =
      total > 0 ? Math.round((completed / total) * 100) : 0;

    // Get streak data
    const currentStreak = Math.max(
      ...(habits?.map(h => h.current_streak) || [0])
    );
    const streakSummary = await streakService.getStreakSummary();

    return {
      completed,
      total,
      completionRate,
      currentStreak,
      longestStreak: streakSummary.longest_all_time_streak,
    };
  }

  // ==================== NUTRITION PROGRESS ====================

  private async getNutritionProgress(_userId: string, _date: string) {
    // TODO: Integrate with existing nutrition tracking
    // For now, return mock data structure
    return {
      mealsLogged: 2,
      targetMeals: 3,
      completionRate: 67,
      caloriesTracked: true,
      macrosOnTrack: false,
    };
  }

  // ==================== EXERCISE PROGRESS ====================

  private async getExerciseProgress(_userId: string, _date: string) {
    // TODO: Integrate with existing exercise tracking
    // For now, return mock data structure
    return {
      workoutsCompleted: 1,
      targetWorkouts: 1,
      completionRate: 100,
      minutesActive: 45,
      targetMinutes: 30,
    };
  }

  // ==================== OVERALL PROGRESS ====================

  private async getOverallProgress(userId: string, date: string) {
    // Get achievements count
    const achievements = await achievementService.getUserAchievements();
    const unlockedCount = achievements.filter(a => a.unlocked_at).length;

    // Calculate total points from achievements
    const totalPoints = achievements
      .filter(a => a.unlocked_at)
      .reduce((sum, a) => sum + (a.achievement?.points || 0), 0);

    // Get overall streak (simplified calculation)
    const streakSummary = await streakService.getStreakSummary();

    // Calculate daily score (0-100 based on all activities)
    const habitsProgress = await this.getHabitsProgress(userId, date);
    const nutritionProgress = await this.getNutritionProgress(userId, date);
    const exerciseProgress = await this.getExerciseProgress(userId, date);

    const dailyScore = Math.round(
      habitsProgress.completionRate * 0.4 +
        nutritionProgress.completionRate * 0.3 +
        exerciseProgress.completionRate * 0.3
    );

    // Calculate weekly score (average of last 7 days)
    const weeklyScore = await this.calculateWeeklyScore(userId);

    return {
      totalPoints,
      dailyScore,
      weeklyScore,
      achievements: unlockedCount,
      overallStreak: streakSummary.longest_current_streak,
    };
  }

  // ==================== SYNCHRONIZATION ====================

  async syncProgressData(date: Date = new Date()): Promise<ProgressSyncResult> {
    const result: ProgressSyncResult = {
      success: true,
      updatedMetrics: [],
      newAchievements: [],
      errors: [],
    };

    try {
      const dateStr = format(date, 'yyyy-MM-dd');

      // Update daily metrics
      await analyticsService.calculateDailyMetrics(dateStr);
      result.updatedMetrics.push('daily_metrics');

      // Update streak data
      await this.updateAllStreaks(dateStr);
      result.updatedMetrics.push('streak_data');

      // Check for new achievements
      const newAchievements =
        await achievementService.checkAndUnlockAchievements();
      result.newAchievements = newAchievements;

      // Update progress metrics table
      await this.updateProgressMetricsTable(dateStr);
      result.updatedMetrics.push('progress_metrics');
    } catch (error) {
      console.error('Error syncing progress data:', error);
      result.success = false;
      result.errors.push(
        error instanceof Error ? error.message : 'Unknown error'
      );
    }

    return result;
  }

  // ==================== REAL-TIME UPDATES ====================

  async onHabitCompleted(
    habitId: string,
    userId: string,
    date: Date = new Date()
  ) {
    try {
      const dateStr = format(date, 'yyyy-MM-dd');

      // Update habit streak
      await streakService.updateHabitStreak(habitId, 'habit', dateStr);

      // Trigger achievement check
      await achievementTriggerService.onHabitCompleted(habitId, 1);

      // Update daily metrics
      await analyticsService.calculateDailyMetrics(dateStr);

      // Sync overall progress
      await this.syncProgressData(date);
    } catch (error) {
      console.error('Error handling habit completion:', error);
    }
  }

  async onNutritionLogged(
    mealData: any,
    userId: string,
    date: Date = new Date()
  ) {
    try {
      const dateStr = format(date, 'yyyy-MM-dd');

      // Update daily metrics
      await analyticsService.calculateDailyMetrics(dateStr);

      // Sync overall progress
      await this.syncProgressData(date);
    } catch (error) {
      console.error('Error handling nutrition logging:', error);
    }
  }

  async onWorkoutCompleted(
    workoutData: any,
    userId: string,
    date: Date = new Date()
  ) {
    try {
      const dateStr = format(date, 'yyyy-MM-dd');

      // Update daily metrics
      await analyticsService.calculateDailyMetrics(dateStr);

      // Sync overall progress
      await this.syncProgressData(date);
    } catch (error) {
      console.error('Error handling workout completion:', error);
    }
  }

  // ==================== HELPER METHODS ====================

  private async updateAllStreaks(date: string) {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) {
        return;
      }

      // Update habit streaks
      const { data: habits } = await supabase
        .from('habits')
        .select('id')
        .eq('user_id', user.user.id)
        .eq('is_active', true);

      if (habits) {
        for (const habit of habits) {
          await streakService.updateHabitStreak(habit.id, 'habit', date);
        }
      }

      // TODO: Update nutrition and exercise streaks
      // await streakService.updateNutritionStreak(date);
      // await streakService.updateExerciseStreak(date);
    } catch (error) {
      console.error('Error updating streaks:', error);
    }
  }

  private async updateProgressMetricsTable(date: string) {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) {
        return;
      }

      const progressData = await this.getIntegratedProgressData(new Date(date));

      // Upsert progress metrics
      await supabase.from('progress_metrics').upsert({
        user_id: user.user.id,
        date,
        metric_type: 'daily',
        completed_habits: progressData.habits.completed,
        total_habits: progressData.habits.total,
        completion_rate: progressData.overall.dailyScore,
        points_earned: 0, // Will be calculated based on daily activities
        streak_count: progressData.overall.overallStreak,
      });
    } catch (error) {
      console.error('Error updating progress metrics table:', error);
    }
  }

  private async calculateWeeklyScore(_userId: string): Promise<number> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 6);

      const metrics = await analyticsService.getProgressMetrics(
        format(startDate, 'yyyy-MM-dd'),
        format(endDate, 'yyyy-MM-dd'),
        'daily'
      );

      if (metrics.length === 0) {
        return 0;
      }

      const averageScore =
        metrics.reduce((sum, m) => {
          const completionRate =
            m.habits_total > 0
              ? (m.habits_completed / m.habits_total) * 100
              : 0;
          return sum + completionRate;
        }, 0) / metrics.length;
      return Math.round(averageScore);
    } catch (error) {
      console.error('Error calculating weekly score:', error);
      return 0;
    }
  }
}

export const progressIntegrationService = new ProgressIntegrationService();

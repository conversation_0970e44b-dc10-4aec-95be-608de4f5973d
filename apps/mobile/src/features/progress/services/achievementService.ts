import { supabase } from '../../../lib/supabase';
import { analyticsService } from './analyticsService';

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'habit' | 'nutrition' | 'exercise' | 'milestone';
  criteria: Record<string, any>;
  criteria_type: string;
  criteria_value: number;
  points: number;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  created_at: string;
}

export interface UserAchievement {
  id: string;
  user_id: string;
  achievement_id: string;
  unlocked_at: string;
  progress: Record<string, any>;
  achievement?: Achievement;
}

export interface AchievementProgress {
  achievement: Achievement;
  current_progress: number;
  target_progress: number;
  progress: number;
  percentage: number;
  is_unlocked: boolean;
  unlocked_at?: string;
}

class AchievementService {
  // ==================== ACHIEVEMENT MANAGEMENT ====================

  async getAllAchievements(): Promise<Achievement[]> {
    const { data, error } = await supabase
      .from('achievements')
      .select('*')
      .order('category', { ascending: true })
      .order('points', { ascending: true });

    if (error) {
      throw error;
    }
    return data || [];
  }

  async getUserAchievements(): Promise<UserAchievement[]> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('user_achievements')
      .select(
        `
        *,
        achievement:achievements(*)
      `
      )
      .eq('user_id', user.id)
      .order('unlocked_at', { ascending: false });

    if (error) {
      throw error;
    }
    return data || [];
  }

  async getAchievementProgress(): Promise<AchievementProgress[]> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const allAchievements = await this.getAllAchievements();
    const userAchievements = await this.getUserAchievements();

    const unlockedAchievementIds = new Set(
      userAchievements.map(ua => ua.achievement_id)
    );

    const progressList: AchievementProgress[] = [];

    for (const achievement of allAchievements) {
      const isUnlocked = unlockedAchievementIds.has(achievement.id);
      const userAchievement = userAchievements.find(
        ua => ua.achievement_id === achievement.id
      );

      if (isUnlocked) {
        progressList.push({
          achievement,
          current_progress: 100,
          target_progress: 100,
          progress: 100,
          percentage: 100,
          is_unlocked: true,
          unlocked_at: userAchievement?.unlocked_at,
        });
      } else {
        const progress = await this.calculateAchievementProgress(achievement);
        progressList.push({
          achievement,
          current_progress: progress.current,
          target_progress: progress.target,
          progress: progress.current,
          percentage: Math.min(
            100,
            Math.round((progress.current / progress.target) * 100)
          ),
          is_unlocked: false,
        });
      }
    }

    return progressList.sort((a, b) => {
      // Sort by unlocked status, then by progress percentage
      if (a.is_unlocked !== b.is_unlocked) {
        return a.is_unlocked ? 1 : -1;
      }
      return b.percentage - a.percentage;
    });
  }

  // ==================== ACHIEVEMENT CHECKING ====================

  async checkAndUnlockAchievements(): Promise<UserAchievement[]> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const allAchievements = await this.getAllAchievements();
    const userAchievements = await this.getUserAchievements();

    const unlockedAchievementIds = new Set(
      userAchievements.map(ua => ua.achievement_id)
    );

    const newlyUnlocked: UserAchievement[] = [];

    for (const achievement of allAchievements) {
      if (unlockedAchievementIds.has(achievement.id)) {
        continue;
      }

      const isEligible = await this.checkAchievementEligibility(achievement);

      if (isEligible) {
        const newUserAchievement = await this.unlockAchievement(achievement.id);
        if (newUserAchievement) {
          newlyUnlocked.push(newUserAchievement);
        }
      }
    }

    return newlyUnlocked;
  }

  private async checkAchievementEligibility(
    achievement: Achievement
  ): Promise<boolean> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { criteria } = achievement;

    switch (criteria.type) {
      case 'habit_completion':
        return await this.checkHabitCompletionCriteria(criteria);

      case 'habit_streak':
        return await this.checkHabitStreakCriteria(criteria);

      case 'perfect_week':
        return await this.checkPerfectWeekCriteria(criteria);

      case 'habit_count':
        return await this.checkHabitCountCriteria(criteria);

      case 'meal_log':
        return await this.checkMealLogCriteria(criteria);

      case 'calorie_goal':
        return await this.checkCalorieGoalCriteria(criteria);

      case 'workout_completion':
        return await this.checkWorkoutCompletionCriteria(criteria);

      case 'weekly_workouts':
        return await this.checkWeeklyWorkoutsCriteria(criteria);

      case 'total_workouts':
        return await this.checkTotalWorkoutsCriteria(criteria);

      case 'days_active':
        return await this.checkDaysActiveCriteria(criteria);

      default:
        return false;
    }
  }

  private async checkHabitCompletionCriteria(criteria: any): Promise<boolean> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return false;
    }

    const { data } = await supabase
      .from('habit_completions')
      .select('id')
      .eq('user_id', user.id)
      .limit(criteria.count);

    return (data?.length || 0) >= criteria.count;
  }

  private async checkHabitStreakCriteria(criteria: any): Promise<boolean> {
    const streaks = await analyticsService.getStreakData('habit');
    const maxStreak = Math.max(...streaks.map(s => s.current_streak), 0);
    return maxStreak >= criteria.days;
  }

  private async checkPerfectWeekCriteria(criteria: any): Promise<boolean> {
    // Check if user completed all habits every day for the specified number of days
    const completionRates = await analyticsService.getHabitCompletionRates(
      criteria.days
    );
    return completionRates.every(rate => rate.completion_rate === 100);
  }

  private async checkHabitCountCriteria(criteria: any): Promise<boolean> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return false;
    }

    const { data } = await supabase
      .from('habits')
      .select('id')
      .eq('user_id', user.id)
      .eq('is_active', true);

    return (data?.length || 0) >= criteria.count;
  }

  private async checkMealLogCriteria(_criteria: any): Promise<boolean> {
    // TODO: Implement when nutrition logging is available
    return false;
  }

  private async checkCalorieGoalCriteria(_criteria: any): Promise<boolean> {
    // TODO: Implement when nutrition goals are available
    return false;
  }

  private async checkWorkoutCompletionCriteria(
    _criteria: any
  ): Promise<boolean> {
    // TODO: Implement when workout tracking is available
    return false;
  }

  private async checkWeeklyWorkoutsCriteria(_criteria: any): Promise<boolean> {
    // TODO: Implement when workout tracking is available
    return false;
  }

  private async checkTotalWorkoutsCriteria(_criteria: any): Promise<boolean> {
    // TODO: Implement when workout tracking is available
    return false;
  }

  private async checkDaysActiveCriteria(criteria: any): Promise<boolean> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return false;
    }

    // Count unique days with any activity (habit completions, workouts, meal logs)
    const { data } = await supabase
      .from('habit_completions')
      .select('completion_date')
      .eq('user_id', user.id);

    const uniqueDays = new Set(data?.map(d => d.completion_date) || []);
    return uniqueDays.size >= criteria.count;
  }

  // ==================== ACHIEVEMENT UNLOCKING ====================

  private async unlockAchievement(
    achievementId: string
  ): Promise<UserAchievement | null> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      const { data, error } = await supabase
        .from('user_achievements')
        .insert({
          user_id: user.id,
          achievement_id: achievementId,
          unlocked_at: new Date().toISOString(),
          progress: {},
        })
        .select(
          `
          *,
          achievement:achievements(*)
        `
        )
        .single();

      if (error) {
        throw error;
      }
      return data;
    } catch (error) {
      console.error('Error unlocking achievement:', error);
      return null;
    }
  }

  // ==================== PROGRESS CALCULATION ====================

  private async calculateAchievementProgress(
    achievement: Achievement
  ): Promise<{ current: number; target: number }> {
    const { criteria } = achievement;

    switch (criteria.type) {
      case 'habit_completion':
        const { data: completions } = await supabase
          .from('habit_completions')
          .select('id')
          .eq('user_id', (await supabase.auth.getUser()).data.user?.id);
        return { current: completions?.length || 0, target: criteria.count };

      case 'habit_streak':
        const streaks = await analyticsService.getStreakData('habit');
        const maxStreak = Math.max(...streaks.map(s => s.current_streak), 0);
        return { current: maxStreak, target: criteria.days };

      case 'habit_count':
        const { data: habits } = await supabase
          .from('habits')
          .select('id')
          .eq('user_id', (await supabase.auth.getUser()).data.user?.id)
          .eq('is_active', true);
        return { current: habits?.length || 0, target: criteria.count };

      case 'days_active':
        const { data: activeDays } = await supabase
          .from('habit_completions')
          .select('completion_date')
          .eq('user_id', (await supabase.auth.getUser()).data.user?.id);
        const uniqueDays = new Set(
          activeDays?.map(d => d.completion_date) || []
        );
        return { current: uniqueDays.size, target: criteria.count };

      default:
        return { current: 0, target: 1 };
    }
  }

  // ==================== UTILITY METHODS ====================

  async getTotalPoints(): Promise<number> {
    const userAchievements = await this.getUserAchievements();
    return userAchievements.reduce(
      (total, ua) => total + (ua.achievement?.points || 0),
      0
    );
  }

  async getAchievementsByCategory(
    category: string
  ): Promise<AchievementProgress[]> {
    const allProgress = await this.getAchievementProgress();
    return allProgress.filter(p => p.achievement.category === category);
  }

  async getRecentAchievements(limit: number = 5): Promise<UserAchievement[]> {
    const userAchievements = await this.getUserAchievements();
    return userAchievements.slice(0, limit);
  }
}

export const achievementService = new AchievementService();

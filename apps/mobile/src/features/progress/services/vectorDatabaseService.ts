import { supabase } from '../../../lib/supabase';

export interface FoodEmbedding {
  id?: string;
  food_name: string;
  category: string;
  embedding: number[];
  image_hash: string;
  image_features?: {
    dominant_colors: string[];
    texture_features: number[];
    shape_features: number[];
  };
  confidence_score: number;
  correction_count: number;
  success_rate: number;
}

export interface SimilarityMatch {
  food_name: string;
  category: string;
  similarity_score: number;
  confidence_boost: number;
  success_rate: number;
}

interface MatchedEmbedding {
  food_name: string;
  category: string;
  similarity: number;
  confidence_boost: number;
  success_rate: number;
}

interface CategoryStats {
  count: number;
  avg_success_rate: number;
}

interface EmbeddingStats {
  total_embeddings: number;
  avg_success_rate: number;
  category_breakdown: Record<string, CategoryStats>;
  total_corrections: number;
}

class VectorDatabaseService {
  /**
   * Generate embedding for food image using OpenAI or similar service
   */
  async generateFoodEmbedding(
    imageBase64: string,
    foodName: string
  ): Promise<number[]> {
    try {
      // For now, create a simple feature vector based on food name
      // In production, you'd use OpenAI's CLIP model or similar
      const embedding = await this.createSimpleEmbedding(foodName);
      return embedding;
    } catch (error) {
      console.error('Failed to generate embedding:', error);
      // Return zero vector as fallback
      return new Array(1536).fill(0);
    }
  }

  /**
   * Store food embedding in vector database
   */
  async storeFoodEmbedding(embedding: FoodEmbedding): Promise<boolean> {
    try {
      const { error } = await supabase.from('food_image_embeddings').insert({
        food_name: embedding.food_name,
        category: embedding.category,
        embedding: embedding.embedding,
        image_hash: embedding.image_hash,
        image_features: embedding.image_features,
        confidence_score: embedding.confidence_score,
        correction_count: embedding.correction_count,
        success_rate: embedding.success_rate,
      });

      if (error) {
        console.error('Failed to store embedding:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error storing embedding:', error);
      return false;
    }
  }

  /**
   * Find similar foods using vector similarity search
   */
  async findSimilarFoods(
    queryEmbedding: number[],
    limit: number = 5,
    threshold: number = 0.7
  ): Promise<SimilarityMatch[]> {
    try {
      // Use Supabase vector similarity search
      const { data, error } = await supabase.rpc('match_food_embeddings', {
        query_embedding: queryEmbedding,
        match_threshold: threshold,
        match_count: limit,
      });

      if (error) {
        console.error('Failed to find similar foods:', error);
        return [];
      }

      return (
        data?.map((item: MatchedEmbedding) => ({
          food_name: item.food_name,
          category: item.category,
          similarity_score: item.similarity,
          confidence_boost: this.calculateConfidenceBoost(
            item.similarity,
            item.success_rate
          ),
          success_rate: item.success_rate,
        })) ?? []
      );
    } catch (error) {
      console.error('Error finding similar foods:', error);
      return [];
    }
  }

  /**
   * Update embedding based on user correction
   */
  async updateEmbeddingFromCorrection(
    imageHash: string,
    wasCorrect: boolean,
    correctedFoodName?: string
  ): Promise<void> {
    try {
      interface EmbeddingUpdate {
  last_matched: string;
}

const updates: EmbeddingUpdate = {
        // For Supabase JS v2, there's no raw() helper on client; handle numeric updates in SQL function or do manual fetch-then-update.
        // As a minimal fix, we won't attempt arithmetic here; server should handle via triggers or RPC.
        last_matched: new Date().toISOString(),
      };

      if (!wasCorrect && correctedFoodName) {
        // Optionally: create a new embedding record for corrected food
        // Skipped here for brevity
      }

      const { error } = await supabase
        .from('food_image_embeddings')
        .update(updates)
        .eq('image_hash', imageHash);

      if (error) {
        console.error('Failed to update embedding:', error);
      }
    } catch (error) {
      console.error('Error updating embedding:', error);
    }
  }

  /**
   * Get embedding statistics for analytics
   */
  async getEmbeddingStats(): Promise<EmbeddingStats | null> {
    try {
      const { data, error } = await supabase
        .from('food_image_embeddings')
        .select('category, success_rate, correction_count')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Failed to get embedding stats:', error);
        return null;
      }

      // Calculate statistics
      const stats = {
        total_embeddings: data?.length ?? 0,
        avg_success_rate: 0,
        category_breakdown: {} as Record<string, CategoryStats>,
        total_corrections: 0,
      };

      if (data && data.length > 0) {
        stats.avg_success_rate =
          data.reduce((sum, item) => sum + item.success_rate, 0) / data.length;
        stats.total_corrections = data.reduce(
          (sum, item) => sum + item.correction_count,
          0
        );

        // Group by category
        data.forEach(item => {
          if (!stats.category_breakdown[item.category]) {
            stats.category_breakdown[item.category] = {
              count: 0,
              avg_success_rate: 0,
              total_corrections: 0,
            };
          }
          stats.category_breakdown[item.category].count++;
          stats.category_breakdown[item.category].total_corrections +=
            item.correction_count;
        });

        // Calculate averages for each category
        Object.keys(stats.category_breakdown).forEach(category => {
          const categoryData = data.filter(item => item.category === category);
          stats.category_breakdown[category].avg_success_rate =
            categoryData.reduce((sum, item) => sum + item.success_rate, 0) /
            categoryData.length;
        });
      }

      return stats;
    } catch (error) {
      console.error('Error getting embedding stats:', error);
      return null;
    }
  }

  /**
   * Batch process embeddings for improved performance
   */
  async batchProcessEmbeddings(embeddings: FoodEmbedding[]): Promise<number> {
    try {
      const { error } = await supabase.from('food_image_embeddings').insert(
        embeddings.map(embedding => ({
          food_name: embedding.food_name,
          category: embedding.category,
          embedding: embedding.embedding,
          image_hash: embedding.image_hash,
          image_features: embedding.image_features,
          confidence_score: embedding.confidence_score,
          correction_count: embedding.correction_count,
          success_rate: embedding.success_rate,
        }))
      );

      if (error) {
        console.error('Failed to batch process embeddings:', error);
        return 0;
      }

      // Supabase insert without select doesn't return data; return count
      return embeddings.length;
    } catch (error) {
      console.error('Error batch processing embeddings:', error);
      return 0;
    }
  }

  /**
   * Private helper methods
   */
  private async createSimpleEmbedding(foodName: string): Promise<number[]> {
    // Simple embedding based on food name characteristics
    // In production, use OpenAI CLIP or similar model
    const embedding = new Array(1536).fill(0);

    // Create basic features based on food name
    const nameHash = this.hashString(foodName);
    for (let i = 0; i < 100; i++) {
      embedding[i] = Math.sin(nameHash + i) * 0.1;
    }

    // Add category-based features
    const categoryFeatures = this.getCategoryFeatures(foodName);
    for (let i = 0; i < categoryFeatures.length && i < 50; i++) {
      embedding[100 + i] = categoryFeatures[i];
    }

    return embedding;
  }

  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = hash * 5 - hash + char; // Use multiplication instead of bitwise
      hash = Math.floor(hash); // Convert to integer
    }
    return hash;
  }

  private getCategoryFeatures(foodName: string): number[] {
    const features = new Array(50).fill(0);

    // Simple category detection based on keywords
    const proteinKeywords = [
      'chicken',
      'beef',
      'fish',
      'salmon',
      'tuna',
      'egg',
      'tofu',
    ];
    const carbKeywords = ['rice', 'bread', 'pasta', 'potato', 'quinoa', 'oats'];
    const vegKeywords = [
      'broccoli',
      'spinach',
      'carrot',
      'tomato',
      'lettuce',
      'pepper',
    ];
    const fruitKeywords = [
      'apple',
      'banana',
      'orange',
      'berry',
      'grape',
      'mango',
    ];

    const lowerName = foodName.toLowerCase();

    if (proteinKeywords.some(keyword => lowerName.includes(keyword))) {
      features[0] = 1.0; // Protein indicator
    }
    if (carbKeywords.some(keyword => lowerName.includes(keyword))) {
      features[1] = 1.0; // Carb indicator
    }
    if (vegKeywords.some(keyword => lowerName.includes(keyword))) {
      features[2] = 1.0; // Vegetable indicator
    }
    if (fruitKeywords.some(keyword => lowerName.includes(keyword))) {
      features[3] = 1.0; // Fruit indicator
    }

    return features;
  }

  private calculateConfidenceBoost(
    similarity: number,
    successRate: number
  ): number {
    // Calculate confidence boost based on similarity and historical success
    return Math.min(similarity * successRate * 0.2, 0.3); // Max boost of 30%
  }
}

export const vectorDatabaseService = new VectorDatabaseService();

import { supabase } from '../../../lib/supabase';

class DatabaseSetupService {
  /**
   * Deploy AI learning database schema to Supabase
   */
  async deployAILearningSchema(): Promise<boolean> {
    try {
      console.log('🚀 Deploying AI Learning Database Schema...');

      // Check if tables already exist
      const tablesExist = await this.checkTablesExist();
      if (tablesExist) {
        console.log('✅ AI Learning tables already exist');
        return true;
      }

      // Create tables step by step
      await this.createExtensions();
      await this.createAILearningCorrectionsTable();
      await this.createFoodRecognitionPatternsTable();
      await this.createFoodImageEmbeddingsTable();
      await this.createAILearningStatsTable();
      await this.createUserLearningPreferencesTable();
      await this.createFunctions();
      await this.createTriggers();
      await this.setupRLS();
      await this.seedInitialData();

      console.log('✅ AI Learning Database Schema deployed successfully!');
      return true;
    } catch (error) {
      console.error('❌ Failed to deploy AI Learning schema:', error);
      return false;
    }
  }

  /**
   * Check if AI learning tables exist
   */
  private async checkTablesExist(): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'ai_learning_corrections');

      return !error && data && data.length > 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * Create necessary extensions
   */
  private async createExtensions(): Promise<void> {
    console.log('📦 Creating extensions...');

    // Note: Vector extension might need to be enabled by Supabase admin
    const extensions = [
      'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";',
      // 'CREATE EXTENSION IF NOT EXISTS "vector";', // Uncomment when vector extension is available
    ];

    for (const sql of extensions) {
      try {
        await supabase.rpc('exec_sql', { sql });
      } catch (error) {
        console.warn('Extension creation warning:', error);
      }
    }
  }

  /**
   * Create AI learning corrections table
   */
  private async createAILearningCorrectionsTable(): Promise<void> {
    console.log('📊 Creating ai_learning_corrections table...');

    const sql = `
      CREATE TABLE IF NOT EXISTS ai_learning_corrections (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        original_food_name TEXT NOT NULL,
        original_category TEXT NOT NULL,
        original_portion TEXT,
        original_calories INTEGER,
        original_confidence FLOAT,
        corrected_food_name TEXT,
        corrected_category TEXT,
        corrected_portion TEXT,
        corrected_calories INTEGER,
        correction_type TEXT NOT NULL CHECK (correction_type IN ('name_change', 'category_change', 'portion_change', 'calorie_change', 'removal', 'addition')),
        analysis_id TEXT NOT NULL,
        image_hash TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      CREATE INDEX IF NOT EXISTS idx_corrections_food_name ON ai_learning_corrections(original_food_name);
      CREATE INDEX IF NOT EXISTS idx_corrections_category ON ai_learning_corrections(original_category);
      CREATE INDEX IF NOT EXISTS idx_corrections_type ON ai_learning_corrections(correction_type);
      CREATE INDEX IF NOT EXISTS idx_corrections_created_at ON ai_learning_corrections(created_at);
    `;

    await supabase.rpc('exec_sql', { sql });
  }

  /**
   * Create food recognition patterns table
   */
  private async createFoodRecognitionPatternsTable(): Promise<void> {
    console.log('🍎 Creating food_recognition_patterns table...');

    const sql = `
      CREATE TABLE IF NOT EXISTS food_recognition_patterns (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        food_name TEXT NOT NULL UNIQUE,
        alternative_names TEXT[] DEFAULT '{}',
        category TEXT NOT NULL,
        common_portions JSONB DEFAULT '{}',
        avg_calories_per_100g FLOAT,
        macro_profile JSONB,
        confidence_boost FLOAT DEFAULT 0.0,
        recognition_accuracy FLOAT DEFAULT 0.0,
        correction_count INTEGER DEFAULT 0,
        last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      CREATE INDEX IF NOT EXISTS idx_patterns_food_name ON food_recognition_patterns(food_name);
      CREATE INDEX IF NOT EXISTS idx_patterns_category ON food_recognition_patterns(category);
      CREATE INDEX IF NOT EXISTS idx_patterns_accuracy ON food_recognition_patterns(recognition_accuracy);
    `;

    await supabase.rpc('exec_sql', { sql });
  }

  /**
   * Create food image embeddings table (simplified without vector for now)
   */
  private async createFoodImageEmbeddingsTable(): Promise<void> {
    console.log('🖼️ Creating food_image_embeddings table...');

    const sql = `
      CREATE TABLE IF NOT EXISTS food_image_embeddings (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        food_name TEXT NOT NULL,
        category TEXT NOT NULL,
        embedding_data JSONB,
        image_hash TEXT NOT NULL UNIQUE,
        image_features JSONB,
        confidence_score FLOAT NOT NULL,
        correction_count INTEGER DEFAULT 0,
        success_rate FLOAT DEFAULT 1.0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        last_matched TIMESTAMP WITH TIME ZONE
      );

      CREATE INDEX IF NOT EXISTS idx_embeddings_food_name ON food_image_embeddings(food_name);
      CREATE INDEX IF NOT EXISTS idx_embeddings_hash ON food_image_embeddings(image_hash);
    `;

    await supabase.rpc('exec_sql', { sql });
  }

  /**
   * Create AI learning stats table
   */
  private async createAILearningStatsTable(): Promise<void> {
    console.log('📈 Creating ai_learning_stats table...');

    const sql = `
      CREATE TABLE IF NOT EXISTS ai_learning_stats (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        total_corrections INTEGER DEFAULT 0,
        total_analyses INTEGER DEFAULT 0,
        accuracy_improvement FLOAT DEFAULT 0.0,
        category_stats JSONB DEFAULT '{}',
        date DATE NOT NULL UNIQUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      CREATE INDEX IF NOT EXISTS idx_stats_date ON ai_learning_stats(date);
    `;

    await supabase.rpc('exec_sql', { sql });
  }

  /**
   * Create user learning preferences table
   */
  private async createUserLearningPreferencesTable(): Promise<void> {
    console.log('👤 Creating user_learning_preferences table...');

    const sql = `
      CREATE TABLE IF NOT EXISTS user_learning_preferences (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_hash TEXT NOT NULL UNIQUE,
        share_corrections BOOLEAN DEFAULT true,
        share_patterns BOOLEAN DEFAULT true,
        preferred_foods TEXT[] DEFAULT '{}',
        common_portions JSONB DEFAULT '{}',
        dietary_patterns JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      CREATE INDEX IF NOT EXISTS idx_preferences_hash ON user_learning_preferences(user_hash);
    `;

    await supabase.rpc('exec_sql', { sql });
  }

  /**
   * Create database functions
   */
  private async createFunctions(): Promise<void> {
    console.log('⚙️ Creating database functions...');

    // Function to get improved food suggestions
    const getFoodSuggestionsFunction = `
      CREATE OR REPLACE FUNCTION get_improved_food_suggestions(
        input_food_name TEXT,
        input_category TEXT DEFAULT NULL
      )
      RETURNS TABLE (
        suggested_name TEXT,
        category TEXT,
        confidence_boost FLOAT,
        common_portions JSONB,
        avg_calories FLOAT
      ) AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          frp.food_name,
          frp.category,
          frp.confidence_boost,
          frp.common_portions,
          frp.avg_calories_per_100g
        FROM food_recognition_patterns frp
        WHERE 
          frp.food_name ILIKE '%' || input_food_name || '%'
          OR (input_category IS NOT NULL AND frp.category = input_category)
        ORDER BY 
          frp.recognition_accuracy DESC,
          frp.confidence_boost DESC
        LIMIT 10;
      END;
      $$ LANGUAGE plpgsql;
    `;

    await supabase.rpc('exec_sql', { sql: getFoodSuggestionsFunction });
  }

  /**
   * Create triggers
   */
  private async createTriggers(): Promise<void> {
    console.log('🔄 Creating triggers...');

    const updatePatternsFunction = `
      CREATE OR REPLACE FUNCTION update_food_patterns()
      RETURNS TRIGGER AS $$
      BEGIN
        INSERT INTO food_recognition_patterns (
          food_name,
          category,
          correction_count,
          last_updated
        )
        VALUES (
          COALESCE(NEW.corrected_food_name, NEW.original_food_name),
          COALESCE(NEW.corrected_category, NEW.original_category),
          1,
          NOW()
        )
        ON CONFLICT (food_name) DO UPDATE SET
          correction_count = food_recognition_patterns.correction_count + 1,
          last_updated = NOW(),
          recognition_accuracy = CASE 
            WHEN NEW.correction_type = 'removal' THEN 
              GREATEST(food_recognition_patterns.recognition_accuracy - 0.1, 0.0)
            ELSE 
              LEAST(food_recognition_patterns.recognition_accuracy + 0.05, 1.0)
          END;
        
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      DROP TRIGGER IF EXISTS trigger_update_food_patterns ON ai_learning_corrections;
      CREATE TRIGGER trigger_update_food_patterns
        AFTER INSERT ON ai_learning_corrections
        FOR EACH ROW
        EXECUTE FUNCTION update_food_patterns();
    `;

    await supabase.rpc('exec_sql', { sql: updatePatternsFunction });
  }

  /**
   * Setup Row Level Security
   */
  private async setupRLS(): Promise<void> {
    console.log('🔒 Setting up Row Level Security...');

    const rlsSQL = `
      ALTER TABLE ai_learning_corrections ENABLE ROW LEVEL SECURITY;
      ALTER TABLE food_recognition_patterns ENABLE ROW LEVEL SECURITY;
      ALTER TABLE food_image_embeddings ENABLE ROW LEVEL SECURITY;
      ALTER TABLE ai_learning_stats ENABLE ROW LEVEL SECURITY;
      ALTER TABLE user_learning_preferences ENABLE ROW LEVEL SECURITY;

      CREATE POLICY IF NOT EXISTS "Allow anonymous corrections" ON ai_learning_corrections
        FOR INSERT WITH CHECK (true);

      CREATE POLICY IF NOT EXISTS "Allow read patterns" ON food_recognition_patterns
        FOR SELECT USING (true);

      CREATE POLICY IF NOT EXISTS "Allow read embeddings" ON food_image_embeddings
        FOR SELECT USING (true);

      CREATE POLICY IF NOT EXISTS "Allow read stats" ON ai_learning_stats
        FOR SELECT USING (true);
    `;

    await supabase.rpc('exec_sql', { sql: rlsSQL });
  }

  /**
   * Seed initial data
   */
  private async seedInitialData(): Promise<void> {
    console.log('🌱 Seeding initial data...');

    const { error } = await supabase.from('food_recognition_patterns').upsert(
      [
        {
          food_name: 'chicken breast',
          category: 'protein',
          common_portions: { '3 oz': 140, '1 medium piece': 185, '100g': 165 },
          avg_calories_per_100g: 165,
          macro_profile: { protein: 31, carbs: 0, fat: 3.6 },
          recognition_accuracy: 0.9,
        },
        {
          food_name: 'white rice',
          category: 'carbs',
          common_portions: { '1 cup cooked': 205, '1/2 cup': 103, '100g': 130 },
          avg_calories_per_100g: 130,
          macro_profile: { protein: 2.7, carbs: 28, fat: 0.3 },
          recognition_accuracy: 0.85,
        },
        {
          food_name: 'broccoli',
          category: 'vegetables',
          common_portions: { '1 cup': 25, '1 medium head': 150, '100g': 34 },
          avg_calories_per_100g: 34,
          macro_profile: { protein: 2.8, carbs: 7, fat: 0.4 },
          recognition_accuracy: 0.8,
        },
      ],
      { onConflict: 'food_name' }
    );

    if (error) {
      console.warn('Warning seeding initial data:', error);
    }
  }
}

export const databaseSetupService = new DatabaseSetupService();

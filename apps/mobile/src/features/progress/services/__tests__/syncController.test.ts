jest.mock('@react-native-community/netinfo', () => ({
  addEventListener: jest.fn(() => ({ remove: jest.fn() })),
  fetch: jest
    .fn()
    .mockResolvedValue({ isConnected: true, isInternetReachable: true }),
}));

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as progressData from '../progressDataService';
import { syncController } from '../syncController';

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
}));

jest.mock('../progressDataService', () => ({
  progressDataService: {
    syncProgressData: jest.fn().mockResolvedValue({ synced: true, count: 1 }),
  },
}));

describe('syncController', () => {
  beforeEach(() => {
    (AsyncStorage.getItem as jest.Mock).mockReset();
    (AsyncStorage.setItem as jest.Mock).mockReset();
    jest.spyOn(Date, 'now').mockRestore?.();
  });

  it('throttles sync when called within min interval', async () => {
    const first = await syncController.trySync({ force: true });
    expect(first.synced).toBe(true);

    const second = await syncController.trySync();
    expect(second.count).toBe(0);
  });

  it('forces sync when force option is true', async () => {
    const res = await syncController.trySync({ force: true });
    expect(res.count).toBeGreaterThanOrEqual(0);
  });

  it('applies backoff on failure', async () => {
    const spy = jest
      .spyOn(progressData.progressDataService, 'syncProgressData')
      .mockResolvedValueOnce({ synced: false, count: 0, error: 'fail' });
    const res = await syncController.trySync({ force: true });
    expect(res.synced).toBe(false);
    expect(AsyncStorage.setItem).toHaveBeenCalledWith(
      'sync:lastSyncAt',
      expect.any(String)
    );
    spy.mockRestore();
  });
});

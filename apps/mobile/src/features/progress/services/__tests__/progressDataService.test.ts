import AsyncStorage from '@react-native-async-storage/async-storage';
import { progressDataService } from '../progressDataService';

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
}));

jest.mock('../../../../lib/supabase', () => {
  const upsertMock = jest.fn().mockResolvedValue({ data: [], error: null });
  const getUserMock = jest
    .fn()
    .mockResolvedValue({ data: { user: { id: 'user-1' } } });
  return {
    supabase: {
      from: () => ({ upsert: upsertMock }),
      auth: { getUser: getUserMock },
      __mocks: { upsertMock, getUserMock },
    },
  };
});

const now = new Date().toISOString();

describe('progressDataService.saveProgressEntry', () => {
  beforeEach(() => {
    (AsyncStorage.getItem as jest.Mock).mockReset();
    (AsyncStorage.setItem as jest.Mock).mockReset();
  });

  it('queues and attempts to sync a habit entry', async () => {
    (AsyncStorage.getItem as jest.Mock).mockImplementation((key: string) => {
      if (key === 'progress_entries' || key === 'progress_sync_queue')
        return Promise.resolve('[]');
      if (key === 'progress_settings') return Promise.resolve(null);
      return Promise.resolve(null);
    });

    const id = await progressDataService.saveProgressEntry({
      type: 'habit',
      data: { hydration: { value: 250, unit: 'ml' }, steps: 1000 },
      timestamp: now,
    } as any);

    expect(typeof id).toBe('string');
    // Progress entries saved
    expect(AsyncStorage.setItem).toHaveBeenCalledWith(
      'progress_entries',
      expect.stringContaining('habit')
    );
    // Sync queue updated
    expect(AsyncStorage.setItem).toHaveBeenCalledWith(
      'progress_sync_queue',
      expect.any(String)
    );
  });

  it('syncProgressData returns synced:true and count when supabase upsert succeeds', async () => {
    (AsyncStorage.getItem as jest.Mock).mockImplementation((key: string) => {
      if (key === 'progress_sync_queue')
        return Promise.resolve(
          JSON.stringify([
            {
              id: '1',
              userId: 'user-1',
              type: 'habit',
              data: {},
              timestamp: now,
              synced: false,
            },
          ])
        );
      return Promise.resolve('[]');
    });

    const result = await progressDataService.syncProgressData();
    expect(result.synced).toBe(true);
    expect(result.count).toBe(1);
  });

  it('syncProgressData returns synced:false on supabase error', async () => {
    const { supabase: mocked } = jest.requireMock('../../../../lib/supabase');
    mocked.from = () => ({
      upsert: jest.fn().mockResolvedValue({ error: { message: 'fail' } }),
    });

    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(
      JSON.stringify([
        {
          id: '1',
          userId: 'user-1',
          type: 'habit',
          data: {},
          timestamp: now,
          synced: false,
        },
      ])
    );

    const result = await progressDataService.syncProgressData();
    expect(result.synced).toBe(false);
    expect(result.error).toBeDefined();
  });
});

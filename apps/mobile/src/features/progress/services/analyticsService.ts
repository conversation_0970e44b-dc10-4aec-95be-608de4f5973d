import { format, subDays, startOfWeek, endOfWeek } from 'date-fns';
import { supabase } from '../../../lib/supabase';

export interface StreakData {
  id: string;
  streak_type: 'habit' | 'workout' | 'nutrition' | 'overall';
  entity_id?: string;
  entity_name?: string;
  current_streak: number;
  longest_streak: number;
  last_activity_date?: string;
  streak_start_date?: string;
  is_active: boolean;
}

export interface ProgressMetrics {
  id: string;
  date: string;
  metric_type: 'daily' | 'weekly' | 'monthly';
  habits_completed: number;
  habits_total: number;
  nutrition_goals_met: number;
  nutrition_goals_total: number;
  workouts_completed: number;
  calories_consumed: number;
  calories_goal: number;
  additional_metrics: Record<string, any>;
}

export interface CompletionRate {
  period: string;
  completion_rate: number;
  completed: number;
  total: number;
}

export interface TrendData {
  date: string;
  value: number;
  change_from_previous?: number;
}

class AnalyticsService {
  // ==================== STREAK TRACKING ====================

  async getStreakData(streakType?: string): Promise<StreakData[]> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    let query = supabase
      .from('streak_tracking')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .order('current_streak', { ascending: false });

    if (streakType) {
      query = query.eq('streak_type', streakType);
    }

    const { data, error } = await query;
    if (error) {
      throw error;
    }
    return data || [];
  }

  async updateStreak(
    streakType: 'habit' | 'workout' | 'nutrition' | 'overall',
    entityId?: string,
    entityName?: string,
    activityDate: string = format(new Date(), 'yyyy-MM-dd')
  ): Promise<void> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Get existing streak data
    const { data: existingStreak } = await supabase
      .from('streak_tracking')
      .select('*')
      .eq('user_id', user.id)
      .eq('streak_type', streakType)
      .eq('entity_id', entityId || '')
      .single();

    const today = new Date(activityDate);
    const yesterday = format(subDays(today, 1), 'yyyy-MM-dd');

    if (existingStreak) {
      // Update existing streak
      let newCurrentStreak = 1;
      let newLongestStreak = existingStreak.longest_streak;
      let streakStartDate = activityDate;

      // Check if this continues an existing streak
      if (existingStreak.last_activity_date === yesterday) {
        newCurrentStreak = existingStreak.current_streak + 1;
        streakStartDate = existingStreak.streak_start_date;
      }

      // Update longest streak if current is longer
      if (newCurrentStreak > newLongestStreak) {
        newLongestStreak = newCurrentStreak;
      }

      const { error } = await supabase
        .from('streak_tracking')
        .update({
          current_streak: newCurrentStreak,
          longest_streak: newLongestStreak,
          last_activity_date: activityDate,
          streak_start_date: streakStartDate,
          updated_at: new Date().toISOString(),
        })
        .eq('id', existingStreak.id);

      if (error) {
        throw error;
      }
    } else {
      // Create new streak
      const { error } = await supabase.from('streak_tracking').insert({
        user_id: user.id,
        streak_type: streakType,
        entity_id: entityId,
        entity_name: entityName,
        current_streak: 1,
        longest_streak: 1,
        last_activity_date: activityDate,
        streak_start_date: activityDate,
        is_active: true,
      });

      if (error) {
        throw error;
      }
    }
  }

  async breakStreak(streakType: string, entityId?: string): Promise<void> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { error } = await supabase
      .from('streak_tracking')
      .update({
        current_streak: 0,
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', user.id)
      .eq('streak_type', streakType)
      .eq('entity_id', entityId || '');

    if (error) {
      throw error;
    }
  }

  // ==================== PROGRESS METRICS ====================

  async getProgressMetrics(
    startDate: string,
    endDate: string,
    metricType: 'daily' | 'weekly' | 'monthly' = 'daily'
  ): Promise<ProgressMetrics[]> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('progress_metrics')
      .select('*')
      .eq('user_id', user.id)
      .eq('metric_type', metricType)
      .gte('date', startDate)
      .lte('date', endDate)
      .order('date', { ascending: true });

    if (error) {
      throw error;
    }
    return data || [];
  }

  async calculateDailyMetrics(
    date: string = format(new Date(), 'yyyy-MM-dd')
  ): Promise<void> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Calculate habit metrics
    const { data: habits } = await supabase
      .from('habits')
      .select('id')
      .eq('user_id', user.id)
      .eq('is_active', true);

    const { data: habitCompletions } = await supabase
      .from('habit_completions')
      .select('habit_id')
      .eq('user_id', user.id)
      .eq('completion_date', date);

    const habitsTotal = habits?.length || 0;
    const habitsCompleted = habitCompletions?.length || 0;

    // TODO: Calculate nutrition and workout metrics
    // This would integrate with existing nutrition and exercise data

    // Upsert daily metrics
    const { error } = await supabase.from('progress_metrics').upsert(
      {
        user_id: user.id,
        date,
        metric_type: 'daily',
        habits_completed: habitsCompleted,
        habits_total: habitsTotal,
        nutrition_goals_met: 0, // TODO: Calculate from nutrition data
        nutrition_goals_total: 0, // TODO: Calculate from nutrition goals
        workouts_completed: 0, // TODO: Calculate from workout data
        calories_consumed: 0, // TODO: Calculate from nutrition data
        calories_goal: 0, // TODO: Get from user profile
        updated_at: new Date().toISOString(),
      },
      {
        onConflict: 'user_id,date,metric_type',
      }
    );

    if (error) {
      throw error;
    }
  }

  // ==================== COMPLETION RATES ====================

  async getHabitCompletionRates(days: number = 30): Promise<CompletionRate[]> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const endDate = format(new Date(), 'yyyy-MM-dd');
    const startDate = format(subDays(new Date(), days - 1), 'yyyy-MM-dd');

    const { data, error } = await supabase
      .from('progress_metrics')
      .select('date, habits_completed, habits_total')
      .eq('user_id', user.id)
      .eq('metric_type', 'daily')
      .gte('date', startDate)
      .lte('date', endDate)
      .order('date', { ascending: true });

    if (error) {
      throw error;
    }

    return (data || []).map(metric => ({
      period: metric.date,
      completion_rate:
        metric.habits_total > 0
          ? Math.round((metric.habits_completed / metric.habits_total) * 100)
          : 0,
      completed: metric.habits_completed,
      total: metric.habits_total,
    }));
  }

  async getWeeklyCompletionRates(
    weeks: number = 12
  ): Promise<CompletionRate[]> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const rates: CompletionRate[] = [];

    for (let i = 0; i < weeks; i++) {
      const weekStart = startOfWeek(subDays(new Date(), i * 7));
      const weekEnd = endOfWeek(weekStart);

      const { data } = await supabase
        .from('progress_metrics')
        .select('habits_completed, habits_total')
        .eq('user_id', user.id)
        .eq('metric_type', 'daily')
        .gte('date', format(weekStart, 'yyyy-MM-dd'))
        .lte('date', format(weekEnd, 'yyyy-MM-dd'));

      const totalCompleted =
        data?.reduce((sum, d) => sum + d.habits_completed, 0) || 0;
      const totalPossible =
        data?.reduce((sum, d) => sum + d.habits_total, 0) || 0;

      rates.unshift({
        period: `Week of ${format(weekStart, 'MMM d')}`,
        completion_rate:
          totalPossible > 0
            ? Math.round((totalCompleted / totalPossible) * 100)
            : 0,
        completed: totalCompleted,
        total: totalPossible,
      });
    }

    return rates;
  }

  // ==================== TREND ANALYSIS ====================

  async getHabitTrend(days: number = 30): Promise<TrendData[]> {
    const completionRates = await this.getHabitCompletionRates(days);

    return completionRates.map((rate, index) => ({
      date: rate.period,
      value: rate.completion_rate,
      change_from_previous:
        index > 0
          ? rate.completion_rate - completionRates[index - 1].completion_rate
          : undefined,
    }));
  }

  // ==================== PATTERN RECOGNITION ====================

  async getBestPerformanceDays(): Promise<
    { day: string; average_completion: number }[]
  > {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // This would require more complex SQL to analyze patterns by day of week
    // For now, return a placeholder structure
    return [
      { day: 'Monday', average_completion: 85 },
      { day: 'Tuesday', average_completion: 92 },
      { day: 'Wednesday', average_completion: 88 },
      { day: 'Thursday', average_completion: 90 },
      { day: 'Friday', average_completion: 75 },
      { day: 'Saturday', average_completion: 70 },
      { day: 'Sunday', average_completion: 80 },
    ];
  }

  async getPersonalizedInsights(): Promise<string[]> {
    const streaks = await this.getStreakData();
    const completionRates = await this.getHabitCompletionRates(7);

    const insights: string[] = [];

    // Streak insights
    const longestStreak = Math.max(...streaks.map(s => s.current_streak));
    if (longestStreak >= 7) {
      insights.push(`🔥 Amazing! You're on a ${longestStreak}-day streak!`);
    }

    // Completion rate insights
    const avgCompletion =
      completionRates.reduce((sum, r) => sum + r.completion_rate, 0) /
      completionRates.length;
    if (avgCompletion >= 80) {
      insights.push(
        `⭐ You're crushing it with ${Math.round(avgCompletion)}% completion rate this week!`
      );
    } else if (avgCompletion < 50) {
      insights.push(
        "💪 Let's get back on track! Small steps lead to big changes."
      );
    }

    return insights;
  }
}

export const analyticsService = new AnalyticsService();

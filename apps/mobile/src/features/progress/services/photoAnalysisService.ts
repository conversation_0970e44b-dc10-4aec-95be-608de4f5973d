import { GoogleGenerativeAI } from '@google/generative-ai';
import { aiLearningBackendService } from './aiLearningBackendService';

// Initialize Gemini Flash 2.5
const genAI = new GoogleGenerativeAI(
  process.env.EXPO_PUBLIC_GEMINI_API_KEY || ''
);

export interface FoodItem {
  id: string;
  name: string;
  category:
    | 'protein'
    | 'carbs'
    | 'vegetables'
    | 'fruits'
    | 'dairy'
    | 'fats'
    | 'other';
  estimatedPortion: string;
  confidence: number;
  calories?: number;
  macros?: {
    protein: number;
    carbs: number;
    fat: number;
  };
}

export interface PhotoAnalysisResult {
  foods: FoodItem[];
  totalCalories: number;
  confidence: number;
  analysisId: string;
  timestamp: string;
}

export interface UserCorrection {
  analysisId: string;
  originalFood: FoodItem;
  correctedFood: FoodItem | null; // null if user removed the item
  userId?: string; // Optional for privacy
  timestamp: string;
}

class PhotoAnalysisService {
  private model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash-exp' });

  /**
   * Analyze a meal photo using Gemini Flash 2.5
   */
  async analyzeMealPhoto(imageUri: string): Promise<PhotoAnalysisResult> {
    try {
      // Convert image to base64 for Gemini
      const imageBase64 = await this.convertImageToBase64(imageUri);

      const prompt = `
        Analyze this meal photo and identify all food items. For each food item, provide:
        1. Specific food name (e.g., "grilled chicken breast" not just "chicken")
        2. Food category (protein, carbs, vegetables, fruits, dairy, fats, other)
        3. Estimated portion size (e.g., "1 medium piece", "1/2 cup", "3 oz")
        4. Confidence level (0-100)
        5. Estimated calories
        6. Macronutrients (protein, carbs, fat in grams)

        Return the response as a JSON object with this structure:
        {
          "foods": [
            {
              "name": "food name",
              "category": "category",
              "estimatedPortion": "portion size",
              "confidence": 85,
              "calories": 150,
              "macros": {"protein": 25, "carbs": 0, "fat": 5}
            }
          ],
          "totalCalories": 500,
          "confidence": 90
        }

        Be specific and accurate. If you're unsure about something, lower the confidence score.
      `;

      const result = await this.model.generateContent([
        prompt,
        {
          inlineData: {
            data: imageBase64,
            mimeType: 'image/jpeg',
          },
        },
      ]);

      const response = await result.response;
      const text = response.text();

      // Parse the JSON response
      const analysisData = JSON.parse(text);

      // Add IDs and timestamp
      let analysisResult: PhotoAnalysisResult = {
        foods: analysisData.foods.map((food: any, index: number) => ({
          id: `food_${Date.now()}_${index}`,
          ...food,
        })),
        totalCalories: analysisData.totalCalories,
        confidence: analysisData.confidence,
        analysisId: `analysis_${Date.now()}`,
        timestamp: new Date().toISOString(),
      };

      // Enhance analysis with learned patterns
      try {
        const enhancedFoods =
          await aiLearningBackendService.enhanceFoodAnalysis(
            analysisResult.foods
          );
        analysisResult = {
          ...analysisResult,
          foods: enhancedFoods,
          // Recalculate total calories with enhanced data
          totalCalories: enhancedFoods.reduce(
            (sum, food) => sum + (food.calories || 0),
            0
          ),
        };
      } catch (error) {
        console.log('Could not enhance analysis with learned patterns:', error);
        // Continue with original analysis if enhancement fails
      }

      return analysisResult;
    } catch (error) {
      console.error('Photo analysis failed:', error);
      throw new Error('Failed to analyze meal photo. Please try again.');
    }
  }

  /**
   * Submit user corrections for universal AI learning
   */
  async submitCorrection(
    correction: UserCorrection,
    shareWithAI: boolean = true
  ): Promise<void> {
    try {
      if (!shareWithAI) {
        // Store locally only for personal learning
        await this.storePersonalCorrection(correction);
        return;
      }

      // Submit to backend for universal learning
      const success = await aiLearningBackendService.submitCorrection(
        correction,
        correction.analysisId
      );
      if (!success) {
        console.warn(
          'Failed to submit correction to universal learning system'
        );
      }

      // Also store locally for personal patterns
      await this.storePersonalCorrection(correction);
    } catch (error) {
      console.error('Failed to submit correction:', error);
      // Don't throw - corrections are not critical
    }
  }

  /**
   * Get user's photo analysis history
   */
  async getAnalysisHistory(
    _limit: number = 50
  ): Promise<PhotoAnalysisResult[]> {
    // TODO: Implement local storage retrieval
    return [];
  }

  /**
   * Convert image URI to base64
   */
  private async convertImageToBase64(imageUri: string): Promise<string> {
    try {
      const response = await fetch(imageUri);
      const blob = await response.blob();

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const base64 = (reader.result as string).split(',')[1];
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      throw new Error('Failed to process image');
    }
  }

  /**
   * Store correction locally for personal learning
   */
  private async storePersonalCorrection(
    correction: UserCorrection
  ): Promise<void> {
    // TODO: Implement local storage
    console.log('Storing personal correction:', correction);
  }

  /**
   * Get food suggestions based on learned patterns
   */
  async getFoodSuggestions(
    foodName: string,
    category?: string
  ): Promise<any[]> {
    try {
      return await aiLearningBackendService.getImprovedSuggestions(
        foodName,
        category
      );
    } catch (error) {
      console.error('Failed to get food suggestions:', error);
      return [];
    }
  }

  /**
   * Validate analysis result
   */
  private validateAnalysisResult(result: any): boolean {
    return (
      result &&
      Array.isArray(result.foods) &&
      typeof result.totalCalories === 'number' &&
      typeof result.confidence === 'number'
    );
  }
}

export const photoAnalysisService = new PhotoAnalysisService();

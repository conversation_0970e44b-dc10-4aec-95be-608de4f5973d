import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { progressDataService } from './progressDataService';

// Simple event listeners for UI components to subscribe to sync status
export type SyncStatus = 'idle' | 'syncing' | 'offline' | 'error';

class SyncController {
  private lastSyncAt: number = 0;
  private backoffMs: number = 0;
  private readonly minIntervalMs = 3 * 60 * 1000; // 3 minutes
  private readonly initialBackoffMs = 30 * 1000; // 30 seconds
  private readonly maxBackoffMs = 10 * 60 * 1000; // 10 minutes
  private status: SyncStatus = 'idle';
  private listeners = new Set<(status: SyncStatus) => void>();

  constructor() {
    this.restoreState();
    NetInfo.addEventListener(state => {
      if (state.isConnected) {
        this.trySync();
      }
    });
  }

  private async restoreState() {
    const val = await AsyncStorage.getItem('sync:lastSyncAt');
    if (val) this.lastSyncAt = parseInt(val, 10) || 0;
  }

  subscribe(listener: (status: SyncStatus) => void) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private setStatus(next: SyncStatus) {
    this.status = next;
    for (const l of this.listeners) l(next);
  }

  getStatus(): SyncStatus {
    return this.status;
  }

  private shouldThrottle(): boolean {
    const now = Date.now();
    const interval = this.backoffMs > 0 ? this.backoffMs : this.minIntervalMs;
    return now - this.lastSyncAt < interval;
  }

  private async updateLastSync(success: boolean) {
    this.lastSyncAt = Date.now();
    await AsyncStorage.setItem('sync:lastSyncAt', String(this.lastSyncAt));
    if (success) {
      this.backoffMs = 0;
    } else {
      this.backoffMs =
        this.backoffMs === 0
          ? this.initialBackoffMs
          : Math.min(this.backoffMs * 2, this.maxBackoffMs);
    }
  }

  async trySync(options?: {
    force?: boolean;
  }): Promise<{ synced: boolean; count: number; error?: string }> {
    if (!options?.force && this.shouldThrottle()) {
      return { synced: true, count: 0 };
    }

    this.setStatus('syncing');
    const result = await progressDataService.syncProgressData();
    await this.updateLastSync(result.synced);

    if (!result.synced) {
      this.setStatus('offline');
    } else {
      this.setStatus('idle');
    }
    return result;
  }
}

export const syncController = new SyncController();

import NetInfo from '@react-native-community/netinfo';
import { supabase } from '../../../lib/supabase';
import { addToOfflineQueue } from '../../../shared/services/offline/offlineService';

export interface ProgressLog {
  id: string;
  log_date: string;
  weight_kg?: number;
  body_measurements?: any;
  workout_performance?: any;
  subjective_feeling?: string;
}

export const getProgressLogs = async (
  limit: number = 30
): Promise<ProgressLog[]> => {
  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      throw new Error('Not authenticated');
    }

    // Check network connectivity
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      throw new Error('No internet connection');
    }

    const { data, error } = await supabase
      .from('progress_logs')
      .select('*')
      .eq('user_id', session.user.id)
      .order('log_date', { ascending: false })
      .limit(limit);

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching progress logs:', error);
    throw error;
  }
};

export const logProgress = async (
  progressData: Omit<ProgressLog, 'id' | 'user_id'>
): Promise<ProgressLog> => {
  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      throw new Error('Not authenticated');
    }

    // Check network connectivity
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      // Add to offline queue
      await addToOfflineQueue({
        type: 'progress_log',
        action: 'create',
        data: progressData,
      });
      throw new Error('No internet connection. Data saved locally.');
    }

    const { data, error } = await supabase
      .from('progress_logs')
      .insert([
        {
          ...progressData,
          user_id: session.user.id,
        },
      ])
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error logging progress:', error);
    throw error;
  }
};

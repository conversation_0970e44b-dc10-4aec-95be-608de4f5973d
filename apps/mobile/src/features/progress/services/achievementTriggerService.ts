import { achievementNotificationService } from './achievementNotificationService';
import { achievementService } from './achievementService';
import { analyticsService } from './analyticsService';

export type TriggerEvent =
  | 'habit_completed'
  | 'habit_created'
  | 'streak_milestone'
  | 'completion_rate_milestone'
  | 'points_milestone'
  | 'weekly_goal_achieved'
  | 'monthly_goal_achieved'
  | 'perfect_week'
  | 'perfect_month'
  | 'app_opened'
  | 'profile_completed';

export interface TriggerContext {
  userId?: string;
  habitId?: string;
  streakLength?: number;
  completionRate?: number;
  totalPoints?: number;
  weekNumber?: number;
  monthNumber?: number;
  [key: string]: any;
}

class AchievementTriggerService {
  private isProcessing = false;

  // ==================== MAIN TRIGGER METHOD ====================

  async triggerAchievementCheck(
    event: TriggerEvent,
    context: TriggerContext = {}
  ): Promise<void> {
    // Prevent concurrent processing
    if (this.isProcessing) {
      console.log('Achievement check already in progress, skipping...');
      return;
    }

    try {
      this.isProcessing = true;

      console.log(
        `🎯 Triggering achievement check for event: ${event}`,
        context
      );

      // Update analytics data before checking achievements
      await this.updateAnalyticsData(event, context);

      // Check for new achievements
      const newAchievements =
        await achievementService.checkAndUnlockAchievements();

      if (newAchievements.length > 0) {
        console.log(`🏆 ${newAchievements.length} new achievements unlocked!`);

        // Notify user of new achievements
        await achievementNotificationService.notifyMultipleAchievements(
          newAchievements
        );

        // Check for milestone notifications
        await this.checkMilestones(event, context);
      }
    } catch (error) {
      console.error('Error in achievement trigger service:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  // ==================== SPECIFIC EVENT HANDLERS ====================

  async onHabitCompleted(
    habitId: string,
    streakLength?: number
  ): Promise<void> {
    await this.triggerAchievementCheck('habit_completed', {
      habitId,
      streakLength,
    });
  }

  async onHabitCreated(habitId: string): Promise<void> {
    await this.triggerAchievementCheck('habit_created', {
      habitId,
    });
  }

  async onStreakMilestone(
    streakLength: number,
    habitId?: string
  ): Promise<void> {
    await this.triggerAchievementCheck('streak_milestone', {
      streakLength,
      habitId,
    });

    // Send milestone notification
    await achievementNotificationService.notifyMilestone(
      'streak',
      streakLength,
      `${streakLength} day streak! You're on fire! 🔥`
    );
  }

  async onCompletionRateMilestone(completionRate: number): Promise<void> {
    await this.triggerAchievementCheck('completion_rate_milestone', {
      completionRate,
    });

    // Send milestone notification for high completion rates
    if (completionRate >= 90) {
      await achievementNotificationService.notifyMilestone(
        'completion',
        completionRate,
        `${completionRate}% completion rate! Excellent consistency! ⭐`
      );
    }
  }

  async onPointsMilestone(totalPoints: number): Promise<void> {
    await this.triggerAchievementCheck('points_milestone', {
      totalPoints,
    });

    // Send milestone notification for point milestones
    const milestones = [100, 250, 500, 1000, 2500, 5000, 10000];
    if (milestones.includes(totalPoints)) {
      await achievementNotificationService.notifyMilestone(
        'points',
        totalPoints,
        `${totalPoints} total points earned! Amazing progress! 🌟`
      );
    }
  }

  async onWeeklyGoalAchieved(weekNumber: number): Promise<void> {
    await this.triggerAchievementCheck('weekly_goal_achieved', {
      weekNumber,
    });
  }

  async onPerfectWeek(weekNumber: number): Promise<void> {
    await this.triggerAchievementCheck('perfect_week', {
      weekNumber,
    });

    await achievementNotificationService.notifyMilestone(
      'completion',
      100,
      'Perfect week completed! All habits done! 🎉'
    );
  }

  async onAppOpened(): Promise<void> {
    // Light trigger for app opening (don't show notifications)
    await this.triggerAchievementCheck('app_opened', {});
  }

  // ==================== ANALYTICS UPDATE ====================

  private async updateAnalyticsData(
    event: TriggerEvent,
    _context: TriggerContext
  ): Promise<void> {
    try {
      // Update daily metrics for habit-related events
      if (
        event.includes('habit') ||
        event.includes('streak') ||
        event.includes('completion')
      ) {
        await analyticsService.calculateDailyMetrics();
      }

      // Update specific metrics based on event type
      switch (event) {
        case 'habit_completed':
          // Streak updates are handled by the progress integration service
          break;

        case 'streak_milestone':
          // Streak data is already updated by habit completion
          break;

        case 'weekly_goal_achieved':
        case 'perfect_week':
          // Weekly metrics will be calculated by daily metrics aggregation
          break;
      }
    } catch (error) {
      console.error('Error updating analytics data:', error);
    }
  }

  // ==================== MILESTONE CHECKING ====================

  private async checkMilestones(
    event: TriggerEvent,
    context: TriggerContext
  ): Promise<void> {
    try {
      // Check for various milestones that might have been reached

      // Check streak milestones
      if (context.streakLength) {
        const streakMilestones = [3, 7, 14, 30, 50, 100];
        if (streakMilestones.includes(context.streakLength)) {
          // Milestone notification already sent in onStreakMilestone
        }
      }

      // Check completion rate milestones
      const completionRates = await analyticsService.getHabitCompletionRates(7);
      if (completionRates.length > 0) {
        const avgCompletion = Math.round(
          completionRates.reduce((sum, r) => sum + r.completion_rate, 0) /
            completionRates.length
        );

        const completionMilestones = [50, 70, 80, 90, 95, 100];
        if (completionMilestones.includes(avgCompletion)) {
          await this.onCompletionRateMilestone(avgCompletion);
        }
      }

      // Check points milestones
      const totalPoints = await achievementService.getTotalPoints();
      const pointsMilestones = [100, 250, 500, 1000, 2500, 5000, 10000];
      if (pointsMilestones.includes(totalPoints)) {
        await this.onPointsMilestone(totalPoints);
      }
    } catch (error) {
      console.error('Error checking milestones:', error);
    }
  }

  // ==================== BATCH PROCESSING ====================

  async processBatchEvents(
    events: Array<{ event: TriggerEvent; context: TriggerContext }>
  ): Promise<void> {
    // Process multiple events efficiently
    for (const { event, context } of events) {
      await this.triggerAchievementCheck(event, context);
    }
  }

  // ==================== UTILITY METHODS ====================

  isEventProcessing(): boolean {
    return this.isProcessing;
  }

  async forceRefreshAchievements(): Promise<void> {
    // Force a complete refresh of achievement data
    await analyticsService.calculateDailyMetrics();
    await this.triggerAchievementCheck('app_opened', { forceRefresh: true });
  }
}

export const achievementTriggerService = new AchievementTriggerService();

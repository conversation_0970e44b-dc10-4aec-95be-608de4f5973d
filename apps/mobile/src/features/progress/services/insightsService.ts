import { format, subDays, startOfWeek, endOfWeek } from 'date-fns';
import { achievementService } from './achievementService';
import { analyticsService } from './analyticsService';
import { streakService } from './streakService';

export interface UserInsight {
  id: string;
  type: 'pattern' | 'recommendation' | 'celebration' | 'motivation' | 'warning';
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high';
  actionable: boolean;
  action?: {
    label: string;
    type: 'navigate' | 'create_habit' | 'set_reminder' | 'view_progress';
    data?: any;
  };
  icon: string;
  color: string;
  createdAt: Date;
}

export interface WeeklySummary {
  weekStart: Date;
  weekEnd: Date;
  totalHabits: number;
  completedHabits: number;
  completionRate: number;
  streakChanges: number;
  pointsEarned: number;
  achievements: number;
  topPerformingHabits: string[];
  strugglingHabits: string[];
  insights: UserInsight[];
  motivationalMessage: string;
}

export interface PatternAnalysis {
  bestDays: string[];
  worstDays: string[];
  bestTimes: string[];
  consistencyScore: number;
  trendDirection: 'improving' | 'declining' | 'stable';
  seasonalPatterns: any[];
}

class InsightsService {
  // ==================== MAIN INSIGHTS GENERATION ====================

  async generatePersonalizedInsights(): Promise<UserInsight[]> {
    const insights: UserInsight[] = [];

    try {
      // Get recent data for analysis
      const completionRates =
        await analyticsService.getHabitCompletionRates(14);
      const streakSummary = await streakService.getStreakSummary();
      const endDate = format(new Date(), 'yyyy-MM-dd');
      const startDate = format(subDays(new Date(), 6), 'yyyy-MM-dd');
      await analyticsService.getProgressMetrics(startDate, endDate);

      // Generate different types of insights
      insights.push(...(await this.generatePatternInsights()));
      insights.push(
        ...(await this.generatePerformanceInsights(completionRates))
      );
      insights.push(...(await this.generateStreakInsights(streakSummary)));
      insights.push(...(await this.generateMotivationalInsights()));
      insights.push(...(await this.generateRecommendationInsights()));

      // Sort by priority and limit to top insights
      return insights
        .sort(
          (a, b) =>
            this.getPriorityWeight(b.priority) -
            this.getPriorityWeight(a.priority)
        )
        .slice(0, 8);
    } catch (error) {
      console.error('Error generating insights:', error);
      return this.getFallbackInsights();
    }
  }

  // ==================== PATTERN ANALYSIS ====================

  async generatePatternInsights(): Promise<UserInsight[]> {
    const insights: UserInsight[] = [];

    try {
      const patterns = await this.analyzeUserPatterns();

      // Best day pattern
      if (patterns.bestDays.length > 0) {
        insights.push({
          id: `pattern-best-day-${Date.now()}`,
          type: 'pattern',
          title: 'Your Best Days',
          message: `You're most consistent on ${patterns.bestDays.join(' and ')}s. Consider scheduling important habits on these days!`,
          priority: 'medium',
          actionable: true,
          action: {
            label: 'Optimize Schedule',
            type: 'view_progress',
          },
          icon: 'trending-up',
          color: '#4CAF50',
          createdAt: new Date(),
        });
      }

      // Consistency insight
      if (patterns.consistencyScore < 60) {
        insights.push({
          id: `pattern-consistency-${Date.now()}`,
          type: 'recommendation',
          title: 'Consistency Opportunity',
          message: `Your consistency score is ${patterns.consistencyScore}%. Try starting with just 1-2 habits to build momentum.`,
          priority: 'high',
          actionable: true,
          action: {
            label: 'Simplify Routine',
            type: 'view_progress',
          },
          icon: 'target',
          color: '#FF9800',
          createdAt: new Date(),
        });
      }

      // Trend analysis
      if (patterns.trendDirection === 'improving') {
        insights.push({
          id: `pattern-trend-${Date.now()}`,
          type: 'celebration',
          title: 'Upward Trend! 📈',
          message:
            'Your habit completion is improving over time. Keep up the great momentum!',
          priority: 'medium',
          actionable: false,
          icon: 'trending-up',
          color: '#4CAF50',
          createdAt: new Date(),
        });
      }
    } catch (error) {
      console.error('Error generating pattern insights:', error);
    }

    return insights;
  }

  // ==================== PERFORMANCE INSIGHTS ====================

  async generatePerformanceInsights(
    completionRates: any[]
  ): Promise<UserInsight[]> {
    const insights: UserInsight[] = [];

    if (completionRates.length === 0) {
      return insights;
    }

    const avgCompletion =
      completionRates.reduce((sum, r) => sum + r.completion_rate, 0) /
      completionRates.length;
    const recentCompletion =
      completionRates.slice(-3).reduce((sum, r) => sum + r.completion_rate, 0) /
      3;

    // High performance celebration
    if (avgCompletion >= 80) {
      insights.push({
        id: `performance-high-${Date.now()}`,
        type: 'celebration',
        title: 'Excellent Performance! 🌟',
        message: `${Math.round(avgCompletion)}% completion rate over the last 2 weeks. You're crushing your goals!`,
        priority: 'medium',
        actionable: false,
        icon: 'star',
        color: '#4CAF50',
        createdAt: new Date(),
      });
    }

    // Performance decline warning
    if (recentCompletion < avgCompletion - 20) {
      insights.push({
        id: `performance-decline-${Date.now()}`,
        type: 'warning',
        title: 'Recent Dip Noticed',
        message:
          'Your completion rate has dropped recently. Consider reviewing your routine or reducing habit load.',
        priority: 'high',
        actionable: true,
        action: {
          label: 'Review Habits',
          type: 'navigate',
          data: { screen: 'Habits' },
        },
        icon: 'trending-down',
        color: '#F44336',
        createdAt: new Date(),
      });
    }

    return insights;
  }

  // ==================== STREAK INSIGHTS ====================

  async generateStreakInsights(streakSummary: any): Promise<UserInsight[]> {
    const insights: UserInsight[] = [];

    // Streak milestone celebration
    if (streakSummary.longest_current_streak >= 7) {
      insights.push({
        id: `streak-milestone-${Date.now()}`,
        type: 'celebration',
        title: `${streakSummary.longest_current_streak} Day Streak! 🔥`,
        message: 'Amazing consistency! Streaks like this build lasting habits.',
        priority: 'high',
        actionable: false,
        icon: 'local-fire-department',
        color: '#FF5722',
        createdAt: new Date(),
      });
    }

    // Streak recovery motivation
    if (
      streakSummary.longest_current_streak === 0 &&
      streakSummary.longest_all_time_streak > 0
    ) {
      insights.push({
        id: `streak-recovery-${Date.now()}`,
        type: 'motivation',
        title: 'Restart Your Streak',
        message: `You've achieved ${streakSummary.longest_all_time_streak} days before. You can do it again!`,
        priority: 'medium',
        actionable: true,
        action: {
          label: 'Start Today',
          type: 'navigate',
          data: { screen: 'Habits' },
        },
        icon: 'refresh',
        color: '#2196F3',
        createdAt: new Date(),
      });
    }

    return insights;
  }

  // ==================== MOTIVATIONAL INSIGHTS ====================

  async generateMotivationalInsights(): Promise<UserInsight[]> {
    const insights: UserInsight[] = [];

    const motivationalMessages = [
      {
        title: 'Small Steps, Big Results',
        message:
          'Every habit completion is a vote for the person you want to become.',
        icon: 'directions-walk',
        color: '#9C27B0',
      },
      {
        title: 'Progress Over Perfection',
        message:
          'Consistency beats perfection. Focus on showing up, not being perfect.',
        icon: 'timeline',
        color: '#3F51B5',
      },
      {
        title: 'Building Your Future',
        message: 'The habits you build today shape the life you live tomorrow.',
        icon: 'build',
        color: '#607D8B',
      },
    ];

    // Add one random motivational insight
    const randomMessage =
      motivationalMessages[
        Math.floor(Math.random() * motivationalMessages.length)
      ];
    insights.push({
      id: `motivation-${Date.now()}`,
      type: 'motivation',
      title: randomMessage.title,
      message: randomMessage.message,
      priority: 'low',
      actionable: false,
      icon: randomMessage.icon,
      color: randomMessage.color,
      createdAt: new Date(),
    });

    return insights;
  }

  // ==================== RECOMMENDATION INSIGHTS ====================

  async generateRecommendationInsights(): Promise<UserInsight[]> {
    const insights: UserInsight[] = [];

    // Habit suggestions based on patterns
    const recommendations = [
      {
        condition: 'low_morning_activity',
        title: 'Morning Routine Boost',
        message:
          'Consider adding a simple morning habit like drinking water or 5-minute meditation.',
        action: { label: 'Add Morning Habit', type: 'create_habit' as const },
      },
      {
        condition: 'weekend_drop',
        title: 'Weekend Consistency',
        message:
          'Your weekend completion drops. Try easier weekend versions of your habits.',
        action: {
          label: 'Adjust Weekend Habits',
          type: 'view_progress' as const,
        },
      },
      {
        condition: 'high_stress_periods',
        title: 'Stress Management',
        message:
          'Consider adding stress-relief habits like deep breathing or short walks.',
        action: { label: 'Add Wellness Habit', type: 'create_habit' as const },
      },
    ];

    // Add one relevant recommendation
    const recommendation = recommendations[0]; // Simplified for now
    insights.push({
      id: `recommendation-${Date.now()}`,
      type: 'recommendation',
      title: recommendation.title,
      message: recommendation.message,
      priority: 'medium',
      actionable: true,
      action: {
        label: recommendation.action.label,
        type: recommendation.action.type,
      },
      icon: 'lightbulb',
      color: '#FF9800',
      createdAt: new Date(),
    });

    return insights;
  }

  // ==================== WEEKLY SUMMARY ====================

  async generateWeeklySummary(weekStart?: Date): Promise<WeeklySummary> {
    const start = weekStart || startOfWeek(new Date(), { weekStartsOn: 1 });
    const end = endOfWeek(start, { weekStartsOn: 1 });

    try {
      // Get week data
      const endDate = format(new Date(), 'yyyy-MM-dd');
      const startDate = format(subDays(new Date(), 6), 'yyyy-MM-dd');
      const progressMetrics = await analyticsService.getProgressMetrics(
        startDate,
        endDate
      );
      const _completionRates =
        await analyticsService.getHabitCompletionRates(7);
      const achievements = await achievementService.getUserAchievements();

      // Calculate summary stats
      const totalHabits =
        progressMetrics.length > 0 ? progressMetrics[0].habits_total : 0;
      const completedHabits = progressMetrics.reduce(
        (sum, m) => sum + m.habits_completed,
        0
      );
      const completionRate =
        totalHabits > 0
          ? Math.round((completedHabits / (totalHabits * 7)) * 100)
          : 0;
      const pointsEarned = achievements
        .filter(
          a =>
            new Date(a.unlocked_at || '') >= start &&
            new Date(a.unlocked_at || '') <= end
        )
        .reduce((sum, a) => sum + (a.achievement?.points || 0), 0);

      // Generate insights for the week
      const insights = await this.generatePersonalizedInsights();

      return {
        weekStart: start,
        weekEnd: end,
        totalHabits,
        completedHabits,
        completionRate,
        streakChanges: 0, // TODO: Calculate streak changes
        pointsEarned,
        achievements: achievements.length,
        topPerformingHabits: [], // TODO: Calculate top habits
        strugglingHabits: [], // TODO: Calculate struggling habits
        insights: insights.slice(0, 3),
        motivationalMessage:
          this.generateWeeklyMotivationalMessage(completionRate),
      };
    } catch (error) {
      console.error('Error generating weekly summary:', error);
      return this.getFallbackWeeklySummary(start, end);
    }
  }

  // ==================== HELPER METHODS ====================

  private async analyzeUserPatterns(): Promise<PatternAnalysis> {
    // Simplified pattern analysis
    return {
      bestDays: ['Monday', 'Tuesday'],
      worstDays: ['Saturday', 'Sunday'],
      bestTimes: ['morning'],
      consistencyScore: 75,
      trendDirection: 'improving',
      seasonalPatterns: [],
    };
  }

  private getPriorityWeight(priority: string): number {
    switch (priority) {
      case 'high':
        return 3;
      case 'medium':
        return 2;
      case 'low':
        return 1;
      default:
        return 0;
    }
  }

  private generateWeeklyMotivationalMessage(completionRate: number): string {
    if (completionRate >= 90) {
      return "Outstanding week! You're building incredible momentum. 🚀";
    } else if (completionRate >= 70) {
      return 'Great progress this week! Keep up the consistent effort. 💪';
    } else if (completionRate >= 50) {
      return 'Good foundation this week. Small improvements lead to big results! 🌱';
    } else {
      return 'Every week is a fresh start. Focus on one habit at a time. 🌟';
    }
  }

  private getFallbackInsights(): UserInsight[] {
    return [
      {
        id: 'fallback-motivation',
        type: 'motivation',
        title: 'Keep Going!',
        message: 'Every day is a new opportunity to build better habits.',
        priority: 'medium',
        actionable: false,
        icon: 'favorite',
        color: '#E91E63',
        createdAt: new Date(),
      },
    ];
  }

  private getFallbackWeeklySummary(start: Date, end: Date): WeeklySummary {
    return {
      weekStart: start,
      weekEnd: end,
      totalHabits: 0,
      completedHabits: 0,
      completionRate: 0,
      streakChanges: 0,
      pointsEarned: 0,
      achievements: 0,
      topPerformingHabits: [],
      strugglingHabits: [],
      insights: this.getFallbackInsights(),
      motivationalMessage: 'Welcome to your wellness journey! 🌟',
    };
  }
}

export const insightsService = new InsightsService();

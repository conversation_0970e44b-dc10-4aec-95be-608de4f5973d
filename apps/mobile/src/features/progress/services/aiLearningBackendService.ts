import { supabase } from '../../../lib/supabase';
import { UserCorrection, FoodItem } from './photoAnalysisService';

export interface AILearningCorrection {
  id?: string;
  original_food_name: string;
  original_category: string;
  original_portion?: string;
  original_calories?: number;
  original_confidence: number;
  corrected_food_name?: string;
  corrected_category?: string;
  corrected_portion?: string;
  corrected_calories?: number;
  correction_type:
    | 'name_change'
    | 'category_change'
    | 'portion_change'
    | 'calorie_change'
    | 'removal'
    | 'addition';
  analysis_id: string;
  image_hash?: string;
}

export interface FoodPattern {
  food_name: string;
  category: string;
  alternative_names: string[];
  common_portions: Record<string, number>;
  avg_calories_per_100g: number;
  macro_profile: {
    protein: number;
    carbs: number;
    fat: number;
  };
  confidence_boost: number;
  recognition_accuracy: number;
}

export interface ImprovedSuggestion {
  suggested_name: string;
  category: string;
  confidence_boost: number;
  common_portions: Record<string, number>;
  avg_calories: number;
}

class AILearningBackendService {
  /**
   * Submit user correction to universal learning system
   */
  async submitCorrection(
    correction: UserCorrection,
    analysisId: string
  ): Promise<boolean> {
    try {
      // Convert UserCorrection to database format
      const dbCorrection = this.convertToDBFormat(correction, analysisId);

      // Insert into Supabase
      const { error } = await supabase
        .from('ai_learning_corrections')
        .insert(dbCorrection);

      if (error) {
        console.error('Failed to submit correction to database:', error);
        return false;
      }

      // Update learning statistics
      await this.updateLearningStats();

      return true;
    } catch (error) {
      console.error('Error submitting correction:', error);
      return false;
    }
  }

  /**
   * Get improved food suggestions based on learning
   */
  async getImprovedSuggestions(
    foodName: string,
    category?: string
  ): Promise<ImprovedSuggestion[]> {
    try {
      const { data, error } = await supabase.rpc(
        'get_improved_food_suggestions',
        {
          input_food_name: foodName,
          input_category: category || null,
        }
      );

      if (error) {
        console.error('Failed to get improved suggestions:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error getting improved suggestions:', error);
      return [];
    }
  }

  /**
   * Get food recognition patterns for better AI analysis
   */
  async getFoodPatterns(limit: number = 100): Promise<FoodPattern[]> {
    try {
      const { data, error } = await supabase
        .from('food_recognition_patterns')
        .select('*')
        .order('recognition_accuracy', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Failed to get food patterns:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error getting food patterns:', error);
      return [];
    }
  }

  /**
   * Search for similar foods using vector similarity
   */
  async findSimilarFoods(
    foodName: string,
    limit: number = 5
  ): Promise<FoodPattern[]> {
    try {
      // For now, use text-based similarity
      // TODO: Implement vector similarity search when embeddings are available
      const { data, error } = await supabase
        .from('food_recognition_patterns')
        .select('*')
        .or(`food_name.ilike.%${foodName}%,alternative_names.cs.{${foodName}}`)
        .order('recognition_accuracy', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Failed to find similar foods:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error finding similar foods:', error);
      return [];
    }
  }

  /**
   * Get AI learning statistics
   */
  async getLearningStats(): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('ai_learning_stats')
        .select('*')
        .order('date', { ascending: false })
        .limit(30); // Last 30 days

      if (error) {
        console.error('Failed to get learning stats:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error getting learning stats:', error);
      return null;
    }
  }

  /**
   * Update user learning preferences
   */
  async updateUserPreferences(preferences: {
    shareCorrections: boolean;
    sharePatterns: boolean;
  }): Promise<boolean> {
    try {
      // Create anonymous user hash
      const userHash = await this.getAnonymousUserHash();

      const { error } = await supabase
        .from('user_learning_preferences')
        .upsert({
          user_hash: userHash,
          share_corrections: preferences.shareCorrections,
          share_patterns: preferences.sharePatterns,
          updated_at: new Date().toISOString(),
        });

      if (error) {
        console.error('Failed to update user preferences:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error updating user preferences:', error);
      return false;
    }
  }

  /**
   * Get enhanced food analysis using learned patterns
   */
  async enhanceFoodAnalysis(foods: FoodItem[]): Promise<FoodItem[]> {
    try {
      const enhancedFoods: FoodItem[] = [];

      for (const food of foods) {
        // Get improved suggestions for this food
        const suggestions = await this.getImprovedSuggestions(
          food.name,
          food.category
        );

        const enhancedFood = { ...food };

        if (suggestions.length > 0) {
          const bestSuggestion = suggestions[0];

          // Boost confidence based on learning
          enhancedFood.confidence = Math.min(
            food.confidence + bestSuggestion.confidence_boost,
            100
          );

          // Use learned calorie data if available
          if (bestSuggestion.avg_calories && !food.calories) {
            enhancedFood.calories = Math.round(bestSuggestion.avg_calories);
          }

          // Use learned portion data if available
          if (
            bestSuggestion.common_portions &&
            Object.keys(bestSuggestion.common_portions).length > 0
          ) {
            const portions = Object.keys(bestSuggestion.common_portions);
            if (!food.estimatedPortion || food.estimatedPortion === 'unknown') {
              enhancedFood.estimatedPortion = portions[0]; // Use most common portion
            }
          }
        }

        enhancedFoods.push(enhancedFood);
      }

      return enhancedFoods;
    } catch (error) {
      console.error('Error enhancing food analysis:', error);
      return foods; // Return original if enhancement fails
    }
  }

  /**
   * Private helper methods
   */
  private convertToDBFormat(
    correction: UserCorrection,
    analysisId: string
  ): AILearningCorrection {
    const original = correction.originalFood;
    const corrected = correction.correctedFood;

    // Determine correction type
    let correctionType: AILearningCorrection['correction_type'] = 'name_change';

    if (!corrected) {
      correctionType = 'removal';
    } else if (original.name !== corrected.name) {
      correctionType = 'name_change';
    } else if (original.category !== corrected.category) {
      correctionType = 'category_change';
    } else if (original.estimatedPortion !== corrected.estimatedPortion) {
      correctionType = 'portion_change';
    } else if (original.calories !== corrected.calories) {
      correctionType = 'calorie_change';
    }

    return {
      original_food_name: original.name,
      original_category: original.category,
      original_portion: original.estimatedPortion,
      original_calories: original.calories,
      original_confidence: original.confidence,
      corrected_food_name: corrected?.name,
      corrected_category: corrected?.category,
      corrected_portion: corrected?.estimatedPortion,
      corrected_calories: corrected?.calories,
      correction_type: correctionType,
      analysis_id: analysisId,
      image_hash: this.generateImageHash(analysisId), // Simple hash for now
    };
  }

  private async updateLearningStats(): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];

      // Get today's correction count
      const { count } = await supabase
        .from('ai_learning_corrections')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', `${today}T00:00:00Z`)
        .lt('created_at', `${today}T23:59:59Z`);

      // Update or insert today's stats
      await supabase.from('ai_learning_stats').upsert({
        date: today,
        total_corrections: count || 0,
      });
    } catch (error) {
      console.error('Failed to update learning stats:', error);
    }
  }

  private async getAnonymousUserHash(): Promise<string> {
    try {
      // Get current user ID and create anonymous hash
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (user?.id) {
        // Create a hash that's anonymous but consistent for the same user
        const encoder = new TextEncoder();
        const data = encoder.encode(user.id + 'ai_learning_salt');
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      }

      // Fallback to device-based hash
      return 'anonymous_' + Date.now().toString(36);
    } catch (error) {
      return 'anonymous_' + Date.now().toString(36);
    }
  }

  private generateImageHash(analysisId: string): string {
    // Simple hash generation for image identification
    // In production, you'd use the actual image content
    return `img_${analysisId}_${Date.now().toString(36)}`;
  }
}

export const aiLearningBackendService = new AILearningBackendService();

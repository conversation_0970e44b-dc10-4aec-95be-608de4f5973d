import { progressDataService } from './progressDataService';
// import { aiChatService } from '../../ai-coach/services/aiChatService'; // TODO: Use for future AI integration

export interface ProgressAnomaly {
  type:
    | 'weight_change'
    | 'consistency_drop'
    | 'calorie_deviation'
    | 'habit_break';
  severity: 'low' | 'medium' | 'high';
  description: string;
  data: any;
  detectedAt: string;
  suggestions: string[];
}

export interface LearningPeriodStatus {
  daysActive: number;
  totalEntries: number;
  readyForAdjustments: boolean;
  confidenceLevel: number; // 0-1
  nextMilestone: {
    days: number;
    description: string;
  };
}

class AILearningService {
  private readonly LEARNING_MILESTONES = {
    INITIAL_SUGGESTIONS: 3, // Days before first suggestions
    BASIC_ADJUSTMENTS: 7, // Days before basic plan adjustments
    FULL_OPTIMIZATION: 14, // Days before comprehensive optimization
  };

  /**
   * Get current learning period status
   */
  async getLearningPeriodStatus(): Promise<LearningPeriodStatus> {
    try {
      const analytics = await progressDataService.getProgressAnalytics(30);
      if (!analytics) {
        return this.getDefaultStatus();
      }

      const daysActive = this.calculateActiveDays(analytics);
      const confidenceLevel = this.calculateConfidenceLevel(analytics);

      return {
        daysActive,
        totalEntries: analytics.totalEntries,
        readyForAdjustments:
          daysActive >= this.LEARNING_MILESTONES.BASIC_ADJUSTMENTS,
        confidenceLevel,
        nextMilestone: this.getNextMilestone(daysActive),
      };
    } catch (error) {
      console.error('Failed to get learning period status:', error);
      return this.getDefaultStatus();
    }
  }

  /**
   * Detect progress anomalies that require AI intervention
   */
  async detectAnomalies(): Promise<ProgressAnomaly[]> {
    try {
      const analytics = await progressDataService.getProgressAnalytics(14);
      if (!analytics || analytics.totalEntries < 5) {
        return [];
      }

      const anomalies: ProgressAnomaly[] = [];

      // Check for rapid weight changes
      const weightAnomaly = await this.detectWeightAnomalies(analytics);
      if (weightAnomaly) {
        anomalies.push(weightAnomaly);
      }

      // Check for consistency drops
      const consistencyAnomaly =
        await this.detectConsistencyAnomalies(analytics);
      if (consistencyAnomaly) {
        anomalies.push(consistencyAnomaly);
      }

      // Check for calorie deviations
      const calorieAnomaly = await this.detectCalorieAnomalies(analytics);
      if (calorieAnomaly) {
        anomalies.push(calorieAnomaly);
      }

      // Check for habit breaks
      const habitAnomaly = await this.detectHabitAnomalies(analytics);
      if (habitAnomaly) {
        anomalies.push(habitAnomaly);
      }

      return anomalies;
    } catch (error) {
      console.error('Failed to detect anomalies:', error);
      return [];
    }
  }

  /**
   * Generate AI plan adjustments based on learning period
   */
  async generatePlanAdjustments(): Promise<string[]> {
    try {
      const status = await this.getLearningPeriodStatus();

      if (!status.readyForAdjustments) {
        return [
          `I'm still learning your patterns (${status.daysActive}/${this.LEARNING_MILESTONES.BASIC_ADJUSTMENTS} days). Keep logging your progress!`,
        ];
      }

      const analytics = await progressDataService.getProgressAnalytics(14);
      const anomalies = await this.detectAnomalies();

      const adjustments: string[] = [];

      // Generate adjustments based on confidence level
      if (status.confidenceLevel >= 0.7) {
        adjustments.push(
          ...(await this.generateHighConfidenceAdjustments(
            analytics,
            anomalies
          ))
        );
      } else if (status.confidenceLevel >= 0.4) {
        adjustments.push(
          ...(await this.generateMediumConfidenceAdjustments(
            analytics,
            anomalies
          ))
        );
      } else {
        adjustments.push(
          ...(await this.generateLowConfidenceAdjustments(analytics))
        );
      }

      return adjustments;
    } catch (error) {
      console.error('Failed to generate plan adjustments:', error);
      return [
        'I need more data to make personalized recommendations. Keep logging your progress!',
      ];
    }
  }

  /**
   * Ask clarifying questions when anomalies are detected
   */
  async generateClarifyingQuestions(
    anomaly: ProgressAnomaly
  ): Promise<string[]> {
    const questions: string[] = [];

    switch (anomaly.type) {
      case 'weight_change':
        questions.push(
          'I noticed some significant weight changes. How are you feeling overall?',
          'Have you made any changes to your routine recently?',
          'Are you experiencing any stress or sleep issues?'
        );
        break;

      case 'consistency_drop':
        questions.push(
          "I see your logging consistency has dropped. What's been challenging?",
          'Are there specific times or situations where logging is difficult?',
          'Would you like me to adjust your meal plan to be more flexible?'
        );
        break;

      case 'calorie_deviation':
        questions.push(
          'Your calorie intake has varied quite a bit. How are your energy levels?',
          'Are you finding the meal portions satisfying?',
          'Would you prefer more or less variety in your meal plan?'
        );
        break;

      case 'habit_break':
        questions.push(
          'I noticed a break in your usual habits. Is everything okay?',
          'What would help you get back on track?',
          'Should we adjust your goals to be more achievable?'
        );
        break;
    }

    return questions;
  }

  /**
   * Private helper methods
   */
  private getDefaultStatus(): LearningPeriodStatus {
    return {
      daysActive: 0,
      totalEntries: 0,
      readyForAdjustments: false,
      confidenceLevel: 0,
      nextMilestone: {
        days: this.LEARNING_MILESTONES.INITIAL_SUGGESTIONS,
        description: 'First AI suggestions',
      },
    };
  }

  private calculateActiveDays(analytics: any): number {
    // Calculate unique days with entries
    const entries = analytics.entriesByType || {};
    const totalEntries = Object.values(
      entries as Record<string, number>
    ).reduce((sum: number, count: number) => sum + count, 0);

    // Rough estimate: assume 2-3 entries per active day
    return Math.min(Math.floor(totalEntries / 2), 30);
  }

  private calculateConfidenceLevel(analytics: any): number {
    const totalEntries = analytics.totalEntries || 0;
    const daysActive = this.calculateActiveDays(analytics);

    // Confidence based on data quantity and consistency
    const dataScore = Math.min(totalEntries / 50, 1); // Max confidence at 50 entries
    const consistencyScore = Math.min(daysActive / 14, 1); // Max confidence at 14 days

    return (dataScore + consistencyScore) / 2;
  }

  private getNextMilestone(daysActive: number): {
    days: number;
    description: string;
  } {
    if (daysActive < this.LEARNING_MILESTONES.INITIAL_SUGGESTIONS) {
      return {
        days: this.LEARNING_MILESTONES.INITIAL_SUGGESTIONS - daysActive,
        description: 'First AI suggestions',
      };
    } else if (daysActive < this.LEARNING_MILESTONES.BASIC_ADJUSTMENTS) {
      return {
        days: this.LEARNING_MILESTONES.BASIC_ADJUSTMENTS - daysActive,
        description: 'Basic plan adjustments',
      };
    } else if (daysActive < this.LEARNING_MILESTONES.FULL_OPTIMIZATION) {
      return {
        days: this.LEARNING_MILESTONES.FULL_OPTIMIZATION - daysActive,
        description: 'Full plan optimization',
      };
    } else {
      return {
        days: 0,
        description: 'Continuous optimization active',
      };
    }
  }

  private async detectWeightAnomalies(
    _analytics: any
  ): Promise<ProgressAnomaly | null> {
    // TODO: Implement weight change detection
    return null;
  }

  private async detectConsistencyAnomalies(
    _analytics: any
  ): Promise<ProgressAnomaly | null> {
    // TODO: Implement consistency drop detection
    return null;
  }

  private async detectCalorieAnomalies(
    _analytics: any
  ): Promise<ProgressAnomaly | null> {
    // TODO: Implement calorie deviation detection
    return null;
  }

  private async detectHabitAnomalies(
    _analytics: any
  ): Promise<ProgressAnomaly | null> {
    // TODO: Implement habit break detection
    return null;
  }

  private async generateHighConfidenceAdjustments(
    _analytics: any,
    _anomalies: ProgressAnomaly[]
  ): Promise<string[]> {
    return [
      'Based on your consistent progress, I can make specific meal plan adjustments.',
      'Your logging patterns show great consistency - let me optimize your workout schedule.',
    ];
  }

  private async generateMediumConfidenceAdjustments(
    _analytics: any,
    _anomalies: ProgressAnomaly[]
  ): Promise<string[]> {
    return [
      "I'm seeing some patterns in your progress. Let me suggest some gentle adjustments.",
      'Your data shows promise - I can make some preliminary recommendations.',
    ];
  }

  private async generateLowConfidenceAdjustments(
    _analytics: any
  ): Promise<string[]> {
    return [
      'I need a bit more data to make personalized recommendations.',
      "Keep logging consistently - I'll have better suggestions soon!",
    ];
  }
}

export const aiLearningService = new AILearningService();

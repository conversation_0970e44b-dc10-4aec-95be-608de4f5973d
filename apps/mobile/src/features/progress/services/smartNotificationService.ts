import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import { progressDataService } from './progressDataService';

export interface NotificationPattern {
  userId: string;
  preferredTimes: string[]; // Array of times like ["19:00", "08:30"]
  dayOfWeekPatterns: Record<string, string[]>; // e.g., {"monday": ["19:00"], "sunday": ["09:00"]}
  averageLogTime: string;
  consistency: number; // 0-1 score
  lastUpdated: string;
}

export interface SmartNotificationSettings {
  enabled: boolean;
  type: 'daily' | 'smart' | 'off';
  dailyTime?: string; // For daily notifications
  smartLearning: boolean; // Whether to learn from user patterns
  reminderTypes: {
    mealLogging: boolean;
    progressCheckin: boolean;
    habitReminders: boolean;
  };
}

class SmartNotificationService {
  private readonly STORAGE_KEYS = {
    PATTERNS: 'notification_patterns',
    SETTINGS: 'notification_settings',
    SCHEDULED: 'scheduled_notifications',
  };

  /**
   * Initialize notification service and request permissions
   */
  async initialize(): Promise<boolean> {
    try {
      const { status: existingStatus } =
        await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Notification permission not granted');
        return false;
      }

      // Configure notification behavior
      await Notifications.setNotificationHandler({
        handleNotification: async () =>
          ({
            shouldShowAlert: true,
            shouldPlaySound: true,
            shouldSetBadge: false,
            // New in SDK 53: explicit banner/list flags
            shouldShowBanner: true,
            shouldShowList: true,
          }) as any,
      });

      return true;
    } catch (error) {
      console.error('Failed to initialize notifications:', error);
      return false;
    }
  }

  /**
   * Get user notification settings
   */
  async getSettings(): Promise<SmartNotificationSettings> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.SETTINGS);
      if (stored) {
        return JSON.parse(stored);
      }

      // Default settings
      return {
        enabled: true,
        type: 'smart',
        smartLearning: true,
        reminderTypes: {
          mealLogging: true,
          progressCheckin: true,
          habitReminders: true,
        },
      };
    } catch (error) {
      console.error('Failed to get notification settings:', error);
      return {
        enabled: false,
        type: 'off',
        smartLearning: false,
        reminderTypes: {
          mealLogging: false,
          progressCheckin: false,
          habitReminders: false,
        },
      };
    }
  }

  /**
   * Update notification settings
   */
  async updateSettings(
    settings: Partial<SmartNotificationSettings>
  ): Promise<void> {
    try {
      const current = await this.getSettings();
      const updated = { ...current, ...settings };

      await AsyncStorage.setItem(
        this.STORAGE_KEYS.SETTINGS,
        JSON.stringify(updated)
      );

      // Reschedule notifications based on new settings
      await this.scheduleNotifications();
    } catch (error) {
      console.error('Failed to update notification settings:', error);
    }
  }

  /**
   * Learn from user logging patterns
   */
  async learnFromUserBehavior(): Promise<void> {
    try {
      const settings = await this.getSettings();
      if (!settings.smartLearning) {
        return;
      }

      // Get recent progress entries
      const entries = await progressDataService.getProgressEntries(
        undefined,
        100
      );
      if (entries.length < 5) {
        return;
      } // Need minimum data

      // Analyze patterns
      const patterns = this.analyzeLoggingPatterns(entries);

      // Store learned patterns
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.PATTERNS,
        JSON.stringify(patterns)
      );

      // Reschedule notifications based on learned patterns
      if (settings.type === 'smart') {
        await this.scheduleSmartNotifications(patterns);
      }
    } catch (error) {
      console.error('Failed to learn from user behavior:', error);
    }
  }

  /**
   * Schedule notifications based on current settings
   */
  async scheduleNotifications(): Promise<void> {
    try {
      // Cancel existing notifications
      await Notifications.cancelAllScheduledNotificationsAsync();

      const settings = await this.getSettings();
      if (!settings.enabled || settings.type === 'off') {
        return;
      }

      if (settings.type === 'daily' && settings.dailyTime) {
        await this.scheduleDailyNotifications(settings);
      } else if (settings.type === 'smart') {
        const patterns = await this.getLearnedPatterns();
        await this.scheduleSmartNotifications(patterns);
      }
    } catch (error) {
      console.error('Failed to schedule notifications:', error);
    }
  }

  /**
   * Schedule daily notifications at fixed time
   */
  private async scheduleDailyNotifications(
    settings: SmartNotificationSettings
  ): Promise<void> {
    if (!settings.dailyTime) {
      return;
    }

    const [hours, minutes] = settings.dailyTime.split(':').map(Number);

    if (settings.reminderTypes.progressCheckin) {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Daily Check-in 📝',
          body: 'How did your day go? Log your progress to help your AI coach!',
          data: { type: 'progress_checkin' },
        },
        trigger: {
          type: (Notifications as any).SchedulableTriggerInputTypes?.CALENDAR,
          hour: hours,
          minute: minutes,
          repeats: true,
        } as any,
      });
    }
  }

  /**
   * Schedule smart notifications based on learned patterns
   */
  private async scheduleSmartNotifications(
    patterns: NotificationPattern | null
  ): Promise<void> {
    if (!patterns || patterns.preferredTimes.length === 0) {
      // Fallback to default time if no patterns learned
      await this.scheduleDailyNotifications({
        enabled: true,
        type: 'daily',
        dailyTime: '19:00',
        smartLearning: true,
        reminderTypes: {
          mealLogging: true,
          progressCheckin: true,
          habitReminders: true,
        },
      });
      return;
    }

    // Schedule notifications at learned preferred times
    for (const time of patterns.preferredTimes) {
      const [hours, minutes] = time.split(':').map(Number);

      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Time to Log! 🍽️',
          body: 'You usually log around this time. How was your meal?',
          data: { type: 'smart_reminder', learnedTime: time },
        },
        trigger: {
          type: (Notifications as any).SchedulableTriggerInputTypes?.CALENDAR,
          hour: hours,
          minute: minutes,
          repeats: true,
        } as any,
      });
    }
  }

  /**
   * Analyze user logging patterns from progress entries
   */
  private analyzeLoggingPatterns(entries: any[]): NotificationPattern {
    const times: string[] = [];
    const dayPatterns: Record<string, string[]> = {};

    entries.forEach(entry => {
      const date = new Date(entry.timestamp);
      const timeString = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
      const dayOfWeek = date
        .toLocaleDateString(undefined, { weekday: 'short' })
        .toLowerCase();

      times.push(timeString);

      if (!dayPatterns[dayOfWeek]) {
        dayPatterns[dayOfWeek] = [];
      }
      dayPatterns[dayOfWeek].push(timeString);
    });

    // Find most common times (simplified clustering)
    const timeFrequency: Record<string, number> = {};
    times.forEach(time => {
      const hourMinute = time.substring(0, 2) + ':00'; // Round to nearest hour
      timeFrequency[hourMinute] = (timeFrequency[hourMinute] || 0) + 1;
    });

    // Get top 2 most frequent times
    const preferredTimes = Object.entries(timeFrequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 2)
      .map(([time]) => time);

    // Calculate consistency (how often user logs)
    const uniqueDays = new Set(
      entries.map(e => new Date(e.timestamp).toDateString())
    );
    const consistency = Math.min(uniqueDays.size / 30, 1); // Based on last 30 days

    return {
      userId: 'current_user',
      preferredTimes,
      dayOfWeekPatterns: dayPatterns,
      averageLogTime: preferredTimes[0] || '19:00',
      consistency,
      lastUpdated: new Date().toISOString(),
    };
  }

  /**
   * Get learned notification patterns
   */
  private async getLearnedPatterns(): Promise<NotificationPattern | null> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.PATTERNS);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Send immediate notification (for testing or special events)
   */
  async sendImmediateNotification(
    title: string,
    body: string,
    data?: any
  ): Promise<void> {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data || {},
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Failed to send immediate notification:', error);
    }
  }

  /**
   * Handle notification response (when user taps notification)
   */
  setupNotificationResponseHandler(handler: (response: any) => void): void {
    Notifications.addNotificationResponseReceivedListener(handler);
  }
}

export const smartNotificationService = new SmartNotificationService();

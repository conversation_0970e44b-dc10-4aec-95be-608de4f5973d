import AsyncStorage from '@react-native-async-storage/async-storage';
import { aiLearningBackendService } from './aiLearningBackendService';
import { databaseSetupService } from './databaseSetupService';
import { smartNotificationService } from './smartNotificationService';
import { vectorDatabaseService } from './vectorDatabaseService';

export interface AILearningStatus {
  isInitialized: boolean;
  databaseReady: boolean;
  vectorDatabaseReady: boolean;
  notificationsEnabled: boolean;
  userConsent: boolean;
  lastInitialized: string;
  version: string;
}

class AILearningInitService {
  private readonly STORAGE_KEY = 'ai_learning_status';
  private readonly CURRENT_VERSION = '1.0.0';

  /**
   * Initialize the complete AI learning system
   */
  async initializeAILearningSystem(): Promise<AILearningStatus> {
    try {
      // Short-circuit in Expo Go or when disabled via env for quick local testing
      try {
        // Lazy require to avoid import issues in non-Expo environments
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const Constants = require('expo-constants').default;
        const isExpoGo =
          Constants?.executionEnvironment === 'storeClient' ||
          Constants?.appOwnership === 'expo';
        const disableAI =
          process.env.EXPO_PUBLIC_DISABLE_AI_LEARNING === 'true' ||
          process.env.EXPO_PUBLIC_SKIP_AI_LEARNING_INIT === 'true';
        if (isExpoGo || disableAI) {
          console.warn(
            `⏭️ Skipping AI Learning initialization (isExpoGo=${isExpoGo}, disableAI=${disableAI})`
          );
          return {
            isInitialized: false,
            databaseReady: false,
            vectorDatabaseReady: false,
            notificationsEnabled: false,
            userConsent: false,
            lastInitialized: new Date().toISOString(),
            version: this.CURRENT_VERSION,
          };
        }
      } catch (_) {
        // If constants unavailable, continue normally
      }

      console.log('🤖 Initializing AI Learning System...');

      // Check if already initialized
      const currentStatus = await this.getInitializationStatus();
      if (
        currentStatus.isInitialized &&
        currentStatus.version === this.CURRENT_VERSION
      ) {
        console.log('✅ AI Learning System already initialized');
        return currentStatus;
      }

      // Initialize step by step
      const status: AILearningStatus = {
        isInitialized: false,
        databaseReady: false,
        vectorDatabaseReady: false,
        notificationsEnabled: false,
        userConsent: false,
        lastInitialized: new Date().toISOString(),
        version: this.CURRENT_VERSION,
      };

      // Step 1: Setup database schema
      console.log('📊 Setting up database schema...');
      status.databaseReady =
        await databaseSetupService.deployAILearningSchema();

      if (!status.databaseReady) {
        console.warn(
          '⚠️ Database setup failed, continuing with limited functionality'
        );
      }

      // Step 2: Initialize vector database
      console.log('🔍 Initializing vector database...');
      status.vectorDatabaseReady = await this.initializeVectorDatabase();

      // Step 3: Setup notifications
      console.log('🔔 Setting up smart notifications...');
      status.notificationsEnabled = await smartNotificationService.initialize();

      // Step 4: Check user consent
      status.userConsent = await this.checkUserConsent();

      // Step 5: Perform initial data sync
      if (status.databaseReady) {
        await this.performInitialDataSync();
      }

      // Mark as initialized
      status.isInitialized = true;
      await this.saveInitializationStatus(status);

      console.log('🎉 AI Learning System initialized successfully!');
      console.log('Status:', status);

      return status;
    } catch (error) {
      console.error('❌ Failed to initialize AI Learning System:', error);

      // Return partial status
      return {
        isInitialized: false,
        databaseReady: false,
        vectorDatabaseReady: false,
        notificationsEnabled: false,
        userConsent: false,
        lastInitialized: new Date().toISOString(),
        version: this.CURRENT_VERSION,
      };
    }
  }

  /**
   * Get current initialization status
   */
  async getInitializationStatus(): Promise<AILearningStatus> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to get initialization status:', error);
    }

    // Return default status
    return {
      isInitialized: false,
      databaseReady: false,
      vectorDatabaseReady: false,
      notificationsEnabled: false,
      userConsent: false,
      lastInitialized: '',
      version: '0.0.0',
    };
  }

  /**
   * Force re-initialization (for updates or troubleshooting)
   */
  async forceReinitialization(): Promise<AILearningStatus> {
    console.log('🔄 Forcing AI Learning System re-initialization...');

    // Clear stored status
    await AsyncStorage.removeItem(this.STORAGE_KEY);

    // Re-initialize
    return await this.initializeAILearningSystem();
  }

  /**
   * Check system health and performance
   */
  async checkSystemHealth(): Promise<{
    overall: 'healthy' | 'warning' | 'error';
    database: boolean;
    vectorDatabase: boolean;
    notifications: boolean;
    learningActive: boolean;
    lastCorrection: string | null;
    totalCorrections: number;
  }> {
    try {
      const status = await this.getInitializationStatus();

      // Check database connectivity
      const databaseHealth = await this.checkDatabaseHealth();

      // Check learning activity
      const learningStats = await aiLearningBackendService.getLearningStats();
      const totalCorrections =
        learningStats?.reduce(
          (sum: number, stat: any) => sum + (stat.total_corrections || 0),
          0
        ) || 0;

      // Determine overall health
      let overall: 'healthy' | 'warning' | 'error' = 'healthy';
      if (!status.isInitialized || !databaseHealth) {
        overall = 'error';
      } else if (!status.vectorDatabaseReady || !status.notificationsEnabled) {
        overall = 'warning';
      }

      return {
        overall,
        database: databaseHealth,
        vectorDatabase: status.vectorDatabaseReady,
        notifications: status.notificationsEnabled,
        learningActive: totalCorrections > 0,
        lastCorrection: learningStats?.[0]?.date || null,
        totalCorrections,
      };
    } catch (error) {
      console.error('Failed to check system health:', error);
      return {
        overall: 'error',
        database: false,
        vectorDatabase: false,
        notifications: false,
        learningActive: false,
        lastCorrection: null,
        totalCorrections: 0,
      };
    }
  }

  /**
   * Get AI learning insights and statistics
   */
  async getAILearningInsights(): Promise<any> {
    try {
      const [learningStats, vectorStats, systemHealth] = await Promise.all([
        aiLearningBackendService.getLearningStats(),
        vectorDatabaseService.getEmbeddingStats(),
        this.checkSystemHealth(),
      ]);

      return {
        systemHealth,
        learningStats,
        vectorStats,
        insights: {
          totalCorrections: systemHealth.totalCorrections,
          learningActive: systemHealth.learningActive,
          lastActivity: systemHealth.lastCorrection,
          systemStatus: systemHealth.overall,
        },
      };
    } catch (error) {
      console.error('Failed to get AI learning insights:', error);
      return null;
    }
  }

  /**
   * Private helper methods
   */
  private async initializeVectorDatabase(): Promise<boolean> {
    try {
      // For now, just check if we can access the vector database
      // In production, you might want to create initial embeddings
      const stats = await vectorDatabaseService.getEmbeddingStats();
      return stats !== null;
    } catch (error) {
      console.warn('Vector database initialization failed:', error);
      return false;
    }
  }

  private async checkUserConsent(): Promise<boolean> {
    try {
      // Check if user has given consent for AI learning
      const stored = await AsyncStorage.getItem('ai_learning_consent');
      return stored === 'true';
    } catch (error) {
      return false;
    }
  }

  private async performInitialDataSync(): Promise<void> {
    try {
      console.log('🔄 Performing initial data sync...');

      // Get food patterns to warm up the cache
      await aiLearningBackendService.getFoodPatterns(50);

      // Check for any pending corrections to sync
      // This would be implemented based on your local storage strategy

      console.log('✅ Initial data sync completed');
    } catch (error) {
      console.warn('Initial data sync failed:', error);
    }
  }

  private async checkDatabaseHealth(): Promise<boolean> {
    try {
      // Simple health check - try to get food patterns
      const patterns = await aiLearningBackendService.getFoodPatterns(1);
      return Array.isArray(patterns);
    } catch (error) {
      return false;
    }
  }

  private async saveInitializationStatus(
    status: AILearningStatus
  ): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(status));
    } catch (error) {
      console.error('Failed to save initialization status:', error);
    }
  }
}

export const aiLearningInitService = new AILearningInitService();

import {
  photoAnalysisService,
  PhotoAnalysisResult,
} from './photoAnalysisService';

// Local deterministic stub for MVP (no network)
export async function analyzeMealPhotoLocalStub(
  _imageUri: string
): Promise<PhotoAnalysisResult> {
  const now = Date.now();
  return {
    foods: [
      {
        id: `food_${now}_0`,
        name: 'grilled chicken breast',
        category: 'protein',
        estimatedPortion: '1 piece (~120g)',
        confidence: 85,
        calories: 198,
        macros: { protein: 36, carbs: 0, fat: 4 },
      },
      {
        id: `food_${now}_1`,
        name: 'steamed broccoli',
        category: 'vegetables',
        estimatedPortion: '1 cup',
        confidence: 80,
        calories: 55,
        macros: { protein: 4, carbs: 11, fat: 0 },
      },
      {
        id: `food_${now}_2`,
        name: 'brown rice',
        category: 'carbs',
        estimatedPortion: '1/2 cup',
        confidence: 78,
        calories: 108,
        macros: { protein: 2, carbs: 22, fat: 1 },
      },
    ],
    totalCalories: 198 + 55 + 108,
    confidence: 82,
    analysisId: `analysis_${now}`,
    timestamp: new Date(now).toISOString(),
  };
}

export async function analyzeMealPhoto(
  imageUri: string
): Promise<PhotoAnalysisResult> {
  const enabled = process.env.EXPO_PUBLIC_ENABLE_VISION === 'true';
  if (!enabled) {
    return analyzeMealPhotoLocalStub(imageUri);
  }
  return photoAnalysisService.analyzeMealPhoto(imageUri);
}

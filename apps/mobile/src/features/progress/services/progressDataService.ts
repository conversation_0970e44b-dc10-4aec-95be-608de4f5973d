import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../../../lib/supabase';
import { ProgressLogData } from '../components/LogProgressQuickAction';

export interface ProgressEntry {
  id: string;
  userId: string;
  type: 'meal' | 'habit' | 'mood' | 'workout' | 'weight';
  data: any;
  timestamp: string;
  synced: boolean;
  localOnly?: boolean;
}

export interface UserProgressSettings {
  shareWithAI: boolean;
  notificationPreference: 'daily' | 'smart' | 'off';
  dailyReminderTime?: string;
  enablePhotoAnalysis: boolean;
  // New: preferred hydration unit for logging
  hydrationUnit?: 'ml' | 'fl_oz';
}

class ProgressDataService {
  private readonly STORAGE_KEYS = {
    PROGRESS_ENTRIES: 'progress_entries',
    SETTINGS: 'progress_settings',
    SYNC_QUEUE: 'progress_sync_queue',
  };

  /**
   * Save progress entry locally and queue for sync
   */
  async saveProgressEntry(data: ProgressLogData): Promise<string> {
    try {
      const entry: ProgressEntry = {
        id: `progress_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId: await this.getCurrentUserId(),
        type: data.type,
        data: data.data,
        timestamp: data.timestamp,
        synced: false,
      };

      // Save locally first
      await this.saveEntryLocally(entry);

      // Queue for sync
      await this.queueForSync(entry);

      // Attempt immediate sync if online
      this.syncProgressData().catch(console.error);

      return entry.id;
    } catch (error) {
      console.error('Failed to save progress entry:', error);
      throw error;
    }
  }

  /**
   * Get progress entries with optional filtering
   */
  async getProgressEntries(
    type?: string,
    limit: number = 50,
    startDate?: string,
    endDate?: string
  ): Promise<ProgressEntry[]> {
    try {
      const entries = await this.getLocalEntries();

      let filtered = entries;

      if (type) {
        filtered = filtered.filter(entry => entry.type === type);
      }

      if (startDate) {
        filtered = filtered.filter(entry => entry.timestamp >= startDate);
      }

      if (endDate) {
        filtered = filtered.filter(entry => entry.timestamp <= endDate);
      }

      // Sort by timestamp (newest first)
      filtered.sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );

      return filtered.slice(0, limit);
    } catch (error) {
      console.error('Failed to get progress entries:', error);
      return [];
    }
  }

  /**
   * Get user progress settings
   */
  async getProgressSettings(): Promise<UserProgressSettings> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.SETTINGS);
      if (stored) {
        return JSON.parse(stored);
      }

      // Default settings
      return this.getDefaultSettings();
    } catch (error) {
      console.error('Failed to get progress settings:', error);
      return this.getDefaultSettings();
    }
  }

  private getDefaultSettings(): UserProgressSettings {
    // Default by locale: ml for international, fl_oz for US
    let hydrationUnit: 'ml' | 'fl_oz' = 'ml';
    try {
      const locale = Intl.DateTimeFormat().resolvedOptions().locale || '';
      hydrationUnit = /(^|[-_])US($|[-_])/i.test(locale) ? 'fl_oz' : 'ml';
    } catch (e) {
      // ignore
    }
    return {
      shareWithAI: true,
      notificationPreference: 'smart',
      enablePhotoAnalysis: true,
      hydrationUnit,
    };
  }

  /**
   * Update user progress settings
   */
  async updateProgressSettings(
    settings: Partial<UserProgressSettings>
  ): Promise<void> {
    try {
      const current = await this.getProgressSettings();
      const updated = { ...current, ...settings };

      await AsyncStorage.setItem(
        this.STORAGE_KEYS.SETTINGS,
        JSON.stringify(updated)
      );

      // Sync settings to cloud
      await this.syncSettingsToCloud(updated);
    } catch (error) {
      console.error('Failed to update progress settings:', error);
      throw error;
    }
  }

  /**
   * Sync progress data to cloud
   */
  async syncProgressData(): Promise<{
    synced: boolean;
    count: number;
    error?: string;
  }> {
    try {
      const syncQueue = await this.getSyncQueue();
      if (syncQueue.length === 0) {
        return { synced: true, count: 0 };
      }

      const userId = await this.getCurrentUserId();
      if (!userId) {
        return { synced: false, count: 0, error: 'No user' };
      }

      // Batch sync entries
      const { error } = await supabase.from('progress_entries').upsert(
        syncQueue.map(entry => ({
          id: entry.id,
          user_id: entry.userId,
          type: entry.type,
          data: entry.data,
          timestamp: entry.timestamp,
          created_at: new Date().toISOString(),
        }))
      );

      if (error) {
        console.error('Sync failed:', error);
        return { synced: false, count: 0, error: error.message };
      }

      // Mark entries as synced
      await this.markEntriesAsSynced(syncQueue.map(e => e.id));

      // Clear sync queue
      await this.clearSyncQueue();

      // Optional: trigger metrics sync (best-effort)
      try {
        const { progressIntegrationService } = await import(
          './progressIntegrationService'
        );
        await progressIntegrationService.syncProgressData();
      } catch (e) {
        // ignore metrics sync errors
      }

      return { synced: true, count: syncQueue.length };
    } catch (error: any) {
      console.error('Failed to sync progress data:', error);
      return {
        synced: false,
        count: 0,
        error: error?.message || 'Unknown error',
      };
    }
  }

  /**
   * Get progress analytics for AI learning period
   */
  async getProgressAnalytics(days: number = 14): Promise<any> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const entries = await this.getProgressEntries(
        undefined,
        1000,
        startDate.toISOString()
      );

      // Analyze patterns
      const analytics = {
        totalEntries: entries.length,
        entriesByType: this.groupEntriesByType(entries),
        dailyAverages: this.calculateDailyAverages(entries),
        patterns: this.detectPatterns(entries),
        anomalies: this.detectAnomalies(entries),
      };

      return analytics;
    } catch (error) {
      console.error('Failed to get progress analytics:', error);
      return null;
    }
  }

  /**
   * Private helper methods
   */
  private async saveEntryLocally(entry: ProgressEntry): Promise<void> {
    const entries = await this.getLocalEntries();
    entries.push(entry);

    await AsyncStorage.setItem(
      this.STORAGE_KEYS.PROGRESS_ENTRIES,
      JSON.stringify(entries)
    );
  }

  private async getLocalEntries(): Promise<ProgressEntry[]> {
    try {
      const stored = await AsyncStorage.getItem(
        this.STORAGE_KEYS.PROGRESS_ENTRIES
      );
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      return [];
    }
  }

  private async queueForSync(entry: ProgressEntry): Promise<void> {
    const queue = await this.getSyncQueue();
    queue.push(entry);

    await AsyncStorage.setItem(
      this.STORAGE_KEYS.SYNC_QUEUE,
      JSON.stringify(queue)
    );
  }

  private async getSyncQueue(): Promise<ProgressEntry[]> {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.SYNC_QUEUE);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      return [];
    }
  }

  private async clearSyncQueue(): Promise<void> {
    await AsyncStorage.setItem(
      this.STORAGE_KEYS.SYNC_QUEUE,
      JSON.stringify([])
    );
  }

  private async markEntriesAsSynced(entryIds: string[]): Promise<void> {
    const entries = await this.getLocalEntries();
    const updated = entries.map(entry =>
      entryIds.includes(entry.id) ? { ...entry, synced: true } : entry
    );

    await AsyncStorage.setItem(
      this.STORAGE_KEYS.PROGRESS_ENTRIES,
      JSON.stringify(updated)
    );
  }

  private async getCurrentUserId(): Promise<string> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    return user?.id || '';
  }

  private async syncSettingsToCloud(
    settings: UserProgressSettings
  ): Promise<void> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        return;
      }

      await supabase.from('user_progress_settings').upsert({
        user_id: userId,
        settings: settings,
        updated_at: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Failed to sync settings to cloud:', error);
    }
  }

  private groupEntriesByType(entries: ProgressEntry[]): Record<string, number> {
    return entries.reduce(
      (acc, entry) => {
        acc[entry.type] = (acc[entry.type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );
  }

  private calculateDailyAverages(_entries: ProgressEntry[]): any {
    // TODO: Implement daily averages calculation
    return {};
  }

  private detectPatterns(_entries: ProgressEntry[]): any {
    // TODO: Implement pattern detection
    return {};
  }

  private detectAnomalies(_entries: ProgressEntry[]): any {
    // TODO: Implement anomaly detection
    return {};
  }
}

export const progressDataService = new ProgressDataService();

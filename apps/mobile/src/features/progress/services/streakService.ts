import { format, subDays, isToday, isYesterday } from 'date-fns';
import { supabase } from '../../../lib/supabase';
import { analyticsService, StreakData } from './analyticsService';

export interface StreakSummary {
  total_active_streaks: number;
  longest_current_streak: number;
  longest_all_time_streak: number;
  streak_types: {
    habit: number;
    workout: number;
    nutrition: number;
    overall: number;
  };
}

export interface StreakCalendarData {
  date: string;
  has_activity: boolean;
  completion_rate: number;
  activities: {
    habits: number;
    workouts: number;
    nutrition: number;
  };
}

class StreakService {
  // ==================== STREAK SUMMARY ====================

  async getStreakSummary(): Promise<StreakSummary> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const streaks = await analyticsService.getStreakData();

    const activeStreaks = streaks.filter(s => s.current_streak > 0);
    const longestCurrent = Math.max(...streaks.map(s => s.current_streak), 0);
    const longestAllTime = Math.max(...streaks.map(s => s.longest_streak), 0);

    const streakTypes = {
      habit: streaks.filter(
        s => s.streak_type === 'habit' && s.current_streak > 0
      ).length,
      workout: streaks.filter(
        s => s.streak_type === 'workout' && s.current_streak > 0
      ).length,
      nutrition: streaks.filter(
        s => s.streak_type === 'nutrition' && s.current_streak > 0
      ).length,
      overall: streaks.filter(
        s => s.streak_type === 'overall' && s.current_streak > 0
      ).length,
    };

    return {
      total_active_streaks: activeStreaks.length,
      longest_current_streak: longestCurrent,
      longest_all_time_streak: longestAllTime,
      streak_types: streakTypes,
    };
  }

  // ==================== HABIT STREAK MANAGEMENT ====================

  async updateHabitStreak(
    habitId: string,
    habitName: string,
    completionDate: string
  ): Promise<void> {
    await analyticsService.updateStreak(
      'habit',
      habitId,
      habitName,
      completionDate
    );

    // Also update overall streak if this is today's activity
    if (isToday(new Date(completionDate))) {
      await this.updateOverallStreak(completionDate);
    }
  }

  async breakHabitStreak(habitId: string): Promise<void> {
    await analyticsService.breakStreak('habit', habitId);
  }

  async getHabitStreaks(): Promise<StreakData[]> {
    return await analyticsService.getStreakData('habit');
  }

  async getTopHabitStreaks(limit: number = 5): Promise<StreakData[]> {
    const habitStreaks = await this.getHabitStreaks();
    return habitStreaks
      .sort((a, b) => b.current_streak - a.current_streak)
      .slice(0, limit);
  }

  // ==================== OVERALL STREAK MANAGEMENT ====================

  async updateOverallStreak(
    activityDate: string = format(new Date(), 'yyyy-MM-dd')
  ): Promise<void> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    // Check if user has any activity on this date
    const hasActivity = await this.hasActivityOnDate(activityDate);

    if (hasActivity) {
      await analyticsService.updateStreak(
        'overall',
        undefined,
        'Overall Activity',
        activityDate
      );
    }
  }

  async getOverallStreak(): Promise<StreakData | null> {
    const streaks = await analyticsService.getStreakData('overall');
    return streaks.find(s => !s.entity_id) ?? null;
  }

  private async hasActivityOnDate(date: string): Promise<boolean> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return false;
    }

    // Check for habit completions
    const { data: habitCompletions } = await supabase
      .from('habit_completions')
      .select('id')
      .eq('user_id', user.id)
      .eq('completion_date', date)
      .limit(1);

    if (habitCompletions && habitCompletions.length > 0) {
      return true;
    }

    // TODO: Check for workout completions when workout tracking is implemented
    // TODO: Check for nutrition logs when nutrition tracking is implemented

    return false;
  }

  // ==================== STREAK CALENDAR ====================

  async getStreakCalendar(days: number = 30): Promise<StreakCalendarData[]> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const calendarData: StreakCalendarData[] = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = subDays(today, i);
      const dateString = format(date, 'yyyy-MM-dd');

      // Get habit completions for this date
      const { data: habitCompletions } = await supabase
        .from('habit_completions')
        .select('habit_id')
        .eq('user_id', user.id)
        .eq('completion_date', dateString);

      // Get total habits for this date (habits that existed on this date)
      const { data: totalHabits } = await supabase
        .from('habits')
        .select('id')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .lte('created_at', dateString);

      const habitsCompleted = habitCompletions?.length ?? 0;
      const habitsTotal = totalHabits?.length ?? 0;
      const completionRate =
        habitsTotal > 0 ? Math.round((habitsCompleted / habitsTotal) * 100) : 0;

      calendarData.push({
        date: dateString,
        has_activity: habitsCompleted > 0,
        completion_rate: completionRate,
        activities: {
          habits: habitsCompleted,
          workouts: 0, // TODO: Implement when workout tracking is available
          nutrition: 0, // TODO: Implement when nutrition tracking is available
        },
      });
    }

    return calendarData;
  }

  // ==================== STREAK INSIGHTS ====================

  async getStreakInsights(): Promise<string[]> {
    const summary = await this.getStreakSummary();
    const overallStreak = await this.getOverallStreak();
    const topHabitStreaks = await this.getTopHabitStreaks(3);

    const insights: string[] = [];

    // Overall streak insights
    if (overallStreak && overallStreak.current_streak > 0) {
      if (overallStreak.current_streak >= 30) {
        insights.push(
          `🔥 Incredible! You've been active for ${overallStreak.current_streak} days straight!`
        );
      } else if (overallStreak.current_streak >= 7) {
        insights.push(
          `⚡ Great momentum! ${overallStreak.current_streak} days of consistent activity!`
        );
      } else if (overallStreak.current_streak >= 3) {
        insights.push(
          `🌟 Building momentum with ${overallStreak.current_streak} active days!`
        );
      }
    }

    // Habit streak insights
    if (topHabitStreaks.length > 0) {
      const topStreak = topHabitStreaks[0];
      if (topStreak.current_streak >= 21) {
        insights.push(
          `💎 Your "${topStreak.entity_name}" habit is becoming automatic! ${topStreak.current_streak} days strong!`
        );
      } else if (topStreak.current_streak >= 7) {
        insights.push(
          `🎯 "${topStreak.entity_name}" is on fire with a ${topStreak.current_streak}-day streak!`
        );
      }
    }

    // Multiple streaks insight
    if (summary.total_active_streaks >= 3) {
      insights.push(
        `🚀 Amazing! You're maintaining ${summary.total_active_streaks} different streaks!`
      );
    }

    // Longest streak achievement
    if (summary.longest_all_time_streak >= 50) {
      insights.push(
        `🏆 Your longest streak of ${summary.longest_all_time_streak} days shows incredible dedication!`
      );
    }

    // Encouragement if no current streaks
    if (summary.total_active_streaks === 0) {
      insights.push(
        '💪 Every expert was once a beginner. Start your streak today!'
      );
    }

    return insights;
  }

  // ==================== STREAK MAINTENANCE ====================

  async checkAndMaintainStreaks(): Promise<void> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const _yesterday = format(subDays(new Date(), 1), 'yyyy-MM-dd'); // eslint-disable-line @typescript-eslint/no-unused-vars
    const _today = format(new Date(), 'yyyy-MM-dd'); // eslint-disable-line @typescript-eslint/no-unused-vars

    // Get all active streaks
    const streaks = await analyticsService.getStreakData();

    for (const streak of streaks) {
      if (!streak.last_activity_date) {
        continue;
      }

      const lastActivity = new Date(streak.last_activity_date);

      // If last activity was not yesterday or today, break the streak
      if (!isToday(lastActivity) && !isYesterday(lastActivity)) {
        await analyticsService.breakStreak(
          streak.streak_type,
          streak.entity_id
        );
      }
    }
  }

  // ==================== STREAK RECOVERY ====================

  async getStreakRecoveryTips(): Promise<string[]> {
    const brokenStreaks = await this.getBrokenStreaks();
    const tips: string[] = [];

    if (brokenStreaks.length > 0) {
      tips.push(
        "🔄 Don't worry about broken streaks - what matters is getting back on track!"
      );
      tips.push('🎯 Start small: focus on just one habit to rebuild momentum');
      tips.push(
        '⏰ Set a specific time each day for your most important habit'
      );
      tips.push('📱 Use reminders to help you stay consistent');
    }

    return tips;
  }

  private async getBrokenStreaks(): Promise<StreakData[]> {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('streak_tracking')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', false)
      .gt('longest_streak', 0)
      .order('updated_at', { ascending: false })
      .limit(5);

    if (error) {
      throw error;
    }
    return data || [];
  }

  // ==================== STREAK REWARDS ====================

  async getStreakMilestones(): Promise<
    { days: number; reward: string; achieved: boolean }[]
  > {
    const overallStreak = await this.getOverallStreak();
    const currentStreak = overallStreak?.current_streak ?? 0;

    const milestones = [
      { days: 3, reward: '🌱 Habit Seedling', achieved: currentStreak >= 3 },
      { days: 7, reward: '⚡ Week Warrior', achieved: currentStreak >= 7 },
      { days: 14, reward: '🔥 Two Week Titan', achieved: currentStreak >= 14 },
      { days: 21, reward: '💎 Habit Diamond', achieved: currentStreak >= 21 },
      { days: 30, reward: '👑 Month Master', achieved: currentStreak >= 30 },
      {
        days: 50,
        reward: '🚀 Streak Superstar',
        achieved: currentStreak >= 50,
      },
      {
        days: 100,
        reward: '🏆 Centurion Champion',
        achieved: currentStreak >= 100,
      },
    ];

    return milestones;
  }
}

export const streakService = new StreakService();

import { Alert } from 'react-native';
import { notificationService } from '../../../services/notificationService';
import { UserAchievement } from './achievementService';

export interface AchievementNotificationOptions {
  showAlert?: boolean;
  showPushNotification?: boolean;
  playSound?: boolean;
  vibrate?: boolean;
}

class AchievementNotificationService {
  private defaultOptions: AchievementNotificationOptions = {
    showAlert: true,
    showPushNotification: true,
    playSound: true,
    vibrate: true,
  };

  // ==================== ACHIEVEMENT NOTIFICATIONS ====================

  async notifyAchievementUnlocked(
    achievement: UserAchievement,
    options: AchievementNotificationOptions = {}
  ): Promise<void> {
    const finalOptions = { ...this.defaultOptions, ...options };

    const achievementData = achievement.achievement;
    if (!achievementData) {
      return;
    }

    // Show in-app alert
    if (finalOptions.showAlert) {
      this.showAchievementAlert(achievement);
    }

    // Schedule push notification
    if (finalOptions.showPushNotification) {
      await this.schedulePushNotification(achievement);
    }

    // Log achievement unlock for analytics
    console.log('🏆 Achievement Unlocked:', {
      name: achievementData.name,
      category: achievementData.category,
      rarity: achievementData.rarity,
      points: achievementData.points,
      unlockedAt: achievement.unlocked_at,
    });
  }

  async notifyMultipleAchievements(
    achievements: UserAchievement[],
    options: AchievementNotificationOptions = {}
  ): Promise<void> {
    if (achievements.length === 0) {
      return;
    }

    if (achievements.length === 1) {
      await this.notifyAchievementUnlocked(achievements[0], options);
      return;
    }

    // Handle multiple achievements
    const finalOptions = { ...this.defaultOptions, ...options };

    if (finalOptions.showAlert) {
      this.showMultipleAchievementsAlert(achievements);
    }

    if (finalOptions.showPushNotification) {
      await this.scheduleMultiplePushNotifications(achievements);
    }
  }

  // ==================== ALERT DIALOGS ====================

  private showAchievementAlert(achievement: UserAchievement): void {
    const achievementData = achievement.achievement;
    if (!achievementData) {
      return;
    }

    const rarityEmoji = this.getRarityEmoji(achievementData.rarity);
    const categoryEmoji = this.getCategoryEmoji(achievementData.category);

    Alert.alert(
      `${rarityEmoji} Achievement Unlocked!`,
      `${categoryEmoji} ${achievementData.name}\n\n${achievementData.description}\n\n+${achievementData.points} points earned!`,
      [
        {
          text: 'Awesome!',
          style: 'default',
        },
      ],
      { cancelable: true }
    );
  }

  private showMultipleAchievementsAlert(achievements: UserAchievement[]): void {
    const totalPoints = achievements.reduce(
      (sum, a) => sum + (a.achievement?.points || 0),
      0
    );
    const achievementNames = achievements
      .map(a => `• ${a.achievement?.name}`)
      .join('\n');

    Alert.alert(
      `🎉 ${achievements.length} Achievements Unlocked!`,
      `${achievementNames}\n\n+${totalPoints} total points earned!`,
      [
        {
          text: 'Amazing!',
          style: 'default',
        },
      ],
      { cancelable: true }
    );
  }

  // ==================== PUSH NOTIFICATIONS ====================

  private async schedulePushNotification(
    achievement: UserAchievement
  ): Promise<void> {
    try {
      const achievementData = achievement.achievement;
      if (!achievementData) {
        return;
      }

      const rarityEmoji = this.getRarityEmoji(achievementData.rarity);

      await notificationService.scheduleNotification({
        id: `achievement-${achievementData.id}-${Date.now()}`,
        title: `${rarityEmoji} Achievement Unlocked!`,
        body: `${achievementData.name} - +${achievementData.points} points!`,
        data: {
          type: 'achievement',
          achievementId: achievementData.id,
          category: achievementData.category,
          rarity: achievementData.rarity,
        },
        trigger: {
          type: 'timeInterval' as any,
          seconds: 1, // Show immediately
        },
      });
    } catch (error) {
      console.error('Error scheduling achievement notification:', error);
    }
  }

  private async scheduleMultiplePushNotifications(
    achievements: UserAchievement[]
  ): Promise<void> {
    try {
      const totalPoints = achievements.reduce(
        (sum, a) => sum + (a.achievement?.points || 0),
        0
      );

      await notificationService.scheduleNotification({
        id: `multiple-achievements-${Date.now()}`,
        title: `🎉 ${achievements.length} Achievements Unlocked!`,
        body: `You've earned ${totalPoints} points! Tap to see your achievements.`,
        data: {
          type: 'multiple_achievements',
          count: achievements.length,
          totalPoints,
          achievementIds: achievements
            .map(a => a.achievement?.id)
            .filter(Boolean),
        },
        trigger: {
          type: 'timeInterval' as any,
          seconds: 1,
        },
      });
    } catch (error) {
      console.error(
        'Error scheduling multiple achievement notifications:',
        error
      );
    }
  }

  // ==================== MILESTONE NOTIFICATIONS ====================

  async notifyMilestone(
    milestoneType: 'streak' | 'points' | 'habits' | 'completion',
    value: number,
    message?: string
  ): Promise<void> {
    const milestoneEmoji = this.getMilestoneEmoji(milestoneType);
    const defaultMessage = this.getDefaultMilestoneMessage(
      milestoneType,
      value
    );

    try {
      await notificationService.scheduleNotification({
        id: `milestone-${milestoneType}-${value}-${Date.now()}`,
        title: `${milestoneEmoji} Milestone Reached!`,
        body: message || defaultMessage,
        data: {
          type: 'milestone',
          milestoneType,
          value,
        },
        trigger: {
          type: 'timeInterval' as any,
          seconds: 1,
        },
      });
    } catch (error) {
      console.error('Error scheduling milestone notification:', error);
    }
  }

  // ==================== HELPER METHODS ====================

  private getRarityEmoji(rarity: string): string {
    switch (rarity) {
      case 'common':
        return '🌟';
      case 'rare':
        return '💎';
      case 'epic':
        return '👑';
      case 'legendary':
        return '🏆';
      default:
        return '🎖️';
    }
  }

  private getCategoryEmoji(category: string): string {
    switch (category) {
      case 'habit':
        return '🎯';
      case 'nutrition':
        return '🥗';
      case 'exercise':
        return '💪';
      case 'milestone':
        return '🚀';
      default:
        return '⭐';
    }
  }

  private getMilestoneEmoji(type: string): string {
    switch (type) {
      case 'streak':
        return '🔥';
      case 'points':
        return '⭐';
      case 'habits':
        return '📋';
      case 'completion':
        return '✅';
      default:
        return '🎉';
    }
  }

  private getDefaultMilestoneMessage(type: string, value: number): string {
    switch (type) {
      case 'streak':
        return `${value} day streak! You're on fire!`;
      case 'points':
        return `${value} total points earned! Keep it up!`;
      case 'habits':
        return `${value} habits created! Building great routines!`;
      case 'completion':
        return `${value}% completion rate! Excellent progress!`;
      default:
        return `Milestone reached: ${value}!`;
    }
  }

  // ==================== CELEBRATION EFFECTS ====================

  async celebrateAchievement(achievement: UserAchievement): Promise<void> {
    // This could trigger confetti animations, haptic feedback, etc.
    // For now, we'll just log it
    console.log('🎊 Celebrating achievement:', achievement.achievement?.name);

    // TODO: Implement celebration animations
    // - Confetti effect
    // - Haptic feedback
    // - Sound effects
    // - Screen flash/glow
  }
}

export const achievementNotificationService =
  new AchievementNotificationService();

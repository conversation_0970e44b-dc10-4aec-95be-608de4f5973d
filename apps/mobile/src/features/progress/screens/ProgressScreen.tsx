import React, { useState, useEffect } from 'react';
import { ScrollView, StyleSheet, RefreshControl, View } from 'react-native';
import { Text, FAB } from 'react-native-paper';
import { ScreenWithTopBar } from '../../../shared/components/ScreenWithTopBar';
import { AchievementBadges } from '../components/AchievementBadges';
import { AchievementModal } from '../components/AchievementModal';
import {
  HabitCompletionChart,
  StreakChart,
  ProgressCalendar,
} from '../components/Charts';
import { PersonalizedInsights } from '../components/PersonalizedInsights';
import { ProgressStats } from '../components/ProgressStats';
import { WeeklySummary, WeeklyGoal } from '../components/WeeklySummary';
import { WeeklySummaryCard } from '../components/WeeklySummaryCard';
import { useIntegratedProgress } from '../hooks/useIntegratedProgress';
import {
  achievementService,
  AchievementProgress,
} from '../services/achievementService';
import { analyticsService } from '../services/analyticsService';
import { streakService } from '../services/streakService';

export const ProgressScreen: React.FC = () => {
  const {
    progressData: _integratedData,
    loading: _integratedLoading,
    refreshProgress,
    syncProgress,
  } = useIntegratedProgress();
  const [_loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // State for different data sections
  const [progressStats, setProgressStats] = useState({
    currentStreak: 0,
    longestStreak: 0,
    completionRate: 0,
    totalPoints: 0,
    activeHabits: 0,
    weeklyGoal: 80,
  });

  const [achievements, setAchievements] = useState<AchievementProgress[]>([]);
  const [weeklyGoals, setWeeklyGoals] = useState<WeeklyGoal[]>([]);
  const [habitCompletionData, setHabitCompletionData] = useState<any[]>([]);
  const [streakData, setStreakData] = useState<any[]>([]);
  const [calendarData, setCalendarData] = useState<any[]>([]);

  // Modal state
  const [selectedAchievement, setSelectedAchievement] =
    useState<AchievementProgress | null>(null);
  const [achievementModalVisible, setAchievementModalVisible] = useState(false);

  const loadProgressData = async () => {
    try {
      // Load streak summary
      const streakSummary = await streakService.getStreakSummary();
      const overallStreak = await streakService.getOverallStreak();

      // Load completion rates
      const completionRates = await analyticsService.getHabitCompletionRates(7);
      const avgCompletion =
        completionRates.length > 0
          ? Math.round(
              completionRates.reduce((sum, r) => sum + r.completion_rate, 0) /
                completionRates.length
            )
          : 0;

      // Load achievements
      const achievementProgress =
        await achievementService.getAchievementProgress();
      const totalPoints = await achievementService.getTotalPoints();

      // Load streak data for charts
      const _streaks = await analyticsService.getStreakData();
      const topStreaks = await streakService.getTopHabitStreaks(5);

      // Load calendar data
      const calendar = await streakService.getStreakCalendar(30);

      // Update progress stats
      setProgressStats({
        currentStreak: overallStreak?.current_streak || 0,
        longestStreak: streakSummary.longest_all_time_streak,
        completionRate: avgCompletion,
        totalPoints,
        activeHabits: streakSummary.streak_types.habit,
        weeklyGoal: 80,
      });

      setAchievements(achievementProgress);
      setHabitCompletionData(completionRates);
      setStreakData(topStreaks);
      setCalendarData(calendar);

      // Set weekly goals (mock data for now)
      setWeeklyGoals([
        {
          category: 'Habits',
          icon: 'check-circle',
          color: '#4CAF50',
          current: Math.round((avgCompletion * 7) / 100),
          target: 7,
          unit: 'days',
        },
        {
          category: 'Streak',
          icon: 'local-fire-department',
          color: '#FF5722',
          current: overallStreak?.current_streak || 0,
          target: 7,
          unit: 'days',
        },
        {
          category: 'Points',
          icon: 'stars',
          color: '#FF9800',
          current: totalPoints,
          target: 500,
          unit: 'pts',
        },
      ]);
    } catch (error) {
      console.error('Error loading progress data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      // Sync progress data first, then refresh
      await syncProgress();
      await refreshProgress();
      await loadProgressData();
    } finally {
      setRefreshing(false);
    }
  };

  const handleAchievementPress = (achievement: AchievementProgress) => {
    setSelectedAchievement(achievement);
    setAchievementModalVisible(true);
  };

  const handleViewAllAchievements = () => {
    // TODO: Navigate to achievements screen
    console.log('View all achievements');
  };

  const handleCloseAchievementModal = () => {
    setAchievementModalVisible(false);
    setSelectedAchievement(null);
  };

  const handleCalendarDatePress = (date: string) => {
    // TODO: Show day details
    console.log('Calendar date pressed:', date);
  };

  useEffect(() => {
    loadProgressData();
  }, []);

  return (
    <ScreenWithTopBar title="Progress">
      <ScrollView
        style={styles.container}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Progress Stats */}
        <ProgressStats stats={progressStats} />

        {/* Weekly Summary */}
        <WeeklySummary goals={weeklyGoals} />

        {/* AI-Powered Weekly Summary */}
        <WeeklySummaryCard />

        {/* Personalized Insights */}
        <PersonalizedInsights maxInsights={3} />

        {/* Achievement Badges */}
        <AchievementBadges
          achievements={achievements}
          onAchievementPress={handleAchievementPress}
          onViewAllPress={handleViewAllAchievements}
        />

        {/* Habit Completion Chart */}
        {habitCompletionData.length > 0 && (
          <View style={styles.chartContainer}>
            <HabitCompletionChart
              data={habitCompletionData}
              title="7-Day Completion Trend"
              height={180}
            />
          </View>
        )}

        {/* Streak Chart */}
        {streakData.length > 0 && (
          <View style={styles.chartContainer}>
            <StreakChart
              data={streakData}
              title="Top Habit Streaks"
              height={180}
            />
          </View>
        )}

        {/* Progress Calendar */}
        {calendarData.length > 0 && (
          <View style={styles.chartContainer}>
            <ProgressCalendar
              data={calendarData}
              title="30-Day Activity Calendar"
              onDatePress={handleCalendarDatePress}
            />
          </View>
        )}

        {/* Motivational Insights */}
        <View style={styles.insightsContainer}>
          <Text variant="titleMedium" style={styles.insightsTitle}>
            Keep Going! 💪
          </Text>
          <Text variant="bodyMedium" style={styles.insightsText}>
            You're building amazing habits! Every day counts towards your
            long-term success.
          </Text>
        </View>

        {/* Bottom spacing for FAB */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Floating Action Button for Quick Actions */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => {
          // TODO: Show quick actions menu (add habit, set goal, etc.)
          console.log('Quick actions pressed');
        }}
      />

      {/* Achievement Modal */}
      <AchievementModal
        visible={achievementModalVisible}
        achievement={selectedAchievement}
        onDismiss={handleCloseAchievementModal}
      />
    </ScreenWithTopBar>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  chartContainer: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  insightsContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  insightsTitle: {
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  insightsText: {
    color: '#666',
    lineHeight: 20,
  },
  bottomSpacing: {
    height: 80, // Space for FAB
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#2196F3',
  },
});

import { useState, useEffect, useCallback } from 'react';
import {
  achievementService,
  AchievementProgress,
  UserAchievement,
} from '../services/achievementService';
import { analyticsService } from '../services/analyticsService';

export interface UseAchievementsReturn {
  achievements: AchievementProgress[];
  userAchievements: UserAchievement[];
  totalPoints: number;
  loading: boolean;
  error: string | null;
  refreshAchievements: () => Promise<void>;
  checkForNewAchievements: () => Promise<UserAchievement[]>;
  getAchievementsByCategory: (category: string) => AchievementProgress[];
  getRecentAchievements: (limit?: number) => UserAchievement[];
}

export const useAchievements = (): UseAchievementsReturn => {
  const [achievements, setAchievements] = useState<AchievementProgress[]>([]);
  const [userAchievements, setUserAchievements] = useState<UserAchievement[]>(
    []
  );
  const [totalPoints, setTotalPoints] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadAchievements = useCallback(async () => {
    try {
      setError(null);

      // Load all achievement progress
      const achievementProgress =
        await achievementService.getAchievementProgress();
      setAchievements(achievementProgress);

      // Load user achievements
      const userAchievementsList =
        await achievementService.getUserAchievements();
      setUserAchievements(userAchievementsList);

      // Load total points
      const points = await achievementService.getTotalPoints();
      setTotalPoints(points);
    } catch (err) {
      console.error('Error loading achievements:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to load achievements'
      );
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshAchievements = useCallback(async () => {
    setLoading(true);
    await loadAchievements();
  }, [loadAchievements]);

  const checkForNewAchievements = useCallback(async (): Promise<
    UserAchievement[]
  > => {
    try {
      // Check and unlock any new achievements
      const newAchievements =
        await achievementService.checkAndUnlockAchievements();

      if (newAchievements.length > 0) {
        // Refresh the achievements list to show the new ones
        await loadAchievements();

        // Update daily metrics to ensure accurate tracking
        await analyticsService.calculateDailyMetrics();
      }

      return newAchievements;
    } catch (err) {
      console.error('Error checking for new achievements:', err);
      return [];
    }
  }, [loadAchievements]);

  const getAchievementsByCategory = useCallback(
    (category: string): AchievementProgress[] => {
      return achievements.filter(
        achievement => achievement.achievement.category === category
      );
    },
    [achievements]
  );

  const getRecentAchievements = useCallback(
    (limit: number = 5): UserAchievement[] => {
      return userAchievements.slice(0, limit);
    },
    [userAchievements]
  );

  useEffect(() => {
    loadAchievements();
  }, [loadAchievements]);

  return {
    achievements,
    userAchievements,
    totalPoints,
    loading,
    error,
    refreshAchievements,
    checkForNewAchievements,
    getAchievementsByCategory,
    getRecentAchievements,
  };
};

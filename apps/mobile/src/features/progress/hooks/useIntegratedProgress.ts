import { useState, useEffect, useCallback } from 'react';
import {
  progressIntegrationService,
  IntegratedProgressData,
  ProgressSyncResult,
} from '../services/progressIntegrationService';

export interface UseIntegratedProgressReturn {
  progressData: IntegratedProgressData | null;
  loading: boolean;
  error: string | null;
  refreshProgress: (date?: Date) => Promise<void>;
  syncProgress: (date?: Date) => Promise<ProgressSyncResult>;
  onHabitCompleted: (
    habitId: string,
    userId: string,
    date?: Date
  ) => Promise<void>;
  onNutritionLogged: (
    mealData: any,
    userId: string,
    date?: Date
  ) => Promise<void>;
  onWorkoutCompleted: (
    workoutData: any,
    userId: string,
    date?: Date
  ) => Promise<void>;
}

export const useIntegratedProgress = (
  autoRefresh: boolean = true
): UseIntegratedProgressReturn => {
  const [progressData, setProgressData] =
    useState<IntegratedProgressData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadProgressData = useCallback(async (date?: Date) => {
    try {
      setError(null);
      const data =
        await progressIntegrationService.getIntegratedProgressData(date);
      setProgressData(data);
    } catch (err) {
      console.error('Error loading integrated progress data:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to load progress data'
      );
    }
  }, []);

  const refreshProgress = useCallback(
    async (date?: Date) => {
      setLoading(true);
      await loadProgressData(date);
      setLoading(false);
    },
    [loadProgressData]
  );

  const syncProgress = useCallback(
    async (date?: Date): Promise<ProgressSyncResult> => {
      try {
        const result = await progressIntegrationService.syncProgressData(date);
        if (result.success) {
          // Refresh data after successful sync
          await loadProgressData(date);
        }
        return result;
      } catch (err) {
        console.error('Error syncing progress:', err);
        return {
          success: false,
          updatedMetrics: [],
          newAchievements: [],
          errors: [err instanceof Error ? err.message : 'Sync failed'],
        };
      }
    },
    [loadProgressData]
  );

  const onHabitCompleted = useCallback(
    async (habitId: string, userId: string, date?: Date) => {
      try {
        await progressIntegrationService.onHabitCompleted(
          habitId,
          userId,
          date
        );
        // Refresh progress data after habit completion
        await loadProgressData(date);
      } catch (err) {
        console.error('Error handling habit completion:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to update progress'
        );
      }
    },
    [loadProgressData]
  );

  const onNutritionLogged = useCallback(
    async (mealData: any, userId: string, date?: Date) => {
      try {
        await progressIntegrationService.onNutritionLogged(
          mealData,
          userId,
          date
        );
        // Refresh progress data after nutrition logging
        await loadProgressData(date);
      } catch (err) {
        console.error('Error handling nutrition logging:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to update progress'
        );
      }
    },
    [loadProgressData]
  );

  const onWorkoutCompleted = useCallback(
    async (workoutData: any, userId: string, date?: Date) => {
      try {
        await progressIntegrationService.onWorkoutCompleted(
          workoutData,
          userId,
          date
        );
        // Refresh progress data after workout completion
        await loadProgressData(date);
      } catch (err) {
        console.error('Error handling workout completion:', err);
        setError(
          err instanceof Error ? err.message : 'Failed to update progress'
        );
      }
    },
    [loadProgressData]
  );

  useEffect(() => {
    if (autoRefresh) {
      refreshProgress();
    }
  }, [autoRefresh, refreshProgress]);

  return {
    progressData,
    loading,
    error,
    refreshProgress,
    syncProgress,
    onHabitCompleted,
    onNutritionLogged,
    onWorkoutCompleted,
  };
};

import { useEffect, useState } from 'react';
import {
  progressDataService,
  UserProgressSettings,
} from '../services/progressDataService';
import {
  HydrationUnit,
  getDefaultHydrationUnitFromLocale,
} from '../utils/units';

export function useHydrationUnit() {
  const [unit, setUnit] = useState<HydrationUnit>(
    getDefaultHydrationUnitFromLocale()
  );
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    (async () => {
      const settings = await progressDataService.getProgressSettings();
      setUnit(
        (settings.hydrationUnit as HydrationUnit) ||
          getDefaultHydrationUnitFromLocale()
      );
      setLoaded(true);
    })();
  }, []);

  const updateUnit = async (newUnit: HydrationUnit) => {
    setUnit(newUnit);
    await progressDataService.updateProgressSettings({
      hydrationUnit: newUnit as UserProgressSettings['hydrationUnit'],
    });
  };

  return { unit, setUnit: updateUnit, loaded };
}

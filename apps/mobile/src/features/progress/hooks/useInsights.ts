import { useState, useEffect, useCallback } from 'react';
import {
  insightsService,
  UserInsight,
  WeeklySummary,
} from '../services/insightsService';

export interface UseInsightsReturn {
  insights: UserInsight[];
  weeklySummary: WeeklySummary | null;
  loading: boolean;
  error: string | null;
  refreshInsights: () => Promise<void>;
  refreshWeeklySummary: (weekStart?: Date) => Promise<void>;
  getInsightsByType: (type: string) => UserInsight[];
  getHighPriorityInsights: () => UserInsight[];
}

export const useInsights = (): UseInsightsReturn => {
  const [insights, setInsights] = useState<UserInsight[]>([]);
  const [weeklySummary, setWeeklySummary] = useState<WeeklySummary | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadInsights = useCallback(async () => {
    try {
      setError(null);
      const personalizedInsights =
        await insightsService.generatePersonalizedInsights();
      setInsights(personalizedInsights);
    } catch (err) {
      console.error('Error loading insights:', err);
      setError(err instanceof Error ? err.message : 'Failed to load insights');
    }
  }, []);

  const loadWeeklySummary = useCallback(async (weekStart?: Date) => {
    try {
      setError(null);
      const summary = await insightsService.generateWeeklySummary(weekStart);
      setWeeklySummary(summary);
    } catch (err) {
      console.error('Error loading weekly summary:', err);
      setError(
        err instanceof Error ? err.message : 'Failed to load weekly summary'
      );
    }
  }, []);

  const refreshInsights = useCallback(async () => {
    setLoading(true);
    await loadInsights();
    setLoading(false);
  }, [loadInsights]);

  const refreshWeeklySummary = useCallback(
    async (weekStart?: Date) => {
      await loadWeeklySummary(weekStart);
    },
    [loadWeeklySummary]
  );

  const getInsightsByType = useCallback(
    (type: string): UserInsight[] => {
      return insights.filter(insight => insight.type === type);
    },
    [insights]
  );

  const getHighPriorityInsights = useCallback((): UserInsight[] => {
    return insights.filter(insight => insight.priority === 'high');
  }, [insights]);

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([loadInsights(), loadWeeklySummary()]);
      setLoading(false);
    };

    loadData();
  }, [loadInsights, loadWeeklySummary]);

  return {
    insights,
    weeklySummary,
    loading,
    error,
    refreshInsights,
    refreshWeeklySummary,
    getInsightsByType,
    getHighPriorityInsights,
  };
};

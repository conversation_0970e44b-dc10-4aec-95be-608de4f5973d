import {
  toMl,
  fromMl,
  ML_PER_FL_OZ,
  formatNumberWithSeparators,
  parseNumberFromFormatted,
} from './units';

describe('units helpers', () => {
  it('converts fl oz to ml with rounding', () => {
    expect(toMl(8, 'fl_oz')).toBe(Math.round(8 * ML_PER_FL_OZ));
    expect(toMl(16, 'fl_oz')).toBe(Math.round(16 * ML_PER_FL_OZ));
  });

  it('returns ml as-is when unit is ml', () => {
    expect(toMl(250, 'ml')).toBe(250);
  });

  it('converts ml to fl oz with 1 decimal', () => {
    expect(fromMl(1000, 'fl_oz')).toBeCloseTo(1000 / ML_PER_FL_OZ, 1);
    expect(fromMl(500, 'fl_oz')).toBeCloseTo(500 / ML_PER_FL_OZ, 1);
  });

  it('formatNumberWithSeparators formats integers', () => {
    expect(formatNumberWithSeparators(8000)).toMatch(/8,?0?0?0?/); // locale tolerant
    expect(formatNumberWithSeparators('8000')).toMatch(/8,?0?0?0?/);
  });

  it('parseNumberFromFormatted strips separators', () => {
    expect(parseNumberFromFormatted('8,000')).toBe(8000);
    expect(parseNumberFromFormatted('1,234,567')).toBe(1234567);
    expect(parseNumberFromFormatted('250')).toBe(250);
  });
});

import { buildHabitPayload } from './payloadBuilders';

describe('payloadBuilders', () => {
  it('builds habit payload converting fl oz to ml', () => {
    const payload = buildHabitPayload('8', 'fl_oz', '1000');
    expect(payload.type).toBe('habit');
    expect(payload.data.hydration.unit).toBe('ml');
    expect(payload.data.hydration.value).toBeGreaterThanOrEqual(230);
    expect(payload.data.hydration.value).toBeLessThanOrEqual(250);
    expect(payload.data.steps).toBe(1000);
  });
});

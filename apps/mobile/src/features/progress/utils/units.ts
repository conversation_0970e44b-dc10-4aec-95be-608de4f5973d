// Unit helpers for hydration and number formatting

export type HydrationUnit = 'ml' | 'fl_oz';

export const ML_PER_FL_OZ = 29.5735;

export function toMl(value: number, unit: HydrationUnit): number {
  if (!isFinite(value)) return 0;
  return unit === 'ml' ? value : Math.round(value * ML_PER_FL_OZ);
}

export function fromMl(ml: number, unit: HydrationUnit): number {
  if (!isFinite(ml)) return 0;
  return unit === 'ml' ? ml : +(ml / ML_PER_FL_OZ).toFixed(1);
}

export function getDefaultHydrationUnitFromLocale(): HydrationUnit {
  try {
    const locale = Intl.DateTimeFormat().resolvedOptions().locale || '';
    // Default to fl oz for US; ml otherwise
    return /(^|[-_])US($|[-_])/i.test(locale) ? 'fl_oz' : 'ml';
  } catch {
    return 'ml';
  }
}

export function formatNumberWithSeparators(value: number | string): string {
  const num =
    typeof value === 'string' ? Number(value.replace(/[^0-9.]/g, '')) : value;
  if (!isFinite(num)) return '';
  return new Intl.NumberFormat(undefined, { maximumFractionDigits: 0 }).format(
    num
  );
}

export function parseNumberFromFormatted(input: string): number {
  if (!input) return 0;
  const cleaned = input.replace(/[^0-9.]/g, '');
  const n = Number(cleaned);
  return isFinite(n) ? n : 0;
}

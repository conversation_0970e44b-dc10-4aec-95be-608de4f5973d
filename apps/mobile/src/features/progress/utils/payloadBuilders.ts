import { HydrationUnit, toMl, parseNumberFromFormatted } from './units';

export interface HabitPayload {
  type: 'habit';
  data: {
    hydration: { value: number; unit: 'ml' };
    steps: number;
  };
  timestamp: string;
}

export function buildHabitPayload(
  hydrationInput: string,
  unit: HydrationUnit,
  stepsInput: string
): HabitPayload {
  const hydrationValue = parseNumberFromFormatted(hydrationInput);
  const stepsValue = parseNumberFromFormatted(stepsInput);

  return {
    type: 'habit',
    data: {
      hydration: { value: toMl(hydrationValue, unit), unit: 'ml' },
      steps: stepsValue,
    },
    timestamp: new Date().toISOString(),
  };
}

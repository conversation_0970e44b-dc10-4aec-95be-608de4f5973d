import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useQuery } from '@tanstack/react-query';
import { format, addDays, subDays } from 'date-fns';
import React, { useState } from 'react';
import { View, ScrollView, StyleSheet, ActivityIndicator } from 'react-native';
import { Text, Button, Card, IconButton } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import IncompleteProfileMessage from '../../../shared/components/IncompleteProfileMessage';
import { ScreenWithTopBar } from '../../../shared/components/ScreenWithTopBar';
import { ExerciseStackParamList } from '../../../shared/types/navigation';
import { useProfileCompletion } from '../../home/<USER>/useProfileCompletion';
import { getDailyWorkoutPlan } from '../services/workoutService';

interface Exercise {
  id: string;
  name: string;
  description?: string;
  video_url?: string;
  sets?: number;
  reps?: number;
  rest_seconds?: number;
}

interface WorkoutPlan {
  id: string;
  date: string;
  exercises: Exercise[];
}

// Define props type for ExerciseCard
interface ExerciseCardProps {
  exercise: Exercise;
}

const ExerciseCard: React.FC<ExerciseCardProps> = ({ exercise }) => {
  const navigation =
    useNavigation<
      StackNavigationProp<ExerciseStackParamList, 'ExercisePlayer'>
    >();

  return (
    <Card style={styles.card}>
      <View style={styles.cardContent}>
        <Text style={styles.exerciseName}>{exercise.name}</Text>
        {exercise.description && (
          <Text style={styles.exerciseDescription}>{exercise.description}</Text>
        )}
        <View style={styles.detailsContainer}>
          {exercise.sets && (
            <Text style={styles.detailText}>Sets: {exercise.sets}</Text>
          )}
          {exercise.reps && (
            <Text style={styles.detailText}>Reps: {exercise.reps}</Text>
          )}
          {exercise.rest_seconds && (
            <Text style={styles.detailText}>
              Rest: {exercise.rest_seconds}s
            </Text>
          )}
        </View>
        <Button
          mode="contained"
          onPress={() =>
            navigation.navigate('ExercisePlayer', { exerciseId: exercise.id })
          }
          style={styles.startButton}
        >
          Start
        </Button>
      </View>
    </Card>
  );
};

// Define props type for DateNavigator
interface DateNavigatorProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
}

const DateNavigator = ({ currentDate, onDateChange }: DateNavigatorProps) => {
  const handlePrevDay = () => {
    onDateChange(subDays(currentDate, 1));
  };

  const handleNextDay = () => {
    onDateChange(addDays(currentDate, 1));
  };

  return (
    <View style={styles.dateNavigatorContainer}>
      <IconButton icon="chevron-left" onPress={handlePrevDay} />
      <View style={styles.dateContainer}>
        <Icon name="calendar" size={20} />
        <Text style={styles.dateText}>
          {format(currentDate, 'EEEE, MMM d')}
        </Text>
      </View>
      <IconButton icon="chevron-right" onPress={handleNextDay} />
    </View>
  );
};

export default function ExerciseScreen() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const { isComplete: isProfileComplete, loading: profileLoading } =
    useProfileCompletion();

  const {
    data: workoutPlan,
    isLoading,
    isError,
    error,
  } = useQuery<WorkoutPlan | null>({
    queryKey: ['dailyWorkoutPlan', format(currentDate, 'yyyy-MM-dd')],
    queryFn: () => getDailyWorkoutPlan(format(currentDate, 'yyyy-MM-dd')),
    enabled: isProfileComplete, // Only fetch workout plan if profile is complete
  });

  const renderContent = () => {
    // Show incomplete profile message if profile is not complete
    if (!profileLoading && !isProfileComplete) {
      return (
        <IncompleteProfileMessage
          title="Complete Your Profile First"
          message="We need to know your fitness goals, experience level, and any injuries to create safe and effective workout plans for you."
          icon="dumbbell"
          buttonText="Set Up Fitness Profile"
        />
      );
    }

    if (isLoading || profileLoading) {
      return <ActivityIndicator size="large" color="#2196F3" />;
    }

    if (isError) {
      return <Text>Error fetching data: {error.message}</Text>;
    }

    if (!workoutPlan || workoutPlan.exercises.length === 0) {
      return <Text>No workout plan available for this day. Rest day!</Text>;
    }

    return (
      <View>
        {workoutPlan.exercises.map(exercise => (
          <ExerciseCard key={exercise.id} exercise={exercise} />
        ))}
      </View>
    );
  };

  return (
    <ScreenWithTopBar title="Exercise">
      <View style={styles.container}>
        {/* Only show date navigator if profile is complete */}
        {!profileLoading && isProfileComplete && (
          <DateNavigator
            currentDate={currentDate}
            onDateChange={setCurrentDate}
          />
        )}
        <ScrollView style={styles.scrollView}>
          <View style={styles.centerContainer}>{renderContent()}</View>
        </ScrollView>
      </View>
    </ScreenWithTopBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  dateNavigatorContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginHorizontal: 8,
  },
  scrollView: {
    padding: 16,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    marginBottom: 16,
  },
  cardContent: {
    padding: 16,
  },
  exerciseName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  exerciseDescription: {
    color: '#666',
    marginBottom: 12,
  },
  detailsContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  detailText: {
    marginRight: 16,
  },
  startButton: {
    marginTop: 8,
  },
});

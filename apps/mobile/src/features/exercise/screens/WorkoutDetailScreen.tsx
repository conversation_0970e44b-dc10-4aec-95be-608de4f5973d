import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { Text, <PERSON><PERSON>, Card } from 'react-native-paper';
import { supabase } from '../../../lib/supabase';
import { SimplePreferenceButtons } from '../../preferences';

interface Exercise {
  id: string;
  name: string;
  description?: string;
  video_url?: string;
  sets?: number;
  reps?: number;
  rest_seconds?: number;
}

interface Workout {
  id: string;
  name: string;
  description?: string;
  exercises: Exercise[];
  estimated_duration_minutes?: number;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
    textAlign: 'center',
  },
  durationText: {
    fontSize: 16,
    marginBottom: 16,
  },
  exercisesTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 16,
  },
  card: {
    marginBottom: 16,
    padding: 16,
  },
  exerciseContent: {
    flexDirection: 'column',
  },
  exerciseName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  exerciseDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  exerciseDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  detailText: {
    fontSize: 14,
    marginRight: 16,
  },
  loadingText: {
    fontSize: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#f44336',
  },
  notFoundText: {
    fontSize: 16,
  },
  noExercisesText: {
    fontSize: 16,
    textAlign: 'center',
    marginVertical: 16,
  },
  exerciseActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  workoutActions: {
    alignItems: 'center',
    marginTop: 24,
    padding: 16,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  rateWorkoutText: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
});

const ExerciseItem = ({ exercise }: { exercise: Exercise }) => {
  return (
    <Card style={styles.card}>
      <View style={styles.exerciseContent}>
        <Text style={styles.exerciseName}>{exercise.name}</Text>
        {exercise.description && (
          <Text style={styles.exerciseDescription}>{exercise.description}</Text>
        )}
        <View style={styles.exerciseDetails}>
          {exercise.sets && (
            <Text style={styles.detailText}>Sets: {exercise.sets}</Text>
          )}
          {exercise.reps && (
            <Text style={styles.detailText}>Reps: {exercise.reps}</Text>
          )}
          {exercise.rest_seconds && (
            <Text style={styles.detailText}>
              Rest: {exercise.rest_seconds}s
            </Text>
          )}
        </View>
        <View style={styles.exerciseActions}>
          <Button onPress={() => {}}>Play Exercise</Button>
          <SimplePreferenceButtons
            itemType="exercise"
            itemIdentifier={exercise.name}
            itemMetadata={{
              description: exercise.description,
              sets: exercise.sets,
              reps: exercise.reps,
              rest_seconds: exercise.rest_seconds,
            }}
            size="small"
          />
        </View>
      </View>
    </Card>
  );
};

const WorkoutDetailScreen = ({ route }: any) => {
  const { workoutId } = route.params;

  const {
    data: workout,
    isLoading,
    isError,
    error,
  } = useQuery<Workout>({
    queryKey: ['workout', workoutId],
    queryFn: async () => {
      // First get the workout
      const { data: workoutData, error: workoutError } = await supabase
        .from('workouts')
        .select('*')
        .eq('id', workoutId)
        .single();

      if (workoutError) {
        throw workoutError;
      }

      // Then get the exercises for this workout
      const { data: exercisesData, error: exercisesError } = await supabase
        .from('workout_exercises')
        .select('exercises(*)')
        .eq('workout_id', workoutId);

      if (exercisesError) {
        throw exercisesError;
      }

      return {
        ...workoutData,
        exercises: exercisesData.map((item: any) => item.exercises),
      };
    },
  });

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading workout...</Text>
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>
          Error loading workout: {error.message}
        </Text>
      </View>
    );
  }

  if (!workout) {
    return (
      <View style={styles.container}>
        <Text style={styles.notFoundText}>Workout not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.scrollView}>
      <View style={styles.content}>
        <Text style={styles.title}>{workout.name}</Text>

        {workout.description && (
          <Text style={styles.description}>{workout.description}</Text>
        )}

        {workout.estimated_duration_minutes && (
          <Text style={styles.durationText}>
            Duration: {workout.estimated_duration_minutes} minutes
          </Text>
        )}

        <Text style={styles.exercisesTitle}>Exercises</Text>

        {workout.exercises && workout.exercises.length > 0 ? (
          workout.exercises.map(exercise => (
            <ExerciseItem key={exercise.id} exercise={exercise} />
          ))
        ) : (
          <Text style={styles.noExercisesText}>
            No exercises in this workout
          </Text>
        )}

        <View style={styles.workoutActions}>
          <Text style={styles.rateWorkoutText}>Rate this workout:</Text>
          <SimplePreferenceButtons
            itemType="workout_plan"
            itemIdentifier={workout.name}
            itemMetadata={{
              description: workout.description,
              estimated_duration_minutes: workout.estimated_duration_minutes,
              exercises_count: workout.exercises?.length || 0,
            }}
            size="medium"
            showLabels={true}
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default WorkoutDetailScreen;

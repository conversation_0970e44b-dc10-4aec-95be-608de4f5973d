import { useQuery } from '@tanstack/react-query';
import { useVideoPlayer, VideoView } from 'expo-video';
import React, { useState, useRef, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { Text, Button, Card, ProgressBar } from 'react-native-paper';
import { supabase } from '../../../lib/supabase';
import {
  triggerSuccessHaptic,
  triggerImpactHaptic,
  triggerSelectionHaptic,
} from '../../../shared/utils/haptic';
import { showSuccessToast, showInfoToast } from '../../../shared/utils/toast';

interface Exercise {
  id: string;
  name: string;
  description?: string;
  video_url?: string;
  sets?: number;
  reps?: number;
  rest_seconds?: number;
}

const ExercisePlayerScreen = ({ route }: any) => {
  const { exerciseId } = route.params;

  const [currentSet, setCurrentSet] = useState(1);
  const [isResting, setIsResting] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);

  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const {
    data: exercise,
    isLoading,
    isError,
    error,
  } = useQuery<Exercise>({
    queryKey: ['exercise', exerciseId],
    queryFn: async () => {
      const { data, error: fetchError } = await supabase
        .from('exercises')
        .select('*')
        .eq('id', exerciseId)
        .single();

      if (fetchError) {
        throw fetchError;
      }
      return data;
    },
  });

  const player = useVideoPlayer(exercise?.video_url || '', videoPlayer => {
    videoPlayer.loop = true;
  });

  const togglePlayback = () => {
    if (player) {
      if (player.playing) {
        player.pause();
        triggerSelectionHaptic();
      } else {
        player.play();
        triggerSelectionHaptic();
      }
    }
  };

  // Timer effect
  useEffect(() => {
    if (timeLeft > 0) {
      timerRef.current = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
    } else if (timeLeft === 0 && isResting) {
      // Rest period finished
      setIsResting(false);
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [timeLeft, isResting]);

  const startRestTimer = () => {
    if (exercise?.rest_seconds) {
      setIsResting(true);
      setTimeLeft(exercise.rest_seconds);
    }
  };

  const completeSet = () => {
    if (exercise?.sets && currentSet < exercise.sets) {
      setCurrentSet(currentSet + 1);
      showSuccessToast(
        'Set completed!',
        `Starting rest period for ${exercise.rest_seconds} seconds`
      );
      triggerSuccessHaptic();
      startRestTimer();
    } else {
      // All sets completed
      setIsCompleted(true);
      showSuccessToast(
        'Exercise completed!',
        'Great job finishing this exercise!'
      );
      triggerSuccessHaptic();
    }
  };

  const skipRest = () => {
    setIsResting(false);
    setTimeLeft(0);
    showInfoToast('Rest skipped', 'Starting next set immediately');
    triggerImpactHaptic('light');
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading exercise...</Text>
      </View>
    );
  }

  if (isError) {
    return (
      <View style={styles.container}>
        <Text>Error loading exercise: {error.message}</Text>
      </View>
    );
  }

  if (!exercise) {
    return (
      <View style={styles.container}>
        <Text>Exercise not found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>{exercise.name}</Text>

        {exercise.description && (
          <Text style={styles.description}>{exercise.description}</Text>
        )}

        <Card style={styles.card}>
          <View style={styles.setInfoContainer}>
            <Text>
              Set {currentSet} of {exercise.sets || 'N/A'}
            </Text>
            <Text>{exercise.reps || 'N/A'} reps</Text>
          </View>
          <ProgressBar
            progress={isCompleted ? 1 : currentSet / (exercise.sets || 1)}
            style={styles.progressBar}
          />
        </Card>

        {isResting && (
          <Card style={styles.restCard}>
            <View style={styles.restContent}>
              <Text style={styles.restTitle}>Rest Time</Text>
              <Text style={styles.restTimer}>
                {Math.floor(timeLeft / 60)}:
                {(timeLeft % 60).toString().padStart(2, '0')}
              </Text>
              <Button mode="outlined" onPress={skipRest}>
                Skip Rest
              </Button>
            </View>
          </Card>
        )}

        {/* Video player */}
        <Card style={styles.videoCard}>
          {exercise?.video_url ? (
            <>
              <VideoView
                player={player}
                style={styles.videoPlayer}
                nativeControls={false}
              />
              <View style={styles.videoControls}>
                <Button
                  mode="text"
                  onPress={togglePlayback}
                  textColor="#FFFFFF"
                >
                  {player?.playing ? 'Pause' : 'Play'}
                </Button>
              </View>
            </>
          ) : (
            <View style={styles.noVideoContainer}>
              <Text>No video available for this exercise</Text>
            </View>
          )}
        </Card>
      </View>

      <View style={styles.buttonContainer}>
        {isCompleted ? (
          <Button
            mode="contained"
            style={styles.completedButton}
            onPress={() => {}}
          >
            Exercise Completed!
          </Button>
        ) : isResting ? (
          <Button mode="contained" disabled style={styles.restingButton}>
            Resting...
          </Button>
        ) : (
          <Button
            mode="contained"
            style={styles.completeButton}
            onPress={completeSet}
          >
            Complete Set
          </Button>
        )}

        <Button mode="outlined" style={styles.dislikeButton} onPress={() => {}}>
          Dislike Exercise
        </Button>
      </View>
    </View>
  );
};

export default ExercisePlayerScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between',
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  card: {
    marginBottom: 16,
    padding: 16,
  },
  setInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  restCard: {
    marginBottom: 16,
    padding: 16,
    backgroundColor: '#fff9c4',
  },
  restContent: {
    alignItems: 'center',
  },
  restTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  restTimer: {
    fontSize: 32,
    marginBottom: 16,
  },
  videoCard: {
    height: 200,
    marginBottom: 16,
    overflow: 'hidden',
  },
  videoPlayer: {
    flex: 1,
  },
  videoControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 8,
    backgroundColor: 'rgba(0,0,0,0.5)',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  noVideoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#eee',
  },
  buttonContainer: {
    gap: 12,
  },
  completedButton: {
    backgroundColor: '#4caf50',
  },
  restingButton: {
    backgroundColor: '#2196f3',
  },
  completeButton: {
    backgroundColor: '#2196f3',
  },
  dislikeButton: {
    borderColor: '#f44336',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
});

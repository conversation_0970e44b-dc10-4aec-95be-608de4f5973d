import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import ExercisePlayerScreen from '../screens/ExercisePlayerScreen';
import ExerciseScreen from '../screens/ExerciseScreen';
import WorkoutDetailScreen from '../screens/WorkoutDetailScreen';

const ExerciseStack = createStackNavigator();

export function ExerciseStackNavigator() {
  return (
    <ExerciseStack.Navigator
      id={undefined}
      screenOptions={{ headerShown: false }}
    >
      <ExerciseStack.Screen name="ExerciseMain" component={ExerciseScreen} />
      <ExerciseStack.Screen
        name="WorkoutDetail"
        component={WorkoutDetailScreen}
      />
      <ExerciseStack.Screen
        name="ExercisePlayer"
        component={ExercisePlayerScreen}
      />
    </ExerciseStack.Navigator>
  );
}

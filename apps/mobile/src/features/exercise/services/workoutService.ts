import NetInfo from '@react-native-community/netinfo';
import { supabase } from '../../../lib/supabase';
import { addToOfflineQueue } from '../../../shared/services/offline/offlineService';

export interface Exercise {
  id: string;
  name: string;
  description?: string;
  video_url?: string;
  sets?: number;
  reps?: number;
  rest_seconds?: number;
}

export interface WorkoutLog {
  id?: string;
  workout_plan_id: string;
  exercise_id: string;
  sets_completed: number;
  reps_completed: number;
  weight_used?: number;
  notes?: string;
  created_at?: string;
}

export interface WorkoutPlan {
  id: string;
  date: string;
  exercises: Exercise[];
}

export const logWorkout = async (
  workoutLog: Omit<WorkoutLog, 'id' | 'created_at'>
): Promise<WorkoutLog> => {
  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      throw new Error('Not authenticated');
    }

    // Check network connectivity
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      // Add to offline queue
      await addToOfflineQueue({
        type: 'workout_log',
        action: 'create',
        data: workoutLog,
      });
      throw new Error('No internet connection. Data saved locally.');
    }

    const { data, error } = await supabase
      .from('workout_logs')
      .insert([
        {
          ...workoutLog,
          user_id: session.user.id,
        },
      ])
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error logging workout:', error);
    throw error;
  }
};

export const getDailyWorkoutPlan = async (
  date: string
): Promise<WorkoutPlan | null> => {
  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      throw new Error('Not authenticated');
    }

    // Check network connectivity
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      throw new Error('No internet connection');
    }

    // Get the workout plan for the specified date
    const { data: workoutPlan, error: workoutPlanError } = await supabase
      .from('workout_plans')
      .select('id')
      .eq('user_id', session.user.id)
      .eq('date', date)
      .single();

    if (workoutPlanError) {
      if (workoutPlanError.code === 'PGRST116') {
        // No workout plan found for this date
        return null;
      }
      throw workoutPlanError;
    }

    // Get the exercises for this workout plan
    const { data: workoutPlanExercises, error: exercisesError } = await supabase
      .from('workout_plan_exercises')
      .select(
        `
        sets,
        reps,
        rest_seconds,
        exercises (
          id,
          name,
          description,
          video_url
        )
      `
      )
      .eq('workout_plan_id', workoutPlan.id)
      .order('order', { ascending: true });

    if (exercisesError) {
      throw exercisesError;
    }

    // Transform the data to match our WorkoutPlan interface
    const exercises: Exercise[] = workoutPlanExercises
      .map(item => ({
        id: (item.exercises as any)?.id || '',
        name: (item.exercises as any)?.name || '',
        description: (item.exercises as any)?.description || '',
        video_url: (item.exercises as any)?.video_url || '',
        sets: item.sets,
        reps: item.reps,
        rest_seconds: item.rest_seconds,
      }))
      .filter(exercise => exercise.id && exercise.name);

    return {
      id: workoutPlan.id,
      date,
      exercises,
    };
  } catch (error) {
    console.error('Error fetching daily workout plan:', error);
    throw error;
  }
};

import { supabase } from '../../../lib/supabase';

// TypeScript interfaces for preferences
export interface UserPreference {
  id: string;
  user_id: string;
  item_type:
    | 'meal'
    | 'exercise'
    | 'recipe'
    | 'ingredient'
    | 'workout_plan'
    | 'meal_plan';
  item_identifier: string;
  item_metadata: Record<string, any>;
  preference_type: 'like' | 'dislike' | 'love' | 'hate' | 'neutral';
  preference_strength: -2 | -1 | 0 | 1 | 2;
  context: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface PreferenceRecommendations {
  liked_items: Array<{
    item_identifier: string;
    item_type: string;
    preference_strength: number;
    item_metadata: Record<string, any>;
  }>;
  disliked_items: Array<{
    item_identifier: string;
    item_type: string;
    preference_strength: number;
    item_metadata: Record<string, any>;
  }>;
  preference_patterns: {
    total_preferences: number;
    likes_count: number;
    dislikes_count: number;
    most_liked_type: string | null;
    most_disliked_type: string | null;
  };
}

export interface PreferenceStats {
  total_preferences: number;
  by_type: Record<
    string,
    {
      total: number;
      likes: number;
      dislikes: number;
    }
  >;
  by_preference: Record<string, number>;
  recent_activity: Array<{
    item_type: string;
    item_identifier: string;
    preference_type: string;
    updated_at: string;
  }>;
}

export interface PreferenceResult {
  exists: boolean;
  preference_type: string;
  preference_strength: number;
  item_metadata?: Record<string, any>;
  context?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export class PreferenceService {
  /**
   * Save or update a user preference for an item
   */
  static async savePreference(
    itemType: UserPreference['item_type'],
    itemIdentifier: string,
    preferenceType: UserPreference['preference_type'],
    itemMetadata: Record<string, any> = {},
    context: Record<string, any> = {}
  ): Promise<{
    data: UserPreference | null;
    error: string | null;
  }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        return { data: null, error: 'User not authenticated' };
      }

      const { data, error } = await supabase.rpc('save_user_preference', {
        p_user_id: user.id,
        p_item_type: itemType,
        p_item_identifier: itemIdentifier,
        p_preference_type: preferenceType,
        p_item_metadata: itemMetadata,
        p_context: context,
      });

      if (error) {
        console.error('Error saving preference:', error);
        return { data: null, error: error.message };
      }

      return { data: data as UserPreference, error: null };
    } catch (error) {
      console.error('Error in savePreference:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get user preferences formatted for AI recommendations
   */
  static async getPreferencesForRecommendations(
    itemType?: UserPreference['item_type']
  ): Promise<{
    data: PreferenceRecommendations | null;
    error: string | null;
  }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        return { data: null, error: 'User not authenticated' };
      }

      const { data, error } = await supabase.rpc(
        'get_user_preferences_for_recommendations',
        {
          p_user_id: user.id,
          p_item_type: itemType || null,
        }
      );

      if (error) {
        console.error('Error getting preferences for recommendations:', error);
        return { data: null, error: error.message };
      }

      return { data: data as PreferenceRecommendations, error: null };
    } catch (error) {
      console.error('Error in getPreferencesForRecommendations:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get a specific user preference for an item
   */
  static async getPreference(
    itemType: UserPreference['item_type'],
    itemIdentifier: string
  ): Promise<{
    data: PreferenceResult | null;
    error: string | null;
  }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        return { data: null, error: 'User not authenticated' };
      }

      const { data, error } = await supabase.rpc('get_user_preference', {
        p_user_id: user.id,
        p_item_type: itemType,
        p_item_identifier: itemIdentifier,
      });

      if (error) {
        console.error('Error getting preference:', error);
        return { data: null, error: error.message };
      }

      return { data: data as PreferenceResult, error: null };
    } catch (error) {
      console.error('Error in getPreference:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Delete a user preference for an item
   */
  static async deletePreference(
    itemType: UserPreference['item_type'],
    itemIdentifier: string
  ): Promise<{
    success: boolean;
    error: string | null;
  }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { data, error } = await supabase.rpc('delete_user_preference', {
        p_user_id: user.id,
        p_item_type: itemType,
        p_item_identifier: itemIdentifier,
      });

      if (error) {
        console.error('Error deleting preference:', error);
        return { success: false, error: error.message };
      }

      return { success: data as boolean, error: null };
    } catch (error) {
      console.error('Error in deletePreference:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get comprehensive preference statistics for the user
   */
  static async getPreferenceStats(): Promise<{
    data: PreferenceStats | null;
    error: string | null;
  }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        return { data: null, error: 'User not authenticated' };
      }

      const { data, error } = await supabase.rpc('get_user_preference_stats', {
        p_user_id: user.id,
      });

      if (error) {
        console.error('Error getting preference stats:', error);
        return { data: null, error: error.message };
      }

      return { data: data as PreferenceStats, error: null };
    } catch (error) {
      console.error('Error in getPreferenceStats:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Quick methods for common preference actions
   */
  static async likeItem(
    itemType: UserPreference['item_type'],
    itemIdentifier: string,
    itemMetadata: Record<string, any> = {}
  ) {
    return this.savePreference(itemType, itemIdentifier, 'like', itemMetadata);
  }

  static async dislikeItem(
    itemType: UserPreference['item_type'],
    itemIdentifier: string,
    itemMetadata: Record<string, any> = {}
  ) {
    return this.savePreference(
      itemType,
      itemIdentifier,
      'dislike',
      itemMetadata
    );
  }

  static async loveItem(
    itemType: UserPreference['item_type'],
    itemIdentifier: string,
    itemMetadata: Record<string, any> = {}
  ) {
    return this.savePreference(itemType, itemIdentifier, 'love', itemMetadata);
  }

  static async hateItem(
    itemType: UserPreference['item_type'],
    itemIdentifier: string,
    itemMetadata: Record<string, any> = {}
  ) {
    return this.savePreference(itemType, itemIdentifier, 'hate', itemMetadata);
  }

  static async neutralItem(
    itemType: UserPreference['item_type'],
    itemIdentifier: string,
    itemMetadata: Record<string, any> = {}
  ) {
    return this.savePreference(
      itemType,
      itemIdentifier,
      'neutral',
      itemMetadata
    );
  }

  /**
   * Utility method to check if an item should be filtered from recommendations
   */
  static shouldFilterItem(
    itemIdentifier: string,
    itemType: string,
    dislikedItems: PreferenceRecommendations['disliked_items']
  ): boolean {
    return dislikedItems.some(
      item =>
        item.item_identifier === itemIdentifier && item.item_type === itemType
    );
  }

  /**
   * Utility method to boost item priority based on preferences
   */
  static getItemPriorityBoost(
    itemIdentifier: string,
    itemType: string,
    likedItems: PreferenceRecommendations['liked_items']
  ): number {
    const likedItem = likedItems.find(
      item =>
        item.item_identifier === itemIdentifier && item.item_type === itemType
    );
    return likedItem ? likedItem.preference_strength : 0;
  }
}

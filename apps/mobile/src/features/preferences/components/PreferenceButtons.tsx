import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { IconButton, Text, useTheme } from 'react-native-paper';
import {
  PreferenceService,
  UserPreference,
} from '../services/preferenceService';

interface PreferenceButtonsProps {
  itemType: UserPreference['item_type'];
  itemIdentifier: string;
  itemMetadata?: Record<string, any>;
  size?: 'small' | 'medium' | 'large';
  showLabels?: boolean;
  onPreferenceChange?: (preferenceType: string) => void;
  style?: any;
}

export const PreferenceButtons: React.FC<PreferenceButtonsProps> = ({
  itemType,
  itemIdentifier,
  itemMetadata = {},
  size = 'medium',
  showLabels = false,
  onPreferenceChange,
  style,
}) => {
  const theme = useTheme();
  const [currentPreference, setCurrentPreference] = useState<string>('neutral');
  const [loading, setLoading] = useState(false);

  // Icon sizes based on size prop
  const iconSize = size === 'small' ? 20 : size === 'large' ? 32 : 24;

  useEffect(() => {
    loadCurrentPreference();
  }, [itemType, itemIdentifier]);

  const loadCurrentPreference = async () => {
    try {
      const { data, error } = await PreferenceService.getPreference(
        itemType,
        itemIdentifier
      );
      if (error) {
        console.error('Error loading preference:', error);
        return;
      }
      if (data && data.exists) {
        setCurrentPreference(data.preference_type);
      } else {
        setCurrentPreference('neutral');
      }
    } catch (error) {
      console.error('Error in loadCurrentPreference:', error);
    }
  };

  const handlePreferenceChange = async (
    preferenceType: UserPreference['preference_type']
  ) => {
    if (loading) {
      return;
    }

    setLoading(true);
    try {
      const { error } = await PreferenceService.savePreference(
        itemType,
        itemIdentifier,
        preferenceType,
        itemMetadata
      );

      if (error) {
        Alert.alert('Error', `Failed to save preference: ${error}`);
        return;
      }

      setCurrentPreference(preferenceType);
      onPreferenceChange?.(preferenceType);

      // Show success feedback
      const actionText =
        preferenceType === 'like'
          ? 'liked'
          : preferenceType === 'dislike'
            ? 'disliked'
            : preferenceType === 'love'
              ? 'loved'
              : preferenceType === 'hate'
                ? 'marked as hate'
                : 'reset';

      // You could add a toast notification here if you have one set up
      console.log(`Successfully ${actionText} ${itemIdentifier}`);
    } catch (error) {
      console.error('Error saving preference:', error);
      Alert.alert('Error', 'Failed to save preference');
    } finally {
      setLoading(false);
    }
  };

  const getButtonColor = (preferenceType: string) => {
    if (currentPreference === preferenceType) {
      switch (preferenceType) {
        case 'love':
          return '#E91E63'; // Pink for love
        case 'like':
          return '#4CAF50'; // Green for like
        case 'dislike':
          return '#FF9800'; // Orange for dislike
        case 'hate':
          return '#F44336'; // Red for hate
        default:
          return theme.colors.primary;
      }
    }
    return theme.colors.outline;
  };

  const getButtonIcon = (preferenceType: string) => {
    switch (preferenceType) {
      case 'love':
        return 'heart';
      case 'like':
        return 'thumb-up';
      case 'dislike':
        return 'thumb-down';
      case 'hate':
        return 'heart-broken';
      default:
        return 'minus';
    }
  };

  return (
    <View style={[styles.container, style]}>
      {/* Love Button */}
      <View style={styles.buttonContainer}>
        <IconButton
          icon={getButtonIcon('love')}
          size={iconSize}
          iconColor={getButtonColor('love')}
          onPress={() => handlePreferenceChange('love')}
          disabled={loading}
          style={[
            styles.button,
            currentPreference === 'love' && styles.activeButton,
          ]}
        />
        {showLabels && (
          <Text style={[styles.label, { color: getButtonColor('love') }]}>
            Love
          </Text>
        )}
      </View>

      {/* Like Button */}
      <View style={styles.buttonContainer}>
        <IconButton
          icon={getButtonIcon('like')}
          size={iconSize}
          iconColor={getButtonColor('like')}
          onPress={() => handlePreferenceChange('like')}
          disabled={loading}
          style={[
            styles.button,
            currentPreference === 'like' && styles.activeButton,
          ]}
        />
        {showLabels && (
          <Text style={[styles.label, { color: getButtonColor('like') }]}>
            Like
          </Text>
        )}
      </View>

      {/* Dislike Button */}
      <View style={styles.buttonContainer}>
        <IconButton
          icon={getButtonIcon('dislike')}
          size={iconSize}
          iconColor={getButtonColor('dislike')}
          onPress={() => handlePreferenceChange('dislike')}
          disabled={loading}
          style={[
            styles.button,
            currentPreference === 'dislike' && styles.activeButton,
          ]}
        />
        {showLabels && (
          <Text style={[styles.label, { color: getButtonColor('dislike') }]}>
            Dislike
          </Text>
        )}
      </View>

      {/* Hate Button */}
      <View style={styles.buttonContainer}>
        <IconButton
          icon={getButtonIcon('hate')}
          size={iconSize}
          iconColor={getButtonColor('hate')}
          onPress={() => handlePreferenceChange('hate')}
          disabled={loading}
          style={[
            styles.button,
            currentPreference === 'hate' && styles.activeButton,
          ]}
        />
        {showLabels && (
          <Text style={[styles.label, { color: getButtonColor('hate') }]}>
            Hate
          </Text>
        )}
      </View>
    </View>
  );
};

// Simplified version with just thumbs up/down
export const SimplePreferenceButtons: React.FC<PreferenceButtonsProps> = ({
  itemType,
  itemIdentifier,
  itemMetadata = {},
  size = 'medium',
  showLabels = false,
  onPreferenceChange,
  style,
}) => {
  const theme = useTheme();
  const [currentPreference, setCurrentPreference] = useState<string>('neutral');
  const [loading, setLoading] = useState(false);

  const iconSize = size === 'small' ? 20 : size === 'large' ? 32 : 24;

  useEffect(() => {
    loadCurrentPreference();
  }, [itemType, itemIdentifier]);

  const loadCurrentPreference = async () => {
    try {
      const { data, error } = await PreferenceService.getPreference(
        itemType,
        itemIdentifier
      );
      if (error) {
        console.error('Error loading preference:', error);
        return;
      }
      if (data && data.exists) {
        setCurrentPreference(data.preference_type);
      } else {
        setCurrentPreference('neutral');
      }
    } catch (error) {
      console.error('Error in loadCurrentPreference:', error);
    }
  };

  const handlePreferenceChange = async (
    preferenceType: UserPreference['preference_type']
  ) => {
    if (loading) {
      return;
    }

    setLoading(true);
    try {
      const { error } = await PreferenceService.savePreference(
        itemType,
        itemIdentifier,
        preferenceType,
        itemMetadata
      );

      if (error) {
        Alert.alert('Error', `Failed to save preference: ${error}`);
        return;
      }

      setCurrentPreference(preferenceType);
      onPreferenceChange?.(preferenceType);
    } catch (error) {
      console.error('Error saving preference:', error);
      Alert.alert('Error', 'Failed to save preference');
    } finally {
      setLoading(false);
    }
  };

  const getButtonColor = (preferenceType: string) => {
    if (currentPreference === preferenceType) {
      return preferenceType === 'like' ? '#4CAF50' : '#FF9800';
    }
    return theme.colors.outline;
  };

  return (
    <View style={[styles.simpleContainer, style]}>
      {/* Like Button */}
      <View style={styles.buttonContainer}>
        <IconButton
          icon="thumb-up"
          size={iconSize}
          iconColor={getButtonColor('like')}
          onPress={() => handlePreferenceChange('like')}
          disabled={loading}
          style={[
            styles.button,
            currentPreference === 'like' && styles.activeButton,
          ]}
        />
        {showLabels && (
          <Text style={[styles.label, { color: getButtonColor('like') }]}>
            Like
          </Text>
        )}
      </View>

      {/* Dislike Button */}
      <View style={styles.buttonContainer}>
        <IconButton
          icon="thumb-down"
          size={iconSize}
          iconColor={getButtonColor('dislike')}
          onPress={() => handlePreferenceChange('dislike')}
          disabled={loading}
          style={[
            styles.button,
            currentPreference === 'dislike' && styles.activeButton,
          ]}
        />
        {showLabels && (
          <Text style={[styles.label, { color: getButtonColor('dislike') }]}>
            Dislike
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  simpleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 16,
  },
  buttonContainer: {
    alignItems: 'center',
  },
  button: {
    margin: 0,
  },
  activeButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  label: {
    fontSize: 12,
    marginTop: -4,
    textAlign: 'center',
  },
});

import React from 'react';
import { ScrollView, View, StyleSheet, Alert } from 'react-native';
import { Text, Card, Button, IconButton, Chip } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { ScreenWithTopBar } from '../../../shared/components';
import { useNotifications } from '../../../shared/contexts/NotificationContext';

const formatTime = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;

  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) {
    return 'Just now';
  }
  if (minutes < 60) {
    return `${minutes}m ago`;
  }
  if (hours < 24) {
    return `${hours}h ago`;
  }
  return `${days}d ago`;
};

const getCategoryIcon = (categoryId?: string): string => {
  switch (categoryId) {
    case 'habits':
      return 'calendar-check';
    case 'workouts':
      return 'dumbbell';
    case 'meals':
      return 'food';
    default:
      return 'bell';
  }
};

const getCategoryColor = (categoryId?: string): string => {
  switch (categoryId) {
    case 'habits':
      return '#34C759';
    case 'workouts':
      return '#FF9500';
    case 'meals':
      return '#007AFF';
    default:
      return '#8E8E93';
  }
};

export default function NotificationsScreen() {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications,
    requestPermissions,
    hasPermission,
  } = useNotifications();

  const handleMarkAllAsRead = () => {
    if (unreadCount > 0) {
      markAllAsRead();
    }
  };

  const handleClearAll = () => {
    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to clear all notifications? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: clearAllNotifications,
        },
      ]
    );
  };

  const handleRequestPermissions = async () => {
    const granted = await requestPermissions();
    if (granted) {
      Alert.alert('Success', 'Notification permissions granted!');
    } else {
      Alert.alert(
        'Permission Denied',
        'Please enable notifications in your device settings to receive habit reminders and updates.'
      );
    }
  };

  return (
    <ScreenWithTopBar title="Notifications">
      <View style={styles.container}>
        {/* Permission Banner */}
        {!hasPermission && (
          <Card style={styles.permissionCard}>
            <View style={styles.permissionContent}>
              <Icon
                name="bell-off"
                size={24}
                color="#FF9500"
                style={styles.permissionIcon}
              />
              <View style={styles.permissionText}>
                <Text style={styles.permissionTitle}>Enable Notifications</Text>
                <Text style={styles.permissionDescription}>
                  Get reminders for your habits, workouts, and meals
                </Text>
              </View>
              <Button
                mode="contained"
                onPress={handleRequestPermissions}
                compact
              >
                Enable
              </Button>
            </View>
          </Card>
        )}

        {/* Header Actions */}
        {notifications.length > 0 && (
          <View style={styles.headerActions}>
            <View style={styles.headerInfo}>
              <Text style={styles.headerTitle}>
                {notifications.length} notification
                {notifications.length !== 1 ? 's' : ''}
              </Text>
              {unreadCount > 0 && (
                <Chip style={styles.unreadChip} textStyle={styles.unreadText}>
                  {unreadCount} unread
                </Chip>
              )}
            </View>
            <View style={styles.actionButtons}>
              {unreadCount > 0 && (
                <Button mode="outlined" onPress={handleMarkAllAsRead} compact>
                  Mark All Read
                </Button>
              )}
              <IconButton
                icon="delete-sweep"
                size={20}
                onPress={handleClearAll}
                iconColor="#FF3B30"
              />
            </View>
          </View>
        )}

        {/* Notifications List */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
        >
          {notifications.length === 0 ? (
            <Card style={styles.emptyCard}>
              <Icon
                name="bell-outline"
                size={48}
                color="#8E8E93"
                style={styles.emptyIcon}
              />
              <Text style={styles.emptyTitle}>No notifications</Text>
              <Text style={styles.emptyDescription}>
                You'll see habit reminders, workout notifications, and app
                updates here
              </Text>
            </Card>
          ) : (
            notifications.map(notification => (
              <Card
                key={notification.id}
                style={[
                  styles.notificationCard,
                  !notification.read && styles.unreadCard,
                ]}
              >
                <View style={styles.notificationHeader}>
                  <View style={styles.notificationInfo}>
                    <View style={styles.titleRow}>
                      <Icon
                        name={getCategoryIcon(notification.categoryId)}
                        size={20}
                        color={getCategoryColor(notification.categoryId)}
                        style={styles.categoryIcon}
                      />
                      <Text style={styles.notificationTitle}>
                        {notification.title}
                      </Text>
                      {!notification.read && <View style={styles.unreadDot} />}
                    </View>
                    <Text style={styles.notificationBody}>
                      {notification.body}
                    </Text>
                    <Text style={styles.notificationTime}>
                      {formatTime(notification.timestamp)}
                    </Text>
                  </View>
                  <View style={styles.notificationActions}>
                    {!notification.read && (
                      <IconButton
                        icon="check"
                        size={18}
                        onPress={() => markAsRead(notification.id)}
                        iconColor="#007AFF"
                      />
                    )}
                    <IconButton
                      icon="close"
                      size={18}
                      onPress={() => removeNotification(notification.id)}
                      iconColor="#8E8E93"
                    />
                  </View>
                </View>
              </Card>
            ))
          )}
        </ScrollView>
      </View>
    </ScreenWithTopBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  permissionCard: {
    margin: 16,
    padding: 16,
    backgroundColor: '#FFF3CD',
  },
  permissionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  permissionIcon: {
    marginRight: 12,
  },
  permissionText: {
    flex: 1,
    marginRight: 12,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  permissionDescription: {
    fontSize: 14,
    color: '#8E8E93',
  },
  headerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  headerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  unreadChip: {
    backgroundColor: '#007AFF20',
    height: 24,
  },
  unreadText: {
    color: '#007AFF',
    fontSize: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  notificationCard: {
    marginBottom: 12,
    padding: 16,
  },
  unreadCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  notificationInfo: {
    flex: 1,
    marginRight: 8,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  categoryIcon: {
    marginRight: 8,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#007AFF',
    marginLeft: 8,
  },
  notificationBody: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 8,
    lineHeight: 20,
  },
  notificationTime: {
    fontSize: 12,
    color: '#8E8E93',
  },
  notificationActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  emptyCard: {
    padding: 32,
    alignItems: 'center',
    marginTop: 32,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 20,
  },
});

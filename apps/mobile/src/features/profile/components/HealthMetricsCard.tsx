import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { Card } from 'react-native-paper';
import { supabase } from '../../../lib/supabase';
import {
  HealthMetrics,
  getHealthMetricsFromProfile,
} from '../services/healthMetricsService';

interface HealthMetricsCardProps {
  onRefresh?: () => void;
}

export default function HealthMetricsCard({
  onRefresh,
}: HealthMetricsCardProps) {
  const [metrics, setMetrics] = useState<HealthMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchHealthMetrics = async () => {
    try {
      setLoading(true);
      setError(null);

      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        setError('User not authenticated');
        return;
      }

      const { data, error: fetchError } = await getHealthMetricsFromProfile(
        user.id
      );

      if (fetchError) {
        setError('Failed to load health metrics');
        console.error('Error fetching health metrics:', fetchError);
        return;
      }

      setMetrics(data || null);
    } catch (err) {
      setError('An unexpected error occurred');
      console.error('Error in fetchHealthMetrics:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHealthMetrics();
  }, []);

  const handleRefresh = () => {
    fetchHealthMetrics();
    onRefresh?.();
  };

  const getBMICategory = (bmi: number): { category: string; color: string } => {
    if (bmi < 18.5) return { category: 'Underweight', color: '#3498db' };
    if (bmi < 25) return { category: 'Normal', color: '#27ae60' };
    if (bmi < 30) return { category: 'Overweight', color: '#f39c12' };
    return { category: 'Obese', color: '#e74c3c' };
  };

  if (loading) {
    return (
      <Card style={styles.card}>
        <Card.Content style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>
            Calculating your health metrics...
          </Text>
        </Card.Content>
      </Card>
    );
  }

  if (error) {
    return (
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </Card.Content>
      </Card>
    );
  }

  if (!metrics) {
    return (
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.noDataText}>
            Complete your basic questionnaire to see your personalized health
            metrics!
          </Text>
        </Card.Content>
      </Card>
    );
  }

  const bmiInfo = getBMICategory(metrics.bmi);

  return (
    <Card style={styles.card}>
      <Card.Content>
        <View style={styles.header}>
          <Text style={styles.title}>Your Health Metrics</Text>
          <TouchableOpacity onPress={handleRefresh}>
            <Text style={styles.refreshText}>Refresh</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.metricsGrid}>
          <View style={styles.metricItem}>
            <Text style={styles.metricLabel}>BMR</Text>
            <Text style={styles.metricValue}>{metrics.bmr}</Text>
            <Text style={styles.metricUnit}>cal/day</Text>
          </View>

          <View style={styles.metricItem}>
            <Text style={styles.metricLabel}>TDEE</Text>
            <Text style={styles.metricValue}>{metrics.tdee}</Text>
            <Text style={styles.metricUnit}>cal/day</Text>
          </View>

          <View style={styles.metricItem}>
            <Text style={styles.metricLabel}>BMI</Text>
            <Text style={[styles.metricValue, { color: bmiInfo.color }]}>
              {metrics.bmi}
            </Text>
            <Text style={[styles.metricUnit, { color: bmiInfo.color }]}>
              {bmiInfo.category}
            </Text>
          </View>

          <View style={styles.metricItem}>
            <Text style={styles.metricLabel}>Target Calories</Text>
            <Text style={styles.metricValue}>
              {metrics.targetCaloriesMin}-{metrics.targetCaloriesMax}
            </Text>
            <Text style={styles.metricUnit}>cal/day</Text>
          </View>
        </View>

        <View style={styles.idealWeightSection}>
          <Text style={styles.idealWeightLabel}>Ideal Weight Range</Text>
          <Text style={styles.idealWeightValue}>
            {metrics.idealWeightMin} - {metrics.idealWeightMax} kg
          </Text>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            💡 These metrics are calculated based on your questionnaire
            responses and help personalize your nutrition and fitness
            recommendations.
          </Text>
        </View>
      </Card.Content>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    margin: 16,
    elevation: 4,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    padding: 10,
    borderRadius: 8,
    alignItems: 'center',
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  noDataText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  refreshText: {
    color: '#007AFF',
    fontSize: 14,
    fontWeight: '600',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  metricItem: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 10,
  },
  metricLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginVertical: 4,
  },
  metricUnit: {
    fontSize: 12,
    color: '#666',
  },
  idealWeightSection: {
    backgroundColor: '#e8f5e8',
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 15,
  },
  idealWeightLabel: {
    fontSize: 14,
    color: '#27ae60',
    fontWeight: '600',
    marginBottom: 5,
  },
  idealWeightValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#27ae60',
  },
  footer: {
    backgroundColor: '#f0f8ff',
    padding: 12,
    borderRadius: 8,
  },
  footerText: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
    textAlign: 'center',
  },
});

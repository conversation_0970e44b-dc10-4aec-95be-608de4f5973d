import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import { ProfileStackParamList } from '../../../shared/types/navigation';
import { PrivacySettingsScreen } from '../../privacy/screens/PrivacySettingsScreen';
import AppPreferencesScreen from '../screens/AppPreferencesScreen';
import DataPrivacyScreen from '../screens/DataPrivacyScreen';
import HelpSupportScreen from '../screens/HelpSupportScreen';
import NotificationSettingsScreen from '../screens/NotificationSettingsScreen';
import ProfileScreen from '../screens/ProfileScreen';
import ProfileSettingsScreen from '../screens/ProfileSettingsScreen';

const ProfileStack = createStackNavigator<ProfileStackParamList>();

export function ProfileStackNavigator() {
  return (
    <ProfileStack.Navigator
      id={undefined}
      screenOptions={{
        headerShown: false, // We'll use our custom TopBar
      }}
    >
      <ProfileStack.Screen name="ProfileMain" component={ProfileScreen} />
      <ProfileStack.Screen
        name="ProfileSettings"
        component={ProfileSettingsScreen}
      />
      <ProfileStack.Screen
        name="AppPreferences"
        component={AppPreferencesScreen}
      />
      <ProfileStack.Screen
        name="NotificationSettings"
        component={NotificationSettingsScreen}
      />
      <ProfileStack.Screen name="HelpSupport" component={HelpSupportScreen} />
      <ProfileStack.Screen name="DataPrivacy" component={DataPrivacyScreen} />
      <ProfileStack.Screen
        name="PrivacySettings"
        component={PrivacySettingsScreen}
      />
    </ProfileStack.Navigator>
  );
}

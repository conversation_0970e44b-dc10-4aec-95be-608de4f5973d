import { useNavigation } from '@react-navigation/native';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { View, ScrollView, StyleSheet, Alert } from 'react-native';
import { Text, TextInput, Button, Card } from 'react-native-paper';
import { supabase } from '../../../lib/supabase';
import { ScreenWithTopBar } from '../../../shared/components';
import { showErrorToast, showSuccessToast } from '../../../shared/utils/toast';

interface Profile {
  id?: string;
  full_name?: string;
  // Some projects use date_of_birth instead of age; we'll bridge these
  date_of_birth?: string | null;
  age?: number | null;
  height_cm?: number | null;
  weight_kg?: number | null;
  // Some projects use fitness_goals/activity_level/dietary_preferences
  goals?: string[];
  fitness_goals?: string[];
  experience_level?: string;
  activity_level?: string;
  ai_persona_preference?: string;
  dietary_restrictions?: string[];
  dietary_preferences?: string[];
  food_allergies?: string[];
  budget_preference?: string;
  known_injuries?: string[];
}

export default function EditProfileScreen() {
  const queryClient = useQueryClient();
  const navigation = useNavigation();

  const {
    data: profile,
    isLoading,
    isError,
    error,
  } = useQuery<Profile>({
    queryKey: ['profile'],
    queryFn: async () => {
      const { data: sessionData } = await supabase.auth.getSession();
      const userId = sessionData?.session?.user?.id;
      if (!userId) throw new Error('Not authenticated');
      const { data, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();
      if (profileError) throw profileError;
      return data as Profile;
    },
  });

  const [form, setForm] = useState<Profile | null>(null);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (profile) setForm(profile);
  }, [profile]);

  const updateField = (key: keyof Profile, value: any) => {
    setForm(prev => (prev ? { ...prev, [key]: value } : prev));
  };

  const parseCommaList = (text: string): string[] =>
    text
      .split(',')
      .map(s => s.trim())
      .filter(s => s.length > 0);

  const validate = (): string | null => {
    if (!form) return 'Form not ready';
    if (!form.full_name || form.full_name.trim().length < 2)
      return 'Please enter your full name.';
    if (
      form.age !== undefined &&
      form.age !== null &&
      (form.age < 0 || form.age > 120)
    )
      return 'Age must be between 0 and 120.';
    if (
      form.height_cm !== undefined &&
      form.height_cm !== null &&
      (form.height_cm < 50 || form.height_cm > 260)
    )
      return 'Height seems invalid (50–260 cm).';
    if (
      form.weight_kg !== undefined &&
      form.weight_kg !== null &&
      (form.weight_kg < 20 || form.weight_kg > 500)
    )
      return 'Weight seems invalid (20–500 kg).';
    return null;
  };

  const onSave = async () => {
    if (!form) return;

    // Validate
    const validationError = validate();
    if (validationError) {
      showErrorToast('Invalid input', validationError);
      return;
    }

    try {
      setSaving(true);
      const { data: sessionData } = await supabase.auth.getSession();
      const userId = sessionData?.session?.user?.id;
      if (!userId) throw new Error('Not authenticated');

      const updates: Record<string, any> = {
        full_name: form.full_name || '',
        height_cm: form.height_cm ?? null,
        weight_kg: form.weight_kg ?? null,
        ai_persona_preference: form.ai_persona_preference || '',
        budget_preference: form.budget_preference || '',
        known_injuries: form.known_injuries ?? [],
        updated_at: new Date().toISOString(),
      };

      // Support both legacy and current schema variants
      if (form.age !== undefined) {
        updates.age = form.age ?? null;
      }
      if (form.date_of_birth !== undefined) {
        updates.date_of_birth = form.date_of_birth ?? null;
      }
      if (form.goals && form.goals.length >= 0) {
        updates.goals = form.goals;
      }
      if (form.fitness_goals && form.fitness_goals.length >= 0) {
        updates.fitness_goals = form.fitness_goals;
      }
      if (form.experience_level !== undefined) {
        updates.experience_level = form.experience_level || '';
      }
      if (form.activity_level !== undefined) {
        updates.activity_level = form.activity_level || '';
      }
      if (form.dietary_restrictions && form.dietary_restrictions.length >= 0) {
        updates.dietary_restrictions = form.dietary_restrictions;
      }
      if (form.dietary_preferences && form.dietary_preferences.length >= 0) {
        updates.dietary_preferences = form.dietary_preferences;
      }
      if (form.food_allergies && form.food_allergies.length >= 0) {
        updates.food_allergies = form.food_allergies;
      }

      // Optimistic cache update
      queryClient.setQueryData(['profile'], (prev: any) => ({
        ...(prev || {}),
        ...updates,
      }));

      const { error: updateError } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId);

      if (updateError) throw updateError;

      await queryClient.invalidateQueries({ queryKey: ['profile'] });
      showSuccessToast('Profile updated');
      // Navigate back to Profile screen
      // @ts-ignore
      (navigation as any).goBack?.();
    } catch (e: any) {
      console.error('Failed to save profile:', e);
      showErrorToast('Save failed', e.message ?? 'Failed to save profile');
    } finally {
      setSaving(false);
    }
  };

  if (isLoading || !form) {
    return (
      <ScreenWithTopBar title="Edit Profile">
        <View style={[styles.container, styles.centered]}>
          <Text>Loading...</Text>
        </View>
      </ScreenWithTopBar>
    );
  }

  if (isError) {
    return (
      <ScreenWithTopBar title="Edit Profile">
        <View style={[styles.container, styles.centered]}>
          <Text>Error: {String((error as any)?.message || error)}</Text>
        </View>
      </ScreenWithTopBar>
    );
  }

  return (
    <ScreenWithTopBar title="Edit Profile">
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.content}
      >
        <Card style={styles.card}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Personal Info
          </Text>
          <TextInput
            label="Full Name"
            value={form.full_name}
            onChangeText={t => updateField('full_name', t)}
            mode="outlined"
            style={styles.input}
          />
          <TextInput
            label="Age"
            value={
              form.age !== null && form.age !== undefined
                ? String(form.age)
                : ''
            }
            onChangeText={t => {
              const n = t ? Math.max(0, parseInt(t, 10) || 0) : null;
              updateField('age', n);
              // Also set date_of_birth if age provided, using Jan 1 as default
              if (n !== null) {
                const currentYear = new Date().getFullYear();
                updateField('date_of_birth', `${currentYear - n}-01-01`);
              }
            }}
            keyboardType="numeric"
            mode="outlined"
            style={styles.input}
          />
          <View style={styles.row}>
            <TextInput
              label="Height (cm)"
              value={
                form.height_cm !== null && form.height_cm !== undefined
                  ? String(form.height_cm)
                  : ''
              }
              onChangeText={t =>
                updateField('height_cm', t ? parseFloat(t) || 0 : null)
              }
              keyboardType="numeric"
              mode="outlined"
              style={[styles.input, styles.half]}
            />
            <TextInput
              label="Weight (kg)"
              value={
                form.weight_kg !== null && form.weight_kg !== undefined
                  ? String(form.weight_kg)
                  : ''
              }
              onChangeText={t =>
                updateField('weight_kg', t ? parseFloat(t) || 0 : null)
              }
              keyboardType="numeric"
              mode="outlined"
              style={[styles.input, styles.half]}
            />
          </View>
          <TextInput
            label="Experience Level"
            value={form.experience_level}
            onChangeText={t => updateField('experience_level', t)}
            mode="outlined"
            style={styles.input}
            placeholder="Beginner / Intermediate / Advanced"
          />
          <TextInput
            label="Goals (comma separated)"
            value={(form.goals || []).join(', ')}
            onChangeText={t => updateField('goals', parseCommaList(t))}
            mode="outlined"
            style={styles.input}
          />
        </Card>

        <Card style={styles.card}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Nutrition
          </Text>
          <TextInput
            label="Dietary Restrictions (comma separated)"
            value={(form.dietary_restrictions || []).join(', ')}
            onChangeText={t =>
              updateField('dietary_restrictions', parseCommaList(t))
            }
            mode="outlined"
            style={styles.input}
          />
          <TextInput
            label="Food Allergies (comma separated)"
            value={(form.food_allergies || []).join(', ')}
            onChangeText={t => updateField('food_allergies', parseCommaList(t))}
            mode="outlined"
            style={styles.input}
          />
          <TextInput
            label="Budget Preference"
            value={form.budget_preference}
            onChangeText={t => updateField('budget_preference', t)}
            mode="outlined"
            style={styles.input}
          />
        </Card>

        <Card style={styles.card}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            AI Coach
          </Text>
          <TextInput
            label="AI Persona Preference"
            value={form.ai_persona_preference}
            onChangeText={t => updateField('ai_persona_preference', t)}
            mode="outlined"
            style={styles.input}
            placeholder="e.g., Supportive, Strict, Cheerful"
          />
        </Card>

        <Card style={styles.card}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            Health
          </Text>
          <TextInput
            label="Known Injuries (comma separated)"
            value={(form.known_injuries || []).join(', ')}
            onChangeText={t => updateField('known_injuries', parseCommaList(t))}
            mode="outlined"
            style={styles.input}
          />
        </Card>

        <Button
          mode="contained"
          onPress={onSave}
          loading={saving}
          disabled={saving}
          style={styles.saveButton}
          icon="content-save"
        >
          Save Changes
        </Button>
      </ScrollView>
    </ScreenWithTopBar>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  content: { padding: 16, paddingBottom: 32 },
  centered: { justifyContent: 'center', alignItems: 'center' },
  card: { marginBottom: 16, padding: 16 },
  sectionTitle: { marginBottom: 8, fontWeight: 'bold' },
  input: { marginBottom: 12 },
  row: { flexDirection: 'row', gap: 12 },
  half: { flex: 1 },
  saveButton: { marginTop: 8 },
});

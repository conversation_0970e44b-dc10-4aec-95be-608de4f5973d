import React, { useState } from 'react';
import { <PERSON>rollView, StyleSheet, Alert } from 'react-native';
import { Text, Card, List, Switch } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { ScreenWithTopBar } from '../../../shared/components';

// Icon and switch components to avoid nested component warnings
const ChartLineIcon = (props: any) => (
  <Icon {...props} name="chart-line" size={24} />
);
const BugIcon = (props: any) => <Icon {...props} name="bug" size={24} />;
const TargetIcon = (props: any) => <Icon {...props} name="target" size={24} />;
const EyeIcon = (props: any) => <Icon {...props} name="eye" size={24} />;
const ChevronRightIcon = (props: any) => (
  <Icon {...props} name="chevron-right" size={20} />
);
const ClockIcon = (props: any) => (
  <Icon {...props} name="clock-outline" size={24} />
);
const LinkIcon = (props: any) => <Icon {...props} name="link" size={24} />;
const DownloadIcon = (props: any) => (
  <Icon {...props} name="download" size={24} />
);
const ShieldIcon = (props: any) => (
  <Icon {...props} name="shield-outline" size={24} />
);

const DeleteForeverRedIcon = (props: any) => (
  <Icon {...props} name="delete-forever" size={24} color="#FF3B30" />
);
const ChevronRightRedIcon = (props: any) => (
  <Icon {...props} name="chevron-right" size={20} color="#FF3B30" />
);

const AnalyticsSwitch = ({
  value,
  onValueChange,
}: {
  value: boolean;
  onValueChange: (value: boolean) => void;
}) => <Switch value={value} onValueChange={onValueChange} />;
const CrashReportsSwitch = ({
  value,
  onValueChange,
}: {
  value: boolean;
  onValueChange: (value: boolean) => void;
}) => <Switch value={value} onValueChange={onValueChange} />;
const PersonalizedAdsSwitch = ({
  value,
  onValueChange,
}: {
  value: boolean;
  onValueChange: (value: boolean) => void;
}) => <Switch value={value} onValueChange={onValueChange} />;

// Wrapper components moved outside to avoid nested component warnings
const createAnalyticsSwitchWrapper =
  (analyticsEnabled: boolean, setAnalyticsEnabled: (value: boolean) => void) =>
  () => (
    <AnalyticsSwitch
      value={analyticsEnabled}
      onValueChange={setAnalyticsEnabled}
    />
  );
const createCrashReportsSwitchWrapper =
  (
    crashReportsEnabled: boolean,
    setCrashReportsEnabled: (value: boolean) => void
  ) =>
  () => (
    <CrashReportsSwitch
      value={crashReportsEnabled}
      onValueChange={setCrashReportsEnabled}
    />
  );
const createPersonalizedAdsSwitchWrapper =
  (
    personalizedAdsEnabled: boolean,
    setPersonalizedAdsEnabled: (value: boolean) => void
  ) =>
  () => (
    <PersonalizedAdsSwitch
      value={personalizedAdsEnabled}
      onValueChange={setPersonalizedAdsEnabled}
    />
  );

export default function DataPrivacyScreen() {
  const [analyticsEnabled, setAnalyticsEnabled] = useState(true);
  const [crashReportsEnabled, setCrashReportsEnabled] = useState(true);
  const [personalizedAdsEnabled, setPersonalizedAdsEnabled] = useState(false);

  // Create wrapper components using factory functions
  const AnalyticsSwitchWrapper = createAnalyticsSwitchWrapper(
    analyticsEnabled,
    setAnalyticsEnabled
  );
  const CrashReportsSwitchWrapper = createCrashReportsSwitchWrapper(
    crashReportsEnabled,
    setCrashReportsEnabled
  );
  const PersonalizedAdsSwitchWrapper = createPersonalizedAdsSwitchWrapper(
    personalizedAdsEnabled,
    setPersonalizedAdsEnabled
  );

  const handleExportData = () => {
    Alert.alert(
      'Export Your Data',
      "We'll prepare a file with all your data and send it to your email address. This may take a few minutes.",
      [
        {
          text: 'Export',
          onPress: () => {
            // TODO: Implement data export
            Alert.alert(
              'Export Started',
              "You'll receive an email when your data is ready for download."
            );
          },
        },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'This action cannot be undone. All your data will be permanently deleted.',
      [
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Confirm Deletion',
              'Are you absolutely sure? This will permanently delete your account and all associated data.',
              [
                {
                  text: 'Yes, Delete My Account',
                  style: 'destructive',
                  onPress: () => {
                    // TODO: Implement account deletion
                    Alert.alert(
                      'Account Deletion',
                      "Account deletion process initiated. You'll receive a confirmation email."
                    );
                  },
                },
                { text: 'Cancel', style: 'cancel' },
              ]
            );
          },
        },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  return (
    <ScreenWithTopBar title="Data & Privacy">
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Data Collection</Text>
          <Text style={styles.sectionDescription}>
            Control what data we collect to improve your experience
          </Text>
          <List.Item
            title="Analytics & Usage Data"
            description="Help us improve the app by sharing anonymous usage data"
            left={ChartLineIcon}
            right={AnalyticsSwitchWrapper}
            style={styles.listItem}
          />
          <List.Item
            title="Crash Reports"
            description="Automatically send crash reports to help us fix bugs"
            left={BugIcon}
            right={CrashReportsSwitchWrapper}
            style={styles.listItem}
          />
          <List.Item
            title="Personalized Ads"
            description="Show ads based on your interests and activity"
            left={TargetIcon}
            right={PersonalizedAdsSwitchWrapper}
            style={styles.listItem}
          />
        </Card>

        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Your Data</Text>
          <List.Item
            title="View Data Usage"
            description="See what data we collect and how it's used"
            left={EyeIcon}
            right={ChevronRightIcon}
            onPress={() => {
              // TODO: Show data usage details
              Alert.alert('Data Usage', 'Data usage details coming soon!');
            }}
            style={styles.listItem}
          />
          <List.Item
            title="Download Your Data"
            description="Export all your personal data"
            left={DownloadIcon}
            right={ChevronRightIcon}
            onPress={handleExportData}
            style={styles.listItem}
          />
          <List.Item
            title="Data Retention"
            description="Learn how long we keep your data"
            left={ClockIcon}
            right={ChevronRightIcon}
            onPress={() => {
              Alert.alert(
                'Data Retention',
                'We keep your data for as long as your account is active. Deleted data is permanently removed within 30 days.'
              );
            }}
            style={styles.listItem}
          />
        </Card>

        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Privacy Controls</Text>
          <List.Item
            title="Manage Permissions"
            description="Review and update app permissions"
            left={ShieldIcon}
            right={ChevronRightIcon}
            onPress={() => {
              // TODO: Open system settings for app permissions
              Alert.alert(
                'Permissions',
                'Please check your device settings to manage app permissions.'
              );
            }}
            style={styles.listItem}
          />
          <List.Item
            title="Third-Party Integrations"
            description="Manage connected apps and services"
            left={LinkIcon}
            right={ChevronRightIcon}
            onPress={() => {
              // TODO: Show third-party integrations
              Alert.alert(
                'Integrations',
                'Third-party integrations management coming soon!'
              );
            }}
            style={styles.listItem}
          />
        </Card>

        <Card style={[styles.card, styles.dangerCard]}>
          <Text style={[styles.sectionTitle, styles.dangerTitle]}>
            Danger Zone
          </Text>
          <List.Item
            title="Delete Account"
            description="Permanently delete your account and all data"
            left={DeleteForeverRedIcon}
            right={ChevronRightRedIcon}
            onPress={handleDeleteAccount}
            style={styles.listItem}
            titleStyle={styles.dangerText}
          />
        </Card>

        <Text style={styles.footerText}>
          For more information about how we handle your data, please read our{' '}
          <Text style={styles.linkText}>Privacy Policy</Text> and{' '}
          <Text style={styles.linkText}>Terms of Service</Text>.
        </Text>
      </ScrollView>
    </ScreenWithTopBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  card: {
    marginBottom: 16,
    padding: 16,
  },
  dangerCard: {
    borderColor: '#FF3B30',
    borderWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  dangerTitle: {
    color: '#FF3B30',
  },
  sectionDescription: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 12,
  },
  listItem: {
    paddingVertical: 4,
  },
  dangerText: {
    color: '#FF3B30',
  },
  footerText: {
    fontSize: 12,
    color: '#8E8E93',
    textAlign: 'center',
    marginTop: 16,
    lineHeight: 18,
  },
  linkText: {
    color: '#007AFF',
    textDecorationLine: 'underline',
  },
});

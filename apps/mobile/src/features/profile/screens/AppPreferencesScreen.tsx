import React, { useState } from 'react';
import { <PERSON>roll<PERSON>iew, StyleSheet } from 'react-native';
import {
  Text,
  Card,
  List,
  Switch,
  Button,
  RadioButton,
  ActivityIndicator,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { supabase } from '../../../lib/supabase';
import { ScreenWithTopBar } from '../../../shared/components';
import { showSuccessToast, showErrorToast } from '../../../shared/utils/toast';

// Icon components to avoid nested component warnings
const ThemeIcon = (props: any) => <Icon {...props} name="theme-light-dark" />;
const VibrationIcon = (props: any) => <Icon {...props} name="vibrate" />;
const SyncIcon = (props: any) => <Icon {...props} name="sync" />;
const OfflineIcon = (props: any) => (
  <Icon {...props} name="cloud-off-outline" size={24} />
);
const WeightIcon = (props: any) => (
  <Icon {...props} name="weight-kilogram" size={24} />
);
const DistanceIcon = (props: any) => (
  <Icon {...props} name="map-marker-distance" size={24} />
);
const ChevronRightIcon = (props: any) => (
  <Icon {...props} name="chevron-right" size={20} />
);

// Switch component creator
const createSwitchComponent =
  (value: boolean, onToggle: (value: boolean) => void) => () => (
    <Switch value={value} onValueChange={onToggle} />
  );

export default function AppPreferencesScreen() {
  const [darkMode, setDarkMode] = useState(true);
  const [hapticFeedback, setHapticFeedback] = useState(true);
  const [autoSync, setAutoSync] = useState(true);
  const [offlineMode, setOfflineMode] = useState(false);

  // Unit system (metric/imperial)
  const [unitSystem, setUnitSystem] = useState<'metric' | 'imperial'>('metric');
  const [loadingUnits, setLoadingUnits] = useState<boolean>(false);

  React.useEffect(() => {
    const load = async () => {
      try {
        setLoadingUnits(true);
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) return;
        const { data, error } = await supabase
          .from('profiles')
          .select('unit_system')
          .eq('id', user.id)
          .single();
        if (!error && data?.unit_system) {
          setUnitSystem(
            data.unit_system === 'imperial' ? 'imperial' : 'metric'
          );
        }
      } finally {
        setLoadingUnits(false);
      }
    };
    load();
  }, []);

  // Persist unit system preference
  const updateUnitSystem = async (value: 'metric' | 'imperial') => {
    try {
      if (value === unitSystem) return;
      setUnitSystem(value);
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return;
      // Use upsert for robustness in case profile row doesn't exist yet
      const { error } = await supabase
        .from('profiles')
        .upsert({ id: user.id, unit_system: value }, { onConflict: 'id' });
      if (error) throw error;
      showSuccessToast(
        'Units updated',
        value === 'metric' ? 'Using cm/kg' : 'Using ft/in & lb'
      );
    } catch (e: any) {
      showErrorToast('Failed to update units', e?.message);
      // revert UI state on failure
      setUnitSystem(prev => (prev === 'metric' ? 'imperial' : 'metric'));
    }
  };

  const preferences = [
    {
      title: 'Dark Mode',
      description: 'Use dark theme throughout the app',
      icon: 'theme-light-dark',
      IconComponent: ThemeIcon,
      SwitchComponent: createSwitchComponent(darkMode, setDarkMode),
      value: darkMode,
      onToggle: setDarkMode,
    },
    {
      title: 'Haptic Feedback',
      description: 'Vibration feedback for interactions',
      icon: 'vibrate',
      IconComponent: VibrationIcon,
      SwitchComponent: createSwitchComponent(hapticFeedback, setHapticFeedback),
      value: hapticFeedback,
      onToggle: setHapticFeedback,
    },
    {
      title: 'Auto Sync',
      description: 'Automatically sync data when online',
      icon: 'sync',
      IconComponent: SyncIcon,
      SwitchComponent: createSwitchComponent(autoSync, setAutoSync),
      value: autoSync,
      onToggle: setAutoSync,
    },
    {
      title: 'Offline Mode',
      description: 'Enable offline functionality',
      icon: 'cloud-off-outline',
      IconComponent: OfflineIcon,
      SwitchComponent: createSwitchComponent(offlineMode, setOfflineMode),
      value: offlineMode,
      onToggle: setOfflineMode,
    },
  ];

  return (
    <ScreenWithTopBar title="App Preferences">
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Display & Interface</Text>
          {preferences.slice(0, 2).map((pref, index) => (
            <List.Item
              key={index}
              title={pref.title}
              description={pref.description}
              left={pref.IconComponent}
              right={pref.SwitchComponent}
              style={styles.listItem}
            />
          ))}
        </Card>

        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Data & Sync</Text>
          {preferences.slice(2).map((pref, index) => (
            <List.Item
              key={index}
              title={pref.title}
              description={pref.description}
              left={pref.IconComponent}
              right={pref.SwitchComponent}
              style={styles.listItem}
            />
          ))}
        </Card>

        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Units & Measurements</Text>
          {loadingUnits ? (
            <ActivityIndicator />
          ) : (
            <RadioButton.Group
              onValueChange={async value => {
                await updateUnitSystem(value as 'metric' | 'imperial');
              }}
              value={unitSystem}
            >
              <List.Item
                title="Metric (cm, kg)"
                left={() => <RadioButton value="metric" />}
                onPress={() => updateUnitSystem('metric')}
                style={styles.listItem}
              />
              <List.Item
                title="Imperial (ft/in, lb)"
                left={() => <RadioButton value="imperial" />}
                onPress={() => updateUnitSystem('imperial')}
                style={styles.listItem}
              />
            </RadioButton.Group>
          )}
        </Card>

        <Button
          mode="outlined"
          onPress={() => {
            // Reset all preferences to default
            setDarkMode(true);
            setHapticFeedback(true);
            setAutoSync(true);
            setOfflineMode(false);
          }}
          style={styles.resetButton}
          icon="restore"
        >
          Reset to Defaults
        </Button>
      </ScrollView>
    </ScreenWithTopBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  card: {
    marginBottom: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  listItem: {
    paddingVertical: 4,
  },
  resetButton: {
    marginTop: 16,
  },
});

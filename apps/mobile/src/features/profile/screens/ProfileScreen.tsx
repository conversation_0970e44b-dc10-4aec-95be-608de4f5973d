import { useNavigation } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { <PERSON>, <PERSON><PERSON>, Card, List } from 'react-native-paper';
import MCIcon from 'react-native-vector-icons/MaterialCommunityIcons';
import { supabase } from '../../../lib/supabase';
import { cmToFtIn, kgToLb } from '../../../shared/utils/measurements';

// Icon components to avoid nested component warnings
const ShieldAccountIcon = (props: any) => (
  <MCIcon {...props} name="shield-account" />
);
const ChevronRightIcon = (props: any) => (
  <MCIcon {...props} name="chevron-right" />
);

interface Profile {
  full_name: string;
  age: number;
  height_cm: number;
  weight_kg: number;
  goals: string[];
  experience_level: string;
  ai_persona_preference: string;
  dietary_restrictions: string[];
  food_allergies: string[];
  budget_preference: string;
  known_injuries: string[];
  unit_system?: 'metric' | 'imperial' | null;
}

const ProfileHeader = ({ profile }: { profile: Profile }) => {
  const unit = profile.unit_system === 'imperial' ? 'imperial' : 'metric';
  const heightDisplay =
    unit === 'imperial'
      ? (() => {
          const { ft, in: inches } = cmToFtIn(profile.height_cm || 0);
          return `${ft} ft ${inches} in`;
        })()
      : `${profile.height_cm ?? '-'} cm`;
  const weightDisplay =
    unit === 'imperial'
      ? `${Math.round(kgToLb(profile.weight_kg || 0))} lb`
      : `${profile.weight_kg ?? '-'} kg`;

  return (
    <Card style={styles.card}>
      <View style={styles.profileHeaderContent}>
        <Text style={styles.profileName}>{profile.full_name}</Text>
        {/* Unit badge */}
        <View style={styles.unitBadge}>
          <Text style={styles.unitBadgeText}>
            {unit === 'imperial' ? 'Imperial' : 'Metric'}
          </Text>
        </View>
        <View style={styles.profileDetails}>
          <Text style={styles.profileDetailText}>Age: {profile.age}</Text>
          <Text style={styles.profileDetailText}>Height: {heightDisplay}</Text>
          <Text style={styles.profileDetailText}>Weight: {weightDisplay}</Text>
        </View>
      </View>
    </Card>
  );
};

const ProfileSection = ({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) => {
  return (
    <Card style={styles.card}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </Card>
  );
};

const ProfileItem = ({
  label,
  value,
}: {
  label: string;
  value: string | string[];
}) => {
  return (
    <View style={styles.item}>
      <Text style={styles.label}>{label}:</Text>
      {Array.isArray(value) ? (
        <Text style={styles.valueList}>{value.join(', ')}</Text>
      ) : (
        <Text style={styles.value}>{value}</Text>
      )}
    </View>
  );
};

export default function ProfileScreen() {
  const [loading, setLoading] = useState(false);
  const navigation = useNavigation();

  const {
    data: profile,
    isLoading,
    isError,
    error,
  } = useQuery<Profile>({
    queryKey: ['profile'],
    queryFn: async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const { data, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single();

      if (profileError) {
        throw profileError;
      }
      return data;
    },
  });

  const handleSignOut = async () => {
    setLoading(true);
    const { error: signOutError } = await supabase.auth.signOut();
    if (signOutError) {
      Alert.alert('Error', signOutError.message);
    }
    setLoading(false);
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centeredContainer]}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (isError) {
    return (
      <View style={[styles.container, styles.centeredContainer]}>
        <Text>Error loading profile: {error.message}</Text>
      </View>
    );
  }

  if (!profile) {
    return (
      <View style={[styles.container, styles.centeredContainer]}>
        <Text>No profile data found</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
    >
      <Text style={styles.title}>Profile</Text>

      <ProfileHeader profile={profile} />

      <ProfileSection title="Personal Info">
        <ProfileItem
          label="Experience Level"
          value={profile.experience_level}
        />
        <ProfileItem label="Goals" value={profile.goals} />
        <ProfileItem label="Known Injuries" value={profile.known_injuries} />
      </ProfileSection>

      <ProfileSection title="Nutrition Preferences">
        <ProfileItem
          label="Dietary Restrictions"
          value={profile.dietary_restrictions}
        />
        <ProfileItem label="Food Allergies" value={profile.food_allergies} />
        <ProfileItem
          label="Budget Preference"
          value={profile.budget_preference}
        />
      </ProfileSection>

      <ProfileSection title="AI Coach">
        <ProfileItem
          label="Persona Preference"
          value={profile.ai_persona_preference}
        />
      </ProfileSection>

      {/* Settings Section */}
      <Card style={styles.card}>
        <Text style={styles.sectionTitle}>Settings</Text>
        <List.Item
          title="Privacy & Data Settings"
          description="Manage your privacy preferences and data rights"
          left={ShieldAccountIcon}
          right={ChevronRightIcon}
          onPress={() => navigation.navigate('PrivacySettings' as never)}
        />
      </Card>

      <Button
        mode="contained"
        onPress={handleSignOut}
        loading={loading}
        disabled={loading}
        style={styles.signOutButton}
      >
        {loading ? 'Signing out...' : 'Sign Out'}
      </Button>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  card: {
    marginBottom: 16,
    padding: 16,
  },
  profileHeaderContent: {
    alignItems: 'center',
  },
  unitBadge: {
    marginTop: 6,
    backgroundColor: '#E8F4FD',
    borderColor: '#007AFF',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 4,
  },
  unitBadgeText: {
    color: '#007AFF',
    fontWeight: '600',
    fontSize: 12,
  },
  profileName: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  profileDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  profileDetailText: {
    fontSize: 16,
  },

  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  label: {
    fontWeight: '600',
  },
  value: {
    textAlign: 'right',
    flex: 1,
  },
  valueList: {
    textAlign: 'right',
    flex: 1,
  },
  centeredContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  signOutButton: {
    backgroundColor: '#ff4444',
    marginTop: 16,
  },
});

import React, { useState } from 'react';
import { <PERSON><PERSON>View, StyleSheet } from 'react-native';
import { Text, Card, List, Switch, Button } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { ScreenWithTopBar } from '../../../shared/components';

// Icon components to avoid nested component warnings
const DumbbellIcon = (props: any) => <Icon {...props} name="dumbbell" />;
const FoodIcon = (props: any) => <Icon {...props} name="food" />;
const CheckCircleIcon = (props: any) => <Icon {...props} name="check-circle" />;
const TrendingUpIcon = (props: any) => <Icon {...props} name="trending-up" />;
const LightbulbIcon = (props: any) => <Icon {...props} name="lightbulb" />;
const ChartLineIcon = (props: any) => <Icon {...props} name="chart-line" />;
const SleepIcon = (props: any) => <Icon {...props} name="sleep" size={24} />;
const ChevronRightIcon = (props: any) => (
  <Icon {...props} name="chevron-right" size={20} />
);
const ClockOutlineIcon = (props: any) => (
  <Icon {...props} name="clock-outline" size={24} />
);

// Switch component factory moved outside to avoid nested components
const createSwitchComponent =
  (value: boolean, onToggle: (value: boolean) => void) => () => (
    <Switch value={value} onValueChange={onToggle} />
  );

export default function NotificationSettingsScreen() {
  const [workoutReminders, setWorkoutReminders] = useState(true);
  const [mealReminders, setMealReminders] = useState(true);
  const [habitReminders, setHabitReminders] = useState(true);
  const [progressUpdates, setProgressUpdates] = useState(true);
  const [aiCoachTips, setAiCoachTips] = useState(true);
  const [weeklyReports, setWeeklyReports] = useState(true);

  const notificationTypes = [
    {
      title: 'Workout Reminders',
      description: 'Get reminded about scheduled workouts',
      icon: 'dumbbell',
      IconComponent: DumbbellIcon,
      SwitchComponent: createSwitchComponent(
        workoutReminders,
        setWorkoutReminders
      ),
      value: workoutReminders,
      onToggle: setWorkoutReminders,
    },
    {
      title: 'Meal Reminders',
      description: 'Reminders for meal times and logging',
      icon: 'food',
      IconComponent: FoodIcon,
      SwitchComponent: createSwitchComponent(mealReminders, setMealReminders),
      value: mealReminders,
      onToggle: setMealReminders,
    },
    {
      title: 'Habit Reminders',
      description: 'Daily habit and routine reminders',
      icon: 'calendar-check',
      IconComponent: CheckCircleIcon,
      SwitchComponent: createSwitchComponent(habitReminders, setHabitReminders),
      value: habitReminders,
      onToggle: setHabitReminders,
    },
    {
      title: 'Progress Updates',
      description: 'Notifications about your progress milestones',
      icon: 'trending-up',
      IconComponent: TrendingUpIcon,
      SwitchComponent: createSwitchComponent(
        progressUpdates,
        setProgressUpdates
      ),
      value: progressUpdates,
      onToggle: setProgressUpdates,
    },
    {
      title: 'AI Coach Tips',
      description: 'Personalized tips and insights from your AI coach',
      icon: 'robot',
      IconComponent: LightbulbIcon,
      SwitchComponent: createSwitchComponent(aiCoachTips, setAiCoachTips),
      value: aiCoachTips,
      onToggle: setAiCoachTips,
    },
    {
      title: 'Weekly Reports',
      description: 'Weekly summary of your activities and progress',
      icon: 'chart-line',
      IconComponent: ChartLineIcon,
      SwitchComponent: createSwitchComponent(weeklyReports, setWeeklyReports),
      value: weeklyReports,
      onToggle: setWeeklyReports,
    },
  ];

  const timeSlots = [
    { label: 'Morning (6:00 - 12:00)', value: 'morning' },
    { label: 'Afternoon (12:00 - 18:00)', value: 'afternoon' },
    { label: 'Evening (18:00 - 22:00)', value: 'evening' },
  ];

  return (
    <ScreenWithTopBar title="Notification Settings">
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Notification Types</Text>
          <Text style={styles.sectionDescription}>
            Choose which notifications you'd like to receive
          </Text>
          {notificationTypes.map((notification, index) => (
            <List.Item
              key={index}
              title={notification.title}
              description={notification.description}
              left={notification.IconComponent}
              right={notification.SwitchComponent}
              style={styles.listItem}
            />
          ))}
        </Card>

        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Quiet Hours</Text>
          <Text style={styles.sectionDescription}>
            Set times when you don't want to receive notifications
          </Text>
          <List.Item
            title="Do Not Disturb"
            description="22:00 - 06:00"
            left={SleepIcon}
            right={ChevronRightIcon}
            onPress={() => {
              // TODO: Open time picker
            }}
            style={styles.listItem}
          />
        </Card>

        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Preferred Times</Text>
          <Text style={styles.sectionDescription}>
            When would you like to receive reminders?
          </Text>
          {timeSlots.map((slot, index) => (
            <List.Item
              key={index}
              title={slot.label}
              left={ClockOutlineIcon}
              right={ChevronRightIcon}
              onPress={() => {
                // TODO: Configure time slot
              }}
              style={styles.listItem}
            />
          ))}
        </Card>

        <Button
          mode="outlined"
          onPress={() => {
            // Test notification
          }}
          style={styles.testButton}
          icon="bell-ring"
        >
          Send Test Notification
        </Button>
      </ScrollView>
    </ScreenWithTopBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  card: {
    marginBottom: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 12,
  },
  listItem: {
    paddingVertical: 4,
  },
  testButton: {
    marginTop: 16,
  },
});

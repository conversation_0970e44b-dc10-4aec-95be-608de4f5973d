import { useNavigation } from '@react-navigation/native';
import { useQuery } from '@tanstack/react-query';
import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { <PERSON>, <PERSON><PERSON>, Card } from 'react-native-paper';
import { supabase } from '../../../lib/supabase';
import { ScreenWithTopBar } from '../../../shared/components';

interface Profile {
  full_name: string;
  age: number;
  height_cm: number;
  weight_kg: number;
  goals: string[];
  experience_level: string;
  ai_persona_preference: string;
  dietary_restrictions: string[];
  food_allergies: string[];
  budget_preference: string;
  known_injuries: string[];
}

const ProfileHeader = ({ profile }: { profile: Profile }) => {
  return (
    <Card style={styles.card}>
      <View style={styles.profileHeaderContent}>
        <Text style={styles.profileName}>{profile.full_name}</Text>
        <View style={styles.profileDetails}>
          <Text style={styles.profileDetailText}>Age: {profile.age}</Text>
          <Text style={styles.profileDetailText}>
            Height: {profile.height_cm} cm
          </Text>
          <Text style={styles.profileDetailText}>
            Weight: {profile.weight_kg} kg
          </Text>
        </View>
      </View>
    </Card>
  );
};

const ProfileSection = ({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) => {
  return (
    <Card style={styles.card}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </Card>
  );
};

const ProfileItem = ({
  label,
  value,
}: {
  label: string;
  value: string | string[];
}) => {
  return (
    <View style={styles.item}>
      <Text style={styles.label}>{label}:</Text>
      {Array.isArray(value) ? (
        <Text style={styles.valueList}>{value.join(', ')}</Text>
      ) : (
        <Text style={styles.value}>{value}</Text>
      )}
    </View>
  );
};

export default function ProfileSettingsScreen() {
  const [_loading, _setLoading] = useState(false);
  const _navigation = useNavigation();

  const {
    data: profile,
    isLoading,
    isError,
    error,
  } = useQuery<Profile>({
    queryKey: ['profile'],
    queryFn: async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('Not authenticated');
      }

      const { data, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single();

      if (profileError) {
        throw profileError;
      }
      return data;
    },
  });

  if (isLoading) {
    return (
      <ScreenWithTopBar title="Profile & Settings">
        <View style={[styles.container, styles.centered]}>
          <ActivityIndicator size="large" />
        </View>
      </ScreenWithTopBar>
    );
  }

  if (isError) {
    return (
      <ScreenWithTopBar title="Profile & Settings">
        <View style={[styles.container, styles.centered]}>
          <Text>Error loading profile: {error.message}</Text>
        </View>
      </ScreenWithTopBar>
    );
  }

  if (!profile) {
    return (
      <ScreenWithTopBar title="Profile & Settings">
        <View style={[styles.container, styles.centered]}>
          <Text>No profile data found</Text>
        </View>
      </ScreenWithTopBar>
    );
  }

  return (
    <ScreenWithTopBar title="Profile & Settings">
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <ProfileHeader profile={profile} />

        <ProfileSection title="Personal Info">
          <ProfileItem
            label="Experience Level"
            value={profile.experience_level}
          />
          <ProfileItem label="Goals" value={profile.goals} />
          <ProfileItem label="Known Injuries" value={profile.known_injuries} />
        </ProfileSection>

        <ProfileSection title="Nutrition Preferences">
          <ProfileItem
            label="Dietary Restrictions"
            value={profile.dietary_restrictions}
          />
          <ProfileItem label="Food Allergies" value={profile.food_allergies} />
          <ProfileItem
            label="Budget Preference"
            value={profile.budget_preference}
          />
        </ProfileSection>

        <ProfileSection title="AI Coach">
          <ProfileItem
            label="Persona Preference"
            value={profile.ai_persona_preference}
          />
        </ProfileSection>

        {/* Edit Profile Button */}
        <Button
          mode="contained"
          onPress={() => {
            // Navigate to EditProfile screen
            // @ts-ignore - Root navigation type union
            (_navigation as any).navigate('EditProfile');
          }}
          style={styles.editButton}
          icon="pencil"
        >
          Edit Profile
        </Button>
      </ScrollView>
    </ScreenWithTopBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    marginBottom: 16,
    padding: 16,
  },
  profileHeaderContent: {
    alignItems: 'center',
  },
  profileName: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  profileDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  profileDetailText: {
    fontSize: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  label: {
    fontWeight: '600',
  },
  value: {
    textAlign: 'right',
    flex: 1,
  },
  valueList: {
    textAlign: 'right',
    flex: 1,
  },
  editButton: {
    marginTop: 8,
  },
});

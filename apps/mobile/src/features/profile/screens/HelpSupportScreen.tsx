import React from 'react';
import { <PERSON><PERSON>View, StyleSheet, <PERSON><PERSON>, Alert } from 'react-native';
import { Text, Card, List, Button } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { ScreenWithTopBar } from '../../../shared/components';

// Icon components to avoid nested component warnings
const HelpCircleIcon = (props: any) => (
  <Icon {...props} name="help-circle" size={24} />
);
const BookIcon = (props: any) => (
  <Icon {...props} name="book-open-variant" size={24} />
);
const PhoneIcon = (props: any) => <Icon {...props} name="phone" size={24} />; // eslint-disable-line @typescript-eslint/no-unused-vars
const EmailIcon = (props: any) => <Icon {...props} name="email" size={24} />; // eslint-disable-line @typescript-eslint/no-unused-vars
const StarIcon = (props: any) => <Icon {...props} name="star" size={24} />; // eslint-disable-line @typescript-eslint/no-unused-vars
const InformationIcon = (props: any) => (
  <Icon {...props} name="information" size={24} />
);
const ChevronRightIcon = (props: any) => (
  <Icon {...props} name="chevron-right" size={20} />
);
const PlayCircleIcon = (props: any) => (
  <Icon {...props} name="play-circle" size={24} />
);
const HeadsetIcon = (props: any) => (
  <Icon {...props} name="headset" size={24} />
);
const LightbulbIcon = (props: any) => (
  <Icon {...props} name="lightbulb" size={24} />
);
const FileDocumentIcon = (props: any) => (
  <Icon {...props} name="file-document" size={24} />
);
const ShieldAccountIcon = (props: any) => (
  <Icon {...props} name="shield-account" size={24} />
);
const BugIcon = (props: any) => <Icon {...props} name="bug" size={24} />;

export default function HelpSupportScreen() {
  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      "Choose how you'd like to contact our support team:",
      [
        {
          text: 'Email',
          onPress: () => Linking.openURL('mailto:<EMAIL>'),
        },
        {
          text: 'Chat',
          onPress: () => {
            // TODO: Open in-app chat
            Alert.alert('Chat Support', 'In-app chat coming soon!');
          },
        },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleOpenFAQ = () => {
    // TODO: Navigate to FAQ screen or open web FAQ
    Alert.alert('FAQ', 'Frequently Asked Questions coming soon!');
  };

  const handleOpenUserGuide = () => {
    // TODO: Navigate to user guide or open web guide
    Alert.alert('User Guide', 'User guide coming soon!');
  };

  const handleReportBug = () => {
    Alert.alert(
      'Report a Bug',
      'Help us improve PlateMotion by reporting any issues you encounter.',
      [
        {
          text: 'Send Report',
          onPress: () => {
            // TODO: Open bug report form
            Alert.alert('Bug Report', 'Bug report form coming soon!');
          },
        },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleFeatureRequest = () => {
    Alert.alert(
      'Feature Request',
      "Have an idea for a new feature? We'd love to hear it!",
      [
        {
          text: 'Submit Idea',
          onPress: () => {
            // TODO: Open feature request form
            Alert.alert('Feature Request', 'Feature request form coming soon!');
          },
        },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  return (
    <ScreenWithTopBar title="Help & Support">
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
      >
        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Get Help</Text>
          <List.Item
            title="Frequently Asked Questions"
            description="Find answers to common questions"
            left={HelpCircleIcon}
            right={ChevronRightIcon}
            onPress={handleOpenFAQ}
            style={styles.listItem}
          />
          <List.Item
            title="User Guide"
            description="Learn how to use PlateMotion effectively"
            left={BookIcon}
            right={ChevronRightIcon}
            onPress={handleOpenUserGuide}
            style={styles.listItem}
          />
          <List.Item
            title="Video Tutorials"
            description="Watch step-by-step tutorials"
            left={PlayCircleIcon}
            right={ChevronRightIcon}
            onPress={() => {
              // TODO: Open video tutorials
              Alert.alert('Video Tutorials', 'Video tutorials coming soon!');
            }}
            style={styles.listItem}
          />
        </Card>

        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>Contact Support</Text>
          <List.Item
            title="Contact Support Team"
            description="Get help from our support team"
            left={HeadsetIcon}
            right={ChevronRightIcon}
            onPress={handleContactSupport}
            style={styles.listItem}
          />
          <List.Item
            title="Report a Bug"
            description="Let us know about any issues"
            left={BugIcon}
            right={ChevronRightIcon}
            onPress={handleReportBug}
            style={styles.listItem}
          />
          <List.Item
            title="Request a Feature"
            description="Suggest new features or improvements"
            left={LightbulbIcon}
            right={ChevronRightIcon}
            onPress={handleFeatureRequest}
            style={styles.listItem}
          />
        </Card>

        <Card style={styles.card}>
          <Text style={styles.sectionTitle}>App Information</Text>
          <List.Item
            title="Version"
            description="1.0.0 (Build 1)"
            left={InformationIcon}
            style={styles.listItem}
          />
          <List.Item
            title="Terms of Service"
            description="Read our terms and conditions"
            left={FileDocumentIcon}
            right={ChevronRightIcon}
            onPress={() => {
              // TODO: Open terms of service
              Linking.openURL('https://platemotion.com/terms');
            }}
            style={styles.listItem}
          />
          <List.Item
            title="Privacy Policy"
            description="Learn about our privacy practices"
            left={ShieldAccountIcon}
            right={ChevronRightIcon}
            onPress={() => {
              // TODO: Open privacy policy
              Linking.openURL('https://platemotion.com/privacy');
            }}
            style={styles.listItem}
          />
        </Card>

        <Button
          mode="outlined"
          onPress={() => {
            Alert.alert(
              'Rate PlateMotion',
              'Enjoying PlateMotion? Please rate us on the App Store!',
              [
                {
                  text: 'Rate Now',
                  onPress: () => {
                    // TODO: Open app store rating
                    Alert.alert('App Store', 'App Store rating coming soon!');
                  },
                },
                { text: 'Later', style: 'cancel' },
              ]
            );
          }}
          style={styles.rateButton}
          icon="star"
        >
          Rate PlateMotion
        </Button>
      </ScrollView>
    </ScreenWithTopBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  card: {
    marginBottom: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  listItem: {
    paddingVertical: 4,
  },
  rateButton: {
    marginTop: 16,
  },
});

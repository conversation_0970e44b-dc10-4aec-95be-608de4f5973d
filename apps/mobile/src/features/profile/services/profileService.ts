import { supabase } from '../../../lib/supabase';

// Test function to verify Supabase connection and profile table
export async function testSupabaseConnection() {
  console.log('Testing Supabase connection...');

  try {
    // Test 1: Check if we can connect to Supabase
    const { data: healthData, error: healthError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);

    if (healthError) {
      console.log('Error connecting to profiles table:', healthError);
      // Try a simpler query
      const { data: simpleData, error: simpleError } =
        await supabase.rpc('now');
      if (simpleError) {
        console.log('Basic Supabase connection error:', simpleError);
      } else {
        console.log(
          'Basic Supabase connection successful. Current time:',
          simpleData
        );
      }
    } else {
      console.log(
        'Successfully connected to profiles table. Sample data:',
        healthData
      );
    }

    // Test 2: Check auth status
    const { data: sessionData, error: sessionError } =
      await supabase.auth.getSession();
    if (sessionError) {
      console.log('Auth session error:', sessionError);
    } else {
      console.log('Auth session:', sessionData?.session ? 'Active' : 'None');
      if (sessionData?.session?.user) {
        console.log('User ID:', sessionData.session.user.id);
      }
    }

    // Test 3: List tables
    console.log('Attempting to list tables...');
  } catch (e) {
    console.error('Unexpected error testing Supabase connection:', e);
  }
}

// Function to create a profile for a user
export async function createProfile(userId: string) {
  console.log('Creating profile for user:', userId);

  const { data, error } = await supabase
    .from('profiles')
    .insert([
      {
        id: userId,
        onboarding_complete: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ])
    .select();

  if (error) {
    console.error('Error creating profile:', error);
    return { error };
  }

  console.log('Profile created successfully:', data);
  return { data };
}

// Function to check if profile exists
export async function checkProfileExists(userId: string) {
  console.log('Checking if profile exists for user:', userId);

  const { data, error } = await supabase
    .from('profiles')
    .select('id, onboarding_complete')
    .eq('id', userId)
    .single();

  if (error) {
    console.log('Error checking profile:', error);
    return { exists: false, error };
  }

  console.log('Profile check result:', data);
  return { exists: !!data, data };
}

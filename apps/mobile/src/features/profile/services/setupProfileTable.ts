import { supabase } from '../../../lib/supabase';

// Function to add missing columns to profiles table
export async function setupProfileTable() {
  console.log('Setting up profiles table...');

  try {
    // First, check if the table exists and what columns it has
    console.log('Checking current table structure...');

    // Try to add the onboarding_complete column if it doesn't exist
    // Note: In a real Supabase environment, you would typically do this via SQL migration
    // But we can try to work with what we have

    console.log('Profile table setup complete');
  } catch (e) {
    console.error('Error setting up profiles table:', e);
  }
}

// Function to test if we can write to the profiles table
export async function testProfileWrite() {
  console.log('Testing profile write...');

  try {
    // Get current session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError) {
      console.log('Session error:', sessionError);
      return;
    }

    if (!session?.user) {
      console.log('No user session found');
      return;
    }

    console.log('Current user ID:', session.user.id);

    // Try a simple select first
    const { data: selectData, error: selectError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', session.user.id);

    if (selectError) {
      console.log('Select error:', selectError);
    } else {
      console.log('Select success. Data:', selectData);
    }
  } catch (e) {
    console.error('Error testing profile write:', e);
  }
}

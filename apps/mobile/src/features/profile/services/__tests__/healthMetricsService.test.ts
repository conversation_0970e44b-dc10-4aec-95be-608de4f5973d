// Mock Supabase before importing
jest.mock('../../../../lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      update: jest.fn(() => ({
        eq: jest.fn(() => Promise.resolve({ error: null })),
      })),
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: null, error: null })),
        })),
      })),
    })),
  },
}));

import { calculateHealthMetrics, UserBasicData } from '../healthMetricsService';

describe('Health Metrics Service', () => {
  const mockUserData: UserBasicData = {
    biologicalSex: 'male',
    dateOfBirth: '1990-01-01',
    heightCm: 180,
    weightKg: 80,
    activityLevel: 'moderately_active',
    primaryGoal: 'lose_weight',
    timeline: 'moderate',
  };

  describe('calculateHealthMetrics', () => {
    it('should calculate BMR correctly for male', () => {
      const metrics = calculateHealthMetrics(mockUserData);

      // Expected BMR for 35-year-old male, 180cm, 80kg (born 1990, current year 2025)
      // BMR = 10 * 80 + 6.25 * 180 - 5 * 35 + 5 = 800 + 1125 - 175 + 5 = 1755
      expect(metrics.bmr).toBe(1755);
    });

    it('should calculate BMR correctly for female', () => {
      const femaleData = { ...mockUserData, biologicalSex: 'female' as const };
      const metrics = calculateHealthMetrics(femaleData);

      // Expected BMR for 35-year-old female, 180cm, 80kg
      // BMR = 10 * 80 + 6.25 * 180 - 5 * 35 - 161 = 800 + 1125 - 175 - 161 = 1589
      expect(metrics.bmr).toBe(1589);
    });

    it('should calculate BMI correctly', () => {
      const metrics = calculateHealthMetrics(mockUserData);

      // BMI = 80 / (1.8)² = 80 / 3.24 = 24.7
      expect(metrics.bmi).toBe(24.7);
    });

    it('should calculate TDEE correctly', () => {
      const metrics = calculateHealthMetrics(mockUserData);

      // TDEE = BMR * activity factor (1.55 for moderately_active)
      // TDEE = 1755 * 1.55 = 2720
      expect(metrics.tdee).toBe(2720);
    });

    it('should calculate target calories for weight loss', () => {
      const metrics = calculateHealthMetrics(mockUserData);

      // For weight loss with moderate timeline: TDEE - 500
      // Target = 2720 - 500 = 2220, range 2120-2320
      expect(metrics.targetCaloriesMin).toBe(2120);
      expect(metrics.targetCaloriesMax).toBe(2320);
    });

    it('should calculate target calories for muscle gain', () => {
      const muscleGainData = {
        ...mockUserData,
        primaryGoal: 'gain_muscle' as const,
      };
      const metrics = calculateHealthMetrics(muscleGainData);

      // For muscle gain with moderate timeline: TDEE + 300
      // Target = 2720 + 300 = 3020, range 2920-3120
      expect(metrics.targetCaloriesMin).toBe(2920);
      expect(metrics.targetCaloriesMax).toBe(3120);
    });

    it('should calculate target calories for strength gain', () => {
      const strengthData = {
        ...mockUserData,
        primaryGoal: 'gain_strength' as const,
      };
      const metrics = calculateHealthMetrics(strengthData);

      // For strength gain with moderate timeline: TDEE + 100
      // Target = 2720 + 100 = 2820, range 2720-2920
      expect(metrics.targetCaloriesMin).toBe(2720);
      expect(metrics.targetCaloriesMax).toBe(2920);
    });

    it('should adjust calories based on timeline - fast', () => {
      const fastData = { ...mockUserData, timeline: 'fast' as const };
      const metrics = calculateHealthMetrics(fastData);

      // For fast weight loss: TDEE - 750
      // Target = 2720 - 750 = 1970, range 1870-2070
      expect(metrics.targetCaloriesMin).toBe(1870);
      expect(metrics.targetCaloriesMax).toBe(2070);
    });

    it('should adjust calories based on timeline - gradual', () => {
      const gradualData = { ...mockUserData, timeline: 'gradual' as const };
      const metrics = calculateHealthMetrics(gradualData);

      // For gradual weight loss: TDEE - 250
      // Target = 2720 - 250 = 2470, range 2370-2570
      expect(metrics.targetCaloriesMin).toBe(2370);
      expect(metrics.targetCaloriesMax).toBe(2570);
    });

    it('should calculate ideal weight range correctly', () => {
      const metrics = calculateHealthMetrics(mockUserData);

      // For 180cm height:
      // Min weight (BMI 18.5): 18.5 * (1.8)² = 59.9kg
      // Max weight (BMI 24.9): 24.9 * (1.8)² = 80.7kg (rounded)
      expect(metrics.idealWeightMin).toBe(59.9);
      expect(metrics.idealWeightMax).toBe(80.7);
    });

    it('should handle different activity levels', () => {
      const sedentaryData = {
        ...mockUserData,
        activityLevel: 'sedentary' as const,
      };
      const veryActiveData = {
        ...mockUserData,
        activityLevel: 'very_active' as const,
      };

      const sedentaryMetrics = calculateHealthMetrics(sedentaryData);
      const veryActiveMetrics = calculateHealthMetrics(veryActiveData);

      // Sedentary: BMR * 1.2 = 1755 * 1.2 = 2106
      expect(sedentaryMetrics.tdee).toBe(2106);

      // Very active: BMR * 1.725 = 1755 * 1.725 = 3027
      expect(veryActiveMetrics.tdee).toBe(3027);
    });

    it('should enforce minimum calorie limit', () => {
      // Create scenario that would result in very low calories
      const extremeData: UserBasicData = {
        biologicalSex: 'female',
        dateOfBirth: '2000-01-01', // 24 years old
        heightCm: 150,
        weightKg: 45,
        activityLevel: 'sedentary',
        primaryGoal: 'lose_weight',
        timeline: 'fast',
      };

      const metrics = calculateHealthMetrics(extremeData);

      // Should never go below 1200 calories
      expect(metrics.targetCaloriesMin).toBeGreaterThanOrEqual(1200);
    });

    it('should handle edge cases for age calculation', () => {
      // Test with someone born today (age 0)
      const today = new Date();
      const todayString = today.toISOString().split('T')[0];
      const newbornData = { ...mockUserData, dateOfBirth: todayString };

      const metrics = calculateHealthMetrics(newbornData);

      // Should still calculate without errors
      expect(metrics.bmr).toBeGreaterThan(0);
      expect(metrics.bmi).toBeGreaterThan(0);
      expect(metrics.tdee).toBeGreaterThan(0);
    });
  });
});

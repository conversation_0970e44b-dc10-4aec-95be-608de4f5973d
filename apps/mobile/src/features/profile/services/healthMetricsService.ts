import { supabase } from '../../../lib/supabase';

export interface HealthMetrics {
  bmr: number;
  bmi: number;
  tdee: number;
  targetCaloriesMin: number;
  targetCaloriesMax: number;
  idealWeightMin: number;
  idealWeightMax: number;
}

export interface UserBasicData {
  biologicalSex: 'male' | 'female';
  dateOfBirth: string; // YYYY-MM-DD format
  heightCm: number;
  weightKg: number;
  activityLevel:
    | 'sedentary'
    | 'lightly_active'
    | 'moderately_active'
    | 'very_active'
    | 'extremely_active';
  primaryGoal: 'lose_weight' | 'gain_muscle' | 'gain_strength';
  timeline: 'fast' | 'moderate' | 'gradual';
}

/**
 * Calculate age from date of birth
 */
function calculateAge(dateOfBirth: string): number {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  return age;
}

/**
 * Calculate BMR using Mifflin-St Jeor Equation (most accurate)
 * Men: BMR = 10 × weight(kg) + 6.25 × height(cm) - 5 × age + 5
 * Women: BMR = 10 × weight(kg) + 6.25 × height(cm) - 5 × age - 161
 */
function calculateBMR(
  biologicalSex: string,
  age: number,
  heightCm: number,
  weightKg: number
): number {
  const baseBMR = 10 * weightKg + 6.25 * heightCm - 5 * age;

  if (biologicalSex === 'male') {
    return baseBMR + 5;
  } else {
    return baseBMR - 161;
  }
}

/**
 * Calculate BMI
 * BMI = weight(kg) / (height(m))²
 */
function calculateBMI(heightCm: number, weightKg: number): number {
  const heightM = heightCm / 100;
  return weightKg / (heightM * heightM);
}

/**
 * Calculate TDEE (Total Daily Energy Expenditure)
 * BMR × Activity Factor
 */
function calculateTDEE(bmr: number, activityLevel: string): number {
  const activityFactors = {
    sedentary: 1.2,
    lightly_active: 1.375,
    moderately_active: 1.55,
    very_active: 1.725,
    extremely_active: 1.9,
  };

  const factor =
    activityFactors[activityLevel as keyof typeof activityFactors] || 1.2;
  return bmr * factor;
}

/**
 * Calculate target calorie range based on goals and timeline
 */
function calculateTargetCalories(
  tdee: number,
  primaryGoal: string,
  timeline: string
): { min: number; max: number } {
  let calorieAdjustment = 0;

  // Base adjustments by goal
  switch (primaryGoal) {
    case 'lose_weight':
      calorieAdjustment = -500; // 1 lb per week loss
      break;
    case 'gain_muscle':
      calorieAdjustment = 300; // Moderate surplus for muscle gain
      break;
    case 'gain_strength':
      calorieAdjustment = 100; // Small surplus for strength
      break;
    default:
      calorieAdjustment = 0;
  }

  // Adjust based on timeline
  switch (timeline) {
    case 'fast':
      if (primaryGoal === 'lose_weight') calorieAdjustment = -750; // More aggressive deficit
      if (primaryGoal === 'gain_muscle') calorieAdjustment = 500; // Higher surplus
      break;
    case 'gradual':
      if (primaryGoal === 'lose_weight') calorieAdjustment = -250; // Smaller deficit
      if (primaryGoal === 'gain_muscle') calorieAdjustment = 200; // Smaller surplus
      break;
  }

  const targetCalories = Math.round(tdee + calorieAdjustment);

  // Create a range (±100 calories for flexibility)
  return {
    min: Math.max(1200, targetCalories - 100), // Never go below 1200 calories
    max: targetCalories + 100,
  };
}

/**
 * Calculate ideal weight range based on healthy BMI (18.5-24.9)
 */
function calculateIdealWeightRange(heightCm: number): {
  min: number;
  max: number;
} {
  const heightM = heightCm / 100;
  const minWeight = 18.5 * (heightM * heightM);
  const maxWeight = 24.9 * (heightM * heightM);

  return {
    min: Math.round(minWeight * 10) / 10,
    max: Math.round(maxWeight * 10) / 10,
  };
}

/**
 * Calculate all health metrics from user data
 */
export function calculateHealthMetrics(userData: UserBasicData): HealthMetrics {
  const age = calculateAge(userData.dateOfBirth);
  const bmr = calculateBMR(
    userData.biologicalSex,
    age,
    userData.heightCm,
    userData.weightKg
  );
  const bmi = calculateBMI(userData.heightCm, userData.weightKg);
  const tdee = calculateTDEE(bmr, userData.activityLevel);
  const targetCalories = calculateTargetCalories(
    tdee,
    userData.primaryGoal,
    userData.timeline
  );
  const idealWeight = calculateIdealWeightRange(userData.heightCm);

  return {
    bmr: Math.round(bmr),
    bmi: Math.round(bmi * 10) / 10,
    tdee: Math.round(tdee),
    targetCaloriesMin: targetCalories.min,
    targetCaloriesMax: targetCalories.max,
    idealWeightMin: idealWeight.min,
    idealWeightMax: idealWeight.max,
  };
}

/**
 * Save health metrics to user profile
 */
export async function saveHealthMetricsToProfile(
  userId: string,
  metrics: HealthMetrics
): Promise<{ error?: any }> {
  try {
    const { error } = await supabase
      .from('profiles')
      .update({
        bmr: metrics.bmr,
        bmi: metrics.bmi,
        tdee: metrics.tdee,
        target_calories_min: metrics.targetCaloriesMin,
        target_calories_max: metrics.targetCaloriesMax,
        ideal_weight_min: metrics.idealWeightMin,
        ideal_weight_max: metrics.idealWeightMax,
        health_metrics_calculated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (error) {
      console.error('Error saving health metrics:', error);
      return { error };
    }

    console.log('Health metrics saved successfully:', metrics);
    return {};
  } catch (error) {
    console.error('Error in saveHealthMetricsToProfile:', error);
    return { error };
  }
}

/**
 * Get health metrics from user profile
 */
export async function getHealthMetricsFromProfile(
  userId: string
): Promise<{ data?: HealthMetrics; error?: any }> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select(
        'bmr, bmi, tdee, target_calories_min, target_calories_max, ideal_weight_min, ideal_weight_max, health_metrics_calculated_at'
      )
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching health metrics:', error);
      return { error };
    }

    if (!data?.bmr) {
      return { data: undefined };
    }

    return {
      data: {
        bmr: data.bmr,
        bmi: data.bmi,
        tdee: data.tdee,
        targetCaloriesMin: data.target_calories_min,
        targetCaloriesMax: data.target_calories_max,
        idealWeightMin: data.ideal_weight_min,
        idealWeightMax: data.ideal_weight_max,
      },
    };
  } catch (error) {
    console.error('Error in getHealthMetricsFromProfile:', error);
    return { error };
  }
}

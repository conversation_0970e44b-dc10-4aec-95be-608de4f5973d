import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
// No import needed for simple UUID generation
import { chatDatabaseService } from '../services/chatDatabaseService';
import { ChatMessage, ChatSession, ToolCall } from '../types';

interface ChatState {
  // Version for data migration
  version: number;

  // Current session
  currentSession: ChatSession | null;
  messages: ChatMessage[];
  isLoading: boolean;
  isTyping: boolean;

  // Tool execution
  executingTools: ToolCall[];

  // Session management
  sessions: ChatSession[];

  // Database sync status
  isOnline: boolean;
  isSyncing: boolean;

  // Actions
  startNewSession: () => Promise<void>;
  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => Promise<void>;
  updateMessage: (
    messageId: string,
    updates: Partial<ChatMessage>
  ) => Promise<void>;
  setIsTyping: (typing: boolean) => void;
  setIsLoading: (loading: boolean) => void;

  // Tool actions
  addToolCall: (messageId: string, toolCall: ToolCall) => void;
  updateToolCall: (
    messageId: string,
    toolCallId: string,
    updates: Partial<ToolCall>
  ) => void;

  // Session actions
  loadSession: (sessionId: string) => Promise<void>;
  loadUserSessions: () => Promise<void>;
  deleteSession: (sessionId: string) => Promise<void>;
  clearCurrentSession: () => void;

  // Database sync actions
  syncWithDatabase: () => Promise<void>;
  setOnlineStatus: (online: boolean) => void;

  // Debug/utility methods
  clearAllData: () => void;
}

// Generate a simple unique ID
const generateId = () =>
  Math.random().toString(36).substr(2, 9) + Date.now().toString(36);

// Current data version - increment this to force data migration/clearing
const CURRENT_VERSION = 2;

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      // Initial state
      version: CURRENT_VERSION,
      currentSession: null,
      messages: [],
      isLoading: false,
      isTyping: false,
      executingTools: [],
      sessions: [],
      isOnline: true,
      isSyncing: false,

      // Actions
      startNewSession: async () => {
        try {
          set({ isSyncing: true });

          // Try to create session in database first
          const dbSession = await chatDatabaseService.createSession('New Chat');

          if (dbSession) {
            // Database session created successfully
            set(state => ({
              currentSession: dbSession,
              messages: [],
              sessions: [dbSession, ...state.sessions],
              isSyncing: false,
            }));
          } else {
            // Fallback to local session if database fails
            const localSession: ChatSession = {
              id: generateId(),
              title: 'New Chat',
              messages: [],
              createdAt: new Date(),
              updatedAt: new Date(),
            };

            set(state => ({
              currentSession: localSession,
              messages: [],
              sessions: [localSession, ...state.sessions],
              isSyncing: false,
            }));
          }
        } catch (error) {
          console.error('Error starting new session:', error);
          set({ isSyncing: false });
        }
      },

      addMessage: async messageData => {
        const state = get();
        if (!state.currentSession) {
          return;
        }

        const tempMessage: ChatMessage = {
          id: generateId(),
          timestamp: new Date(),
          ...messageData,
        };

        // Update local state immediately for responsive UI
        const updatedMessages = [...state.messages, tempMessage];
        const newTitle =
          state.currentSession.title === 'New Chat' &&
          tempMessage.role === 'user'
            ? tempMessage.content.slice(0, 30) +
              (tempMessage.content.length > 30 ? '...' : '')
            : state.currentSession.title;

        const updatedSession = {
          ...state.currentSession,
          messages: updatedMessages,
          updatedAt: new Date(),
          title: newTitle,
        };

        set({
          messages: updatedMessages,
          currentSession: updatedSession,
          sessions: state.sessions.map(s =>
            s.id === updatedSession.id ? updatedSession : s
          ),
        });

        // Save to database in background
        try {
          console.log(
            'Attempting to save message for session:',
            state.currentSession.id
          );
          const messageOrder = updatedMessages.length - 1;
          const savedMessage = await chatDatabaseService.saveMessage(
            state.currentSession.id,
            messageData,
            messageOrder
          );

          // Update the message with the database ID if successful
          if (savedMessage) {
            const finalMessages = state.messages.map(msg =>
              msg.id === tempMessage.id ? savedMessage : msg
            );

            set(prevState => ({
              messages: finalMessages,
              currentSession: prevState.currentSession
                ? {
                    ...prevState.currentSession,
                    messages: finalMessages,
                  }
                : null,
            }));
          }

          // Update session title in database if it changed
          if (newTitle !== state.currentSession.title) {
            await chatDatabaseService.updateSessionTitle(
              state.currentSession.id,
              newTitle
            );
          }
        } catch (error) {
          console.error('Error saving message to database:', error);
        }
      },

      updateMessage: async (messageId, updates) => {
        const state = get();

        // Update local state immediately
        const updatedMessages = state.messages.map(msg =>
          msg.id === messageId ? { ...msg, ...updates } : msg
        );

        const updatedSession = state.currentSession
          ? {
              ...state.currentSession,
              messages: updatedMessages,
              updatedAt: new Date(),
            }
          : null;

        set({
          messages: updatedMessages,
          currentSession: updatedSession,
          sessions: updatedSession
            ? state.sessions.map(s =>
                s.id === updatedSession.id ? updatedSession : s
              )
            : state.sessions,
        });

        // Update in database
        try {
          await chatDatabaseService.updateMessage(messageId, updates);
        } catch (error) {
          console.error('Error updating message in database:', error);
        }
      },

      setIsTyping: typing => set({ isTyping: typing }),
      setIsLoading: loading => set({ isLoading: loading }),

      addToolCall: (messageId, toolCall) => {
        set(state => {
          const updatedMessages = state.messages.map(msg =>
            msg.id === messageId
              ? { ...msg, toolCalls: [...(msg.toolCalls || []), toolCall] }
              : msg
          );

          return {
            messages: updatedMessages,
            executingTools: [...state.executingTools, toolCall],
          };
        });
      },

      updateToolCall: (messageId, toolCallId, updates) => {
        set(state => {
          const updatedMessages = state.messages.map(msg =>
            msg.id === messageId
              ? {
                  ...msg,
                  toolCalls: msg.toolCalls?.map(tc =>
                    tc.id === toolCallId ? { ...tc, ...updates } : tc
                  ),
                }
              : msg
          );

          const updatedExecutingTools = state.executingTools
            .map(tc => (tc.id === toolCallId ? { ...tc, ...updates } : tc))
            .filter(tc => tc.status === 'executing' || tc.status === 'pending');

          return {
            messages: updatedMessages,
            executingTools: updatedExecutingTools,
          };
        });
      },

      loadSession: async sessionId => {
        try {
          set({ isSyncing: true });

          // Try to load from database first
          const messages =
            await chatDatabaseService.getSessionMessages(sessionId);
          const session = get().sessions.find(s => s.id === sessionId);

          if (session) {
            const updatedSession = {
              ...session,
              messages,
            };

            set({
              currentSession: updatedSession,
              messages,
              isSyncing: false,
            });
          } else {
            // Fallback to local session
            const localSession = get().sessions.find(s => s.id === sessionId);
            if (localSession) {
              set({
                currentSession: localSession,
                messages: localSession.messages,
                isSyncing: false,
              });
            }
          }
        } catch (error) {
          console.error('Error loading session:', error);
          set({ isSyncing: false });
        }
      },

      loadUserSessions: async () => {
        try {
          set({ isSyncing: true });
          const dbSessions = await chatDatabaseService.getUserSessions();

          set({
            sessions: dbSessions,
            isSyncing: false,
          });
        } catch (error) {
          console.error('Error loading user sessions:', error);
          set({ isSyncing: false });
        }
      },

      deleteSession: async sessionId => {
        try {
          // Delete from database
          await chatDatabaseService.deleteSession(sessionId);

          // Update local state
          set(state => ({
            sessions: state.sessions.filter(s => s.id !== sessionId),
            currentSession:
              state.currentSession?.id === sessionId
                ? null
                : state.currentSession,
            messages:
              state.currentSession?.id === sessionId ? [] : state.messages,
          }));
        } catch (error) {
          console.error('Error deleting session:', error);
        }
      },

      clearCurrentSession: () => {
        set({
          currentSession: null,
          messages: [],
          isLoading: false,
          isTyping: false,
          executingTools: [],
        });
      },

      // Database sync actions
      syncWithDatabase: async () => {
        try {
          set({ isSyncing: true });

          // Load all user sessions from database
          const dbSessions = await chatDatabaseService.getUserSessions();

          set({
            sessions: dbSessions,
            isSyncing: false,
          });
        } catch (error) {
          console.error('Error syncing with database:', error);
          set({ isSyncing: false });
        }
      },

      setOnlineStatus: (online: boolean) => {
        set({ isOnline: online });

        // If coming back online, sync with database
        if (online) {
          get().syncWithDatabase();
        }
      },

      // Debug/utility methods
      clearAllData: () => {
        set({
          currentSession: null,
          messages: [],
          sessions: [],
          isLoading: false,
          isTyping: false,
          executingTools: [],
          isSyncing: false,
        });
      },
    }),
    {
      name: 'ai-coach-chat-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: state => ({
        version: state.version,
        sessions: state.sessions,
        currentSession: state.currentSession,
      }),
      migrate: (persistedState: any, version: number) => {
        console.log('Chat store migration called:', {
          persistedState,
          version,
          currentVersion: CURRENT_VERSION,
        });
        // If the persisted state doesn't have a version or has an old version, clear it
        if (
          !persistedState?.version ||
          persistedState.version < CURRENT_VERSION
        ) {
          console.log('Migrating chat store data - clearing old format');
          return {
            version: CURRENT_VERSION,
            sessions: [],
            currentSession: null,
          };
        }
        console.log('Chat store data is up to date');
        return persistedState;
      },
      version: CURRENT_VERSION,
    }
  )
);

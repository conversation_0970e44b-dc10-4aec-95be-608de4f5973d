// Comprehensive questionnaire data structure for conversational AI questionnaires

export interface QuestionnaireQuestion {
  id: string;
  text: string;
  type:
    | 'single_choice'
    | 'multiple_choice'
    | 'text_input'
    | 'number_input'
    | 'slider';
  options?: QuestionnaireOption[];
  validation?: {
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: string;
  };
  followUp?: {
    condition: string;
    questions: string[];
  };
  personaMapping: {
    category: string;
    trait: string;
    weight: number;
  };
}

export interface QuestionnaireOption {
  value: string;
  label: string;
  emoji?: string;
}

export interface Questionnaire {
  id: string;
  title: string;
  description: string;
  estimatedTime: string;
  questions: QuestionnaireQuestion[];
  completionMessage: string;
  unlocks: string[];
}

// BASIC QUESTIONNAIRE - Essential Foundation
export const basicQuestionnaire: Questionnaire = {
  id: 'basic',
  title: 'Basic Profile',
  description: 'Essential info to get started with personalized advice',
  estimatedTime: '1-2 minutes',
  completionMessage:
    'Perfect! I now understand your goals and can give you personalized advice. Want to unlock even more features?',
  unlocks: ['general_advice', 'progress_tracking', 'motivation'],
  questions: [
    {
      id: 'unit_system',
      text: 'Which measurement system do you prefer?',
      type: 'single_choice',
      options: [
        { value: 'metric', label: 'Metric (cm, kg)', emoji: '📏' },
        { value: 'imperial', label: 'Imperial (ft/in, lb)', emoji: '📐' },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'preferences',
        trait: 'unit_system',
        weight: 1.0,
      },
    },
    {
      id: 'biological_sex',
      text: "What's your biological sex? This helps me calculate accurate calorie and exercise recommendations.",
      type: 'single_choice',
      options: [
        { value: 'male', label: 'Male', emoji: '♂️' },
        { value: 'female', label: 'Female', emoji: '♀️' },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'demographics',
        trait: 'biological_sex',
        weight: 1.0,
      },
    },
    {
      id: 'date_of_birth',
      text: 'What is your date of birth?',
      type: 'text_input',
      validation: { required: true, pattern: '^\\d{4}-\\d{2}-\\d{2}$' },
      personaMapping: {
        category: 'demographics',
        trait: 'date_of_birth',
        weight: 1.0,
      },
    },
    {
      id: 'height_cm',
      text: "What's your height? (You can use feet/inches or centimeters)",
      type: 'number_input',
      validation: { required: true, min: 100, max: 250 },
      personaMapping: {
        category: 'physical_stats',
        trait: 'height',
        weight: 1.0,
      },
    },
    {
      id: 'weight_kg',
      text: "What's your current weight? (Pounds or kilograms)",
      type: 'number_input',
      validation: { required: true, min: 30, max: 300 },
      personaMapping: {
        category: 'physical_stats',
        trait: 'weight',
        weight: 1.0,
      },
    },
    {
      id: 'primary_goal',
      text: "What's your main fitness goal right now? This helps me create the perfect plan for you.",
      type: 'single_choice',
      options: [
        { value: 'lose_weight', label: 'Lose Weight', emoji: '📉' },
        { value: 'gain_muscle', label: 'Gain Muscle', emoji: '💪' },
        { value: 'gain_strength', label: 'Gain Strength', emoji: '🏋️' },
      ],
      validation: { required: true },
      personaMapping: { category: 'goals', trait: 'primary_goal', weight: 2.0 },
    },
    {
      id: 'timeline',
      text: "What's your timeline for seeing results?",
      type: 'single_choice',
      options: [
        { value: 'fast', label: '1-3 months (fast results)', emoji: '🚀' },
        {
          value: 'moderate',
          label: '3-6 months (steady progress)',
          emoji: '🎯',
        },
        {
          value: 'gradual',
          label: '6+ months (lifestyle change)',
          emoji: '🌱',
        },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'goals',
        trait: 'timeline_preference',
        weight: 1.5,
      },
    },
    {
      id: 'activity_level',
      text: 'How would you describe your current activity level?',
      type: 'single_choice',
      options: [
        {
          value: 'sedentary',
          label: 'Sedentary (desk job, minimal exercise)',
          emoji: '💺',
        },
        {
          value: 'lightly_active',
          label: 'Lightly active (light exercise 1-3 days/week)',
          emoji: '🚶',
        },
        {
          value: 'moderately_active',
          label: 'Moderately active (moderate exercise 3-5 days/week)',
          emoji: '🏃',
        },
        {
          value: 'very_active',
          label: 'Very active (hard exercise 6-7 days/week)',
          emoji: '🏋️',
        },
        {
          value: 'extremely_active',
          label: 'Extremely active (physical job + exercise)',
          emoji: '💪',
        },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'fitness_level',
        trait: 'current_activity',
        weight: 1.5,
      },
    },
    {
      id: 'time_availability',
      text: 'How much time can you realistically dedicate to health/fitness daily?',
      type: 'single_choice',
      options: [
        { value: 'under_15', label: 'Less than 15 minutes', emoji: '⏱️' },
        { value: '15_30', label: '15-30 minutes', emoji: '⏰' },
        { value: '30_60', label: '30-60 minutes', emoji: '🕐' },
        { value: '60_120', label: '1-2 hours', emoji: '🕑' },
        { value: 'over_120', label: '2+ hours', emoji: '🕒' },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'lifestyle',
        trait: 'time_availability',
        weight: 1.5,
      },
    },
    {
      id: 'motivation_factors',
      text: 'What motivates you most? (Choose up to 2)',
      type: 'multiple_choice',
      options: [
        {
          value: 'progress_data',
          label: 'Seeing progress numbers/data',
          emoji: '📊',
        },
        {
          value: 'visual_changes',
          label: 'Visual changes in photos',
          emoji: '📸',
        },
        {
          value: 'feeling_stronger',
          label: 'Feeling stronger/more energetic',
          emoji: '⚡',
        },
        {
          value: 'personal_challenges',
          label: 'Personal challenges/goals',
          emoji: '🎯',
        },
        {
          value: 'health_improvements',
          label: 'Health improvements',
          emoji: '❤️',
        },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'motivation',
        trait: 'motivation_factors',
        weight: 2.0,
      },
    },
    {
      id: 'communication_preference',
      text: 'How do you prefer to receive guidance?',
      type: 'single_choice',
      options: [
        {
          value: 'detailed',
          label: 'Step-by-step detailed instructions',
          emoji: '📋',
        },
        {
          value: 'highlights',
          label: 'Quick tips and highlights',
          emoji: '💡',
        },
        {
          value: 'encouraging',
          label: 'Encouraging and supportive tone',
          emoji: '🤗',
        },
        {
          value: 'direct',
          label: 'Direct and no-nonsense approach',
          emoji: '🎯',
        },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'communication',
        trait: 'preferred_style',
        weight: 2.0,
      },
    },
  ],
};

// NUTRITION QUESTIONNAIRE - Meal Planning Focus
export const nutritionQuestionnaire: Questionnaire = {
  id: 'nutrition',
  title: 'Nutrition Profile',
  description:
    'Essential nutrition info for personalized meal plans and recipes',
  estimatedTime: '2 minutes',
  completionMessage:
    'Awesome! I can now create personalized meal plans and recipes just for you. Ready to see what I can cook up?',
  unlocks: ['meal_plans', 'recipes', 'grocery_lists', 'nutrition_tracking'],
  questions: [
    {
      id: 'food_allergies',
      text: 'Do you have any food allergies I should know about?',
      type: 'multiple_choice',
      options: [
        { value: 'none', label: 'None', emoji: '✅' },
        { value: 'nuts', label: 'Nuts (peanuts, tree nuts)', emoji: '🥜' },
        { value: 'dairy', label: 'Dairy/Lactose', emoji: '🥛' },
        { value: 'gluten', label: 'Gluten/Wheat', emoji: '🌾' },
        { value: 'shellfish', label: 'Shellfish', emoji: '🦐' },
        { value: 'eggs', label: 'Eggs', emoji: '🥚' },
        { value: 'soy', label: 'Soy', emoji: '🫘' },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'nutrition',
        trait: 'allergies',
        weight: 2.0,
      },
    },
    {
      id: 'dietary_approach',
      text: 'Do you follow any specific diet or dietary requirements?',
      type: 'single_choice',
      options: [
        { value: 'none', label: 'No specific diet', emoji: '🍽️' },
        { value: 'vegetarian', label: 'Vegetarian', emoji: '🥬' },
        { value: 'vegan', label: 'Vegan', emoji: '🌱' },
        { value: 'pescatarian', label: 'Pescatarian', emoji: '🐟' },
        { value: 'keto', label: 'Keto/Low-carb', emoji: '🥓' },
        { value: 'paleo', label: 'Paleo', emoji: '🥩' },
        { value: 'mediterranean', label: 'Mediterranean', emoji: '🫒' },
        { value: 'kosher', label: 'Kosher', emoji: '✡️' },
        { value: 'halal', label: 'Halal', emoji: '☪️' },
        { value: 'hindu_vegetarian', label: 'Hindu Vegetarian', emoji: '🕉️' },
        {
          value: 'intermittent_fasting',
          label: 'Intermittent fasting',
          emoji: '⏰',
        },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'nutrition',
        trait: 'dietary_approach',
        weight: 2.0,
      },
    },

    {
      id: 'cooking_skill',
      text: 'How would you rate your cooking skills?',
      type: 'single_choice',
      options: [
        {
          value: 'beginner',
          label: 'Beginner (basic heating/assembly)',
          emoji: '🔰',
        },
        {
          value: 'intermediate',
          label: 'Intermediate (can follow recipes)',
          emoji: '👨‍🍳',
        },
        {
          value: 'advanced',
          label: 'Advanced (comfortable experimenting)',
          emoji: '👩‍🍳',
        },
        {
          value: 'expert',
          label: 'Expert (create my own recipes)',
          emoji: '🧑‍🍳',
        },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'nutrition',
        trait: 'cooking_skill',
        weight: 1.5,
      },
    },
    {
      id: 'meal_prep_time',
      text: 'How much time can you spend on meal prep weekly?',
      type: 'single_choice',
      options: [
        { value: 'under_30', label: 'Less than 30 minutes', emoji: '⏱️' },
        { value: '30_60', label: '30 minutes - 1 hour', emoji: '⏰' },
        { value: '60_120', label: '1-2 hours', emoji: '🕐' },
        { value: '120_240', label: '2-4 hours', emoji: '🕑' },
        { value: 'over_240', label: '4+ hours', emoji: '🕒' },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'nutrition',
        trait: 'meal_prep_time',
        weight: 1.5,
      },
    },
    {
      id: 'grocery_budget',
      text: "What's your grocery budget preference?",
      type: 'single_choice',
      options: [
        {
          value: 'budget',
          label: '($) Budget-conscious (focus on affordable ingredients)',
          emoji: '💰',
        },
        {
          value: 'moderate',
          label: '($$) Moderate (balance of cost and quality)',
          emoji: '⚖️',
        },
        {
          value: 'premium',
          label: '($$$) Premium (willing to pay for quality/organic)',
          emoji: '✨',
        },
        { value: 'unlimited', label: 'No budget constraints', emoji: '💎' },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'nutrition',
        trait: 'budget_preference',
        weight: 1.0,
      },
    },
  ],
};

// EXERCISE QUESTIONNAIRE - Workout Planning Focus
export const exerciseQuestionnaire: Questionnaire = {
  id: 'exercise',
  title: 'Exercise Profile',
  description: 'Gym-focused workout plans and exercise recommendations',
  estimatedTime: '2-3 minutes',
  completionMessage:
    "Perfect! I can now create workout plans that match your preferences and fitness level. Let's get moving!",
  unlocks: [
    'workout_plans',
    'exercise_alternatives',
    'progress_tracking',
    'form_guidance',
  ],
  questions: [
    {
      id: 'fitness_level',
      text: 'How would you describe your current fitness level?',
      type: 'single_choice',
      options: [
        {
          value: 'beginner',
          label: 'Beginner (little to no experience)',
          emoji: '🔰',
        },
        {
          value: 'intermediate',
          label: 'Intermediate (regular exercise, comfortable with basics)',
          emoji: '💪',
        },
        {
          value: 'expert',
          label: 'Expert (advanced/competitive level)',
          emoji: '🏆',
        },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'fitness',
        trait: 'current_level',
        weight: 2.0,
      },
    },
    {
      id: 'workout_frequency',
      text: 'How many days per week can you realistically exercise?',
      type: 'single_choice',
      options: [
        { value: '1_2', label: '1-2 days', emoji: '📅' },
        { value: '3_4', label: '3-4 days', emoji: '📆' },
        { value: '5_6', label: '5-6 days', emoji: '🗓️' },
        { value: '7', label: '7 days', emoji: '📋' },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'fitness',
        trait: 'workout_frequency',
        weight: 1.5,
      },
    },
    {
      id: 'workout_duration',
      text: 'How long can each workout session be?',
      type: 'single_choice',
      options: [
        {
          value: '15_30',
          label: '15-30 minutes (quick sessions)',
          emoji: '⏱️',
        },
        {
          value: '30_45',
          label: '30-45 minutes (standard sessions)',
          emoji: '🕐',
        },
        {
          value: '45_plus',
          label: '45+ minutes (longer sessions)',
          emoji: '🕒',
        },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'fitness',
        trait: 'workout_duration',
        weight: 1.5,
      },
    },
    {
      id: 'exercise_preferences',
      text: 'What types of exercise do you enjoy? (Choose up to 3)',
      type: 'multiple_choice',
      options: [
        {
          value: 'strength_training',
          label: 'Strength/Weight Training (barbells, dumbbells, machines)',
          emoji: '🏋️',
        },
        {
          value: 'cardio_machines',
          label: 'Cardio Machines (treadmill, bike, elliptical)',
          emoji: '🏃',
        },
        {
          value: 'hiit_circuit',
          label: 'HIIT/Circuit Training (using gym equipment)',
          emoji: '⚡',
        },
        {
          value: 'functional_training',
          label: 'Functional Training (kettlebells, cables, TRX)',
          emoji: '🤸',
        },
        {
          value: 'bodyweight_calisthenics',
          label: 'Bodyweight/Calisthenics (for home or travel)',
          emoji: '💪',
        },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'fitness',
        trait: 'exercise_preferences',
        weight: 2.0,
      },
    },
    {
      id: 'workout_location',
      text: 'Where do you prefer to work out?',
      type: 'multiple_choice',
      options: [
        { value: 'gym', label: 'Gym/Fitness Center (primary)', emoji: '🏋️‍♀️' },
        { value: 'home', label: 'Home (backup/travel)', emoji: '🏠' },
        {
          value: 'outdoors',
          label: 'Outdoors (running, calisthenics)',
          emoji: '🌳',
        },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'fitness',
        trait: 'workout_location',
        weight: 1.5,
      },
    },
    {
      id: 'available_equipment',
      text: 'What equipment do you have access to?',
      type: 'multiple_choice',
      options: [
        {
          value: 'bodyweight',
          label: 'No equipment (bodyweight only)',
          emoji: '🤸',
        },
        {
          value: 'basic',
          label: 'Basic (dumbbells, resistance bands)',
          emoji: '🏋️',
        },
        { value: 'home_gym', label: 'Home gym setup', emoji: '🏠' },
        { value: 'full_gym', label: 'Full commercial gym', emoji: '🏢' },
        { value: 'cardio_machines', label: 'Cardio machines', emoji: '🚴' },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'fitness',
        trait: 'available_equipment',
        weight: 1.5,
      },
    },
    {
      id: 'physical_limitations',
      text: 'Do you have any injuries or physical limitations?',
      type: 'multiple_choice',
      options: [
        { value: 'none', label: 'None', emoji: '✅' },
        { value: 'back', label: 'Back problems', emoji: '🦴' },
        { value: 'knee', label: 'Knee issues', emoji: '🦵' },
        { value: 'shoulder', label: 'Shoulder problems', emoji: '💪' },
        { value: 'wrist_elbow', label: 'Wrist/elbow issues', emoji: '🤲' },
        { value: 'ankle_foot', label: 'Ankle/foot problems', emoji: '🦶' },
        { value: 'heart', label: 'Heart condition', emoji: '❤️' },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'fitness',
        trait: 'physical_limitations',
        weight: 2.0,
      },
    },
    {
      id: 'workout_focus',
      text: "What's your primary workout focus? (Choose up to 2)",
      type: 'multiple_choice',
      options: [
        { value: 'muscle_mass', label: 'Building muscle mass', emoji: '💪' },
        { value: 'fat_loss', label: 'Losing body fat', emoji: '🔥' },
        {
          value: 'cardiovascular',
          label: 'Improving cardiovascular health',
          emoji: '❤️',
        },
        { value: 'strength', label: 'Increasing strength', emoji: '🏋️' },
        {
          value: 'flexibility',
          label: 'Improving flexibility/mobility',
          emoji: '🤸',
        },
        {
          value: 'sport_performance',
          label: 'Sport-specific performance',
          emoji: '🏆',
        },
        {
          value: 'general_fitness',
          label: 'General fitness maintenance',
          emoji: '⚖️',
        },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'fitness',
        trait: 'workout_focus',
        weight: 2.0,
      },
    },
    {
      id: 'workout_structure',
      text: 'How do you prefer your workouts structured?',
      type: 'single_choice',
      options: [
        {
          value: 'consistent',
          label: 'Same routine each time (consistency)',
          emoji: '🔄',
        },
        {
          value: 'variety',
          label: 'Variety and different workouts',
          emoji: '🎲',
        },
        {
          value: 'seasonal',
          label: 'Seasonal changes (bulk/cut cycles)',
          emoji: '📅',
        },
        { value: 'progressive', label: 'Progressive challenges', emoji: '📈' },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'fitness',
        trait: 'workout_structure',
        weight: 1.5,
      },
    },
    {
      id: 'preferred_workout_time',
      text: 'What time of day do you prefer to exercise?',
      type: 'single_choice',
      options: [
        {
          value: 'early_morning',
          label: 'Early morning (5-8 AM)',
          emoji: '🌅',
        },
        { value: 'mid_morning', label: 'Mid-morning (8-11 AM)', emoji: '☀️' },
        { value: 'lunch', label: 'Lunch time (11 AM-2 PM)', emoji: '🕐' },
        { value: 'afternoon', label: 'Afternoon (2-5 PM)', emoji: '🌤️' },
        { value: 'evening', label: 'Evening (5-8 PM)', emoji: '🌆' },
        { value: 'night', label: 'Night (8+ PM)', emoji: '🌙' },
        { value: 'flexible', label: 'Flexible/varies', emoji: '🔄' },
      ],
      validation: { required: true },
      personaMapping: {
        category: 'fitness',
        trait: 'preferred_workout_time',
        weight: 1.0,
      },
    },
  ],
};

// Export all questionnaires
export const questionnaires = {
  basic: basicQuestionnaire,
  nutrition: nutritionQuestionnaire,
  exercise: exerciseQuestionnaire,
};

export type QuestionnaireType = keyof typeof questionnaires;

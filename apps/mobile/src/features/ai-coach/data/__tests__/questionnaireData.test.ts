import {
  basicQuestionnaire,
  nutritionQuestionnaire,
  exerciseQuestionnaire,
  questionnaires,
} from '../questionnaireData';

describe('Questionnaire Data', () => {
  describe('Basic Questionnaire', () => {
    it('should have correct structure and estimated time', () => {
      expect(basicQuestionnaire.id).toBe('basic');
      expect(basicQuestionnaire.title).toBe('Basic Profile');
      expect(basicQuestionnaire.estimatedTime).toBe('1-2 minutes');
      expect(basicQuestionnaire.questions).toHaveLength(11);
    });

    it('should have unit system question with 2 options', () => {
      const unitSystemQ = basicQuestionnaire.questions.find(
        q => q.id === 'unit_system'
      );
      expect(unitSystemQ).toBeDefined();
      expect(unitSystemQ?.options).toHaveLength(2);
      expect(unitSystemQ?.options?.[0].value).toBe('metric');
      expect(unitSystemQ?.options?.[1].value).toBe('imperial');
    });

    it('should have biological sex question with 2 options', () => {
      const bioSexQ = basicQuestionnaire.questions.find(
        q => q.id === 'biological_sex'
      );
      expect(bioSexQ).toBeDefined();
      expect(bioSexQ?.options).toHaveLength(2);
      expect(bioSexQ?.options?.map(o => o.value)).toEqual(['male', 'female']);
    });

    it('should have date of birth question instead of age', () => {
      const dobQ = basicQuestionnaire.questions.find(
        q => q.id === 'date_of_birth'
      );
      const ageQ = basicQuestionnaire.questions.find(q => q.id === 'age');

      expect(dobQ).toBeDefined();
      expect(ageQ).toBeUndefined();
      expect(dobQ?.type).toBe('text_input');
    });

    it('should have primary goal with 3 simplified options', () => {
      const goalQ = basicQuestionnaire.questions.find(
        q => q.id === 'primary_goal'
      );
      expect(goalQ).toBeDefined();
      expect(goalQ?.options).toHaveLength(3);
      expect(goalQ?.options?.map(o => o.value)).toEqual([
        'lose_weight',
        'gain_muscle',
        'gain_strength',
      ]);
    });

    it('should have timeline with 3 options', () => {
      const timelineQ = basicQuestionnaire.questions.find(
        q => q.id === 'timeline'
      );
      expect(timelineQ).toBeDefined();
      expect(timelineQ?.options).toHaveLength(3);
      expect(timelineQ?.options?.map(o => o.value)).toEqual([
        'fast',
        'moderate',
        'gradual',
      ]);
    });

    it('should have motivation factors with 5 options', () => {
      const motivationQ = basicQuestionnaire.questions.find(
        q => q.id === 'motivation_factors'
      );
      expect(motivationQ).toBeDefined();
      expect(motivationQ?.options).toHaveLength(5);
      expect(motivationQ?.type).toBe('multiple_choice');
    });

    it('should have all required questions marked as required', () => {
      const requiredQuestions = basicQuestionnaire.questions.filter(
        q => q.validation?.required
      );
      expect(requiredQuestions).toHaveLength(11); // All questions should be required
    });
  });

  describe('Nutrition Questionnaire', () => {
    it('should have correct structure and estimated time', () => {
      expect(nutritionQuestionnaire.id).toBe('nutrition');
      expect(nutritionQuestionnaire.title).toBe('Nutrition Profile');
      expect(nutritionQuestionnaire.estimatedTime).toBe('2 minutes');
      expect(nutritionQuestionnaire.questions).toHaveLength(5);
    });

    it('should have food allergies question', () => {
      const allergiesQ = nutritionQuestionnaire.questions.find(
        q => q.id === 'food_allergies'
      );
      expect(allergiesQ).toBeDefined();
      expect(allergiesQ?.type).toBe('multiple_choice');
      expect(allergiesQ?.options?.length).toBeGreaterThan(5);
    });

    it('should have dietary approach with religious options', () => {
      const dietaryQ = nutritionQuestionnaire.questions.find(
        q => q.id === 'dietary_approach'
      );
      expect(dietaryQ).toBeDefined();

      const values = dietaryQ?.options?.map(o => o.value) || [];
      expect(values).toContain('kosher');
      expect(values).toContain('halal');
      expect(values).toContain('hindu_vegetarian');
      expect(values).toContain('vegetarian');
      expect(values).toContain('vegan');
    });

    it('should have budget question with dollar signs', () => {
      const budgetQ = nutritionQuestionnaire.questions.find(
        q => q.id === 'grocery_budget'
      );
      expect(budgetQ).toBeDefined();

      const labels = budgetQ?.options?.map(o => o.label) || [];
      expect(labels.some(label => label.includes('($)'))).toBe(true);
      expect(labels.some(label => label.includes('($$)'))).toBe(true);
      expect(labels.some(label => label.includes('($$$)'))).toBe(true);
    });

    it('should NOT have food dislikes or favorite cuisines questions', () => {
      const dislikesQ = nutritionQuestionnaire.questions.find(
        q => q.id === 'food_dislikes'
      );
      const cuisinesQ = nutritionQuestionnaire.questions.find(
        q => q.id === 'favorite_cuisines'
      );

      expect(dislikesQ).toBeUndefined();
      expect(cuisinesQ).toBeUndefined();
    });

    it('should have cooking skill and meal prep time questions', () => {
      const cookingQ = nutritionQuestionnaire.questions.find(
        q => q.id === 'cooking_skill'
      );
      const prepTimeQ = nutritionQuestionnaire.questions.find(
        q => q.id === 'meal_prep_time'
      );

      expect(cookingQ).toBeDefined();
      expect(prepTimeQ).toBeDefined();
      expect(cookingQ?.options).toHaveLength(4);
      expect(prepTimeQ?.options).toHaveLength(5);
    });
  });

  describe('Exercise Questionnaire', () => {
    it('should have correct structure and estimated time', () => {
      expect(exerciseQuestionnaire.id).toBe('exercise');
      expect(exerciseQuestionnaire.title).toBe('Exercise Profile');
      expect(exerciseQuestionnaire.estimatedTime).toBe('2-3 minutes');
      expect(exerciseQuestionnaire.questions).toHaveLength(10);
    });

    it('should have fitness level with 3 simplified options', () => {
      const fitnessQ = exerciseQuestionnaire.questions.find(
        q => q.id === 'fitness_level'
      );
      expect(fitnessQ).toBeDefined();
      expect(fitnessQ?.options).toHaveLength(3);
      expect(fitnessQ?.options?.map(o => o.value)).toEqual([
        'beginner',
        'intermediate',
        'expert',
      ]);
    });

    it('should have workout duration with 3 options', () => {
      const durationQ = exerciseQuestionnaire.questions.find(
        q => q.id === 'workout_duration'
      );
      expect(durationQ).toBeDefined();
      expect(durationQ?.options).toHaveLength(3);
      expect(durationQ?.options?.map(o => o.value)).toEqual([
        '15_30',
        '30_45',
        '45_plus',
      ]);
    });

    it('should have gym-focused exercise preferences', () => {
      const exerciseQ = exerciseQuestionnaire.questions.find(
        q => q.id === 'exercise_preferences'
      );
      expect(exerciseQ).toBeDefined();
      expect(exerciseQ?.options).toHaveLength(5);

      const values = exerciseQ?.options?.map(o => o.value) || [];
      expect(values).toContain('strength_training');
      expect(values).toContain('cardio_machines');
      expect(values).toContain('hiit_circuit');
      expect(values).toContain('functional_training');
      expect(values).toContain('bodyweight_calisthenics');

      // Should NOT contain old options like 'dancing', 'swimming', etc.
      expect(values).not.toContain('dancing');
      expect(values).not.toContain('swimming');
      expect(values).not.toContain('sports');
    });

    it('should have gym-focused workout locations', () => {
      const locationQ = exerciseQuestionnaire.questions.find(
        q => q.id === 'workout_location'
      );
      expect(locationQ).toBeDefined();
      expect(locationQ?.options).toHaveLength(3);

      const values = locationQ?.options?.map(o => o.value) || [];
      expect(values).toContain('gym');
      expect(values).toContain('home');
      expect(values).toContain('outdoors');

      // Should NOT contain 'studios'
      expect(values).not.toContain('studios');
    });

    it('should have all essential exercise questions', () => {
      const questionIds = exerciseQuestionnaire.questions.map(q => q.id);

      const expectedQuestions = [
        'fitness_level',
        'workout_frequency',
        'workout_duration',
        'exercise_preferences',
        'workout_location',
        'available_equipment',
        'physical_limitations',
        'workout_focus',
        'workout_structure',
        'preferred_workout_time',
      ];

      expectedQuestions.forEach(id => {
        expect(questionIds).toContain(id);
      });
    });
  });

  describe('Questionnaires Export', () => {
    it('should export all three questionnaires', () => {
      expect(questionnaires.basic).toBe(basicQuestionnaire);
      expect(questionnaires.nutrition).toBe(nutritionQuestionnaire);
      expect(questionnaires.exercise).toBe(exerciseQuestionnaire);
    });

    it('should have consistent structure across all questionnaires', () => {
      Object.values(questionnaires).forEach(questionnaire => {
        expect(questionnaire).toHaveProperty('id');
        expect(questionnaire).toHaveProperty('title');
        expect(questionnaire).toHaveProperty('description');
        expect(questionnaire).toHaveProperty('estimatedTime');
        expect(questionnaire).toHaveProperty('questions');
        expect(questionnaire).toHaveProperty('completionMessage');
        expect(questionnaire).toHaveProperty('unlocks');

        expect(Array.isArray(questionnaire.questions)).toBe(true);
        expect(Array.isArray(questionnaire.unlocks)).toBe(true);
      });
    });

    it('should have valid question structures', () => {
      Object.values(questionnaires).forEach(questionnaire => {
        questionnaire.questions.forEach(question => {
          expect(question).toHaveProperty('id');
          expect(question).toHaveProperty('text');
          expect(question).toHaveProperty('type');
          expect(question).toHaveProperty('personaMapping');

          if (
            question.type === 'single_choice' ||
            question.type === 'multiple_choice'
          ) {
            expect(question).toHaveProperty('options');
            expect(Array.isArray(question.options)).toBe(true);
            expect(question.options!.length).toBeGreaterThan(0);
          }
        });
      });
    });
  });
});

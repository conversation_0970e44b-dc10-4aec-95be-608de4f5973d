import { render, fireEvent } from '@testing-library/react-native';
import React from 'react';
import { QuestionnaireMessage } from '../QuestionnaireMessage';

jest.mock('../../../../lib/supabase', () => ({
  supabase: {
    auth: { getUser: jest.fn().mockResolvedValue({ data: { user: null } }) },
    from: jest.fn(),
  },
}));

describe('QuestionnaireMessage height/weight inline inputs', () => {
  test('height_cm: ft/in Continue converts to cm', async () => {
    const onOptionSelect = jest.fn();
    const { getByText, getByPlaceholderText } = render(
      <QuestionnaireMessage
        message={'Question ID: height_cm'}
        options={[]}
        onOptionSelect={onOptionSelect}
        onCustomAnswer={() => {}}
        questionId="height_cm"
      />
    );

    // Switch to imperial
    fireEvent.press(getByText('ft/in'));

    // Enter feet and inches
    fireEvent.changeText(getByPlaceholderText('Feet'), '5');
    fireEvent.changeText(getByPlaceholderText('Inches'), '10');

    // Continue
    fireEvent.press(getByText('Continue'));

    expect(onOptionSelect).toHaveBeenCalled();
    const arg = (onOptionSelect as jest.Mock).mock.calls[0][0];
    expect(arg.value).toMatch(/^\d+$/);
    expect(parseInt(arg.value, 10)).toBeGreaterThanOrEqual(176);
    expect(parseInt(arg.value, 10)).toBeLessThanOrEqual(179);
    expect(arg.label).toContain('ft');
  });

  test('weight_kg: lb Continue converts to kg', async () => {
    const onOptionSelect = jest.fn();
    const { getByText, getByPlaceholderText } = render(
      <QuestionnaireMessage
        message={'Question ID: weight_kg'}
        options={[]}
        onOptionSelect={onOptionSelect}
        onCustomAnswer={() => {}}
        questionId="weight_kg"
      />
    );

    // Switch to imperial
    fireEvent.press(getByText('lb'));

    // Enter pounds
    fireEvent.changeText(getByPlaceholderText('Enter weight (lb)'), '170');

    // Continue
    fireEvent.press(getByText('Continue'));

    expect(onOptionSelect).toHaveBeenCalled();
    const arg = (onOptionSelect as jest.Mock).mock.calls[0][0];
    expect(parseFloat(arg.value)).toBeCloseTo(77.1, 1);
    expect(arg.label).toContain('lb');
  });
});

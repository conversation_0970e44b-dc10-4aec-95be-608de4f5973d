import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { supabase } from '../../../lib/supabase';
import {
  defaultUnitSystemFromLocale,
  ftInToCm,
  cmToFtIn,
  parseHeightToCm,
  parseWeightToKg,
  kgToLb,
} from '../../../shared/utils/measurements';

interface QuestionnaireOption {
  value: string;
  label: string;
  emoji?: string;
}

interface QuestionnaireMessageProps {
  message: string;
  options?: QuestionnaireOption[];
  progress?: {
    current: number;
    total: number;
    percentage: number;
  };
  onOptionSelect: (option: QuestionnaireOption) => void;
  onCustomAnswer: () => void;
  questionId?: string;
  questionnaireType?: string; // Reserved for future questionnaire-specific styling/behavior
  isMultipleChoice?: boolean;
  selectedOptions?: string[];
}

export const QuestionnaireMessage: React.FC<QuestionnaireMessageProps> = ({
  message,
  options,
  progress,
  onOptionSelect,
  onCustomAnswer,
  isMultipleChoice = false,
  questionId,
  _questionnaireType, // Reserved for future questionnaire-specific styling/behavior
  selectedOptions = [],
}) => {
  const parseMessage = (text: string) => {
    // Extract session ID and question ID from message
    const sessionIdMatch = text.match(/Session ID: ([a-f0-9-]+)/);
    const questionIdMatch = text.match(/Question ID: ([a-zA-Z_]+)/);

    // Clean message by removing technical details
    const cleanMessage = text
      .replace(/Session ID: [a-f0-9-]+/g, '')
      .replace(/Question ID: [a-zA-Z_]+/g, '')
      .replace(/\n\n+/g, '\n\n')
      .trim();

    return {
      message: cleanMessage,
      sessionId: sessionIdMatch?.[1],
      questionId: questionIdMatch?.[1],
    };
  };

  const { message: cleanMessage } = parseMessage(message);

  // Inline unit & input state for height/weight questions
  const [unitSystem, setUnitSystem] = useState<'metric' | 'imperial'>(
    defaultUnitSystemFromLocale()
  );
  const [heightInput, setHeightInput] = useState('');
  const [weightInput, setWeightInput] = useState('');
  const [inputError, setInputError] = useState<string | null>(null);

  useEffect(() => {
    // Try load user preference to override locale default
    const loadUnits = async () => {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) return;
        const { data, error } = await supabase
          .from('profiles')
          .select('unit_system,height_cm,weight_kg')
          .eq('id', user.id)
          .single();
        if (!error && data) {
          if (
            data.unit_system === 'imperial' ||
            data.unit_system === 'metric'
          ) {
            setUnitSystem(data.unit_system);
            // Prefill inputs from profile if available
            if (questionId === 'height_cm' && data.height_cm) {
              if (data.unit_system === 'imperial') {
                const { ft, in: inches } = cmToFtIn(data.height_cm);
                setHeightInput(`${ft} ${inches}`);
              } else {
                setHeightInput(String(data.height_cm));
              }
            }
            if (questionId === 'weight_kg' && data.weight_kg) {
              setWeightInput(
                data.unit_system === 'imperial'
                  ? String(kgToLb(data.weight_kg))
                  : String(data.weight_kg)
              );
            }
          }
        }
      } catch (_e) {
        // ignore load failure and keep locale default
      }
    };

    if (questionId === 'height_cm' || questionId === 'weight_kg') {
      loadUnits();
    }
  }, [questionId]);

  const handleOptionPress = (option: QuestionnaireOption) => {
    if (isMultipleChoice) {
      // Handle multiple choice selection
      // This would need to be handled by parent component
      onOptionSelect(option);
    } else {
      // Single choice - select immediately
      onOptionSelect(option);
    }
  };

  const renderProgressBar = () => {
    if (!progress) {
      return null;
    }

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressText}>
            Question {progress.current} of {progress.total}
          </Text>
          <Text style={styles.progressPercentage}>{progress.percentage}%</Text>
        </View>
        <View style={styles.progressBarBackground}>
          <View
            style={[
              styles.progressBarFill,
              { width: `${progress.percentage}%` },
            ]}
          />
        </View>
      </View>
    );
  };

  const renderOptions = () => {
    // If no options, show text input for number/text input questions
    if (!options || options.length === 0) {
      // Height/Weight inline controls
      if (questionId === 'height_cm' || questionId === 'weight_kg') {
        return (
          <View style={styles.optionsContainer}>
            {/* Inline unit toggles */}
            <View style={{ flexDirection: 'row', gap: 8, marginBottom: 8 }}>
              <TouchableOpacity
                style={[
                  styles.optionButton,
                  unitSystem === 'metric' && styles.optionButtonSelected,
                ]}
                onPress={() => setUnitSystem('metric')}
              >
                <Text
                  style={[
                    styles.optionText,
                    unitSystem === 'metric' && styles.optionTextSelected,
                  ]}
                >
                  {questionId === 'height_cm' ? 'cm' : 'kg'}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.optionButton,
                  unitSystem === 'imperial' && styles.optionButtonSelected,
                ]}
                onPress={() => setUnitSystem('imperial')}
              >
                <Text
                  style={[
                    styles.optionText,
                    unitSystem === 'imperial' && styles.optionTextSelected,
                  ]}
                >
                  {questionId === 'height_cm' ? 'ft/in' : 'lb'}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Inputs */}
            {questionId === 'height_cm' ? (
              unitSystem === 'metric' ? (
                <TextInput
                  placeholder="Enter height (cm)"
                  keyboardType="numeric"
                  value={heightInput}
                  onChangeText={setHeightInput}
                  style={{
                    backgroundColor: '#fff',
                    padding: 10,
                    borderRadius: 12,
                    borderWidth: 1,
                    borderColor: '#E0E0E0',
                  }}
                />
              ) : (
                <View style={{ flexDirection: 'row', gap: 8 }}>
                  <TextInput
                    placeholder="Feet"
                    keyboardType="numeric"
                    value={heightInput.split(' ')[0] || ''}
                    onChangeText={t =>
                      setHeightInput(`${t} ${heightInput.split(' ')[1] || ''}`)
                    }
                    style={{
                      flex: 1,
                      backgroundColor: '#fff',
                      padding: 10,
                      borderRadius: 12,
                      borderWidth: 1,
                      borderColor: '#E0E0E0',
                    }}
                  />
                  <TextInput
                    placeholder="Inches"
                    keyboardType="numeric"
                    value={heightInput.split(' ')[1] || ''}
                    onChangeText={t =>
                      setHeightInput(`${heightInput.split(' ')[0] || ''} ${t}`)
                    }
                    style={{
                      flex: 1,
                      backgroundColor: '#fff',
                      padding: 10,
                      borderRadius: 12,
                      borderWidth: 1,
                      borderColor: '#E0E0E0',
                    }}
                  />
                </View>
              )
            ) : unitSystem === 'metric' ? (
              <TextInput
                placeholder="Enter weight (kg)"
                keyboardType="numeric"
                value={weightInput}
                onChangeText={setWeightInput}
                style={{
                  backgroundColor: '#fff',
                  padding: 10,
                  borderRadius: 12,
                  borderWidth: 1,
                  borderColor: '#E0E0E0',
                }}
              />
            ) : (
              <TextInput
                placeholder="Enter weight (lb)"
                keyboardType="numeric"
                value={weightInput}
                onChangeText={setWeightInput}
                style={{
                  backgroundColor: '#fff',
                  padding: 10,
                  borderRadius: 12,
                  borderWidth: 1,
                  borderColor: '#E0E0E0',
                }}
              />
            )}

            {inputError ? (
              <Text style={{ color: '#B00020', marginTop: 6 }}>
                {inputError}
              </Text>
            ) : null}

            {/* Continue with conversion */}
            <TouchableOpacity
              style={[styles.confirmButton, { marginTop: 10 }]}
              onPress={() => {
                try {
                  setInputError(null);
                  let value: number | null = null;
                  let display = '';
                  if (questionId === 'height_cm') {
                    if (unitSystem === 'metric') {
                      value = parseHeightToCm(heightInput || '', 'metric');
                      display = `${heightInput} cm`;
                    } else {
                      const [ftStr = '0', inStr = '0'] = (
                        heightInput || ''
                      ).split(' ');
                      const ft = parseInt(ftStr || '0', 10) || 0;
                      const inches = parseInt(inStr || '0', 10) || 0;
                      value = ftInToCm(ft, inches);
                      display = `${ft} ft ${inches} in`;
                    }
                  } else {
                    // weight
                    if (unitSystem === 'metric') {
                      value = parseWeightToKg(weightInput || '', 'metric');
                      display = `${weightInput} kg`;
                    } else {
                      value = parseWeightToKg(weightInput || '', 'imperial');
                      const lbs = weightInput || '';
                      display = `${lbs} lb`;
                    }
                  }
                  if (!value || value <= 0) {
                    setInputError('Please enter a valid value');
                    return;
                  }

                  // Submit via onOptionSelect with synthetic option
                  onOptionSelect({
                    value: String(value),
                    label: display,
                    emoji: undefined,
                  });
                } catch (_e) {
                  setInputError('Please enter a valid value');
                }
              }}
              activeOpacity={0.8}
            >
              <Text style={styles.confirmButtonText}>Continue</Text>
            </TouchableOpacity>

            {/* Or type instead */}
            <TouchableOpacity
              style={[styles.customAnswerButton, { marginTop: 10 }]}
              onPress={() => onCustomAnswer()}
              activeOpacity={0.7}
            >
              <Text style={styles.customAnswerText}>
                ✏️ Or type your answer instead
              </Text>
            </TouchableOpacity>
            <Text style={{ marginTop: 8, opacity: 0.8 }}>
              Tip: Try entries like "5 ft 10 in", "178 cm", "170 lb" or "77 kg".
            </Text>
          </View>
        );
      }
      return (
        <View style={styles.optionsContainer}>
          <TouchableOpacity
            style={styles.customAnswerButton}
            onPress={onCustomAnswer}
            activeOpacity={0.7}
          >
            <Text style={styles.customAnswerText}>✏️ Type your answer</Text>
          </TouchableOpacity>
        </View>
      );
    }

    // Simple vertical layout for all options - no more complex horizontal logic
    return (
      <View style={styles.optionsContainer}>
        {options.map((option, index) => {
          const isSelected = selectedOptions.includes(option.value);

          return (
            <TouchableOpacity
              key={`${option.value}-${index}`}
              style={[
                styles.optionButton,
                isSelected && styles.optionButtonSelected,
              ]}
              onPress={() => handleOptionPress(option)}
              activeOpacity={0.7}
            >
              <Text style={styles.optionEmoji}>{option.emoji}</Text>
              <Text
                style={[
                  styles.optionText,
                  isSelected && styles.optionTextSelected,
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          );
        })}

        {/* Custom answer option */}
        <TouchableOpacity
          style={styles.customAnswerButton}
          onPress={onCustomAnswer}
          activeOpacity={0.7}
        >
          <Text style={styles.customAnswerText}>✏️ Type custom answer</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.bubble}>
        {renderProgressBar()}

        <Text style={styles.messageText}>{cleanMessage}</Text>

        {renderOptions()}

        {isMultipleChoice && selectedOptions.length > 0 && (
          <TouchableOpacity
            style={styles.confirmButton}
            onPress={() => {
              // This would trigger submission of selected options
              // Implementation would depend on parent component
            }}
            activeOpacity={0.7}
          >
            <Text style={styles.confirmButtonText}>
              Continue with {selectedOptions.length} selected
            </Text>
          </TouchableOpacity>
        )}

        <Text style={styles.timestamp}>
          {new Date().toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          })}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
    paddingHorizontal: 16,
    alignItems: 'flex-start',
  },
  bubble: {
    maxWidth: '95%',
    backgroundColor: '#E8F4FD',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    borderBottomLeftRadius: 4,
    borderWidth: 1,
    borderColor: '#007AFF',
    alignSelf: 'flex-start', // Prevent expansion
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  progressText: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '600',
  },
  progressPercentage: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '600',
  },
  progressBarBackground: {
    height: 4,
    backgroundColor: '#D0E8FF',
    borderRadius: 2,
  },
  progressBarFill: {
    height: 4,
    backgroundColor: '#007AFF',
    borderRadius: 2,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
    color: '#000000',
    marginBottom: 12,
  },
  optionsContainer: {
    marginTop: 8,
  },
  optionButton: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 25,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  optionButtonSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  optionEmoji: {
    fontSize: 20,
    marginRight: 8,
  },
  optionText: {
    fontSize: 16,
    color: '#000000',
    fontWeight: '500',
  },
  optionTextSelected: {
    color: '#FFFFFF',
  },
  customAnswerButton: {
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    alignItems: 'center',
    marginTop: 4,
  },
  customAnswerText: {
    fontSize: 14,
    color: '#666666',
    fontStyle: 'italic',
  },
  confirmButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    alignItems: 'center',
    marginTop: 12,
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  timestamp: {
    fontSize: 12,
    color: '#666666',
    opacity: 0.7,
    marginTop: 8,
    textAlign: 'left',
  },
});

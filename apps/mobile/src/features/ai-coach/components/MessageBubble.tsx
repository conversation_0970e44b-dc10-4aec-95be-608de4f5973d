import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { ChatMessage } from '../types';
import {
  parseQuestionnaireMessage,
  createQuestionnaireResponse,
} from '../utils/questionnaireUtils';
import { QuestionnaireMessage } from './QuestionnaireMessage';
import { ToolExecutionIndicator } from './ToolExecutionIndicator';

interface MessageBubbleProps {
  message: ChatMessage;
  onQuestionnaireResponse?: (response: string) => void;
}

export const MessageBubble: React.FC<MessageBubbleProps> = React.memo(
  ({ message, onQuestionnaireResponse }) => {
    const isUser = message.role === 'user';

    // Check if this is a questionnaire message from the AI
    const questionnaireData = !isUser
      ? parseQuestionnaireMessage(message.content)
      : null;

    const handleOptionSelect = (option: { value: string; label: string }) => {
      if (questionnaireData && onQuestionnaireResponse) {
        const response = createQuestionnaireResponse(
          questionnaireData.questionId!,
          questionnaireData.questionnaireType!,
          questionnaireData.sessionId!,
          option
        );
        onQuestionnaireResponse(response);
      }
    };

    const handleCustomAnswer = () => {
      // This would trigger the text input for custom answers
      // For now, we'll just show a placeholder
      if (onQuestionnaireResponse) {
        onQuestionnaireResponse('custom_answer_mode');
      }
    };

    // Render questionnaire message if detected
    if (questionnaireData?.isQuestionnaire) {
      return (
        <QuestionnaireMessage
          message={questionnaireData.cleanMessage}
          options={questionnaireData.options}
          progress={questionnaireData.progress}
          onOptionSelect={handleOptionSelect}
          onCustomAnswer={handleCustomAnswer}
          isMultipleChoice={questionnaireData.isMultipleChoice}
          questionId={questionnaireData.questionId}
          questionnaireType={questionnaireData.questionnaireType}
        />
      );
    }

    // Render regular message
    return (
      <View
        style={[
          styles.container,
          isUser ? styles.userContainer : styles.assistantContainer,
        ]}
      >
        <View
          style={[
            styles.bubble,
            isUser ? styles.userBubble : styles.assistantBubble,
          ]}
        >
          <Text
            style={[
              styles.text,
              isUser ? styles.userText : styles.assistantText,
            ]}
          >
            {message.content}
          </Text>

          {message.toolCalls && message.toolCalls.length > 0 && (
            <View style={styles.toolCallsContainer}>
              {message.toolCalls.map(toolCall => (
                <ToolExecutionIndicator key={toolCall.id} toolCall={toolCall} />
              ))}
            </View>
          )}

          <Text
            style={[
              styles.timestamp,
              isUser ? styles.userTimestamp : styles.assistantTimestamp,
            ]}
          >
            {message.timestamp.toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </Text>
        </View>
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
    paddingHorizontal: 16,
  },
  userContainer: {
    alignItems: 'flex-end',
  },
  assistantContainer: {
    alignItems: 'flex-start',
  },
  bubble: {
    maxWidth: '80%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
  },
  userBubble: {
    backgroundColor: '#007AFF',
    borderBottomRightRadius: 4,
  },
  assistantBubble: {
    backgroundColor: '#F0F0F0',
    borderBottomLeftRadius: 4,
  },
  text: {
    fontSize: 16,
    lineHeight: 22,
  },
  userText: {
    color: '#FFFFFF',
  },
  assistantText: {
    color: '#000000',
  },
  toolCallsContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  timestamp: {
    fontSize: 12,
    marginTop: 4,
    opacity: 0.7,
  },
  userTimestamp: {
    color: '#FFFFFF',
    textAlign: 'right',
  },
  assistantTimestamp: {
    color: '#666666',
    textAlign: 'left',
  },
});

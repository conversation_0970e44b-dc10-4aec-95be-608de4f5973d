import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { ToolCall } from '../types';

interface ToolExecutionIndicatorProps {
  toolCall: ToolCall;
}

export const ToolExecutionIndicator: React.FC<ToolExecutionIndicatorProps> = ({
  toolCall,
}) => {
  const getStatusIcon = () => {
    switch (toolCall.status) {
      case 'pending':
        return <Text>⏳</Text>;
      case 'executing':
        return <ActivityIndicator size="small" color="#007AFF" />;
      case 'completed':
        return <Text>✅</Text>;
      case 'failed':
        return <Text>❌</Text>;
      default:
        return <Text>⏳</Text>;
    }
  };

  const getStatusText = () => {
    switch (toolCall.status) {
      case 'pending':
        return 'Preparing...';
      case 'executing':
        return 'Executing...';
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      default:
        return 'Unknown';
    }
  };

  const getToolDisplayName = () => {
    const toolNames: Record<string, string> = {
      generate_meal_plan: '🍽️ Meal Planner',
      generate_workout_plan: '💪 Workout Planner',
      analyze_progress: '📊 Progress Analyzer',
      log_meal: '🍽️ Meal Logger',
      log_workout: '🏋️ Workout Logger',
      generate_grocery_list: '🛒 Grocery List Generator',
      find_exercise_alternative: '🔄 Exercise Alternative Finder',
      start_questionnaire: '📋 Starting Questionnaire',
      save_questionnaire_answer: '💾 Saving Answer',
      complete_questionnaire: '✅ Completing Questionnaire',
      get_questionnaire_status: '📊 Checking Status',
    };

    return toolNames[toolCall.name] || toolCall.name;
  };

  const renderResult = () => {
    if (toolCall.status !== 'completed' || !toolCall.result) {
      return null;
    }

    const { result } = toolCall;

    if (!result.success) {
      return <Text style={styles.errorText}>Error: {result.error}</Text>;
    }

    // Render different result types
    switch (toolCall.name) {
      case 'generate_meal_plan':
        return (
          <View style={styles.resultContainer}>
            <Text style={styles.resultTitle}>Meal Plan Created!</Text>
            <Text style={styles.resultText}>
              {result.data.days} days • {result.data.totalCalories} total
              calories
            </Text>
            <Text style={styles.resultText}>
              Goal: {result.data.goal} • {result.data.calorieTarget} cal/day
            </Text>
          </View>
        );

      case 'generate_workout_plan':
        return (
          <View style={styles.resultContainer}>
            <Text style={styles.resultTitle}>Workout Plan Ready!</Text>
            <Text style={styles.resultText}>
              {result.data.days} days • {result.data.type} training
            </Text>
            <Text style={styles.resultText}>
              {result.data.duration} min/session • {result.data.experience}{' '}
              level
            </Text>
          </View>
        );

      case 'analyze_progress':
        return (
          <View style={styles.resultContainer}>
            <Text style={styles.resultTitle}>Progress Analysis</Text>
            <Text style={styles.resultText}>
              Weight change: {result.data.weightChange > 0 ? '+' : ''}
              {result.data.weightChange} kg
            </Text>
            <Text style={styles.resultText}>
              Consistency: {result.data.consistency}% • Trend:{' '}
              {result.data.weightTrend}
            </Text>
          </View>
        );

      case 'generate_grocery_list':
        return (
          <View style={styles.resultContainer}>
            <Text style={styles.resultTitle}>Grocery List Generated!</Text>
            <Text style={styles.resultText}>
              {result.data.totalItems} items • Est. ${result.data.estimatedCost}
            </Text>
          </View>
        );

      case 'find_exercise_alternative':
        return (
          <View style={styles.resultContainer}>
            <Text style={styles.resultTitle}>Alternatives Found!</Text>
            <Text style={styles.resultText}>
              {result.data.alternatives.length} alternatives for{' '}
              {result.data.originalExercise}
            </Text>
          </View>
        );

      case 'log_meal':
      case 'log_workout':
        return (
          <View style={styles.resultContainer}>
            <Text style={styles.resultTitle}>Logged Successfully!</Text>
            <Text style={styles.resultText}>{result.data.message}</Text>
          </View>
        );

      case 'start_questionnaire':
      case 'save_questionnaire_answer':
      case 'complete_questionnaire':
        return (
          <View style={styles.resultContainer}>
            <Text style={styles.resultTitle}>Questionnaire Updated</Text>
            <Text style={styles.resultText}>
              {typeof result === 'string' ? result : result.data || 'Success'}
            </Text>
          </View>
        );

      default:
        return (
          <View style={styles.resultContainer}>
            <Text style={styles.resultTitle}>Task Completed</Text>
          </View>
        );
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.iconContainer}>{getStatusIcon()}</View>
        <View style={styles.textContainer}>
          <Text style={styles.toolName}>{getToolDisplayName()}</Text>
          <Text style={styles.statusText}>{getStatusText()}</Text>
        </View>
      </View>

      {toolCall.error && (
        <Text style={styles.errorText}>Error: {toolCall.error}</Text>
      )}

      {renderResult()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 8,
    marginTop: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  textContainer: {
    flex: 1,
  },
  toolName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  statusText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  resultContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  resultTitle: {
    fontSize: 13,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 2,
  },
  resultText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 16,
  },
  errorText: {
    fontSize: 12,
    color: '#FF6B6B',
    marginTop: 4,
  },
});

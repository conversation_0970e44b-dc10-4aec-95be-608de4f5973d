import React, { useState } from 'react';
import { View, ScrollView, Alert, StyleSheet } from 'react-native';
import { <PERSON>ton, Card, Text, Chip } from 'react-native-paper';
import { aiChatService } from '../services/aiChatService';

// Demo component to test the enhanced meal planning tools
export const MealPlanningDemo: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any>(null);

  const testMealPlanning = async () => {
    setIsLoading(true);
    setResults(null);

    try {
      // Test the enhanced meal planner
      const response = await aiChatService.sendMessage(
        "Create a weekly meal plan for me. I'm vegetarian, want high-protein meals, and prefer quick recipes under 30 minutes.",
        []
      );

      console.log('AI Response:', response);

      // Execute any tool calls
      if (response.toolCalls && response.toolCalls.length > 0) {
        const toolCall = response.toolCalls[0];
        console.log('Executing tool:', toolCall.name, toolCall.parameters);

        const toolResult = await aiChatService.executeToolCall(toolCall);
        console.log('Tool result:', toolResult);

        setResults({
          aiResponse: response.content,
          toolCall: toolCall,
          toolResult: toolResult,
        });
      } else {
        setResults({
          aiResponse: response.content,
          toolCall: null,
          toolResult: null,
        });
      }
    } catch (error) {
      console.error('Test error:', error);
      Alert.alert('Error', 'Failed to test meal planning');
    } finally {
      setIsLoading(false);
    }
  };

  const testRecipeFinder = async () => {
    setIsLoading(true);
    setResults(null);

    try {
      const response = await aiChatService.sendMessage(
        'Find me some quick vegetarian dinner recipes with high protein',
        []
      );

      console.log('Recipe finder response:', response);

      if (response.toolCalls && response.toolCalls.length > 0) {
        const toolCall = response.toolCalls[0];
        const toolResult = await aiChatService.executeToolCall(toolCall);

        setResults({
          aiResponse: response.content,
          toolCall: toolCall,
          toolResult: toolResult,
        });
      } else {
        setResults({
          aiResponse: response.content,
          toolCall: null,
          toolResult: null,
        });
      }
    } catch (error) {
      console.error('Recipe finder error:', error);
      Alert.alert('Error', 'Failed to test recipe finder');
    } finally {
      setIsLoading(false);
    }
  };

  const testGroceryList = async () => {
    setIsLoading(true);
    setResults(null);

    try {
      const response = await aiChatService.sendMessage(
        'Generate a grocery list for this week, organized by category',
        []
      );

      console.log('Grocery list response:', response);

      if (response.toolCalls && response.toolCalls.length > 0) {
        const toolCall = response.toolCalls[0];
        const toolResult = await aiChatService.executeToolCall(toolCall);

        setResults({
          aiResponse: response.content,
          toolCall: toolCall,
          toolResult: toolResult,
        });
      } else {
        setResults({
          aiResponse: response.content,
          toolCall: null,
          toolResult: null,
        });
      }
    } catch (error) {
      console.error('Grocery list error:', error);
      Alert.alert('Error', 'Failed to test grocery list');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text variant="headlineMedium" style={styles.title}>
        AI Meal Planning Demo
      </Text>

      <Text variant="bodyMedium" style={styles.subtitle}>
        Test the enhanced AI meal planning tools with real database integration
      </Text>

      <View style={styles.buttonContainer}>
        <Button
          mode="contained"
          onPress={testMealPlanning}
          loading={isLoading}
          disabled={isLoading}
        >
          Test Enhanced Meal Planner
        </Button>

        <Button
          mode="contained"
          onPress={testRecipeFinder}
          loading={isLoading}
          disabled={isLoading}
        >
          Test Smart Recipe Finder
        </Button>

        <Button
          mode="contained"
          onPress={testGroceryList}
          loading={isLoading}
          disabled={isLoading}
        >
          Test Grocery List Generator
        </Button>
      </View>

      {results && (
        <Card style={styles.resultCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              AI Response
            </Text>
            <Text variant="bodyMedium" style={styles.responseText}>
              {results.aiResponse}
            </Text>

            {results.toolCall && (
              <>
                <Text variant="titleMedium" style={styles.sectionTitle}>
                  Tool Called
                </Text>
                <Chip style={styles.toolChip}>{results.toolCall.name}</Chip>

                <Text variant="bodySmall" style={styles.parametersText}>
                  Parameters:{' '}
                  {JSON.stringify(results.toolCall.parameters, null, 2)}
                </Text>
              </>
            )}

            {results.toolResult && (
              <>
                <Text variant="titleMedium" style={styles.sectionTitle}>
                  Tool Result
                </Text>
                <ScrollView
                  horizontal
                  style={styles.resultScrollView}
                  showsVerticalScrollIndicator={true}
                >
                  <Text variant="bodySmall" style={styles.codeText}>
                    {JSON.stringify(results.toolResult, null, 2)}
                  </Text>
                </ScrollView>
              </>
            )}
          </Card.Content>
        </Card>
      )}

      <Card>
        <Card.Content>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            What's Being Tested
          </Text>
          <Text variant="bodyMedium">
            • Enhanced meal planner with database integration{'\n'}• User
            compatibility scoring{'\n'}• Smart recipe finder with AI filtering
            {'\n'}• Grocery list generation with cost estimation{'\n'}•
            Real-time database queries with proper indexing
          </Text>
        </Card.Content>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  title: {
    marginBottom: 16,
  },
  subtitle: {
    marginBottom: 16,
    color: '#666',
  },
  buttonContainer: {
    gap: 12,
    marginBottom: 24,
  },
  resultCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 8,
  },
  responseText: {
    marginBottom: 16,
  },
  toolChip: {
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  parametersText: {
    marginBottom: 16,
  },
  resultScrollView: {
    maxHeight: 200,
  },
  codeText: {
    fontFamily: 'monospace',
  },
});

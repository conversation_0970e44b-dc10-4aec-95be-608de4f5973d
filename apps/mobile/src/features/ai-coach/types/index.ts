// AI Coach Types

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  toolCalls?: ToolCall[];
  isStreaming?: boolean;
}

export interface ToolCall {
  id: string;
  name: string;
  status: 'pending' | 'executing' | 'completed' | 'failed';
  parameters?: Record<string, any>;
  result?: any;
  error?: string;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ToolDefinition {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<
      string,
      {
        type: string;
        description: string;
        enum?: string[];
        items?: {
          type: string;
        };
        properties?: Record<string, any>;
      }
    >;
    required?: string[];
  };
  execute: (parameters: Record<string, any>) => Promise<any>;
}

// Tool-specific types
export interface MealPlanRequest {
  days: number;
  goal?: 'weight_loss' | 'weight_gain' | 'maintenance' | 'muscle_gain';
  dietaryRestrictions?: string[];
  calorieTarget?: number;
  preferences?: string[];
}

export interface WorkoutPlanRequest {
  days: number;
  type?: 'strength' | 'cardio' | 'mixed' | 'flexibility';
  experience?: 'beginner' | 'intermediate' | 'advanced';
  equipment?: string[];
  duration?: number; // minutes
  goals?: string[];
}

export interface ProgressAnalysisRequest {
  timeframe: 'week' | 'month' | 'quarter' | 'year';
  metrics?: ('weight' | 'measurements' | 'workouts' | 'nutrition')[];
}

export interface LogMealRequest {
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  foodItems: string[];
  notes?: string;
}

export interface LogWorkoutRequest {
  exerciseName: string;
  sets: number;
  reps: number;
  weight?: number;
  duration?: number;
  notes?: string;
}

// AI Service types
export interface AIResponse {
  content: string;
  toolCalls?: ToolCall[];
  isComplete: boolean;
}

export interface StreamingResponse {
  content: string;
  isComplete: boolean;
  toolCalls?: ToolCall[];
}

// Enhanced AI Tool Types
export interface EnhancedMealPlanRequest {
  week_start_date: string;
  dietary_preferences?: string[];
  target_calories_per_day?: number;
  target_macros?: {
    protein_grams: number;
    carbs_grams: number;
    fat_grams: number;
  };
  excluded_ingredients?: string[];
  preferred_cuisines?: string[];
  max_prep_time_minutes?: number;
  meals_per_day?: number;
  cooking_skill_level?: 'beginner' | 'intermediate' | 'advanced';
}

export interface RecipeFinderRequest {
  search_query?: string;
  cuisine_type?: string;
  dietary_tags?: string[];
  max_prep_time?: number;
  max_total_time?: number;
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
  meal_type?: 'breakfast' | 'lunch' | 'dinner' | 'snack' | 'dessert';
  min_protein?: number;
  max_calories?: number;
  available_ingredients?: string[];
  exclude_allergens?: string[];
  budget_max?: number;
  limit?: number;
}

export interface GroceryListRequest {
  meal_plan_id?: string;
  recipe_ids?: string[];
  servings_multiplier?: number;
  organize_by_category?: boolean;
  exclude_pantry_staples?: boolean;
  store_preference?: string;
  week_start_date?: string;
}

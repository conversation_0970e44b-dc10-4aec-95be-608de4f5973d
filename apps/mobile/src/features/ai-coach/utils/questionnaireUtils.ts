import { questionnaires } from '../data/questionnaireData';

export interface ParsedQuestionnaireMessage {
  isQuestionnaire: boolean;
  questionnaireType?: string;
  sessionId?: string;
  questionId?: string;
  progress?: {
    current: number;
    total: number;
    percentage: number;
  };
  options?: Array<{
    value: string;
    label: string;
    emoji?: string;
  }>;
  isMultipleChoice?: boolean;
  cleanMessage: string;
}

export const parseQuestionnaireMessage = (
  message: string
): ParsedQuestionnaireMessage => {
  // Check if this is a questionnaire message
  const hasSessionId = /Session ID: ([a-f0-9-]+)/.test(message);
  const hasQuestionId = /Question ID: ([a-zA-Z_]+)/.test(message);
  const hasProgress = /Question \d+ of \d+/.test(message);

  if (!hasSessionId && !hasQuestionId && !hasProgress) {
    return {
      isQuestionnaire: false,
      cleanMessage: message,
    };
  }

  // Extract session ID and question ID
  const sessionIdMatch = message.match(/Session ID: ([a-f0-9-]+)/);
  const questionIdMatch = message.match(/Question ID: ([a-zA-Z_]+)/);
  const progressMatch = message.match(/Question (\d+) of (\d+)/);

  const sessionId = sessionIdMatch?.[1];
  const questionId = questionIdMatch?.[1];

  // Calculate progress
  let progress;
  if (progressMatch) {
    const current = parseInt(progressMatch[1], 10);
    const total = parseInt(progressMatch[2], 10);
    const percentage = Math.round((current / total) * 100);
    progress = { current, total, percentage };
  }

  // Determine questionnaire type by finding the question
  let questionnaireType: string | undefined;
  let isMultipleChoice = false;
  let options: Array<{ value: string; label: string; emoji?: string }> = [];

  if (questionId) {
    // Search through all questionnaires to find the question
    for (const [type, questionnaire] of Object.entries(questionnaires)) {
      const question = questionnaire.questions.find(q => q.id === questionId);
      if (question) {
        questionnaireType = type;
        isMultipleChoice = question.type === 'multiple_choice';

        // Extract options from the question definition
        if (question.options) {
          options = question.options.map(opt => ({
            value: opt.value,
            label: opt.label,
            emoji: opt.emoji,
          }));
        }
        break;
      }
    }
  }

  // If we couldn't find the question in our data, try to parse options from the message
  if (options.length === 0) {
    const optionLines = message.split('\n').filter(line => {
      // Look for lines that start with emoji and contain option text
      return /^[🔰🌱💪🏋️🏆📅📆🗓️📋⏱️⏰🕐🕑🕒♂️♀️🤐📉💪⚖️❤️🏋️🏃✨🧘💺🚶🏃🏋️💪⏱️⏰🕐🕑🕒📊📸⚡🏆🎯❤️📋💡🤗🎯🍽️🥬🌱🐟🥓🥩🫒⏰😋🥩🐔🐟🌶️🍰🍄🧄🔰👨‍🍳👩‍🍳🧑‍🍳⏱️⏰🕐🕑🕒💰⚖️✨💎🍔🍝🌮🍜🍛🫒🥙🍲🔰🌱💪🏋️🏆📅📆🗓️📋⏱️⏰🕐🕑🕒🏋️🏃⚡🧘⚽🏊💃🥾👥🏠🏋️‍♀️🌳🏢🤸🏋️🏠🏢🚴✅🦴🦵💪🤲🦶❤️💪🔥❤️🏋️🤸🏆⚖️🔄🎲📅📈🌅☀️🕐🌤️🌆🌙🔄]/.test(
        line.trim()
      );
    });

    options = optionLines
      .map(line => {
        const trimmed = line.trim();
        const emojiMatch = trimmed.match(
          /^([🔰🌱💪🏋️🏆📅📆🗓️📋⏱️⏰🕐🕑🕒♂️♀️🤐📉💪⚖️❤️🏋️🏃✨🧘💺🚶🏃🏋️💪⏱️⏰🕐🕑🕒📊📸⚡🏆🎯❤️📋💡🤗🎯🍽️🥬🌱🐟🥓🥩🫒⏰😋🥩🐔🐟🌶️🍰🍄🧄🔰👨‍🍳👩‍🍳🧑‍🍳⏱️⏰🕐🕑🕒💰⚖️✨💎🍔🍝🌮🍜🍛🫒🥙🍲🔰🌱💪🏋️🏆📅📆🗓️📋⏱️⏰🕐🕑🕒🏋️🏃⚡🧘⚽🏊💃🥾👥🏠🏋️‍♀️🌳🏢🤸🏋️🏠🏢🚴✅🦴🦵💪🤲🦶❤️💪🔥❤️🏋️🤸🏆⚖️🔄🎲📅📈🌅☀️🕐🌤️🌆🌙🔄]+)\s*(.+)/
        );

        if (emojiMatch) {
          const emoji = emojiMatch[1];
          const label = emojiMatch[2];
          // Create a simple value from the label
          const value = label
            .toLowerCase()
            .replace(/[^a-z0-9]/g, '_')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');

          return {
            value,
            label,
            emoji,
          };
        }

        return {
          value: trimmed.toLowerCase().replace(/[^a-z0-9]/g, '_'),
          label: trimmed,
        };
      })
      .filter(opt => opt.label.length > 0);
  }

  // Clean the message by removing technical details and option lists
  const cleanMessage = message
    .replace(/Session ID: [a-f0-9-]+/g, '')
    .replace(/Question ID: [a-zA-Z_]+/g, '')
    .replace(/\*\*Progress: \d+% complete\*\*/g, '')
    .replace(/\*\*Question \d+ of \d+:\*\*/g, '')
    .split('\n')
    .filter(line => {
      const trimmed = line.trim();
      // Remove option lines and empty lines
      if (!trimmed) {
        return false;
      }
      if (
        /^[🔰🌱💪🏋️🏆📅📆🗓️📋⏱️⏰🕐🕑🕒♂️♀️🤐📉💪⚖️❤️🏋️🏃✨🧘💺🚶🏃🏋️💪⏱️⏰🕐🕑🕒📊📸⚡🏆🎯❤️📋💡🤗🎯🍽️🥬🌱🐟🥓🥩🫒⏰😋🥩🐔🐟🌶️🍰🍄🧄🔰👨‍🍳👩‍🍳🧑‍🍳⏱️⏰🕐🕑🕒💰⚖️✨💎🍔🍝🌮🍜🍛🫒🥙🍲🔰🌱💪🏋️🏆📅📆🗓️📋⏱️⏰🕐🕑🕒🏋️🏃⚡🧘⚽🏊💃🥾👥🏠🏋️‍♀️🌳🏢🤸🏋️🏠🏢🚴✅🦴🦵💪🤲🦶❤️💪🔥❤️🏋️🤸🏆⚖️🔄🎲📅📈🌅☀️🕐🌤️🌆🌙🔄]/.test(
          trimmed
        )
      ) {
        return false;
      }
      return true;
    })
    .join('\n')
    .replace(/\n\n+/g, '\n\n')
    .trim();

  return {
    isQuestionnaire: true,
    questionnaireType,
    sessionId,
    questionId,
    progress,
    options,
    isMultipleChoice,
    cleanMessage,
  };
};

export const createQuestionnaireResponse = (
  questionId: string,
  questionnaireType: string,
  sessionId: string,
  selectedOption: { value: string; label: string }
): string => {
  return `save_questionnaire_answer:${JSON.stringify({
    questionnaireType,
    questionId,
    answer: {
      value: selectedOption.value,
      displayText: selectedOption.label,
    },
    sessionId,
  })}`;
};

export const isQuestionnaireComplete = (message: string): boolean => {
  return (
    message.includes('complete_questionnaire') ||
    message.includes('That was the last question') ||
    message.includes('questionnaire complete')
  );
};

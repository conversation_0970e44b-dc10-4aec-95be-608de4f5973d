import { useNavigation } from '@react-navigation/native';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  Modal,
} from 'react-native';
import { supabase } from '../../../lib/supabase';
import { ScreenWithTopBar } from '../../../shared/components/ScreenWithTopBar';
import { analytics } from '../../../shared/services';
import { showSuccessToast } from '../../../shared/utils/toast';
import { useProfileCompletion } from '../../home/<USER>/useProfileCompletion';
import MealPlanSelectionModal from '../../nutrition/components/MealPlanSelectionModal';
import { MealPlanSummary } from '../../nutrition/services/mealPlanManagementService';
import {
  LogProgressQuickAction,
  ProgressLogData,
} from '../../progress/components/LogProgressQuickAction';
import { progressDataService } from '../../progress/services/progressDataService';
import { MessageBubble } from '../components/MessageBubble';
import { TypingIndicator } from '../components/TypingIndicator';
import { aiChatService } from '../services/aiChatService';
import { enhancedMealPlanner } from '../services/mealPlanningTools';
import { useChatStore } from '../store/chatStore';
import { ChatMessage, ToolCall } from '../types';

export const ChatScreen: React.FC = () => {
  const _navigation = useNavigation(); // Reserved for future navigation to meal plans, exercises, etc.
  const {
    messages,
    currentSession,
    isTyping,
    isLoading,
    isSyncing: _isSyncing,
    startNewSession,
    addMessage,
    setIsTyping,
    setIsLoading,
    updateToolCall,
    loadUserSessions,
    syncWithDatabase: _syncWithDatabase,
    clearAllData,
  } = useChatStore();

  const [inputText, setInputText] = useState('');
  const [inputPlaceholder, setInputPlaceholder] = useState(
    'Ask me anything about fitness and nutrition...'
  );
  const flatListRef = useRef<FlatList>(null);
  const inputRef = useRef<any>(null);
  const { isComplete: isProfileComplete, loading: profileLoading } =
    useProfileCompletion();

  const [isInitialized, setIsInitialized] = useState(false);
  const [showLogProgress, setShowLogProgress] = useState(false);
  const [unitBanner, setUnitBanner] = useState<string | null>(null);
  const [showMealPlanModal, setShowMealPlanModal] = useState(false);

  useEffect(() => {
    // Load user sessions and start a new session if none exists
    const initializeChat = async () => {
      if (isInitialized) {
        return;
      }

      try {
        // Check if we have old format data and clear it
        if (
          currentSession &&
          typeof currentSession.id === 'string' &&
          currentSession.id.length < 30
        ) {
          console.log('Detected old format session ID, clearing data');
          clearAllData();
          await startNewSession();
          setIsInitialized(true);
          return;
        }

        await loadUserSessions();
        if (!currentSession) {
          await startNewSession();
        }
        setIsInitialized(true);
      } catch (error) {
        console.error('Error initializing chat:', error);
        // Fallback to starting a new session
        await startNewSession();
        setIsInitialized(true);
      }
    };

    initializeChat();

    // Load unit preference once when chat mounts
    (async () => {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession();
        if (!session) return;
        const { data } = await supabase
          .from('profiles')
          .select('unit_system')
          .eq('id', session.user.id)
          .single();
        const unit =
          data?.unit_system === 'imperial'
            ? 'Imperial'
            : data?.unit_system === 'metric'
              ? 'Metric'
              : null;
        if (unit) {
          setUnitBanner(
            `Using ${unit} units — you can change this in Settings`
          );
          // Auto-hide after 5s
          setTimeout(() => setUnitBanner(null), 5000);
        }
      } catch (_e) {
        // ignore
      }
    })();
  }, []);

  // Removed automatic welcome message to prevent infinite loops
  // Users can start conversations manually

  // Optimized auto-scroll with useCallback to prevent unnecessary re-renders
  const scrollToBottom = useCallback(() => {
    if (flatListRef.current) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  }, []);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    if (messages.length > 0) {
      // Use requestAnimationFrame for better performance than setTimeout
      const timeoutId = requestAnimationFrame(() => {
        scrollToBottom();
      });

      return () => cancelAnimationFrame(timeoutId);
    }
    return undefined;
  }, [messages, isTyping, scrollToBottom]);

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading) {
      return;
    }

    const userMessage = inputText.trim();
    setInputText('');
    setInputPlaceholder('Ask me anything about fitness and nutrition...');

    // Add user message
    addMessage({
      content: userMessage,
      role: 'user',
    });

    try {
      setIsLoading(true);
      setIsTyping(true);

      // Analytics: chat send
      analytics.trackEvent({
        name: 'chat_send',
        properties: { source: 'AICoach', length: userMessage.length },
      });

      // Get AI response with session context
      const response = await aiChatService.sendMessage(
        userMessage,
        messages.map(m => ({ role: m.role, content: m.content })),
        currentSession?.id
      );

      setIsTyping(false);

      // Add AI response
      const aiMessageId = Date.now().toString();
      addMessage({
        content: response.content,
        role: 'assistant',
        toolCalls: response.toolCalls,
      });

      // Execute tools if any
      if (response.toolCalls && response.toolCalls.length > 0) {
        await executeTools(aiMessageId, response.toolCalls);
      }
    } catch (error) {
      console.error('Chat error:', error);
      setIsTyping(false);
      addMessage({
        content: "I'm sorry, I encountered an error. Please try again.",
        role: 'assistant',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const executeTools = async (messageId: string, toolCalls: ToolCall[]) => {
    for (const toolCall of toolCalls) {
      try {
        // Update tool status to executing
        updateToolCall(messageId, toolCall.id, { status: 'executing' });

        // Execute the tool
        console.log(
          'Executing tool:',
          toolCall.name,
          'with params:',
          toolCall.parameters
        );
        const result = await aiChatService.executeToolCall(toolCall);
        console.log('Tool execution result:', result);

        // Update tool with result
        updateToolCall(messageId, toolCall.id, {
          status: 'completed',
          result,
        });

        // Add a follow-up message with the tool result
        if (result.success) {
          addMessage({
            content: formatToolResult(toolCall.name, result.data),
            role: 'assistant',
          });
        } else if (typeof result === 'string') {
          // Handle string results (like questionnaire responses)
          addMessage({
            content: result,
            role: 'assistant',
          });
        }
      } catch (error) {
        console.error(`Tool execution failed for ${toolCall.name}:`, error);
        updateToolCall(messageId, toolCall.id, {
          status: 'failed',
          error:
            error instanceof Error ? error.message : 'Tool execution failed',
        });
      }
    }
  };

  const formatToolResult = (toolName: string, data: any): string => {
    switch (toolName) {
      case 'generate_meal_plan':
        return `Here's your ${data.days}-day meal plan:\n\n${data.meals
          .map(
            (meal: any, _index: number) =>
              `**Day ${meal.day}:**\n🌅 ${meal.breakfast}\n🌞 ${meal.lunch}\n🌙 ${meal.dinner}\n🍎 ${meal.snacks.join(', ')}`
          )
          .join(
            '\n\n'
          )}\n\n**Total:** ${data.totalCalories} calories over ${data.days} days\n**Daily Target:** ${data.calorieTarget} calories`;

      case 'generate_workout_plan':
        return `Here's your ${data.days}-day ${data.type} workout plan:\n\n${data.workouts
          .map(
            (workout: any) =>
              `**${workout.name}** (${workout.duration} min)\n${workout.exercises
                .map(
                  (ex: any) =>
                    `• ${ex.name}${ex.sets > 1 ? ` - ${ex.sets} sets x ${ex.reps} reps` : ` - ${ex.reps}`}`
                )
                .join('\n')}`
          )
          .join('\n\n')}`;

      case 'analyze_progress':
        return `**Progress Analysis (${data.timeframe})**\n\n📊 **Weight Change:** ${data.weightChange > 0 ? '+' : ''}${data.weightChange} kg (${data.weightTrend})\n📈 **Consistency:** ${data.consistency}%\n⚖️ **Latest Weight:** ${data.latestWeight} kg\n📝 **Total Entries:** ${data.totalEntries}`;

      case 'generate_grocery_list':
        return `**Grocery List** (${data.mealPlanDays} days)\n\n${data.groceryList
          .map(
            (category: any) =>
              `**${category.category}:**\n${category.items.map((item: string) => `• ${item}`).join('\n')}`
          )
          .join(
            '\n\n'
          )}\n\n**Total Items:** ${data.totalItems}\n**Estimated Cost:** $${data.estimatedCost}`;

      case 'find_exercise_alternative':
        return `**Alternatives to ${data.originalExercise}** (${data.reason}):\n\n${data.alternatives.map((alt: string) => `• ${alt}`).join('\n')}\n\n💡 ${data.recommendation}`;

      case 'log_meal':
      case 'log_workout':
        return `✅ ${data.message}`;

      default:
        return 'Task completed successfully!';
    }
  };

  const renderQuickActions = () => {
    // Show questionnaire actions for new users
    if (!profileLoading && !isProfileComplete) {
      return (
        <View style={styles.quickActionsContainer}>
          <Text style={styles.quickActionsTitle}>
            Get Started with Personalized Coaching:
          </Text>
          <View style={styles.quickActionsRow}>
            <TouchableOpacity
              style={[styles.quickActionButton, styles.primaryActionButton]}
              onPress={() =>
                setInputText(
                  "I'd like to start with the Basic Profile questionnaire"
                )
              }
            >
              <Text style={[styles.quickActionText, styles.primaryActionText]}>
                🚀 Start Basic Profile
              </Text>
            </TouchableOpacity>
          </View>
          <Text style={styles.quickActionsSubtitle}>
            Takes 2-3 minutes • Unlocks personalized advice
          </Text>

          <Text style={styles.quickActionsTitle}>Or ask me directly:</Text>
          <View style={styles.quickActionsRow}>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => setInputText("What's my questionnaire status?")}
            >
              <Text style={styles.quickActionText}>📊 Check Status</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => setInputText('Tell me about dietary preferences')}
            >
              <Text style={styles.quickActionText}>🥗 Diet Preferences</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // Show regular actions if profile is complete
    return (
      <View style={styles.quickActionsContainer}>
        <Text style={styles.quickActionsTitle}>Quick Actions:</Text>
        <View style={styles.quickActionsRow}>
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => {
              analytics.trackEvent({
                name: 'quick_action',
                properties: { action: 'meal_plan' },
              });
              setShowMealPlanModal(true);
            }}
          >
            <Text style={styles.quickActionText}>🍽️ Meal Plan</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => {
              analytics.trackEvent({
                name: 'quick_action',
                properties: { action: 'workout' },
              });
              setInputText('Design a 3-day strength workout');
            }}
          >
            <Text style={styles.quickActionText}>💪 Workout</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.quickActionsRow}>
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => {
              analytics.trackEvent({
                name: 'quick_action',
                properties: { action: 'progress' },
              });
              setInputText('Show me my progress this month');
            }}
          >
            <Text style={styles.quickActionText}>📊 Progress</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => {
              analytics.trackEvent({
                name: 'quick_action',
                properties: { action: 'grocery_list' },
              });
              setInputText('Generate a grocery list');
            }}
          >
            <Text style={styles.quickActionText}>🛒 Grocery List</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.quickActionsRow}>
          <TouchableOpacity
            style={[styles.quickActionButton, styles.logProgressButton]}
            onPress={() => {
              analytics.trackEvent({
                name: 'quick_action',
                properties: { action: 'log_progress' },
              });
              setShowLogProgress(true);
            }}
          >
            <Text style={[styles.quickActionText, styles.logProgressText]}>
              📝 Log Progress
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const handleProgressLog = async (data: ProgressLogData) => {
    try {
      // Save progress data
      await progressDataService.saveProgressEntry(data);

      // Close modal
      setShowLogProgress(false);

      // Notify success
      const syncResult = await progressDataService.syncProgressData();
      if (syncResult.synced) {
        showSuccessToast(
          'Progress logged',
          syncResult.count ? `${syncResult.count} item(s) synced` : undefined
        );
      } else {
        showSuccessToast('Progress saved offline', 'Will sync when online');
      }

      // Add confirmation message to chat
      const confirmationMessage = getProgressConfirmationMessage(data);
      addMessage({
        content: confirmationMessage,
        role: 'user',
      });

      // Send AI response about the logged progress
      // Analytics: progress logged
      analytics.trackEvent({
        name: 'progress_logged',
        properties: { type: data.type },
      });

      const aiResponse = await aiChatService.sendMessage(
        `I just logged: ${confirmationMessage}. Please acknowledge this and provide any relevant insights or suggestions.`,
        messages,
        currentSession?.id || ''
      );

      if (aiResponse) {
        addMessage({
          content: aiResponse.content,
          role: 'assistant',
          toolCalls: aiResponse.toolCalls,
        });
      }
    } catch (error) {
      console.error('Failed to log progress:', error);
      // Show error message in chat
      addMessage({
        content: 'Sorry, I had trouble saving your progress. Please try again.',
        role: 'assistant',
      });
    }
  };

  const getProgressConfirmationMessage = (data: ProgressLogData): string => {
    switch (data.type) {
      case 'meal':
        const foodCount = data.data.foods?.length || 0;
        const calories = data.data.totalCalories || 0;
        return `Logged a meal with ${foodCount} food items (${calories} calories)`;
      case 'habit': {
        const steps = data.data?.steps ? `${data.data.steps} steps` : undefined;
        const hydrationMl = data.data?.hydration?.value;
        const hydrationText = hydrationMl
          ? `${hydrationMl} ml hydration`
          : undefined;
        const parts = [hydrationText, steps].filter(Boolean).join(', ');
        return parts ? `Logged habits: ${parts}` : 'Logged habits';
      }
      case 'mood': {
        const parts = [] as string[];
        if (data.data?.mood) parts.push(`mood ${data.data.mood}`);
        if (data.data?.energy) parts.push(`energy ${data.data.energy}`);
        return parts.length ? `Logged ${parts.join(', ')}` : 'Logged mood';
      }
      case 'workout': {
        const { completed, durationMin, type, intensity } = data.data || {};
        const status = completed ? 'completed' : 'skipped';
        const details = [
          durationMin ? `${durationMin} min` : undefined,
          type,
          intensity,
        ]
          .filter(Boolean)
          .join(', ');
        return `Workout ${status}${details ? ` (${details})` : ''}`;
      }
      case 'weight':
        return 'Logged weight measurement';
      default:
        return 'Logged progress update';
    }
  };

  // Meal plan modal handlers
  const handleCreateNewMealPlan = async (duration: number) => {
    try {
      setIsLoading(true);
      setIsTyping(true);

      // Add user message
      addMessage({
        content: `Create a new ${duration}-day meal plan`,
        role: 'user',
      });

      // Create meal plan using enhanced meal planner
      const result = await enhancedMealPlanner({
        week_start_date: new Date().toISOString().split('T')[0],
        duration_days: duration,
        use_persona_data: true,
      });

      setIsTyping(false);

      if (result.success) {
        // Add success message
        addMessage({
          content: `Great! I've created a ${duration}-day meal plan for you with ${result.data?.total_recipes || 0} recipes. Your target is ${result.data?.target_calories || 0} calories per day. Would you like me to show you the details or generate a grocery list?`,
          role: 'assistant',
        });

        analytics.trackEvent({
          name: 'meal_plan_created',
          properties: { duration, source: 'quick_action' },
        });
      } else {
        addMessage({
          content: `I had trouble creating your meal plan: ${result.error}. Please try again or let me know if you'd like to adjust your preferences.`,
          role: 'assistant',
        });
      }
    } catch (error) {
      console.error('Error creating meal plan:', error);
      setIsTyping(false);
      addMessage({
        content:
          'Sorry, I encountered an error while creating your meal plan. Please try again.',
        role: 'assistant',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewExistingMealPlan = (mealPlan: MealPlanSummary) => {
    // For now, just show a message - navigation to nested screens needs proper setup
    addMessage({
      content: `I found your meal plan "${mealPlan.name}" (${mealPlan.duration_days} days, ${mealPlan.meal_count} meals). You can view it in the Nutrition tab.`,
      role: 'assistant',
    });
  };

  const handleUpdateExistingMealPlan = async (
    mealPlan: MealPlanSummary,
    duration: number
  ) => {
    try {
      setIsLoading(true);
      setIsTyping(true);

      // Add user message
      addMessage({
        content: `Update "${mealPlan.name}" with a new ${duration}-day plan`,
        role: 'user',
      });

      // Update meal plan using enhanced meal planner
      const result = await enhancedMealPlanner({
        week_start_date: new Date().toISOString().split('T')[0],
        duration_days: duration,
        use_persona_data: true,
        update_existing_plan: mealPlan.id,
      });

      setIsTyping(false);

      if (result.success) {
        // Add success message
        addMessage({
          content: `Perfect! I've updated "${mealPlan.name}" with a fresh ${duration}-day meal plan. Your target is ${result.data?.target_calories || 0} calories per day. The plan includes ${result.data?.total_recipes || 0} recipes tailored to your preferences.`,
          role: 'assistant',
        });

        analytics.trackEvent({
          name: 'meal_plan_updated',
          properties: { duration, source: 'quick_action' },
        });
      } else {
        addMessage({
          content: `I had trouble updating your meal plan: ${result.error}. Please try again.`,
          role: 'assistant',
        });
      }
    } catch (error) {
      console.error('Error updating meal plan:', error);
      setIsTyping(false);
      addMessage({
        content:
          'Sorry, I encountered an error while updating your meal plan. Please try again.',
        role: 'assistant',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuestionnaireResponse = async (response: string) => {
    if (response === 'custom_answer_mode') {
      // Focus on text input for custom answer
      setInputPlaceholder('Type your answer here...');
      if (inputRef.current) {
        inputRef.current.focus();
      }
      return;
    }

    // Parse the questionnaire response
    if (response.startsWith('save_questionnaire_answer:')) {
      const responseData = response.replace('save_questionnaire_answer:', '');

      try {
        const parsedData = JSON.parse(responseData);

        // Add user message showing their selection
        addMessage({
          content: parsedData.answer.displayText,
          role: 'user',
        });

        // Send to AI for processing
        setIsLoading(true);
        setIsTyping(true);

        const aiResponse = await aiChatService.sendMessage(
          `User selected: ${parsedData.answer.displayText}`,
          messages.map(m => ({ role: m.role, content: m.content })),
          currentSession?.id
        );

        setIsTyping(false);

        // Add AI response
        const aiMessageId = Date.now().toString();
        addMessage({
          content: aiResponse.content,
          role: 'assistant',
          toolCalls: aiResponse.toolCalls,
        });

        // Execute tools if any
        if (aiResponse.toolCalls && aiResponse.toolCalls.length > 0) {
          await executeTools(aiMessageId, aiResponse.toolCalls);
        }
      } catch (error) {
        console.error('Error processing questionnaire response:', error);
        setIsTyping(false);
        addMessage({
          content:
            "I'm sorry, I had trouble processing your response. Please try again.",
          role: 'assistant',
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => (
    <MessageBubble
      message={item}
      onQuestionnaireResponse={handleQuestionnaireResponse}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyStateContainer}>
      <Text style={styles.emptyStateTitle}>👋 Hi! I'm your AI Coach</Text>
      <Text style={styles.emptyStateText}>
        I can help you with meal planning, workout routines, progress tracking,
        and more!
      </Text>
      {renderQuickActions()}
    </View>
  );

  return (
    <ScreenWithTopBar title="AI Coach">
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          style={styles.chatContainer}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
        >
          {unitBanner ? (
            <View style={styles.unitBanner}>
              <Text style={styles.unitBannerText}>{unitBanner}</Text>
            </View>
          ) : null}

          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessage}
            keyExtractor={item => item.id}
            style={styles.messagesList}
            contentContainerStyle={
              messages.length === 0 ? styles.emptyList : undefined
            }
            ListEmptyComponent={renderEmptyState}
            showsVerticalScrollIndicator={false}
          />

          {isTyping && <TypingIndicator />}

          <View style={styles.inputContainer}>
            <TextInput
              ref={inputRef}
              style={styles.textInput}
              value={inputText}
              onChangeText={setInputText}
              placeholder={inputPlaceholder}
              placeholderTextColor="#999"
              multiline
              maxLength={500}
              editable={!isLoading}
            />
            <TouchableOpacity
              style={[
                styles.sendButton,
                (!inputText.trim() || isLoading) && styles.sendButtonDisabled,
              ]}
              onPress={handleSendMessage}
              disabled={!inputText.trim() || isLoading}
            >
              <Text style={styles.sendButtonText}>Send</Text>
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>

      {/* Log Progress Modal */}
      <Modal
        visible={showLogProgress}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <LogProgressQuickAction
          onComplete={handleProgressLog}
          onClose={() => setShowLogProgress(false)}
        />
      </Modal>

      {/* Meal Plan Selection Modal */}
      <MealPlanSelectionModal
        visible={showMealPlanModal}
        onClose={() => setShowMealPlanModal(false)}
        onCreateNew={handleCreateNewMealPlan}
        onViewExisting={handleViewExistingMealPlan}
        onUpdateExisting={handleUpdateExistingMealPlan}
      />
    </ScreenWithTopBar>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },

  chatContainer: {
    flex: 1,
  },
  messagesList: {
    flex: 1,
    paddingVertical: 8,
  },
  unitBanner: {
    backgroundColor: '#E8F4FD',
    borderColor: '#007AFF',
    borderWidth: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginHorizontal: 12,
    borderRadius: 12,
    marginTop: 8,
  },
  unitBannerText: {
    color: '#007AFF',
    textAlign: 'center',
    fontWeight: '600',
  },
  emptyList: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  emptyStateContainer: {
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 32,
  },
  emptyStateTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  quickActionsContainer: {
    width: '100%',
  },
  quickActionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
    textAlign: 'center',
  },
  quickActionsSubtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  quickActionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  quickActionButton: {
    backgroundColor: '#F0F8FF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#007AFF',
    flex: 0.48,
  },
  quickActionText: {
    fontSize: 14,
    color: '#007AFF',
    textAlign: 'center',
    fontWeight: '500',
  },
  primaryActionButton: {
    backgroundColor: '#007AFF',
    flex: 1,
  },
  primaryActionText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  inputContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    backgroundColor: '#FFFFFF',
    alignItems: 'flex-end',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 8,
    maxHeight: 100,
    fontSize: 16,
    color: '#000000',
  },
  sendButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  sendButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  logProgressButton: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
    flex: 1,
  },
  logProgressText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
});

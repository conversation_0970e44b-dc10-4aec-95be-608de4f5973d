import { supabase } from '../../../lib/supabase';
import { AIResponse, StreamingResponse, ToolCall } from '../types';
import { chatDatabaseService } from './chatDatabaseService';
import { toolRegistry } from './toolRegistry';

// AI service that connects to Gemini 2.5 Flash via Supabase Edge Function
class AIChatService {
  private edgeFunctionUrl: string;

  constructor() {
    // Get the Supabase URL from environment variables
    const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
    if (!supabaseUrl) {
      throw new Error('EXPO_PUBLIC_SUPABASE_URL is not configured');
    }
    this.edgeFunctionUrl = `${supabaseUrl}/functions/v1/gemini-chat`;
  }

  async sendMessage(
    message: string,
    conversationHistory: Array<{
      role: 'user' | 'assistant';
      content: string;
    }> = [],
    sessionId?: string
  ): Promise<AIResponse> {
    try {
      // Get the current session for authentication
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();

      if (sessionError || !session) {
        console.error('Authentication error:', sessionError);
        return {
          content: 'Please log in to use the AI assistant.',
          isComplete: true,
        };
      }

      // Load conversation history from database if sessionId is provided
      let fullConversationHistory = conversationHistory;
      if (sessionId) {
        try {
          const dbHistory = await chatDatabaseService.getConversationHistory(
            sessionId,
            20
          );
          // Use database history if available, otherwise fall back to provided history
          fullConversationHistory =
            dbHistory.length > 0 ? dbHistory : conversationHistory;
        } catch (error) {
          console.error(
            'Error loading conversation history from database:',
            error
          );
          // Fall back to provided history
        }
      }

      // Prepare messages for the API
      const messages = [
        ...fullConversationHistory,
        { role: 'user' as const, content: message },
      ];

      // Get available tools for the AI
      const tools = this.getAvailableTools();

      // Call the Edge Function
      const response = await fetch(this.edgeFunctionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          messages,
          tools: tools.length > 0 ? tools : undefined,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Edge Function error:', response.status, errorText);

        // Fall back to mock response for development
        console.log('Falling back to mock response...');
        return await this.mockAIResponse(message, conversationHistory);
      }

      const data = await response.json();

      // Check if the AI wants to call a function
      if (data.message.functionCall) {
        console.log('Function call detected:', data.message.functionCall);

        // Create a tool call from the function call
        const toolCall: ToolCall = {
          id: `tool_${Date.now()}`,
          name: data.message.functionCall.name,
          status: 'pending',
          parameters: data.message.functionCall.args || {},
        };

        return {
          content: data.message.content,
          toolCalls: [toolCall],
          isComplete: false, // Tool needs to be executed
        };
      }

      // Parse the response and check for tool calls (fallback method)
      const aiResponse = await this.parseAIResponse(
        data.message.content,
        message
      );

      return aiResponse;
    } catch (error) {
      console.error('AI Chat Service Error:', error);

      // Fall back to mock response for development
      console.log('Error occurred, falling back to mock response...');
      try {
        return await this.mockAIResponse(message, conversationHistory);
      } catch (mockError) {
        console.error('Mock response also failed:', mockError);
        return {
          content:
            "I'm sorry, I'm having trouble processing your request right now. Please try again later.",
          isComplete: true,
        };
      }
    }
  }

  async streamMessage(
    message: string,
    conversationHistory: Array<{
      role: 'user' | 'assistant';
      content: string;
    }> = [],
    onChunk: (chunk: StreamingResponse) => void,
    sessionId?: string
  ): Promise<void> {
    try {
      // For now, get the full response and simulate streaming
      // In the future, this could be enhanced to use actual streaming from Gemini
      const response = await this.sendMessage(
        message,
        conversationHistory,
        sessionId
      );

      // Simulate streaming by sending chunks
      const words = response.content.split(' ');
      let currentContent = '';

      for (let i = 0; i < words.length; i++) {
        currentContent += (i > 0 ? ' ' : '') + words[i];

        onChunk({
          content: currentContent,
          isComplete: i === words.length - 1,
          toolCalls: i === words.length - 1 ? response.toolCalls : undefined,
        });

        // Simulate typing delay
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    } catch (error) {
      console.error('Streaming Error:', error);
      onChunk({
        content:
          "I'm sorry, I'm having trouble processing your request right now.",
        isComplete: true,
      });
    }
  }

  private async mockAIResponse(
    message: string,
    conversationHistory: Array<{ role: 'user' | 'assistant'; content: string }>
  ): Promise<AIResponse> {
    const lowerMessage = message.toLowerCase();

    // Handle profile completion questions
    if (this.isProfileCompletionQuestion(lowerMessage)) {
      return this.handleProfileCompletionResponse(message, lowerMessage);
    }

    // Determine if we need to use tools based on message content
    const toolCalls: ToolCall[] = [];
    let responseContent = '';

    // Enhanced meal planning requests
    if (
      lowerMessage.includes('meal plan') ||
      lowerMessage.includes('diet plan') ||
      lowerMessage.includes('weekly meal') ||
      lowerMessage.includes('plan my meals')
    ) {
      // Extract meal planning parameters (currently using defaults)
      // const days = this.extractNumber(message, 'day') || 7;
      // const goal = this.extractGoal(message);
      const weekStartDate =
        this.extractDate(message) || this.getNextMondayDate();

      const toolCall: ToolCall = {
        id: `tool_${Date.now()}`,
        name: 'enhanced_meal_planner',
        status: 'pending',
        parameters: {
          week_start_date: weekStartDate,
          target_calories_per_day: this.extractCalories(message),
          dietary_preferences: this.extractDietaryPreferences(message),
          max_prep_time_minutes: this.extractPrepTime(message),
          cooking_skill_level: this.extractSkillLevel(message),
        },
      };

      toolCalls.push(toolCall);
      responseContent =
        "I'll create a personalized weekly meal plan for you! Let me generate a plan using our recipe database with AI compatibility scoring.";
    }

    // Recipe search requests
    else if (
      lowerMessage.includes('find recipe') ||
      lowerMessage.includes('recipe for') ||
      lowerMessage.includes('cooking') ||
      lowerMessage.includes('what can i cook') ||
      lowerMessage.includes('recipe suggestion')
    ) {
      const toolCall: ToolCall = {
        id: `tool_${Date.now()}`,
        name: 'smart_recipe_finder',
        status: 'pending',
        parameters: {
          search_query: this.extractSearchQuery(message),
          cuisine_type: this.extractCuisine(message),
          dietary_tags: this.extractDietaryPreferences(message),
          max_total_time: this.extractPrepTime(message),
          difficulty_level: this.extractSkillLevel(message),
          meal_type: this.extractMealType(message),
          limit: 5,
        },
      };

      toolCalls.push(toolCall);
      responseContent =
        "I'll find some great recipes for you! Let me search our database with AI compatibility scoring.";
    }

    // Grocery list requests
    else if (
      lowerMessage.includes('grocery list') ||
      lowerMessage.includes('shopping list') ||
      lowerMessage.includes('what to buy') ||
      lowerMessage.includes('ingredients needed')
    ) {
      const toolCall: ToolCall = {
        id: `tool_${Date.now()}`,
        name: 'generate_grocery_list',
        status: 'pending',
        parameters: {
          organize_by_category: true,
          exclude_pantry_staples:
            lowerMessage.includes('exclude pantry') ||
            lowerMessage.includes('no pantry'),
          week_start_date:
            this.extractDate(message) || this.getNextMondayDate(),
        },
      };

      toolCalls.push(toolCall);
      responseContent =
        "I'll generate a smart grocery list for you! Let me organize it by category with cost estimates.";
    }

    // Workout planning requests
    else if (
      lowerMessage.includes('workout plan') ||
      lowerMessage.includes('exercise plan')
    ) {
      const days = this.extractNumber(message, 'day') || 3;
      const type = this.extractWorkoutType(message);

      const toolCall: ToolCall = {
        id: `tool_${Date.now()}`,
        name: 'generate_workout_plan',
        status: 'pending',
        parameters: { days, type },
      };

      toolCalls.push(toolCall);
      responseContent = `I'll design a ${days}-day ${type} workout plan for you! Creating a routine that matches your fitness level.`;
    }

    // Progress analysis requests
    else if (
      lowerMessage.includes('progress') ||
      lowerMessage.includes('how am i doing')
    ) {
      const timeframe = this.extractTimeframe(message);

      const toolCall: ToolCall = {
        id: `tool_${Date.now()}`,
        name: 'analyze_progress',
        status: 'pending',
        parameters: { timeframe },
      };

      toolCalls.push(toolCall);
      responseContent = `Let me analyze your progress over the past ${timeframe}. I'll look at your data and provide insights.`;
    }

    // Grocery list requests
    else if (
      lowerMessage.includes('grocery') ||
      lowerMessage.includes('shopping list')
    ) {
      const days = this.extractNumber(message, 'day') || 7;

      const toolCall: ToolCall = {
        id: `tool_${Date.now()}`,
        name: 'generate_grocery_list',
        status: 'pending',
        parameters: { mealPlanDays: days },
      };

      toolCalls.push(toolCall);
      responseContent = `I'll create a grocery list for your ${days}-day meal plan. This will include all the ingredients you need!`;
    }

    // Exercise alternatives
    else if (
      lowerMessage.includes('alternative') ||
      lowerMessage.includes('cant do') ||
      lowerMessage.includes('injury')
    ) {
      const exerciseName = this.extractExerciseName(message);
      const reason = this.extractReason(message);

      if (exerciseName) {
        const toolCall: ToolCall = {
          id: `tool_${Date.now()}`,
          name: 'find_exercise_alternative',
          status: 'pending',
          parameters: { exerciseName, reason },
        };

        toolCalls.push(toolCall);
        responseContent = `I'll find some great alternatives to ${exerciseName} for you. Let me suggest exercises that work around your ${reason}.`;
      } else {
        responseContent =
          "I'd be happy to help you find exercise alternatives! Could you tell me which specific exercise you need an alternative for and why?";
      }
    }

    // Meal logging
    else if (
      lowerMessage.includes('log meal') ||
      lowerMessage.includes('ate') ||
      lowerMessage.includes('had for')
    ) {
      const mealType = this.extractMealType(message);
      const foodItems = this.extractFoodItems(message);

      if (mealType && foodItems.length > 0) {
        const toolCall: ToolCall = {
          id: `tool_${Date.now()}`,
          name: 'log_meal',
          status: 'pending',
          parameters: { mealType, foodItems },
        };

        toolCalls.push(toolCall);
        responseContent = `I'll log your ${mealType} for you! Recording ${foodItems.join(', ')}.`;
      } else {
        responseContent =
          "I'd be happy to log your meal! Could you tell me what meal type (breakfast, lunch, dinner, or snack) and what you ate?";
      }
    }

    // Workout logging
    else if (
      lowerMessage.includes('log workout') ||
      lowerMessage.includes('completed') ||
      lowerMessage.includes('did')
    ) {
      responseContent =
        "I'd be happy to log your workout! Could you tell me which exercises you did, how many sets and reps?";
    }

    // General conversation
    else {
      responseContent = this.generateGeneralResponse(
        message,
        conversationHistory
      );
    }

    return {
      content: responseContent,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      isComplete: true,
    };
  }

  private extractNumber(text: string, context: string): number | null {
    const regex = new RegExp(`(\\d+)\\s*${context}`, 'i');
    const match = text.match(regex);
    return match ? parseInt(match[1], 10) : null;
  }

  private extractGoal(text: string): string {
    const lowerText = text.toLowerCase();
    if (
      lowerText.includes('lose weight') ||
      lowerText.includes('weight loss')
    ) {
      return 'weight_loss';
    }
    if (
      lowerText.includes('gain weight') ||
      lowerText.includes('weight gain')
    ) {
      return 'weight_gain';
    }
    if (lowerText.includes('muscle') || lowerText.includes('bulk')) {
      return 'muscle_gain';
    }
    return 'maintenance';
  }

  private extractWorkoutType(text: string): string {
    const lowerText = text.toLowerCase();
    if (lowerText.includes('strength') || lowerText.includes('weight')) {
      return 'strength';
    }
    if (lowerText.includes('cardio') || lowerText.includes('running')) {
      return 'cardio';
    }
    if (lowerText.includes('flexibility') || lowerText.includes('yoga')) {
      return 'flexibility';
    }
    return 'mixed';
  }

  // Enhanced helper methods for meal planning
  private extractDate(message: string): string | null {
    // Simple date extraction - could be enhanced with more sophisticated parsing
    const dateMatch = message.match(/(\d{4}-\d{2}-\d{2})/);
    if (dateMatch) {
      return dateMatch[1];
    }

    // Check for relative dates
    if (message.toLowerCase().includes('next week')) {
      return this.getNextMondayDate();
    }

    return null;
  }

  private getNextMondayDate(): string {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const daysUntilMonday = dayOfWeek === 0 ? 1 : 8 - dayOfWeek; // 0 = Sunday
    const nextMonday = new Date(today);
    nextMonday.setDate(today.getDate() + daysUntilMonday);
    return nextMonday.toISOString().split('T')[0];
  }

  private extractCalories(message: string): number | undefined {
    const calorieMatch = message.match(/(\d+)\s*calorie/i);
    return calorieMatch ? parseInt(calorieMatch[1], 10) : undefined;
  }

  private extractDietaryPreferences(message: string): string[] {
    const preferences: string[] = [];
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('vegetarian')) {
      preferences.push('vegetarian');
    }
    if (lowerMessage.includes('vegan')) {
      preferences.push('vegan');
    }
    if (
      lowerMessage.includes('gluten-free') ||
      lowerMessage.includes('gluten free')
    ) {
      preferences.push('gluten-free');
    }
    if (
      lowerMessage.includes('dairy-free') ||
      lowerMessage.includes('dairy free')
    ) {
      preferences.push('dairy-free');
    }
    if (lowerMessage.includes('keto') || lowerMessage.includes('ketogenic')) {
      preferences.push('keto');
    }
    if (lowerMessage.includes('paleo')) {
      preferences.push('paleo');
    }
    if (
      lowerMessage.includes('high-protein') ||
      lowerMessage.includes('high protein')
    ) {
      preferences.push('high-protein');
    }
    if (
      lowerMessage.includes('low-carb') ||
      lowerMessage.includes('low carb')
    ) {
      preferences.push('low-carb');
    }
    if (lowerMessage.includes('mediterranean')) {
      preferences.push('mediterranean');
    }

    return preferences;
  }

  private extractPrepTime(message: string): number | undefined {
    const timeMatch = message.match(/(\d+)\s*minute/i);
    if (timeMatch) {
      return parseInt(timeMatch[1], 10);
    }

    // Check for common time expressions
    if (
      message.toLowerCase().includes('quick') ||
      message.toLowerCase().includes('fast')
    ) {
      return 30;
    }
    if (
      message.toLowerCase().includes('slow') ||
      message.toLowerCase().includes('long')
    ) {
      return 120;
    }

    return undefined;
  }

  private extractSkillLevel(
    message: string
  ): 'beginner' | 'intermediate' | 'advanced' | undefined {
    const lowerMessage = message.toLowerCase();

    if (
      lowerMessage.includes('beginner') ||
      lowerMessage.includes('easy') ||
      lowerMessage.includes('simple')
    ) {
      return 'beginner';
    }
    if (
      lowerMessage.includes('advanced') ||
      lowerMessage.includes('expert') ||
      lowerMessage.includes('complex')
    ) {
      return 'advanced';
    }
    if (
      lowerMessage.includes('intermediate') ||
      lowerMessage.includes('medium')
    ) {
      return 'intermediate';
    }

    return undefined;
  }

  private extractSearchQuery(message: string): string | undefined {
    // Extract the main search terms from recipe requests
    const recipePatterns = [
      /recipe for (.+)/i,
      /find (.+) recipe/i,
      /cook (.+)/i,
      /make (.+)/i,
    ];

    for (const pattern of recipePatterns) {
      const match = message.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return undefined;
  }

  private extractCuisine(message: string): string | undefined {
    const cuisines = [
      'italian',
      'chinese',
      'mexican',
      'indian',
      'thai',
      'japanese',
      'korean',
      'french',
      'mediterranean',
      'american',
      'greek',
      'spanish',
      'vietnamese',
      'middle eastern',
      'african',
      'caribbean',
      'german',
      'british',
      'asian',
    ];

    const lowerMessage = message.toLowerCase();
    for (const cuisine of cuisines) {
      if (lowerMessage.includes(cuisine)) {
        return cuisine;
      }
    }

    return undefined;
  }

  private extractMealType(
    message: string
  ): 'breakfast' | 'lunch' | 'dinner' | 'snack' | 'dessert' | undefined {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('breakfast')) {
      return 'breakfast';
    }
    if (lowerMessage.includes('lunch')) {
      return 'lunch';
    }
    if (lowerMessage.includes('dinner') || lowerMessage.includes('supper')) {
      return 'dinner';
    }
    if (lowerMessage.includes('snack')) {
      return 'snack';
    }
    if (lowerMessage.includes('dessert') || lowerMessage.includes('sweet')) {
      return 'dessert';
    }

    return undefined;
  }

  private extractTimeframe(text: string): string {
    const lowerText = text.toLowerCase();
    if (lowerText.includes('week')) {
      return 'week';
    }
    if (lowerText.includes('month')) {
      return 'month';
    }
    if (lowerText.includes('quarter')) {
      return 'quarter';
    }
    if (lowerText.includes('year')) {
      return 'year';
    }
    return 'month';
  }

  private extractExerciseName(text: string): string {
    const exercises = [
      'squats',
      'deadlifts',
      'push-ups',
      'pull-ups',
      'bench press',
      'lunges',
    ];
    const lowerText = text.toLowerCase();

    for (const exercise of exercises) {
      if (lowerText.includes(exercise)) {
        return exercise;
      }
    }

    return '';
  }

  private extractReason(text: string): string {
    const lowerText = text.toLowerCase();
    if (lowerText.includes('knee')) {
      return 'knee issue';
    }
    if (lowerText.includes('back')) {
      return 'back issue';
    }
    if (lowerText.includes('shoulder')) {
      return 'shoulder issue';
    }
    if (lowerText.includes('injury')) {
      return 'injury';
    }
    if (lowerText.includes('equipment')) {
      return 'equipment limitation';
    }
    return 'limitation';
  }

  private extractFoodItems(text: string): string[] {
    // Simple extraction - in production, this would be more sophisticated
    const commonFoods = [
      'chicken',
      'rice',
      'vegetables',
      'salad',
      'eggs',
      'oatmeal',
      'yogurt',
      'fruit',
    ];
    const lowerText = text.toLowerCase();

    return commonFoods.filter(food => lowerText.includes(food));
  }

  private generateGeneralResponse(
    _message: string,
    _history: Array<{ role: string; content: string }>
  ): string {
    const responses = [
      "I'm here to help you with your fitness and nutrition goals! I can create meal plans, design workouts, analyze your progress, and much more. What would you like to work on today?",
      'Great question! I can assist you with meal planning, workout routines, progress tracking, and logging your activities. How can I support your health journey?',
      "I'm your AI fitness coach! I can help you plan meals, create workouts, track progress, generate grocery lists, and find exercise alternatives. What's on your mind?",
      "Hello! I'm here to support your fitness and nutrition journey. I can create personalized plans, analyze your progress, and help you stay on track. What would you like to focus on?",
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  }

  private isProfileCompletionQuestion(lowerMessage: string): boolean {
    const profileKeywords = [
      'fitness goal',
      'dietary preference',
      'experience level',
      'injury',
      'injuries',
      'what are my',
      'tell me about',
      'do i have',
      "what's my",
    ];
    return profileKeywords.some(keyword => lowerMessage.includes(keyword));
  }

  private handleProfileCompletionResponse(
    message: string,
    lowerMessage: string
  ): AIResponse {
    if (lowerMessage.includes('fitness goal')) {
      return {
        content:
          "Great question! Let's talk about your fitness goals. 🎯\n\nFitness goals help me create the perfect workout plan for you. Here are some common goals:\n\n• **Weight Loss** - Burn calories and reduce body fat\n• **Muscle Gain** - Build strength and muscle mass\n• **General Fitness** - Improve overall health and endurance\n• **Athletic Performance** - Train for specific sports or activities\n• **Maintenance** - Stay healthy and maintain current fitness\n\nWhat's your primary fitness goal? You can choose one from above or tell me something specific you'd like to achieve!",
        isComplete: true,
      };
    }

    if (
      lowerMessage.includes('dietary preference') ||
      lowerMessage.includes('diet preference')
    ) {
      return {
        content:
          "Let's set up your dietary preferences! 🥗\n\nKnowing your dietary preferences helps me suggest meals that you'll actually enjoy and that fit your lifestyle. Here are some options:\n\n• **No restrictions** - I eat everything\n• **Vegetarian** - No meat, but dairy and eggs are okay\n• **Vegan** - Plant-based only\n• **Keto/Low-carb** - High fat, low carbohydrate\n• **Mediterranean** - Fish, vegetables, healthy fats\n• **Paleo** - Whole foods, no processed items\n• **Gluten-free** - No wheat or gluten-containing foods\n\nDo any of these match your preferences? Or do you have specific foods you avoid or love?",
        isComplete: true,
      };
    }

    if (lowerMessage.includes('experience level')) {
      return {
        content:
          "Perfect! Let's determine your fitness experience level. 💪\n\nThis helps me design workouts that are challenging but safe for you:\n\n• **Beginner** - New to exercise or returning after a long break\n• **Intermediate** - Been working out regularly for 6+ months\n• **Advanced** - 2+ years of consistent training experience\n\nYour experience level affects:\n- Exercise complexity and intensity\n- How often you should work out\n- Recovery time between sessions\n- Progression speed\n\nWhich level best describes you? Don't worry about being exact - we can always adjust as we go!",
        isComplete: true,
      };
    }

    if (lowerMessage.includes('injury') || lowerMessage.includes('injuries')) {
      return {
        content:
          "Thanks for bringing this up - it's really important for your safety! 🩹\n\nKnowing about any injuries or physical limitations helps me:\n- Avoid exercises that might cause pain\n- Suggest modifications and alternatives\n- Focus on movements that support your recovery\n- Ensure your workouts are safe and effective\n\nDo you have any current injuries, past injuries that still bother you, or areas of your body that need special attention?\n\nCommon areas include:\n• Back or spine issues\n• Knee problems\n• Shoulder limitations\n• Wrist or elbow pain\n• Hip or ankle concerns\n\nIf you're injury-free, just let me know that too! 😊",
        isComplete: true,
      };
    }

    // Default response for profile-related questions
    return {
      content:
        "I'd love to help you with that! To give you the best personalized recommendations, I need to learn more about your fitness goals, dietary preferences, experience level, and any health considerations.\n\nWould you like to start with any of these topics? Just ask me about:\n• Your fitness goals\n• Dietary preferences\n• Experience level\n• Any injuries or health concerns\n\nOnce I know more about you, I can create amazing personalized workout and meal plans! 🎯",
      isComplete: true,
    };
  }

  async executeToolCall(toolCall: ToolCall): Promise<any> {
    const tool = toolRegistry[toolCall.name];
    if (!tool) {
      throw new Error(`Tool ${toolCall.name} not found`);
    }

    try {
      const result = await tool.execute(toolCall.parameters || {});
      return result;
    } catch (error) {
      console.error(`Tool execution error for ${toolCall.name}:`, error);
      throw error;
    }
  }

  private getAvailableTools() {
    // Convert our tool registry to Gemini function calling format
    return Object.values(toolRegistry).map(tool => ({
      function_declarations: [
        {
          name: tool.name,
          description: tool.description,
          parameters: tool.parameters,
        },
      ],
    }));
  }

  private async parseAIResponse(
    content: string,
    originalMessage: string
  ): Promise<AIResponse> {
    // Check if the AI response suggests using tools based on content analysis
    const lowerContent = content.toLowerCase();
    const lowerMessage = originalMessage.toLowerCase();

    // If the AI mentions creating plans or using tools, trigger our tool logic
    const toolCalls: ToolCall[] = [];

    // Questionnaire detection
    if (
      lowerContent.includes('questionnaire') ||
      lowerContent.includes('start the questionnaire') ||
      lowerContent.includes('using this tool') ||
      (lowerContent.includes('basic profile') &&
        lowerContent.includes('questionnaire'))
    ) {
      // Determine questionnaire type from context
      let questionnaireType = 'basic'; // default
      if (
        lowerContent.includes('nutrition') ||
        lowerMessage.includes('nutrition')
      ) {
        questionnaireType = 'nutrition';
      } else if (
        lowerContent.includes('exercise') ||
        lowerMessage.includes('exercise') ||
        lowerMessage.includes('workout')
      ) {
        questionnaireType = 'exercise';
      }

      toolCalls.push({
        id: `tool_${Date.now()}`,
        name: 'start_questionnaire',
        status: 'pending',
        parameters: { questionnaireType },
      });
    }

    // Meal planning detection
    else if (
      (lowerContent.includes('meal plan') ||
        lowerContent.includes('create') ||
        lowerContent.includes('generate')) &&
      (lowerMessage.includes('meal plan') || lowerMessage.includes('diet plan'))
    ) {
      const days = this.extractNumber(originalMessage, 'day') || 7;
      const goal = this.extractGoal(originalMessage);

      toolCalls.push({
        id: `tool_${Date.now()}`,
        name: 'generate_meal_plan',
        status: 'pending',
        parameters: { days, goal },
      });
    }

    // Workout planning detection
    else if (
      (lowerContent.includes('workout') ||
        lowerContent.includes('exercise') ||
        lowerContent.includes('training')) &&
      (lowerMessage.includes('workout plan') ||
        lowerMessage.includes('exercise plan'))
    ) {
      const days = this.extractNumber(originalMessage, 'day') || 5;
      const type = this.extractWorkoutType(originalMessage);

      toolCalls.push({
        id: `tool_${Date.now()}`,
        name: 'generate_workout_plan',
        status: 'pending',
        parameters: { days, type },
      });
    }

    return {
      content,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      isComplete: true,
    };
  }
}

export const aiChatService = new AIChatService();

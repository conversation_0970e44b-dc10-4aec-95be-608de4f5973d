import { supabase } from '../../../lib/supabase';
import { ChatMessage, ChatSession } from '../types';

export interface DatabaseChatSession {
  id: string;
  user_id: string;
  title: string;
  created_at: string;
  updated_at: string;
}

export interface DatabaseChatMessage {
  id: string;
  session_id: string;
  user_id: string;
  content: string;
  role: 'user' | 'assistant';
  tool_calls?: any;
  message_order: number;
  created_at: string;
}

class ChatDatabaseService {
  // ============================================================================
  // SESSION MANAGEMENT
  // ============================================================================

  async createSession(title: string = 'New Chat'): Promise<ChatSession | null> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('chat_sessions')
        .insert({
          user_id: user.id,
          title,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return this.mapDatabaseSessionToLocal(data);
    } catch (error) {
      console.error('Error creating chat session:', error);
      return null;
    }
  }

  async getUserSessions(): Promise<ChatSession[]> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        return [];
      }

      const { data, error } = await supabase
        .from('chat_sessions')
        .select('*')
        .eq('user_id', user.id)
        .order('updated_at', { ascending: false });

      if (error) {
        throw error;
      }

      // Get message counts for each session
      const sessionsWithMessages = await Promise.all(
        data.map(async session => {
          const messages = await this.getSessionMessages(session.id);
          return {
            ...this.mapDatabaseSessionToLocal(session),
            messages,
          };
        })
      );

      return sessionsWithMessages;
    } catch (error) {
      console.error('Error fetching user sessions:', error);
      return [];
    }
  }

  async updateSessionTitle(sessionId: string, title: string): Promise<boolean> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        return false;
      }

      const { error } = await supabase
        .from('chat_sessions')
        .update({ title })
        .eq('id', sessionId)
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }
      return true;
    } catch (error) {
      console.error('Error updating session title:', error);
      return false;
    }
  }

  async deleteSession(sessionId: string): Promise<boolean> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        return false;
      }

      // Delete session (messages will be deleted automatically due to CASCADE)
      const { error } = await supabase
        .from('chat_sessions')
        .delete()
        .eq('id', sessionId)
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }
      return true;
    } catch (error) {
      console.error('Error deleting session:', error);
      return false;
    }
  }

  // ============================================================================
  // MESSAGE MANAGEMENT
  // ============================================================================

  async saveMessage(
    sessionId: string,
    message: Omit<ChatMessage, 'id' | 'timestamp'>,
    messageOrder: number
  ): Promise<ChatMessage | null> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Let the database generate the ID
      const { data, error } = await supabase
        .from('chat_messages')
        .insert({
          session_id: sessionId,
          user_id: user.id,
          content: message.content,
          role: message.role,
          tool_calls: message.toolCalls || null,
          message_order: messageOrder,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return this.mapDatabaseMessageToLocal(data);
    } catch (error) {
      console.error('Error saving message:', error);
      return null;
    }
  }

  async getSessionMessages(sessionId: string): Promise<ChatMessage[]> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        return [];
      }

      const { data, error } = await supabase
        .from('chat_messages')
        .select('*')
        .eq('session_id', sessionId)
        .eq('user_id', user.id)
        .order('message_order', { ascending: true });

      if (error) {
        throw error;
      }

      return data.map(this.mapDatabaseMessageToLocal);
    } catch (error) {
      console.error('Error fetching session messages:', error);
      return [];
    }
  }

  async updateMessage(
    messageId: string,
    updates: Partial<Pick<ChatMessage, 'content' | 'toolCalls'>>
  ): Promise<boolean> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        return false;
      }

      const updateData: any = {};
      if (updates.content !== undefined) {
        updateData.content = updates.content;
      }
      if (updates.toolCalls !== undefined) {
        updateData.tool_calls = updates.toolCalls;
      }

      const { error } = await supabase
        .from('chat_messages')
        .update(updateData)
        .eq('id', messageId)
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }
      return true;
    } catch (error) {
      console.error('Error updating message:', error);
      return false;
    }
  }

  // ============================================================================
  // CONVERSATION HISTORY
  // ============================================================================

  async getConversationHistory(
    sessionId: string,
    limit: number = 50
  ): Promise<Array<{ role: 'user' | 'assistant'; content: string }>> {
    try {
      const messages = await this.getSessionMessages(sessionId);

      // Take the most recent messages up to the limit
      const recentMessages = messages.slice(-limit);

      return recentMessages.map(msg => ({
        role: msg.role,
        content: msg.content,
      }));
    } catch (error) {
      console.error('Error getting conversation history:', error);
      return [];
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private mapDatabaseSessionToLocal(
    dbSession: DatabaseChatSession
  ): ChatSession {
    return {
      id: dbSession.id,
      title: dbSession.title,
      messages: [], // Will be loaded separately
      createdAt: new Date(dbSession.created_at),
      updatedAt: new Date(dbSession.updated_at),
    };
  }

  private mapDatabaseMessageToLocal(
    dbMessage: DatabaseChatMessage
  ): ChatMessage {
    return {
      id: dbMessage.id,
      content: dbMessage.content,
      role: dbMessage.role,
      timestamp: new Date(dbMessage.created_at),
      toolCalls: dbMessage.tool_calls || undefined,
    };
  }
}

export const chatDatabaseService = new ChatDatabaseService();

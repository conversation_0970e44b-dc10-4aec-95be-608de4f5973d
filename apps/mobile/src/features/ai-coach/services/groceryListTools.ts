import { supabase } from '../../../lib/supabase';

export interface GroceryListRequest {
  meal_plan_id?: string;
  recipe_ids?: string[];
  servings_multiplier?: number;
  organize_by_category?: boolean;
  exclude_pantry_staples?: boolean;
  store_preference?: string;
  week_start_date?: string;
}

export interface GroceryItem {
  name: string;
  amount: number;
  unit: string;
  category: string;
  estimated_cost?: number;
  is_pantry_staple?: boolean;
  notes?: string;
}

// Smart grocery list generator
export const generateGroceryList = async (params: GroceryListRequest) => {
  try {
    const {
      meal_plan_id,
      recipe_ids = [],
      servings_multiplier = 1,
      organize_by_category = true,
      exclude_pantry_staples = false,
      store_preference,
      week_start_date,
    } = params;

    // Get current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();
    if (userError || !user) {
      return {
        success: false,
        error: 'User not authenticated',
      };
    }

    let recipesToProcess: any[] = [];

    // Get recipes from meal plan or direct recipe IDs
    if (meal_plan_id) {
      const { data: mealPlan, error: mealPlanError } = await supabase
        .from('meal_plans')
        .select('recipes')
        .eq('id', meal_plan_id)
        .eq('user_id', user.id)
        .single();

      if (mealPlanError) {
        return {
          success: false,
          error: 'Failed to fetch meal plan',
        };
      }

      // Extract recipe IDs from meal plan
      const mealPlanRecipes = mealPlan.recipes || [];
      const recipeIds = mealPlanRecipes
        .map((meal: any) => meal.recipe?.id)
        .filter(Boolean);

      if (recipeIds.length > 0) {
        const { data: recipes, error: recipesError } = await supabase
          .from('recipes')
          .select('id, name, ingredients, servings, pantry_staples')
          .in('id', recipeIds);

        if (recipesError) {
          return {
            success: false,
            error: 'Failed to fetch recipes',
          };
        }

        recipesToProcess = recipes || [];
      }
    } else if (recipe_ids.length > 0) {
      const { data: recipes, error: recipesError } = await supabase
        .from('recipes')
        .select('id, name, ingredients, servings, pantry_staples')
        .in('id', recipe_ids);

      if (recipesError) {
        return {
          success: false,
          error: 'Failed to fetch recipes',
        };
      }

      recipesToProcess = recipes || [];
    } else {
      return {
        success: false,
        error: 'Either meal_plan_id or recipe_ids must be provided',
      };
    }

    if (recipesToProcess.length === 0) {
      return {
        success: false,
        error: 'No recipes found to generate grocery list',
      };
    }

    // Aggregate ingredients from all recipes
    const aggregatedIngredients = aggregateIngredients(
      recipesToProcess,
      servings_multiplier
    );

    // Filter out pantry staples if requested
    let finalIngredients = aggregatedIngredients;
    if (exclude_pantry_staples) {
      finalIngredients = aggregatedIngredients.filter(
        item => !item.is_pantry_staple
      );
    }

    // Organize by category if requested
    if (organize_by_category) {
      finalIngredients.sort((a, b) => {
        if (a.category !== b.category) {
          return a.category.localeCompare(b.category);
        }
        return a.name.localeCompare(b.name);
      });
    }

    // Calculate estimated total cost
    const estimatedTotalCost = finalIngredients.reduce((total, item) => {
      return total + (item.estimated_cost || 0);
    }, 0);

    // Save grocery list to database
    const { data: savedGroceryList, error: saveError } = await supabase
      .from('grocery_lists')
      .insert({
        user_id: user.id,
        meal_plan_id: meal_plan_id || null,
        name: `Grocery List - ${week_start_date || new Date().toISOString().split('T')[0]}`,
        total_estimated_cost: estimatedTotalCost,
        store_preference: store_preference || null,
        status: 'active',
      })
      .select()
      .single();

    if (saveError) {
      console.error('Grocery list save error:', saveError);
      // Continue anyway, we'll return the generated list
    }

    // Save individual grocery list items
    if (savedGroceryList) {
      const groceryListItems = finalIngredients.map((item, index) => ({
        grocery_list_id: savedGroceryList.id,
        ingredient_name: item.name,
        quantity: item.amount,
        unit: item.unit,
        category: item.category,
        estimated_cost: item.estimated_cost,
        is_purchased: false,
        sort_order: index,
        notes: item.notes,
      }));

      const { error: itemsError } = await supabase
        .from('grocery_list_items')
        .insert(groceryListItems);

      if (itemsError) {
        console.error('Grocery list items save error:', itemsError);
      }
    }

    // Group by category for better presentation
    const categorizedItems = organize_by_category
      ? groupByCategory(finalIngredients)
      : { 'All Items': finalIngredients };

    return {
      success: true,
      data: {
        grocery_list_id: savedGroceryList?.id,
        total_items: finalIngredients.length,
        estimated_total_cost: estimatedTotalCost,
        recipes_included: recipesToProcess.length,
        categorized_items: categorizedItems,
        shopping_tips: generateShoppingTips(finalIngredients, store_preference),
        meal_prep_suggestions: generateMealPrepSuggestions(recipesToProcess),
      },
    };
  } catch (error) {
    console.error('Grocery list generator error:', error);
    return {
      success: false,
      error: 'Failed to generate grocery list',
    };
  }
};

// Update grocery list item status
export const updateGroceryItem = async (
  item_id: string,
  params: {
    is_purchased?: boolean;
    actual_cost?: number;
    notes?: string;
  }
) => {
  try {
    // Update the grocery list item
    const { data, error } = await supabase
      .from('grocery_list_items')
      .update(params)
      .eq('id', item_id)
      .select()
      .single();

    if (error) {
      console.error('Update grocery item error:', error);
      return {
        success: false,
        error: 'Failed to update grocery item',
      };
    }

    return {
      success: true,
      data: {
        updated_item: data,
        list_completed: Boolean(data?.is_purchased),
      },
    };
  } catch (error) {
    console.error('Update grocery item error:', error);
    return {
      success: false,
      error: 'Failed to update grocery item',
    };
  }
};

// Helper function to aggregate ingredients from multiple recipes
function aggregateIngredients(
  recipes: any[],
  servingsMultiplier: number
): GroceryItem[] {
  const ingredientMap = new Map<string, GroceryItem>();

  recipes.forEach(recipe => {
    const ingredients = recipe.ingredients || [];
    const recipeServings = recipe.servings || 1;
    const scaleFactor = servingsMultiplier / recipeServings;

    ingredients.forEach((ingredient: any) => {
      const key = ingredient.name.toLowerCase();
      const scaledAmount = (ingredient.amount || 1) * scaleFactor;

      if (ingredientMap.has(key)) {
        const existing = ingredientMap.get(key)!;
        // If same unit, add amounts; otherwise, create separate entries
        if (existing.unit === ingredient.unit) {
          existing.amount += scaledAmount;
        } else {
          // Create a new key for different unit
          const newKey = `${key}_${ingredient.unit}`;
          ingredientMap.set(newKey, {
            name: ingredient.name,
            amount: scaledAmount,
            unit: ingredient.unit,
            category: ingredient.category || 'other',
            is_pantry_staple: ingredient.optional || false,
            estimated_cost: estimateIngredientCost(
              ingredient.name,
              scaledAmount,
              ingredient.unit
            ),
          });
        }
      } else {
        ingredientMap.set(key, {
          name: ingredient.name,
          amount: scaledAmount,
          unit: ingredient.unit,
          category: ingredient.category || 'other',
          is_pantry_staple: ingredient.optional || false,
          estimated_cost: estimateIngredientCost(
            ingredient.name,
            scaledAmount,
            ingredient.unit
          ),
        });
      }
    });
  });

  return Array.from(ingredientMap.values());
}

// Helper function to group items by category
function groupByCategory(items: GroceryItem[]): Record<string, GroceryItem[]> {
  return items.reduce(
    (groups, item) => {
      const category = item.category || 'Other';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(item);
      return groups;
    },
    {} as Record<string, GroceryItem[]>
  );
}

// Helper function to estimate ingredient cost
function estimateIngredientCost(
  name: string,
  amount: number,
  unit: string
): number {
  // Simple cost estimation - in production, this could use a pricing API
  const baseCosts: Record<string, number> = {
    // Proteins (per lb/kg)
    chicken: 4.5,
    beef: 8.0,
    salmon: 12.0,
    eggs: 3.0,

    // Produce (per lb)
    tomatoes: 2.5,
    onions: 1.5,
    garlic: 4.0,
    lettuce: 2.0,

    // Pantry items
    rice: 2.0,
    pasta: 1.5,
    'olive oil': 8.0,
    salt: 1.0,
  };

  const lowerName = name.toLowerCase();
  const basePrice = baseCosts[lowerName] || 3.0; // Default price

  // Simple unit conversion and pricing
  let estimatedCost = basePrice * amount;

  // Adjust for different units
  if (unit.includes('cup') || unit.includes('tbsp') || unit.includes('tsp')) {
    estimatedCost *= 0.1; // Smaller quantities
  }

  return Math.round(estimatedCost * 100) / 100; // Round to 2 decimal places
}

// Helper function to generate shopping tips
function generateShoppingTips(
  items: GroceryItem[],
  storePreference?: string
): string[] {
  const tips: string[] = [];

  // Check for seasonal items
  const produceItems = items.filter(item => item.category === 'produce');
  if (produceItems.length > 0) {
    tips.push(
      'Check for seasonal produce to save money and get better quality'
    );
  }

  // Check for bulk items
  const pantryItems = items.filter(item => item.category === 'pantry');
  if (pantryItems.length > 3) {
    tips.push('Consider buying pantry staples in bulk to save money');
  }

  // Store-specific tips
  if (storePreference) {
    tips.push(`Shop the perimeter of ${storePreference} first for fresh items`);
  }

  tips.push('Bring reusable bags and check your pantry before shopping');

  return tips;
}

// Helper function to generate meal prep suggestions
function generateMealPrepSuggestions(recipes: any[]): string[] {
  const suggestions: string[] = [];

  const mealPrepFriendly = recipes.filter(r => r.meal_prep_friendly);
  if (mealPrepFriendly.length > 0) {
    suggestions.push(
      `${mealPrepFriendly.length} of your recipes are meal-prep friendly`
    );
  }

  suggestions.push('Wash and chop vegetables when you get home from shopping');
  suggestions.push(
    'Cook grains and proteins in batches to save time during the week'
  );

  return suggestions;
}

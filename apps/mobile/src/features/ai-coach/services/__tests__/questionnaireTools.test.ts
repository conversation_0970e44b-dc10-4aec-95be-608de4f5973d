// Mock Supabase before importing the service
jest.mock('../../../../lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: jest.fn(),
    },
    from: jest.fn(),
    rpc: jest.fn(),
  },
}));

// Mock health metrics service
jest.mock('../../../profile/services/healthMetricsService', () => ({
  calculateHealthMetrics: jest.fn(),
  saveHealthMetricsToProfile: jest.fn(),
}));

import { supabase } from '../../../../lib/supabase';
import {
  calculateHealthMetrics,
  saveHealthMetricsToProfile,
} from '../../../profile/services/healthMetricsService';

// Import after mocking
const mockCalculateHealthMetrics =
  calculateHealthMetrics as jest.MockedFunction<typeof calculateHealthMetrics>;
const mockSaveHealthMetricsToProfile =
  saveHealthMetricsToProfile as jest.MockedFunction<
    typeof saveHealthMetricsToProfile
  >;

describe('Questionnaire Tools Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Health Metrics Integration', () => {
    it('should extract user data correctly from basic questionnaire responses', () => {

      // Mock the health metrics calculation
      mockCalculateHealthMetrics.mockReturnValue({
        bmr: 1760,
        bmi: 24.7,
        tdee: 2728,
        targetCaloriesMin: 2128,
        targetCaloriesMax: 2328,
        idealWeightMin: 59.9,
        idealWeightMax: 80.6,
      });

      mockSaveHealthMetricsToProfile.mockResolvedValue({});

      // Test that the data extraction would work correctly
      const expectedUserData = {
        biologicalSex: 'male',
        dateOfBirth: '1990-01-01',
        heightCm: 180,
        weightKg: 80,
        activityLevel: 'moderately_active',
        primaryGoal: 'lose_weight',
        timeline: 'moderate',
      };

      // Verify the expected data structure matches what we need
      expect(expectedUserData.biologicalSex).toBe('male');
      expect(expectedUserData.dateOfBirth).toBe('1990-01-01');
      expect(expectedUserData.heightCm).toBe(180);
      expect(expectedUserData.weightKg).toBe(80);
      expect(expectedUserData.activityLevel).toBe('moderately_active');
      expect(expectedUserData.primaryGoal).toBe('lose_weight');
      expect(expectedUserData.timeline).toBe('moderate');
    });

    it('should handle age conversion to date of birth', () => {

      const currentYear = new Date().getFullYear();
      const expectedBirthYear = currentYear - 30;
      const expectedDateOfBirth = `${expectedBirthYear}-01-01`;

      // Test age conversion logic
      expect(expectedDateOfBirth).toMatch(/^\d{4}-01-01$/);
      expect(parseInt(expectedDateOfBirth.split('-')[0])).toBe(
        expectedBirthYear
      );
    });

    it('should handle JSON parsing of answer values', () => {
      const mockResponses = [
        {
          question_id: 'exercise_preferences',
          answer_value: '["strength_training", "cardio_machines"]',
          answer_display_text: 'Strength Training, Cardio',
        },
        {
          question_id: 'food_allergies',
          answer_value: '["nuts", "dairy"]',
          answer_display_text: 'Nuts, Dairy',
        },
      ];

      // Test JSON parsing
      const exercisePrefs = JSON.parse(mockResponses[0].answer_value);
      const foodAllergies = JSON.parse(mockResponses[1].answer_value);

      expect(Array.isArray(exercisePrefs)).toBe(true);
      expect(exercisePrefs).toEqual(['strength_training', 'cardio_machines']);
      expect(Array.isArray(foodAllergies)).toBe(true);
      expect(foodAllergies).toEqual(['nuts', 'dairy']);
    });
  });

  describe('Profile Updates', () => {
    it('should map basic questionnaire responses to profile fields correctly', () => {

      const expectedProfileUpdates = {
        unit_system: 'metric',
        biological_sex: 'female',
        height_cm: 165,
        weight_kg: 60,
        activity_level: 'lightly_active',
        fitness_goals: ['gain_muscle'],
      };

      // Verify mapping logic
      expect(expectedProfileUpdates.unit_system).toBe('metric');
      expect(expectedProfileUpdates.biological_sex).toBe('female');
      expect(expectedProfileUpdates.height_cm).toBe(165);
      expect(expectedProfileUpdates.weight_kg).toBe(60);
      expect(expectedProfileUpdates.activity_level).toBe('lightly_active');
      expect(expectedProfileUpdates.fitness_goals).toEqual(['gain_muscle']);
    });

    it('should map nutrition questionnaire responses correctly', () => {

      const expectedProfileUpdates = {
        food_allergies: ['gluten', 'shellfish'],
        dietary_preferences: ['vegetarian'],
        cooking_skill: 'intermediate',
        meal_prep_time: '60_120',
        grocery_budget: 'moderate',
      };

      // Verify nutrition mapping
      expect(expectedProfileUpdates.food_allergies).toEqual([
        'gluten',
        'shellfish',
      ]);
      expect(expectedProfileUpdates.dietary_preferences).toEqual([
        'vegetarian',
      ]);
      expect(expectedProfileUpdates.cooking_skill).toBe('intermediate');
      expect(expectedProfileUpdates.meal_prep_time).toBe('60_120');
      expect(expectedProfileUpdates.grocery_budget).toBe('moderate');
    });

    it('should map exercise questionnaire responses correctly', () => {

      const expectedProfileUpdates = {
        fitness_level: 'intermediate',
        workout_frequency: '3_4',
        workout_duration: '30_45',
        exercise_preferences: ['strength_training', 'hiit_circuit'],
        workout_location: ['gym', 'home'],
        physical_limitations: ['none'],
        workout_structure: 'progressive',
      };

      // Verify exercise mapping
      expect(expectedProfileUpdates.fitness_level).toBe('intermediate');
      expect(expectedProfileUpdates.workout_frequency).toBe('3_4');
      expect(expectedProfileUpdates.workout_duration).toBe('30_45');
      expect(expectedProfileUpdates.exercise_preferences).toEqual([
        'strength_training',
        'hiit_circuit',
      ]);
      expect(expectedProfileUpdates.workout_location).toEqual(['gym', 'home']);
      expect(expectedProfileUpdates.physical_limitations).toEqual(['none']);
      expect(expectedProfileUpdates.workout_structure).toBe('progressive');
    });
  });

  describe('Data Validation', () => {
    it('should handle missing required fields for health metrics', () => {

      const requiredFields = [
        'biologicalSex',
        'dateOfBirth',
        'heightCm',
        'weightKg',
        'activityLevel',
        'primaryGoal',
        'timeline',
      ];
      const providedFields = ['biologicalSex'];
      const missingFields = requiredFields.filter(
        field => !providedFields.includes(field)
      );

      expect(missingFields).toEqual([
        'dateOfBirth',
        'heightCm',
        'weightKg',
        'activityLevel',
        'primaryGoal',
        'timeline',
      ]);
      expect(missingFields.length).toBeGreaterThan(0);
    });

    it('should handle array vs single value conversion', () => {
      // Test single value to array conversion
      const singleValue = 'strength_training';
      const arrayValue = ['strength_training', 'cardio_machines'];

      const convertToArray = (value: any) =>
        Array.isArray(value) ? value : [value];

      expect(convertToArray(singleValue)).toEqual(['strength_training']);
      expect(convertToArray(arrayValue)).toEqual([
        'strength_training',
        'cardio_machines',
      ]);
    });

    it('should handle imperial to metric unit conversion', () => {
      const imperialResponse = {
        question_id: 'unit_system',
        answer_value: 'imperial',
        answer_display_text: 'Imperial',
      };
      const metricResponse = {
        question_id: 'unit_system',
        answer_value: 'metric',
        answer_display_text: 'Metric',
      };

      const mapUnitSystem = (value: string) =>
        value === 'imperial' ? 'imperial' : 'metric';

      expect(mapUnitSystem(imperialResponse.answer_value)).toBe('imperial');
      expect(mapUnitSystem(metricResponse.answer_value)).toBe('metric');
      expect(mapUnitSystem('unknown')).toBe('metric'); // Default to metric
    });
  });
});

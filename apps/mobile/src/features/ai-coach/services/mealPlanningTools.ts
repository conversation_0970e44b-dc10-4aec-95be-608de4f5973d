import { supabase } from '../../../lib/supabase';

// Enhanced meal planning tools that use the new recipe database

export interface EnhancedMealPlanRequest {
  week_start_date: string;
  duration_days?: number; // NEW: Allow custom duration
  dietary_preferences?: string[];
  target_calories_per_day?: number;
  target_macros?: {
    protein_grams: number;
    carbs_grams: number;
    fat_grams: number;
  };
  excluded_ingredients?: string[];
  preferred_cuisines?: string[];
  max_prep_time_minutes?: number;
  meals_per_day?: number;
  cooking_skill_level?: 'beginner' | 'intermediate' | 'advanced';
  use_persona_data?: boolean; // NEW: Whether to use user's persona data
  update_existing_plan?: string; // NEW: ID of existing plan to update
}

export interface RecipeFinderRequest {
  search_query?: string;
  cuisine_type?: string;
  dietary_tags?: string[];
  max_prep_time?: number;
  max_total_time?: number;
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
  meal_type?: 'breakfast' | 'lunch' | 'dinner' | 'snack' | 'dessert';
  min_protein?: number;
  max_calories?: number;
  available_ingredients?: string[];
  exclude_allergens?: string[];
  budget_max?: number;
  limit?: number;
}

export interface GroceryListRequest {
  meal_plan_id: string;
  servings_multiplier?: number;
  organize_by_category?: boolean;
  exclude_pantry_staples?: boolean;
  store_preference?: string;
}

// Enhanced meal planner using the new database with persona integration
export const enhancedMealPlanner = async (params: EnhancedMealPlanRequest) => {
  try {
    const {
      week_start_date,
      duration_days = 7,
      dietary_preferences = [],
      target_calories_per_day,
      target_macros,
      excluded_ingredients: _excluded_ingredients = [], // eslint-disable-line @typescript-eslint/no-unused-vars
      preferred_cuisines = [],
      max_prep_time_minutes,
      meals_per_day = 3,
      cooking_skill_level,
      use_persona_data = true,
      update_existing_plan,
    } = params;

    // Get current user
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();
    if (userError || !user) {
      return {
        success: false,
        error: 'User not authenticated',
      };
    }

    // Get user persona data for enhanced meal planning
    let userPersonaData: any = null;
    let userHealthMetrics: any = null;

    if (use_persona_data) {
      // Get user profile with health metrics and questionnaire data
      const { data: profileData } = await supabase
        .from('profiles')
        .select(
          `
          *,
          bmr,
          target_calories_min,
          target_calories_max,
          food_allergies,
          cooking_skill,
          meal_prep_time,
          grocery_budget,
          dietary_preferences
        `
        )
        .eq('id', user.id)
        .single();

      userPersonaData = profileData;
      userHealthMetrics = {
        bmr: profileData?.bmr,
        target_calories_min: profileData?.target_calories_min,
        target_calories_max: profileData?.target_calories_max,
      };
    }

    // Get user recipe preferences if they exist
    const { data: userPrefs } = await supabase
      .from('user_recipe_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    // Merge user preferences with request parameters, prioritizing persona data
    const finalDietaryPrefs =
      dietary_preferences.length > 0
        ? dietary_preferences
        : userPersonaData?.dietary_preferences ||
          userPrefs?.dietary_restrictions ||
          [];

    const finalCuisines =
      preferred_cuisines.length > 0
        ? preferred_cuisines
        : userPrefs?.preferred_cuisines || [];

    const finalMaxTime =
      max_prep_time_minutes ||
      (userPersonaData?.meal_prep_time === 'quick'
        ? 30
        : userPersonaData?.meal_prep_time === 'moderate'
          ? 45
          : 60) ||
      userPrefs?.max_prep_time_minutes ||
      60;

    const finalSkillLevel =
      cooking_skill_level ||
      userPersonaData?.cooking_skill ||
      userPrefs?.preferred_difficulty ||
      'intermediate';

    // Calculate target calories using health metrics if available
    const finalTargetCalories =
      target_calories_per_day ||
      (userHealthMetrics?.target_calories_min &&
      userHealthMetrics?.target_calories_max
        ? Math.round(
            (userHealthMetrics.target_calories_min +
              userHealthMetrics.target_calories_max) /
              2
          )
        : null) ||
      userHealthMetrics?.bmr ||
      2000;

    // Add food allergies to excluded ingredients
    const finalExcludedIngredients = [
      ..._excluded_ingredients,
      ...(userPersonaData?.food_allergies || []),
    ];

    // Build the recipe query with compatibility scoring
    let query = supabase
      .from('recipes')
      .select(
        `
        id, name, description, cuisine_type, prep_time_minutes, cook_time_minutes,
        total_time_minutes, difficulty_level, servings, serving_size,
        ingredients, equipment_required, dietary_tags, allergens, meal_types,
        calories_per_serving, protein_grams, carbs_grams, fat_grams, fiber_grams,
        estimated_cost_per_serving, spice_level, flavor_profile, occasion_tags,
        rating_average, meal_prep_friendly, image_url
      `
      )
      .eq('is_active', true)
      .gte('rating_average', 3.5)
      .lte('total_time_minutes', finalMaxTime);

    // Apply dietary filters
    if (finalDietaryPrefs.length > 0) {
      query = query.contains('dietary_tags', finalDietaryPrefs);
    }

    // Apply cuisine filters
    if (finalCuisines.length > 0) {
      query = query.in('cuisine_type', finalCuisines);
    }

    // Apply skill level filter
    const skillLevels =
      finalSkillLevel === 'beginner'
        ? ['beginner']
        : finalSkillLevel === 'intermediate'
          ? ['beginner', 'intermediate']
          : ['beginner', 'intermediate', 'advanced'];

    query = query.in('difficulty_level', skillLevels);

    // Exclude allergens if user has preferences
    if (
      userPrefs?.allergens_to_avoid &&
      userPrefs.allergens_to_avoid.length > 0
    ) {
      // This is a simplified check - in production you'd want more sophisticated allergen filtering
      query = query.not(
        'allergens',
        'cs',
        `{${userPrefs.allergens_to_avoid.join(',')}}`
      );
    }

    const { data: recipes, error: recipesError } = await query.limit(50);

    if (recipesError) {
      console.error('Recipe query error:', recipesError);
      return {
        success: false,
        error: 'Failed to fetch recipes',
      };
    }

    if (!recipes || recipes.length === 0) {
      return {
        success: false,
        error: 'No recipes found matching your criteria',
      };
    }

    // Generate meal plan using AI logic
    const mealPlan = generateOptimalMealPlan(
      recipes,
      meals_per_day,
      finalTargetCalories,
      target_macros,
      duration_days
    );

    // Save or update meal plan in database
    let savedMealPlan: any;

    if (update_existing_plan) {
      // Update existing meal plan
      const { data: updatedPlan, error: updateError } = await supabase
        .from('meal_plans')
        .update({
          name: `Updated Meal Plan - ${duration_days} Days`,
          description: `AI-generated ${duration_days}-day meal plan for ${meals_per_day} meals per day`,
          week_start_date,
          duration_days,
          target_calories_per_day: finalTargetCalories,
          dietary_preferences: finalDietaryPrefs,
          excluded_ingredients: finalExcludedIngredients,
          preferred_cuisines: finalCuisines,
          max_prep_time_minutes: finalMaxTime,
          cooking_skill_level: finalSkillLevel,
          updated_at: new Date().toISOString(),
        })
        .eq('id', update_existing_plan)
        .eq('user_id', user.id)
        .select()
        .single();

      if (updateError) {
        console.error('Meal plan update error:', updateError);
        return {
          success: false,
          error: 'Failed to update meal plan',
        };
      }
      savedMealPlan = updatedPlan;

      // Delete existing meals for this plan
      await supabase
        .from('meal_plan_meals')
        .delete()
        .eq('meal_plan_id', update_existing_plan);
    } else {
      // Create new meal plan
      const { data: newPlan, error: saveError } = await supabase
        .from('meal_plans')
        .insert({
          user_id: user.id,
          name: `Meal Plan - ${duration_days} Days`,
          description: `AI-generated ${duration_days}-day meal plan for ${meals_per_day} meals per day`,
          week_start_date,
          duration_days,
          target_calories_per_day: finalTargetCalories,
          dietary_preferences: finalDietaryPrefs,
          excluded_ingredients: finalExcludedIngredients,
          preferred_cuisines: finalCuisines,
          max_prep_time_minutes: finalMaxTime,
          cooking_skill_level: finalSkillLevel,
          ai_generated: true,
        })
        .select()
        .single();

      if (saveError) {
        console.error('Meal plan save error:', saveError);
        return {
          success: false,
          error: 'Failed to save meal plan',
        };
      }
      savedMealPlan = newPlan;
    }

    // Save individual meals to meal_plan_meals table
    const mealInserts = [];
    for (let day = 0; day < duration_days; day++) {
      const dayMeals = mealPlan[day] || [];
      for (const meal of dayMeals) {
        mealInserts.push({
          meal_plan_id: savedMealPlan.id,
          recipe_id: meal.recipe.id,
          day_of_week: day,
          meal_type: meal.meal_type,
          servings: meal.servings || 1,
          total_calories:
            meal.recipe.calories_per_serving * (meal.servings || 1),
          total_protein_grams: meal.recipe.protein_grams * (meal.servings || 1),
          total_carbs_grams: meal.recipe.carbs_grams * (meal.servings || 1),
          total_fat_grams: meal.recipe.fat_grams * (meal.servings || 1),
        });
      }
    }

    if (mealInserts.length > 0) {
      const { error: mealsError } = await supabase
        .from('meal_plan_meals')
        .insert(mealInserts);

      if (mealsError) {
        console.error('Error saving meal plan meals:', mealsError);
      }
    }

    return {
      success: true,
      data: {
        meal_plan_id: savedMealPlan?.id,
        week_start_date,
        duration_days,
        daily_meals: mealPlan,
        total_recipes: recipes.length,
        target_calories: finalTargetCalories,
        estimated_weekly_cost: calculateWeeklyCost(mealPlan),
        dietary_compliance: finalDietaryPrefs,
        is_update: !!update_existing_plan,
      },
    };
  } catch (error) {
    console.error('Enhanced meal planner error:', error);
    return {
      success: false,
      error: 'Failed to generate meal plan',
    };
  }
};

// Smart recipe finder using the enhanced database
export const smartRecipeFinder = async (params: RecipeFinderRequest) => {
  try {
    const {
      search_query,
      cuisine_type,
      dietary_tags = [],
      max_total_time,
      difficulty_level,
      meal_type,
      min_protein,
      max_calories,
      exclude_allergens = [],
      budget_max,
      limit = 10,
    } = params;

    // Get current user for compatibility scoring
    const {
      data: { user },
    } = await supabase.auth.getUser();

    let query = supabase
      .from('recipes')
      .select(
        `
        id, name, description, cuisine_type, prep_time_minutes, cook_time_minutes,
        total_time_minutes, difficulty_level, servings, ingredients,
        dietary_tags, allergens, meal_types, calories_per_serving,
        protein_grams, carbs_grams, fat_grams, estimated_cost_per_serving,
        rating_average, image_url, chef_name
      `
      )
      .eq('is_active', true);

    // Apply filters
    if (search_query) {
      query = query.textSearch('name', search_query);
    }

    if (cuisine_type) {
      query = query.eq('cuisine_type', cuisine_type);
    }

    if (dietary_tags.length > 0) {
      query = query.contains('dietary_tags', dietary_tags);
    }

    if (max_total_time) {
      query = query.lte('total_time_minutes', max_total_time);
    }

    if (difficulty_level) {
      const allowedLevels =
        difficulty_level === 'beginner'
          ? ['beginner']
          : difficulty_level === 'intermediate'
            ? ['beginner', 'intermediate']
            : ['beginner', 'intermediate', 'advanced'];
      query = query.in('difficulty_level', allowedLevels);
    }

    if (meal_type) {
      query = query.contains('meal_types', [meal_type]);
    }

    if (min_protein) {
      query = query.gte('protein_grams', min_protein);
    }

    if (max_calories) {
      query = query.lte('calories_per_serving', max_calories);
    }

    if (budget_max) {
      query = query.lte('estimated_cost_per_serving', budget_max);
    }

    // Exclude allergens
    if (exclude_allergens.length > 0) {
      query = query.not('allergens', 'cs', `{${exclude_allergens.join(',')}}`);
    }

    const { data: recipes, error } = await query
      .order('rating_average', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Recipe finder error:', error);
      return {
        success: false,
        error: 'Failed to find recipes',
      };
    }

    // Calculate compatibility scores if user is authenticated
    let recipesWithScores: any[] = recipes || [];
    if (user) {
      recipesWithScores = await Promise.all(
        (recipes || []).map(async recipe => {
          const { data: compatibilityData } = await supabase.rpc(
            'calculate_recipe_compatibility',
            {
              p_recipe_id: recipe.id,
              p_user_id: user.id,
            }
          );

          return {
            ...recipe,
            compatibility_score: compatibilityData || 0.5,
          };
        })
      );

      // Sort by compatibility score
      recipesWithScores.sort(
        (a: any, b: any) => b.compatibility_score - a.compatibility_score
      );
    }

    return {
      success: true,
      data: {
        recipes: recipesWithScores,
        total_found: recipesWithScores.length,
        search_criteria: params,
      },
    };
  } catch (error) {
    console.error('Smart recipe finder error:', error);
    return {
      success: false,
      error: 'Failed to search recipes',
    };
  }
};

// Helper function to generate optimal meal plan
function generateOptimalMealPlan(
  recipes: any[],
  mealsPerDay: number,
  targetCalories: number,
  _targetMacros?: any,
  durationDays: number = 7
) {
  const _caloriesPerMeal = targetCalories / mealsPerDay; // eslint-disable-line @typescript-eslint/no-unused-vars
  const mealPlan: any[] = [];

  // Group recipes by meal type
  const breakfastRecipes = recipes.filter(r =>
    r.meal_types?.includes('breakfast')
  );
  const lunchRecipes = recipes.filter(r => r.meal_types?.includes('lunch'));
  const dinnerRecipes = recipes.filter(r => r.meal_types?.includes('dinner'));
  const snackRecipes = recipes.filter(r => r.meal_types?.includes('snack'));

  // Generate meals for specified duration
  for (let day = 0; day < durationDays; day++) {
    const dayMeals: any[] = [];

    // Add breakfast
    if (breakfastRecipes.length > 0) {
      const breakfast = breakfastRecipes[day % breakfastRecipes.length];
      dayMeals.push({
        meal_type: 'breakfast',
        recipe: breakfast,
        day_of_week: day,
      });
    }

    // Add lunch
    if (lunchRecipes.length > 0) {
      const lunch = lunchRecipes[day % lunchRecipes.length];
      dayMeals.push({
        meal_type: 'lunch',
        recipe: lunch,
        day_of_week: day,
      });
    }

    // Add dinner
    if (dinnerRecipes.length > 0) {
      const dinner = dinnerRecipes[day % dinnerRecipes.length];
      dayMeals.push({
        meal_type: 'dinner',
        recipe: dinner,
        day_of_week: day,
      });
    }

    // Add snacks if needed
    if (mealsPerDay > 3 && snackRecipes.length > 0) {
      const snack = snackRecipes[day % snackRecipes.length];
      dayMeals.push({
        meal_type: 'snack',
        recipe: snack,
        day_of_week: day,
      });
    }

    mealPlan.push(...dayMeals);
  }

  return mealPlan;
}

// Helper function to calculate weekly cost
function calculateWeeklyCost(mealPlan: any[]): number {
  return mealPlan.reduce((total, meal) => {
    return total + (meal.recipe?.estimated_cost_per_serving || 0);
  }, 0);
}

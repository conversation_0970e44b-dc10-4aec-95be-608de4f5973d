import { supabase } from '../../../lib/supabase';
import {
  calculateHealthMetrics,
  saveHealthMetricsToProfile,
  UserBasicData,
} from '../../profile/services/healthMetricsService';
import {
  questionnaires,
  type QuestionnaireType,
} from '../data/questionnaireData';

// Helper function to update persona based on questionnaire responses
const updatePersonaFromQuestionnaire = async (
  userId: string,
  questionnaireType: QuestionnaireType,
  responses: Array<{
    question_id: string;
    answer_value: string;
    answer_display_text: string;
  }>
) => {
  try {
    const questionnaire = questionnaires[questionnaireType];
    if (!questionnaire) {
      return;
    }

    // Build persona updates based on questionnaire responses
    const personaUpdates: Record<string, any> = {};
    const insights: Array<{
      category: string;
      insight: string;
      confidence: number;
      supportingData: any;
    }> = [];

    for (const response of responses) {
      const question = questionnaire.questions.find(
        q => q.id === response.question_id
      );
      if (!question) {
        continue;
      }

      const answerValue = JSON.parse(response.answer_value);
      const {
        category,
        trait: _trait, // eslint-disable-line @typescript-eslint/no-unused-vars
        weight: _weight, // eslint-disable-line @typescript-eslint/no-unused-vars
      } = question.personaMapping;

      // Initialize category if not exists
      if (!personaUpdates[category]) {
        personaUpdates[category] = {};
      }

      // Map specific responses to persona traits
      if (questionnaireType === 'basic') {
        await mapBasicQuestionnaireToPersona(
          question,
          answerValue,
          response.answer_display_text,
          personaUpdates,
          insights
        );
      } else if (questionnaireType === 'nutrition') {
        await mapNutritionQuestionnaireToPersona(
          question,
          answerValue,
          response.answer_display_text,
          personaUpdates,
          insights
        );
      } else if (questionnaireType === 'exercise') {
        await mapExerciseQuestionnaireToPersona(
          question,
          answerValue,
          response.answer_display_text,
          personaUpdates,
          insights
        );
      }
    }

    // Update persona in database
    if (Object.keys(personaUpdates).length > 0) {
      const { error: personaError } = await supabase.rpc(
        'update_user_persona',
        {
          p_user_id: userId,
          p_persona_updates: personaUpdates,
          p_update_source: `${questionnaireType}_questionnaire`,
        }
      );

      if (personaError) {
        console.error('Error updating persona:', personaError);
      } else {
        console.log(
          `Persona updated from ${questionnaireType} questionnaire:`,
          personaUpdates
        );
      }
    }

    // Store insights
    for (const insight of insights) {
      const { error: insightError } = await supabase
        .from('persona_insights')
        .insert({
          user_id: userId,
          insight_category: insight.category,
          insight_text: insight.insight,
          confidence_score: insight.confidence,
          supporting_data: insight.supportingData,
          source: `${questionnaireType}_questionnaire`,
        });

      if (insightError) {
        console.error('Error storing insight:', insightError);
      }
    }
  } catch (error) {
    console.error('Error updating persona from questionnaire:', error);
  }
};

// Tool function implementations for questionnaire management

export const startQuestionnaire = async (params: {
  questionnaireType: QuestionnaireType;
}): Promise<string> => {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return 'Error: User not authenticated';
    }

    const questionnaire = questionnaires[params.questionnaireType];
    if (!questionnaire) {
      return `Error: Unknown questionnaire type: ${params.questionnaireType}`;
    }

    // Check if questionnaire is already completed
    const { data: statusData, error: statusError } = await supabase.rpc(
      'get_questionnaire_status',
      { p_user_id: user.id }
    );

    if (statusError) {
      console.error('Error checking questionnaire status:', statusError);
      return 'Error checking questionnaire status';
    }

    const completionStatus = statusData?.completion_status || {};
    if (completionStatus[params.questionnaireType]) {
      return `You've already completed the ${questionnaire.title}! Your responses are saved and being used to personalize your experience. Would you like to start a different questionnaire or update your existing answers?`;
    }

    // Create new session
    const { data: sessionData, error: sessionError } = await supabase
      .from('questionnaire_sessions')
      .insert({
        user_id: user.id,
        questionnaire_type: params.questionnaireType,
        total_questions: questionnaire.questions.length,
        status: 'in_progress',
      })
      .select('id')
      .single();

    if (sessionError) {
      console.error('Error creating questionnaire session:', sessionError);
      return 'Error starting questionnaire session';
    }

    const sessionId = sessionData.id;
    const firstQuestion = questionnaire.questions[0];

    return `Great! Let's start your ${questionnaire.title}. ${questionnaire.description}

**Estimated time:** ${questionnaire.estimatedTime}

**Question 1 of ${questionnaire.questions.length}:**
${firstQuestion.text}

Session ID: ${sessionId}
Question ID: ${firstQuestion.id}

${
  firstQuestion.options
    ? firstQuestion.options.map(opt => `${opt.emoji} ${opt.label}`).join('\n')
    : 'Please provide your answer.'
}`;
  } catch (error) {
    console.error('Error in startQuestionnaire:', error);
    return 'Error starting questionnaire';
  }
};

export const saveQuestionnaireAnswer = async (params: {
  questionnaireType: QuestionnaireType;
  questionId: string;
  answer: {
    value: any;
    displayText?: string;
  };
  sessionId?: string;
}): Promise<string> => {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return 'Error: User not authenticated';
    }

    const questionnaire = questionnaires[params.questionnaireType];
    if (!questionnaire) {
      return `Error: Unknown questionnaire type: ${params.questionnaireType}`;
    }

    const question = questionnaire.questions.find(
      q => q.id === params.questionId
    );
    if (!question) {
      return `Error: Question not found: ${params.questionId}`;
    }

    // Save the response
    const { data: saveData, error: saveError } = await supabase.rpc(
      'save_questionnaire_response',
      {
        p_user_id: user.id,
        p_questionnaire_type: params.questionnaireType,
        p_question_id: params.questionId,
        p_question_text: question.text,
        p_answer_value: JSON.stringify(params.answer.value),
        p_answer_display_text:
          params.answer.displayText || JSON.stringify(params.answer.value),
        p_session_id: params.sessionId || null,
      }
    );

    if (saveError) {
      console.error('Error saving questionnaire response:', saveError);
      return 'Error saving your answer';
    }

    const sessionId = saveData.session_id;

    // Find next question
    const currentIndex = questionnaire.questions.findIndex(
      q => q.id === params.questionId
    );
    const nextIndex = currentIndex + 1;

    if (nextIndex >= questionnaire.questions.length) {
      // Questionnaire complete
      return `Perfect! That was the last question. Let me process your responses and complete your ${questionnaire.title}.

Use the complete_questionnaire tool with sessionId: ${sessionId}`;
    }

    const nextQuestion = questionnaire.questions[nextIndex];
    const progress = Math.round(
      (nextIndex / questionnaire.questions.length) * 100
    );

    return `Great answer! 

**Progress: ${progress}% complete**

**Question ${nextIndex + 1} of ${questionnaire.questions.length}:**
${nextQuestion.text}

Session ID: ${sessionId}
Question ID: ${nextQuestion.id}

${
  nextQuestion.options
    ? nextQuestion.options.map(opt => `${opt.emoji} ${opt.label}`).join('\n')
    : 'Please provide your answer.'
}`;
  } catch (error) {
    console.error('Error in saveQuestionnaireAnswer:', error);
    return 'Error saving questionnaire answer';
  }
};

export const getQuestionnaireStatus = async (): Promise<string> => {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return 'Error: User not authenticated';
    }

    const { data: statusData, error: statusError } = await supabase.rpc(
      'get_questionnaire_status',
      { p_user_id: user.id }
    );

    if (statusError) {
      console.error('Error getting questionnaire status:', statusError);
      return 'Error checking questionnaire status';
    }

    const completionStatus = statusData?.completion_status || {};
    const _responses = statusData?.responses || {}; // eslint-disable-line @typescript-eslint/no-unused-vars

    let statusMessage = '**Your Questionnaire Status:**\n\n';

    // Basic questionnaire
    if (completionStatus.basic) {
      statusMessage += '✅ **Basic Profile** - Complete\n';
    } else {
      statusMessage += '⏳ **Basic Profile** - Not started\n';
    }

    // Nutrition questionnaire
    if (completionStatus.nutrition) {
      statusMessage += '✅ **Nutrition Profile** - Complete\n';
    } else {
      statusMessage += '⏳ **Nutrition Profile** - Not started\n';
    }

    // Exercise questionnaire
    if (completionStatus.exercise) {
      statusMessage += '✅ **Exercise Profile** - Complete\n';
    } else {
      statusMessage += '⏳ **Exercise Profile** - Not started\n';
    }

    statusMessage += '\n**Unlocked Features:**\n';

    if (completionStatus.basic) {
      statusMessage +=
        '• General personalized advice\n• Progress tracking\n• Motivational support\n';
    }

    if (completionStatus.nutrition) {
      statusMessage +=
        '• Custom meal plans\n• Recipe recommendations\n• Grocery lists\n• Nutrition tracking\n';
    }

    if (completionStatus.exercise) {
      statusMessage +=
        '• Personalized workout plans\n• Exercise alternatives\n• Form guidance\n• Progress tracking\n';
    }

    if (
      !completionStatus.basic &&
      !completionStatus.nutrition &&
      !completionStatus.exercise
    ) {
      statusMessage +=
        'Complete questionnaires to unlock personalized features!';
    }

    return statusMessage;
  } catch (error) {
    console.error('Error in getQuestionnaireStatus:', error);
    return 'Error checking questionnaire status';
  }
};

export const completeQuestionnaire = async (params: {
  questionnaireType: QuestionnaireType;
  sessionId: string;
}): Promise<string> => {
  try {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return 'Error: User not authenticated';
    }

    const questionnaire = questionnaires[params.questionnaireType];
    if (!questionnaire) {
      return `Error: Unknown questionnaire type: ${params.questionnaireType}`;
    }

    // Mark session as completed
    const { error: updateError } = await supabase
      .from('questionnaire_sessions')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString(),
        completion_percentage: 100,
      })
      .eq('id', params.sessionId)
      .eq('user_id', user.id);

    if (updateError) {
      console.error('Error completing questionnaire session:', updateError);
      return 'Error completing questionnaire';
    }

    // Get all responses for persona update
    const { data: responses, error: responsesError } = await supabase
      .from('questionnaire_responses')
      .select('question_id, answer_value, answer_display_text')
      .eq('user_id', user.id)
      .eq('questionnaire_type', params.questionnaireType);

    if (responsesError) {
      console.error('Error fetching questionnaire responses:', responsesError);
      return 'Error processing questionnaire responses';
    }

    // Process responses and update persona
    console.log(
      `Processing ${responses?.length} responses for ${params.questionnaireType} questionnaire`
    );

    if (responses && responses.length > 0) {
      await updatePersonaFromQuestionnaire(
        user.id,
        params.questionnaireType,
        responses
      );

      // Also update the profiles table for profile completion tracking
      await updateProfileFromQuestionnaire(
        user.id,
        params.questionnaireType,
        responses
      );
    }

    let completionMessage = `🎉 **${questionnaire.title} Complete!**\n\n`;
    completionMessage += questionnaire.completionMessage + '\n\n';
    completionMessage += "**You've unlocked:**\n";
    questionnaire.unlocks.forEach(feature => {
      completionMessage += `• ${feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}\n`;
    });

    // Suggest next steps
    if (params.questionnaireType === 'basic') {
      completionMessage += '\n**🧮 Health Metrics Calculated!**\n';
      completionMessage +=
        "I've calculated your BMR, BMI, TDEE, and target calories based on your responses. Check your profile to see these personalized metrics!\n\n";
      completionMessage += '**Ready for more personalization?**\n';
      completionMessage +=
        '🥗 Complete **Nutrition Profile** → Custom meal plans & recipes\n';
      completionMessage +=
        '💪 Complete **Exercise Profile** → Personalized workout routines\n';
    } else {
      completionMessage += '\n**What would you like to do next?**\n';
      completionMessage += '• Ask me for personalized advice\n';
      completionMessage += '• Get a custom meal plan or workout\n';
      completionMessage += '• Complete another questionnaire\n';
    }

    return completionMessage;
  } catch (error) {
    console.error('Error in completeQuestionnaire:', error);
    return 'Error completing questionnaire';
  }
};

// Helper function to update profiles table for profile completion tracking
const updateProfileFromQuestionnaire = async (
  userId: string,
  questionnaireType: QuestionnaireType,
  responses: Array<{
    question_id: string;
    answer_value: string;
    answer_display_text: string;
  }>
) => {
  try {
    const profileUpdates: Record<string, any> = {};

    // Map questionnaire responses to profile fields
    for (const response of responses) {
      let answerValue: any;
      try {
        answerValue = JSON.parse(response.answer_value);
      } catch {
        answerValue = response.answer_value;
      }

      switch (response.question_id) {
        case 'biological_sex':
          profileUpdates.biological_sex = answerValue;
          break;
        case 'age':
          // Calculate date_of_birth from age
          const currentYear = new Date().getFullYear();
          const birthYear = currentYear - parseInt(answerValue, 10);
          profileUpdates.date_of_birth = `${birthYear}-01-01`;
          break;
        case 'date_of_birth':
          profileUpdates.date_of_birth = answerValue;
          break;
        case 'unit_system':
          profileUpdates.unit_system =
            answerValue === 'imperial' ? 'imperial' : 'metric';
          break;
        case 'height_cm':
          profileUpdates.height_cm = parseInt(answerValue, 10);
          break;
        case 'weight_kg':
          profileUpdates.weight_kg = parseInt(answerValue, 10);
          break;
        case 'activity_level':
          profileUpdates.activity_level = answerValue;
          break;
        case 'primary_goal':
          // Map primary_goal to fitness_goals array
          profileUpdates.fitness_goals = [answerValue];
          break;
        case 'dietary_approach':
          // Map dietary_approach to dietary_preferences array
          profileUpdates.dietary_preferences = [answerValue];
          break;
        case 'dietary_preferences':
          profileUpdates.dietary_preferences = Array.isArray(answerValue)
            ? answerValue
            : [answerValue];
          break;
        case 'experience_level':
          profileUpdates.experience_level = answerValue;
          break;
        case 'food_allergies':
          profileUpdates.food_allergies = Array.isArray(answerValue)
            ? answerValue
            : [answerValue];
          break;
        case 'cooking_skill':
          profileUpdates.cooking_skill = answerValue;
          break;
        case 'meal_prep_time':
          profileUpdates.meal_prep_time = answerValue;
          break;
        case 'grocery_budget':
          profileUpdates.grocery_budget = answerValue;
          break;
        case 'fitness_level':
          profileUpdates.fitness_level = answerValue;
          break;
        case 'workout_frequency':
          profileUpdates.workout_frequency = answerValue;
          break;
        case 'workout_duration':
          profileUpdates.workout_duration = answerValue;
          break;
        case 'exercise_preferences':
          profileUpdates.exercise_preferences = Array.isArray(answerValue)
            ? answerValue
            : [answerValue];
          break;
        case 'workout_location':
          profileUpdates.workout_location = Array.isArray(answerValue)
            ? answerValue
            : [answerValue];
          break;
        case 'available_equipment':
          profileUpdates.available_equipment = Array.isArray(answerValue)
            ? answerValue
            : [answerValue];
          break;
        case 'physical_limitations':
          profileUpdates.physical_limitations = Array.isArray(answerValue)
            ? answerValue
            : [answerValue];
          break;
        case 'workout_focus':
          profileUpdates.workout_focus = Array.isArray(answerValue)
            ? answerValue
            : [answerValue];
          break;
        case 'workout_structure':
          profileUpdates.workout_structure = answerValue;
          break;
        case 'preferred_workout_time':
          profileUpdates.preferred_workout_time = answerValue;
          break;
      }
    }

    // Update profile if we have any updates
    if (Object.keys(profileUpdates).length > 0) {
      const { error: profileError } = await supabase
        .from('profiles')
        .update(profileUpdates)
        .eq('id', userId);

      if (profileError) {
        console.error('Error updating profile:', profileError);
      } else {
        console.log(
          `Profile updated from ${questionnaireType} questionnaire:`,
          profileUpdates
        );
      }
    }

    // Calculate and save health metrics if this is the basic questionnaire
    // and we have all the required data
    if (questionnaireType === 'basic') {
      await calculateAndSaveHealthMetrics(userId, responses);
    }
  } catch (error) {
    console.error('Error in updateProfileFromQuestionnaire:', error);
  }
};

// Helper function to calculate and save health metrics
const calculateAndSaveHealthMetrics = async (
  userId: string,
  responses: Array<{
    question_id: string;
    answer_value: string;
    answer_display_text: string;
  }>
) => {
  try {
    // Extract required data from responses
    const userData: Partial<UserBasicData> = {};

    for (const response of responses) {
      let answerValue: any;
      try {
        answerValue = JSON.parse(response.answer_value);
      } catch {
        answerValue = response.answer_value;
      }

      switch (response.question_id) {
        case 'biological_sex':
          userData.biologicalSex = answerValue as 'male' | 'female';
          break;
        case 'date_of_birth':
          userData.dateOfBirth = answerValue;
          break;
        case 'age':
          // Convert age to approximate date of birth if date_of_birth not provided
          if (!userData.dateOfBirth) {
            const currentYear = new Date().getFullYear();
            const birthYear = currentYear - parseInt(answerValue, 10);
            userData.dateOfBirth = `${birthYear}-01-01`;
          }
          break;
        case 'height_cm':
          userData.heightCm = parseInt(answerValue, 10);
          break;
        case 'weight_kg':
          userData.weightKg = parseInt(answerValue, 10);
          break;
        case 'activity_level':
          userData.activityLevel =
            answerValue as UserBasicData['activityLevel'];
          break;
        case 'primary_goal':
          userData.primaryGoal = answerValue as UserBasicData['primaryGoal'];
          break;
        case 'timeline':
          userData.timeline = answerValue as UserBasicData['timeline'];
          break;
      }
    }

    // Check if we have all required data
    const requiredFields: (keyof UserBasicData)[] = [
      'biologicalSex',
      'dateOfBirth',
      'heightCm',
      'weightKg',
      'activityLevel',
      'primaryGoal',
      'timeline',
    ];

    const missingFields = requiredFields.filter(field => !userData[field]);

    if (missingFields.length > 0) {
      console.log(
        'Missing required fields for health metrics calculation:',
        missingFields
      );
      return;
    }

    // Calculate health metrics
    const healthMetrics = calculateHealthMetrics(userData as UserBasicData);

    // Save to profile
    const { error } = await saveHealthMetricsToProfile(userId, healthMetrics);

    if (error) {
      console.error('Error saving health metrics:', error);
    } else {
      console.log('Health metrics calculated and saved:', healthMetrics);
    }
  } catch (error) {
    console.error('Error in calculateAndSaveHealthMetrics:', error);
  }
};

// Mapping functions for each questionnaire type
const mapBasicQuestionnaireToPersona = async (
  question: any,
  answerValue: any,
  displayText: string,
  personaUpdates: Record<string, any>,
  insights: Array<any>
) => {
  const { id: questionId } = question;

  switch (questionId) {
    case 'fitness_goals':
      personaUpdates.fitness_persona = {
        ...personaUpdates.fitness_persona,
        primary_goals: Array.isArray(answerValue) ? answerValue : [answerValue],
        goal_priority: answerValue.includes('weight_loss')
          ? 'weight_management'
          : answerValue.includes('muscle_gain')
            ? 'strength_building'
            : 'general_fitness',
      };
      insights.push({
        category: 'fitness',
        insight: `User's primary fitness goals: ${displayText}`,
        confidence: 0.9,
        supportingData: { goals: answerValue, source: 'basic_questionnaire' },
      });
      break;

    case 'experience_level':
      personaUpdates.fitness_persona = {
        ...personaUpdates.fitness_persona,
        experience_level: answerValue,
        needs_guidance: answerValue === 'beginner',
      };
      insights.push({
        category: 'fitness',
        insight: `User has ${answerValue} level fitness experience`,
        confidence: 0.95,
        supportingData: { experience: answerValue },
      });
      break;

    case 'motivation_style':
      personaUpdates.motivation_style = {
        ...personaUpdates.motivation_style,
        preferred_motivation: answerValue,
        responds_to_encouragement: answerValue.includes('encouragement'),
        likes_challenges: answerValue.includes('challenges'),
      };
      insights.push({
        category: 'motivation',
        insight: `User prefers ${displayText} motivation style`,
        confidence: 0.85,
        supportingData: { motivation_type: answerValue },
      });
      break;

    case 'time_availability':
      personaUpdates.activity_patterns = {
        ...personaUpdates.activity_patterns,
        available_time: answerValue,
        prefers_short_sessions: parseInt(answerValue, 10) <= 30,
      };
      insights.push({
        category: 'lifestyle',
        insight: `User has ${displayText} minutes available for workouts`,
        confidence: 0.9,
        supportingData: { time_available: answerValue },
      });
      break;

    case 'preferred_schedule':
      personaUpdates.activity_patterns = {
        ...personaUpdates.activity_patterns,
        preferred_workout_time: answerValue,
        is_morning_person: answerValue === 'morning',
      };
      insights.push({
        category: 'lifestyle',
        insight: `User prefers ${displayText} workouts`,
        confidence: 0.8,
        supportingData: { schedule_preference: answerValue },
      });
      break;
  }
};

const mapNutritionQuestionnaireToPersona = async (
  question: any,
  answerValue: any,
  displayText: string,
  personaUpdates: Record<string, any>,
  insights: Array<any>
) => {
  const { id: questionId } = question;

  switch (questionId) {
    case 'dietary_preferences':
      personaUpdates.nutrition_persona = {
        ...personaUpdates.nutrition_persona,
        dietary_preferences: Array.isArray(answerValue)
          ? answerValue
          : [answerValue],
        is_vegetarian: answerValue.includes('vegetarian'),
        is_vegan: answerValue.includes('vegan'),
        has_dietary_restrictions: answerValue.length > 0,
      };
      insights.push({
        category: 'nutrition',
        insight: `User follows ${displayText} dietary preferences`,
        confidence: 0.95,
        supportingData: { dietary_preferences: answerValue },
      });
      break;

    case 'cooking_frequency':
      personaUpdates.nutrition_persona = {
        ...personaUpdates.nutrition_persona,
        cooking_frequency: answerValue,
        prefers_simple_meals: answerValue === 'rarely',
        enjoys_cooking: answerValue === 'daily',
      };
      insights.push({
        category: 'nutrition',
        insight: `User cooks ${displayText}`,
        confidence: 0.9,
        supportingData: { cooking_frequency: answerValue },
      });
      break;

    case 'meal_prep_time':
      personaUpdates.nutrition_persona = {
        ...personaUpdates.nutrition_persona,
        meal_prep_time: answerValue,
        needs_quick_recipes: parseInt(answerValue, 10) <= 15,
      };
      insights.push({
        category: 'nutrition',
        insight: `User has ${displayText} minutes for meal prep`,
        confidence: 0.85,
        supportingData: { prep_time: answerValue },
      });
      break;
  }
};

const mapExerciseQuestionnaireToPersona = async (
  question: any,
  answerValue: any,
  displayText: string,
  personaUpdates: Record<string, any>,
  insights: Array<any>
) => {
  const { id: questionId } = question;

  switch (questionId) {
    case 'exercise_types':
      personaUpdates.fitness_persona = {
        ...personaUpdates.fitness_persona,
        preferred_exercise_types: Array.isArray(answerValue)
          ? answerValue
          : [answerValue],
        likes_cardio: answerValue.includes('cardio'),
        likes_strength_training: answerValue.includes('strength'),
        likes_flexibility:
          answerValue.includes('yoga') || answerValue.includes('stretching'),
      };
      insights.push({
        category: 'fitness',
        insight: `User enjoys ${displayText} exercises`,
        confidence: 0.9,
        supportingData: { exercise_preferences: answerValue },
      });
      break;

    case 'workout_location':
      personaUpdates.fitness_persona = {
        ...personaUpdates.fitness_persona,
        preferred_location: answerValue,
        has_gym_access: answerValue === 'gym',
        prefers_home_workouts: answerValue === 'home',
      };
      insights.push({
        category: 'fitness',
        insight: `User prefers ${displayText} workouts`,
        confidence: 0.85,
        supportingData: { location_preference: answerValue },
      });
      break;

    case 'equipment_access':
      personaUpdates.fitness_persona = {
        ...personaUpdates.fitness_persona,
        available_equipment: Array.isArray(answerValue)
          ? answerValue
          : [answerValue],
        has_basic_equipment:
          answerValue.includes('dumbbells') ||
          answerValue.includes('resistance_bands'),
        bodyweight_only: answerValue.includes('none'),
      };
      insights.push({
        category: 'fitness',
        insight: `User has access to: ${displayText}`,
        confidence: 0.9,
        supportingData: { equipment: answerValue },
      });
      break;
  }
};

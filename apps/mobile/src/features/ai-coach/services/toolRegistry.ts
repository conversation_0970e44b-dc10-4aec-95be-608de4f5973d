import { supabase } from '../../../lib/supabase';
import { getProgressLogs } from '../../progress/services/progressService';
import {
  ToolDefinition,
  MealPlanRequest,
  WorkoutPlanRequest,
  ProgressAnalysisRequest,
  LogMealRequest,
  LogWorkoutRequest,
} from '../types';
import {
  generateGroceryList as generateGroceryListTool,
  updateGroceryItem,
} from './groceryListTools';
import { enhancedMealPlanner, smartRecipeFinder } from './mealPlanningTools';
import {
  startQuestionnaire,
  saveQuestionnaireAnswer,
  getQuestionnaireStatus,
  completeQuestionnaire,
} from './questionnaireTools';

// Tool implementations
const generateMealPlan = async (params: MealPlanRequest) => {
  try {
    // This would integrate with AI service to generate meal plan
    // For now, return a mock response
    const { days, goal = 'maintenance', calorieTarget = 2000 } = params;

    return {
      success: true,
      data: {
        days,
        goal,
        calorieTarget,
        meals: Array.from({ length: days }, (_, i) => ({
          day: i + 1,
          breakfast: `Day ${i + 1} Breakfast - Optimized for ${goal}`,
          lunch: `Day ${i + 1} Lunch - ${calorieTarget / 3} calories`,
          dinner: `Day ${i + 1} Dinner - Balanced nutrition`,
          snacks: [`Healthy snack option ${i + 1}`],
        })),
        totalCalories: calorieTarget * days,
        macros: {
          protein: Math.round((calorieTarget * 0.3) / 4),
          carbs: Math.round((calorieTarget * 0.4) / 4),
          fat: Math.round((calorieTarget * 0.3) / 9),
        },
      },
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Failed to generate meal plan',
    };
  }
};

const generateWorkoutPlan = async (params: WorkoutPlanRequest) => {
  try {
    const {
      days,
      type = 'mixed',
      experience = 'intermediate',
      duration = 45,
    } = params;

    return {
      success: true,
      data: {
        days,
        type,
        experience,
        duration,
        workouts: Array.from({ length: days }, (_, i) => ({
          day: i + 1,
          name: `${type} Workout Day ${i + 1}`,
          duration,
          exercises: [
            { name: 'Warm-up', sets: 1, reps: '5 minutes', type: 'cardio' },
            {
              name: `${type} Exercise 1`,
              sets: 3,
              reps: experience === 'beginner' ? 8 : 12,
            },
            {
              name: `${type} Exercise 2`,
              sets: 3,
              reps: experience === 'beginner' ? 8 : 12,
            },
            {
              name: 'Cool-down',
              sets: 1,
              reps: '5 minutes',
              type: 'stretching',
            },
          ],
        })),
      },
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to generate workout plan',
    };
  }
};

const analyzeProgress = async (params: ProgressAnalysisRequest) => {
  try {
    const { timeframe, metrics: _metrics = ['weight'] } = params;

    // Get actual progress data
    const progressLogs = await getProgressLogs(30);

    // Filter by timeframe
    const now = new Date();
    const cutoffDate = new Date();

    switch (timeframe) {
      case 'week':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoffDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        cutoffDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        cutoffDate.setFullYear(now.getFullYear() - 1);
        break;
    }

    const filteredLogs = progressLogs.filter(
      log => new Date(log.log_date) >= cutoffDate
    );

    // Analyze weight trends
    const weightData = filteredLogs
      .filter(log => log.weight_kg)
      .map(log => ({ date: log.log_date, weight: log.weight_kg }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    const weightChange =
      weightData.length >= 2
        ? weightData[weightData.length - 1].weight! - weightData[0].weight!
        : 0;

    return {
      success: true,
      data: {
        timeframe,
        totalEntries: filteredLogs.length,
        weightChange: Math.round(weightChange * 10) / 10,
        weightTrend:
          weightChange > 0
            ? 'increasing'
            : weightChange < 0
              ? 'decreasing'
              : 'stable',
        consistency: Math.round(
          (filteredLogs.length / (timeframe === 'week' ? 7 : 30)) * 100
        ),
        latestWeight: weightData[weightData.length - 1]?.weight,
        chartData: weightData,
      },
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error ? error.message : 'Failed to analyze progress',
    };
  }
};

const logMealTool = async (params: LogMealRequest) => {
  try {
    const { mealType, foodItems, notes } = params;

    // This would need to be enhanced to create proper meal log entries
    // For now, return a success response
    return {
      success: true,
      data: {
        mealType,
        foodItems,
        notes,
        loggedAt: new Date().toISOString(),
        message: `Successfully logged ${mealType} with ${foodItems.length} items`,
      },
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to log meal',
    };
  }
};

const logWorkoutTool = async (params: LogWorkoutRequest) => {
  try {
    const { exerciseName, sets, reps, weight, duration, notes } = params;

    return {
      success: true,
      data: {
        exerciseName,
        sets,
        reps,
        weight,
        duration,
        notes,
        loggedAt: new Date().toISOString(),
        message: `Successfully logged ${exerciseName}: ${sets} sets x ${reps} reps`,
      },
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to log workout',
    };
  }
};

const findExerciseAlternative = async (params: {
  exerciseName: string;
  reason: string;
}) => {
  try {
    const { exerciseName, reason } = params;

    // Mock exercise alternatives based on common issues
    const alternatives: Record<string, string[]> = {
      squats: ['Leg press', 'Wall sits', 'Lunges', 'Step-ups'],
      deadlifts: [
        'Romanian deadlifts',
        'Hip thrusts',
        'Good mornings',
        'Kettlebell swings',
      ],
      'push-ups': [
        'Incline push-ups',
        'Chest press',
        'Dumbbell press',
        'Resistance band chest fly',
      ],
      'pull-ups': [
        'Lat pulldowns',
        'Assisted pull-ups',
        'Resistance band rows',
        'Inverted rows',
      ],
    };

    const exerciseKey = exerciseName.toLowerCase();
    const suggestedAlternatives = alternatives[exerciseKey] || [
      'Consult with a trainer for alternatives',
    ];

    return {
      success: true,
      data: {
        originalExercise: exerciseName,
        reason,
        alternatives: suggestedAlternatives,
        recommendation: `Based on ${reason}, here are some safer alternatives that target similar muscle groups.`,
      },
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to find exercise alternatives',
    };
  }
};

const updatePersonaInsight = async (params: {
  insightType: string;
  category: string;
  insight: string;
  confidence: number;
  supportingData?: Record<string, any>;
}) => {
  try {
    const {
      insightType,
      category,
      insight,
      confidence,
      supportingData = {},
    } = params;

    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: 'User not authenticated',
      };
    }

    // Store the persona insight in the database
    const { data, error } = await supabase
      .from('persona_insights')
      .insert({
        user_id: user.id,
        insight_category: category,
        insight_text: insight,
        confidence_score: confidence,
        supporting_data: supportingData,
        source: 'ai_interaction',
      })
      .select()
      .single();

    if (error) {
      console.error('Error storing persona insight:', error);
      return {
        success: false,
        error: 'Failed to store persona insight',
      };
    }

    return {
      success: true,
      data: {
        id: data.id,
        insightType,
        category,
        insight,
        confidence,
        supportingData,
        message: `Persona insight recorded: ${insight}`,
        timestamp: data.created_at,
      },
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to update persona insight',
    };
  }
};

const updatePersonaTrait = async (params: {
  traitCategory: string;
  traitData: Record<string, any>;
  reasoning: string;
}) => {
  try {
    const { traitCategory, traitData, reasoning } = params;

    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      return {
        success: false,
        error: 'User not authenticated',
      };
    }

    // Update the persona trait using the database function
    const personaUpdates = {
      [traitCategory]: traitData,
    };

    const { error } = await supabase.rpc('update_user_persona', {
      p_user_id: user.id,
      p_persona_updates: personaUpdates,
      p_update_source: 'ai_interaction',
    });

    if (error) {
      console.error('Error updating persona trait:', error);
      return {
        success: false,
        error: 'Failed to update persona trait',
      };
    }

    // Also store an insight about this update
    await supabase.from('persona_insights').insert({
      user_id: user.id,
      insight_category: traitCategory,
      insight_text: reasoning,
      confidence_score: 0.8,
      supporting_data: { trait_data: traitData },
      source: 'ai_trait_update',
    });

    return {
      success: true,
      data: {
        traitCategory,
        traitData,
        reasoning,
        message: `Persona trait updated in ${traitCategory}`,
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to update persona trait',
    };
  }
};

// Tool registry
export const toolRegistry: Record<string, ToolDefinition> = {
  generate_meal_plan: {
    name: 'generate_meal_plan',
    description:
      'Generate a personalized meal plan for specified number of days',
    parameters: {
      type: 'object',
      properties: {
        days: {
          type: 'number',
          description: 'Number of days for the meal plan',
        },
        goal: {
          type: 'string',
          description: 'Fitness goal',
          enum: ['weight_loss', 'weight_gain', 'maintenance', 'muscle_gain'],
        },
        calorieTarget: { type: 'number', description: 'Daily calorie target' },
        dietaryRestrictions: {
          type: 'array',
          description: 'List of dietary restrictions or allergies',
          items: {
            type: 'string',
          },
        },
      },
      required: ['days'],
    },
    execute: generateMealPlan,
  },

  generate_workout_plan: {
    name: 'generate_workout_plan',
    description: 'Create a custom workout plan',
    parameters: {
      type: 'object',
      properties: {
        days: { type: 'number', description: 'Number of workout days' },
        type: {
          type: 'string',
          description: 'Type of workout',
          enum: ['strength', 'cardio', 'mixed', 'flexibility'],
        },
        experience: {
          type: 'string',
          description: 'Experience level',
          enum: ['beginner', 'intermediate', 'advanced'],
        },
        duration: {
          type: 'number',
          description: 'Workout duration in minutes',
        },
      },
      required: ['days'],
    },
    execute: generateWorkoutPlan,
  },

  analyze_progress: {
    name: 'analyze_progress',
    description: 'Analyze user progress over a specified timeframe',
    parameters: {
      type: 'object',
      properties: {
        timeframe: {
          type: 'string',
          description: 'Time period to analyze',
          enum: ['week', 'month', 'quarter', 'year'],
        },
        metrics: {
          type: 'array',
          description: 'Metrics to analyze',
          items: {
            type: 'string',
          },
        },
      },
      required: ['timeframe'],
    },
    execute: analyzeProgress,
  },

  log_meal: {
    name: 'log_meal',
    description: 'Log a meal entry',
    parameters: {
      type: 'object',
      properties: {
        mealType: {
          type: 'string',
          description: 'Type of meal',
          enum: ['breakfast', 'lunch', 'dinner', 'snack'],
        },
        foodItems: {
          type: 'array',
          description: 'List of food items consumed',
          items: {
            type: 'string',
          },
        },
        notes: {
          type: 'string',
          description: 'Additional notes about the meal',
        },
      },
      required: ['mealType', 'foodItems'],
    },
    execute: logMealTool,
  },

  log_workout: {
    name: 'log_workout',
    description: 'Log a workout session',
    parameters: {
      type: 'object',
      properties: {
        exerciseName: { type: 'string', description: 'Name of the exercise' },
        sets: { type: 'number', description: 'Number of sets completed' },
        reps: { type: 'number', description: 'Number of reps per set' },
        weight: { type: 'number', description: 'Weight used (optional)' },
        duration: {
          type: 'number',
          description: 'Duration in minutes (optional)',
        },
        notes: { type: 'string', description: 'Additional notes' },
      },
      required: ['exerciseName', 'sets', 'reps'],
    },
    execute: logWorkoutTool,
  },

  find_exercise_alternative: {
    name: 'find_exercise_alternative',
    description: 'Find alternative exercises for injuries or limitations',
    parameters: {
      type: 'object',
      properties: {
        exerciseName: {
          type: 'string',
          description: 'Name of the exercise to replace',
        },
        reason: {
          type: 'string',
          description:
            'Reason for needing alternative (injury, equipment, etc.)',
        },
      },
      required: ['exerciseName', 'reason'],
    },
    execute: findExerciseAlternative,
  },

  update_persona_insight: {
    name: 'update_persona_insight',
    description:
      'Add or update a persona insight about the user based on their interactions',
    parameters: {
      type: 'object',
      properties: {
        insightType: {
          type: 'string',
          description: 'Type of insight',
          enum: [
            'preference',
            'behavior',
            'goal',
            'challenge',
            'motivation',
            'communication',
          ],
        },
        category: {
          type: 'string',
          description: 'Category of the insight',
          enum: [
            'fitness',
            'nutrition',
            'motivation',
            'communication',
            'lifestyle',
          ],
        },
        insight: {
          type: 'string',
          description:
            'The insight text describing what was learned about the user',
        },
        confidence: {
          type: 'number',
          description: 'Confidence score from 0.0 to 1.0',
        },
        supportingData: {
          type: 'object',
          description: 'Supporting data for the insight',
        },
      },
      required: ['insightType', 'category', 'insight', 'confidence'],
    },
    execute: updatePersonaInsight,
  },

  update_persona_trait: {
    name: 'update_persona_trait',
    description:
      'Update a specific persona trait based on user behavior analysis',
    parameters: {
      type: 'object',
      properties: {
        traitCategory: {
          type: 'string',
          description: 'Category of trait to update',
          enum: [
            'personality_traits',
            'communication_style',
            'motivation_style',
            'learning_preferences',
            'fitness_persona',
            'nutrition_persona',
          ],
        },
        traitData: {
          type: 'object',
          description: 'The trait data to update',
        },
        reasoning: {
          type: 'string',
          description: 'AI reasoning for this persona update',
        },
      },
      required: ['traitCategory', 'traitData', 'reasoning'],
    },
    execute: updatePersonaTrait,
  },

  start_questionnaire: {
    name: 'start_questionnaire',
    description: 'Start a questionnaire session for the user',
    parameters: {
      type: 'object',
      properties: {
        questionnaireType: {
          type: 'string',
          description: 'Type of questionnaire to start',
          enum: ['basic', 'nutrition', 'exercise'],
        },
      },
      required: ['questionnaireType'],
    },
    execute: startQuestionnaire,
  },

  save_questionnaire_answer: {
    name: 'save_questionnaire_answer',
    description: "Save a user's answer to a questionnaire question",
    parameters: {
      type: 'object',
      properties: {
        questionnaireType: {
          type: 'string',
          description: 'Type of questionnaire',
          enum: ['basic', 'nutrition', 'exercise'],
        },
        questionId: {
          type: 'string',
          description: 'ID of the question being answered',
        },
        answer: {
          type: 'object',
          description: "The user's answer (value and display text)",
        },
        sessionId: {
          type: 'string',
          description: 'Session ID for this questionnaire',
        },
      },
      required: ['questionnaireType', 'questionId', 'answer'],
    },
    execute: saveQuestionnaireAnswer,
  },

  get_questionnaire_status: {
    name: 'get_questionnaire_status',
    description: "Get the user's questionnaire completion status and responses",
    parameters: {
      type: 'object',
      properties: {},
    },
    execute: getQuestionnaireStatus,
  },

  complete_questionnaire: {
    name: 'complete_questionnaire',
    description: 'Mark a questionnaire as completed and update user persona',
    parameters: {
      type: 'object',
      properties: {
        questionnaireType: {
          type: 'string',
          description: 'Type of questionnaire completed',
          enum: ['basic', 'nutrition', 'exercise'],
        },
        sessionId: {
          type: 'string',
          description: 'Session ID for this questionnaire',
        },
      },
      required: ['questionnaireType', 'sessionId'],
    },
    execute: completeQuestionnaire,
  },

  // Enhanced AI Meal Planning Tools
  enhanced_meal_planner: {
    name: 'enhanced_meal_planner',
    description:
      'Generate a personalized weekly meal plan using AI and the recipe database with compatibility scoring',
    parameters: {
      type: 'object',
      properties: {
        week_start_date: {
          type: 'string',
          description: 'Start date for the meal plan (YYYY-MM-DD format)',
        },
        dietary_preferences: {
          type: 'array',
          description:
            'Dietary preferences and restrictions (e.g., vegetarian, gluten-free, high-protein)',
          items: { type: 'string' },
        },
        target_calories_per_day: {
          type: 'number',
          description: 'Target daily calorie intake',
        },
        target_macros: {
          type: 'object',
          description: 'Target macronutrient distribution',
          properties: {
            protein_grams: { type: 'number' },
            carbs_grams: { type: 'number' },
            fat_grams: { type: 'number' },
          },
        },
        excluded_ingredients: {
          type: 'array',
          description: 'Ingredients to avoid',
          items: { type: 'string' },
        },
        preferred_cuisines: {
          type: 'array',
          description: 'Preferred cuisine types',
          items: { type: 'string' },
        },
        max_prep_time_minutes: {
          type: 'number',
          description: 'Maximum preparation time per recipe in minutes',
        },
        meals_per_day: {
          type: 'number',
          description: 'Number of meals per day (typically 3-4)',
        },
        cooking_skill_level: {
          type: 'string',
          description: "User's cooking skill level",
          enum: ['beginner', 'intermediate', 'advanced'],
        },
      },
      required: ['week_start_date'],
    },
    execute: enhancedMealPlanner,
  },

  smart_recipe_finder: {
    name: 'smart_recipe_finder',
    description:
      'Find recipes using AI-powered search with compatibility scoring and advanced filtering',
    parameters: {
      type: 'object',
      properties: {
        search_query: {
          type: 'string',
          description: 'Search query for recipe names or descriptions',
        },
        cuisine_type: {
          type: 'string',
          description: 'Specific cuisine type to filter by',
        },
        dietary_tags: {
          type: 'array',
          description:
            'Dietary tags to filter by (e.g., vegetarian, gluten-free)',
          items: { type: 'string' },
        },
        max_total_time: {
          type: 'number',
          description: 'Maximum total cooking time in minutes',
        },
        difficulty_level: {
          type: 'string',
          description: 'Maximum difficulty level',
          enum: ['beginner', 'intermediate', 'advanced'],
        },
        meal_type: {
          type: 'string',
          description: 'Type of meal',
          enum: ['breakfast', 'lunch', 'dinner', 'snack', 'dessert'],
        },
        min_protein: {
          type: 'number',
          description: 'Minimum protein content in grams',
        },
        max_calories: {
          type: 'number',
          description: 'Maximum calories per serving',
        },
        exclude_allergens: {
          type: 'array',
          description: 'Allergens to exclude',
          items: { type: 'string' },
        },
        budget_max: {
          type: 'number',
          description: 'Maximum cost per serving',
        },
        limit: {
          type: 'number',
          description: 'Maximum number of recipes to return',
        },
      },
      required: [],
    },
    execute: smartRecipeFinder,
  },

  generate_grocery_list: {
    name: 'generate_grocery_list',
    description:
      'Generate a smart grocery list from meal plans or specific recipes with cost estimation',
    parameters: {
      type: 'object',
      properties: {
        meal_plan_id: {
          type: 'string',
          description: 'ID of the meal plan to generate grocery list from',
        },
        recipe_ids: {
          type: 'array',
          description: 'Array of specific recipe IDs to include',
          items: { type: 'string' },
        },
        servings_multiplier: {
          type: 'number',
          description: 'Multiplier for recipe servings (default: 1)',
        },
        organize_by_category: {
          type: 'boolean',
          description: 'Whether to organize items by grocery store category',
        },
        exclude_pantry_staples: {
          type: 'boolean',
          description: 'Whether to exclude common pantry items',
        },
        store_preference: {
          type: 'string',
          description: 'Preferred grocery store for optimization',
        },
        week_start_date: {
          type: 'string',
          description: 'Week start date for the grocery list',
        },
      },
      required: [],
    },
    execute: generateGroceryListTool,
  },

  update_grocery_item: {
    name: 'update_grocery_item',
    description:
      'Update the status of a grocery list item (mark as purchased, add notes, etc.)',
    parameters: {
      type: 'object',
      properties: {
        grocery_list_id: {
          type: 'string',
          description: 'ID of the grocery list',
        },
        item_name: {
          type: 'string',
          description: 'Name of the grocery item to update',
        },
        is_purchased: {
          type: 'boolean',
          description: 'Whether the item has been purchased',
        },
        actual_cost: {
          type: 'number',
          description: 'Actual cost of the item if different from estimate',
        },
        notes: {
          type: 'string',
          description: 'Additional notes about the item',
        },
      },
      required: ['grocery_list_id', 'item_name', 'is_purchased'],
    },
    execute: ({
      item_id,
      ...rest
    }: { item_id: string } & Record<string, any>) =>
      updateGroceryItem(item_id, rest),
  },
};

// Integration test for the enhanced AI meal planning tools
import { aiChatService } from '../services/aiChatService';
import { toolRegistry } from '../services/toolRegistry';

// Test function to verify the meal planning integration
export const testMealPlanningIntegration = async () => {
  console.log('🧪 Testing AI Meal Planning Integration...');

  try {
    // Test 1: Enhanced Meal Planner Tool Recognition
    console.log('\n1️⃣ Testing meal planner tool recognition...');
    const mealPlanResponse = await aiChatService.sendMessage(
      "Create a weekly meal plan for me. I'm vegetarian and want high-protein meals under 30 minutes.",
      []
    );

    console.log('✅ Meal plan response:', {
      content: mealPlanResponse.content,
      hasToolCalls: !!mealPlanResponse.toolCalls,
      toolName: mealPlanResponse.toolCalls?.[0]?.name,
      parameters: mealPlanResponse.toolCalls?.[0]?.parameters,
    });

    // Test 2: Recipe Finder Tool Recognition
    console.log('\n2️⃣ Testing recipe finder tool recognition...');
    const recipeResponse = await aiChatService.sendMessage(
      'Find me some quick vegetarian dinner recipes with high protein',
      []
    );

    console.log('✅ Recipe finder response:', {
      content: recipeResponse.content,
      hasToolCalls: !!recipeResponse.toolCalls,
      toolName: recipeResponse.toolCalls?.[0]?.name,
      parameters: recipeResponse.toolCalls?.[0]?.parameters,
    });

    // Test 3: Grocery List Tool Recognition
    console.log('\n3️⃣ Testing grocery list tool recognition...');
    const groceryResponse = await aiChatService.sendMessage(
      'Generate a grocery list for this week, organized by category',
      []
    );

    console.log('✅ Grocery list response:', {
      content: groceryResponse.content,
      hasToolCalls: !!groceryResponse.toolCalls,
      toolName: groceryResponse.toolCalls?.[0]?.name,
      parameters: groceryResponse.toolCalls?.[0]?.parameters,
    });

    // Test 4: Tool Registry Verification
    console.log('\n4️⃣ Testing tool registry...');
    const availableTools = Object.keys(toolRegistry);
    const mealPlanningTools = [
      'enhanced_meal_planner',
      'smart_recipe_finder',
      'generate_grocery_list',
      'update_grocery_item',
    ];

    const missingTools = mealPlanningTools.filter(
      tool => !availableTools.includes(tool)
    );

    if (missingTools.length === 0) {
      console.log(
        '✅ All meal planning tools are registered:',
        mealPlanningTools
      );
    } else {
      console.log('❌ Missing tools:', missingTools);
    }

    // Test 5: Tool Execution (if tools are called)
    console.log('\n5️⃣ Testing tool execution...');

    if (mealPlanResponse.toolCalls && mealPlanResponse.toolCalls.length > 0) {
      try {
        const toolCall = mealPlanResponse.toolCalls[0];
        console.log(`Executing tool: ${toolCall.name}`);

        const result = await aiChatService.executeToolCall(toolCall);
        console.log('✅ Tool execution result:', {
          success: result?.success,
          hasData: !!result?.data,
          dataKeys: result?.data ? Object.keys(result.data) : [],
        });
      } catch (error) {
        console.log(
          '⚠️ Tool execution error (expected in test environment):',
          error instanceof Error ? error.message : String(error)
        );
      }
    }

    console.log('\n🎉 AI Meal Planning Integration Test Complete!');
    return {
      success: true,
      results: {
        mealPlannerRecognized: !!mealPlanResponse.toolCalls,
        recipeFinderRecognized: !!recipeResponse.toolCalls,
        groceryListRecognized: !!groceryResponse.toolCalls,
        allToolsRegistered: missingTools.length === 0,
      },
    };
  } catch (error) {
    console.error('❌ Integration test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

// Helper function to test individual tools
export const testIndividualTool = async (
  toolName: string,
  _parameters: any = {}
) => {
  console.log(`🔧 Testing individual tool: ${toolName}`);

  try {
    const tool = toolRegistry[toolName];
    if (!tool) {
      throw new Error(`Tool ${toolName} not found in registry`);
    }

    console.log('Tool definition:', {
      name: tool.name,
      description: tool.description,
      requiredParams: tool.parameters.required,
    });

    // Note: Actual execution would require proper authentication and database setup
    console.log('✅ Tool definition is valid');

    return { success: true };
  } catch (error) {
    console.error(`❌ Tool test failed for ${toolName}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

// Test helper for parameter extraction
export const testParameterExtraction = () => {
  console.log('🔍 Testing parameter extraction...');

  const testMessages = [
    {
      message:
        'Create a meal plan for next week with vegetarian high-protein meals under 30 minutes',
      expectedExtractions: {
        dietary_preferences: ['vegetarian', 'high-protein'],
        max_prep_time_minutes: 30,
      },
    },
    {
      message: 'Find me some quick Italian dinner recipes for 2000 calories',
      expectedExtractions: {
        cuisine_type: 'italian',
        meal_type: 'dinner',
        max_calories: 2000,
      },
    },
    {
      message: 'Generate a grocery list excluding pantry staples',
      expectedExtractions: {
        exclude_pantry_staples: true,
        organize_by_category: true,
      },
    },
  ];

  testMessages.forEach((test, index) => {
    console.log(`\nTest ${index + 1}: "${test.message}"`);
    console.log('Expected extractions:', test.expectedExtractions);
    // Note: Actual extraction testing would require access to private methods
    console.log('✅ Test case defined');
  });

  return { success: true };
};

// Export all test functions
export const mealPlanningTests = {
  testMealPlanningIntegration,
  testIndividualTool,
  testParameterExtraction,
};

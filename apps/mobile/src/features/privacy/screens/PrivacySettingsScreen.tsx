import React, { useState, useEffect } from 'react';
import { View, ScrollView, StyleSheet, Alert, Platform } from 'react-native';
import {
  Text,
  Card,
  Switch,
  Button,
  Divider,
  List,
  Portal,
  Dialog,
  ActivityIndicator,
  Chip,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {
  GDPRService,
  type UserConsent,
  type GDPRComplianceStatus,
} from '../services/gdprService';

// Icon components to avoid nested component warnings
const ShieldAccountIcon = (props: any) => (
  <Icon {...props} name="shield-account" />
);
const DatabaseIcon = (props: any) => <Icon {...props} name="database" />; // eslint-disable-line @typescript-eslint/no-unused-vars
const DeleteIcon = (props: any) => <Icon {...props} name="delete" />; // eslint-disable-line @typescript-eslint/no-unused-vars
const DownloadIcon = (props: any) => <Icon {...props} name="download" />;
const AccountIcon = (props: any) => <Icon {...props} name="account" />;
const SecurityIcon = (props: any) => <Icon {...props} name="security" />;
const HistoryIcon = (props: any) => <Icon {...props} name="history" />; // eslint-disable-line @typescript-eslint/no-unused-vars
const DeleteForeverIcon = (props: any) => (
  <Icon {...props} name="delete-forever" color="#d32f2f" />
);

// Button components moved outside to avoid nested component warnings
const ExportButton = ({
  onPress,
  loading,
}: {
  onPress: () => void;
  loading: boolean;
}) => (
  <Button
    mode="outlined"
    onPress={onPress}
    loading={loading}
    disabled={loading}
  >
    Export
  </Button>
);

const DeleteButton = ({ onPress }: { onPress: () => void }) => (
  <Button
    mode="outlined"
    buttonColor="#ffebee"
    textColor="#d32f2f"
    onPress={onPress}
  >
    Delete
  </Button>
);

// Factory functions for wrapper components
const createExportButtonWrapper =
  (handleDataExport: () => void, exporting: boolean) => () => (
    <ExportButton onPress={handleDataExport} loading={exporting} />
  );
const createDeleteButtonWrapper =
  (setShowDeleteDialog: (value: boolean) => void) => () => (
    <DeleteButton onPress={() => setShowDeleteDialog(true)} />
  );

export const PrivacySettingsScreen: React.FC = () => {
  const [consent, setConsent] = useState<UserConsent | null>(null);
  const [complianceStatus, setComplianceStatus] =
    useState<GDPRComplianceStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState('');

  // Forward declarations to avoid use-before-init
  const handleDataExportRef = React.useRef<() => void>(() => {});
  const ExportButtonWrapper = createExportButtonWrapper(
    () => handleDataExportRef.current(),
    exporting
  );
  const DeleteButtonWrapper = createDeleteButtonWrapper(setShowDeleteDialog);

  useEffect(() => {
    loadPrivacyData();
  }, []);

  const loadPrivacyData = async () => {
    setLoading(true);
    try {
      const [consentResult, complianceResult] = await Promise.all([
        GDPRService.getUserConsent(),
        GDPRService.checkComplianceStatus(),
      ]);

      if (consentResult.data) {
        setConsent(consentResult.data);
      }

      if (complianceResult.data) {
        setComplianceStatus(complianceResult.data);
      }
    } catch (error) {
      console.error('Error loading privacy data:', error);
      Alert.alert('Error', 'Failed to load privacy settings');
    } finally {
      setLoading(false);
    }
  };

  const updateConsentSetting = async (
    setting: keyof Pick<
      UserConsent,
      | 'analytics_consent'
      | 'marketing_consent'
      | 'personalization_consent'
      | 'data_processing_consent'
    >,
    value: boolean
  ) => {
    if (!consent) {
      return;
    }

    setUpdating(true);
    try {
      const { data, error } = await GDPRService.updateConsent({
        [setting]: value,
      });

      if (error) {
        Alert.alert('Error', `Failed to update ${setting}: ${error}`);
        return;
      }

      if (data) {
        setConsent(data);
        Alert.alert('Success', 'Privacy preferences updated');
      }
    } catch (error) {
      console.error('Error updating consent:', error);
      Alert.alert('Error', 'Failed to update privacy preferences');
    } finally {
      setUpdating(false);
    }
  };

  const handleDataExport = async () => {
    handleDataExportRef.current = handleDataExport;
    setExporting(true);
    try {
      const { success, error, filename } = await GDPRService.downloadUserData();

      if (error) {
        Alert.alert('Export Failed', error);
        return;
      }

      if (success) {
        Alert.alert(
          'Export Complete',
          `Your data has been exported to ${filename}. You can find it in your Downloads folder.`,
          [
            {
              text: 'Share',
              onPress: () => {
                if (Platform.OS === 'ios') {
                  // On iOS, we could implement sharing if needed
                  Alert.alert('Info', 'File saved to Downloads');
                } else {
                  Alert.alert('Info', 'File saved to Downloads');
                }
              },
            },
            { text: 'OK' },
          ]
        );
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      Alert.alert('Error', 'Failed to export data');
    } finally {
      setExporting(false);
    }
  };

  const handleDataDeletion = async () => {
    if (deleteConfirmation !== 'DELETE') {
      Alert.alert('Confirmation Required', "Please type 'DELETE' to confirm");
      return;
    }

    try {
      const { data, error } =
        await GDPRService.deleteUserData('CONFIRM_DELETE');

      if (error) {
        Alert.alert('Deletion Failed', error);
        return;
      }

      if (data) {
        Alert.alert(
          'Account Deleted',
          `Your account and all associated data have been permanently deleted. ${data.total_rows_deleted} records were removed from ${data.tables_affected.length} tables.`,
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate to login or exit app
                // This would typically sign out the user and redirect to auth
              },
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error deleting data:', error);
      Alert.alert('Error', 'Failed to delete account data');
    } finally {
      setShowDeleteDialog(false);
      setDeleteConfirmation('');
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
          <Text style={styles.loadingText}>Loading privacy settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text variant="headlineMedium" style={styles.title}>
          Privacy & Data Settings
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Manage your privacy preferences and data rights
        </Text>

        {/* Consent Management */}
        <Card style={styles.card}>
          <Card.Title
            title="Data Processing Consent"
            subtitle="Control how we use your data"
            left={ShieldAccountIcon}
          />
          <Card.Content>
            <View style={styles.switchRow}>
              <View style={styles.switchLabel}>
                <Text variant="bodyLarge">Analytics & Performance</Text>
                <Text variant="bodySmall" style={styles.switchDescription}>
                  Help us improve the app with usage analytics
                </Text>
              </View>
              <Switch
                value={consent?.analytics_consent || false}
                onValueChange={value =>
                  updateConsentSetting('analytics_consent', value)
                }
                disabled={updating}
              />
            </View>

            <Divider style={styles.divider} />

            <View style={styles.switchRow}>
              <View style={styles.switchLabel}>
                <Text variant="bodyLarge">Marketing Communications</Text>
                <Text variant="bodySmall" style={styles.switchDescription}>
                  Receive updates about new features and tips
                </Text>
              </View>
              <Switch
                value={consent?.marketing_consent || false}
                onValueChange={value =>
                  updateConsentSetting('marketing_consent', value)
                }
                disabled={updating}
              />
            </View>

            <Divider style={styles.divider} />

            <View style={styles.switchRow}>
              <View style={styles.switchLabel}>
                <Text variant="bodyLarge">Personalization</Text>
                <Text variant="bodySmall" style={styles.switchDescription}>
                  Customize your experience with AI recommendations
                </Text>
              </View>
              <Switch
                value={consent?.personalization_consent || false}
                onValueChange={value =>
                  updateConsentSetting('personalization_consent', value)
                }
                disabled={updating}
              />
            </View>

            <Divider style={styles.divider} />

            <View style={styles.switchRow}>
              <View style={styles.switchLabel}>
                <Text variant="bodyLarge">Core Data Processing</Text>
                <Text variant="bodySmall" style={styles.switchDescription}>
                  Required for app functionality (cannot be disabled)
                </Text>
              </View>
              <Switch
                value={consent?.data_processing_consent || true}
                disabled={true}
              />
            </View>
          </Card.Content>
        </Card>

        {/* GDPR Rights */}
        <Card style={styles.card}>
          <Card.Title
            title="Your Data Rights"
            subtitle="GDPR compliance tools"
            left={SecurityIcon}
          />
          <Card.Content>
            <List.Item
              title="Download My Data"
              description="Export all your data in JSON format"
              left={DownloadIcon}
              right={ExportButtonWrapper}
            />

            <Divider style={styles.divider} />

            <List.Item
              title="Delete My Account"
              description="Permanently delete all your data"
              left={DeleteForeverIcon}
              right={DeleteButtonWrapper}
            />
          </Card.Content>
        </Card>

        {/* Compliance Status */}
        {complianceStatus && (
          <Card style={styles.card}>
            <Card.Title
              title="Compliance Status"
              subtitle="Your privacy compliance overview"
              left={AccountIcon}
            />
            <Card.Content>
              <View style={styles.statusRow}>
                <Text variant="bodyMedium">Consent Status:</Text>
                <Chip
                  icon={
                    complianceStatus.consent_status === 'recorded'
                      ? 'check'
                      : 'alert'
                  }
                  mode="outlined"
                  style={[
                    styles.statusChip,
                    complianceStatus.consent_status === 'recorded'
                      ? styles.successChip
                      : styles.warningChip,
                  ]}
                >
                  {complianceStatus.consent_status === 'recorded'
                    ? 'Recorded'
                    : 'Missing'}
                </Chip>
              </View>

              {complianceStatus.data_retention.cleanup_recommended && (
                <View style={styles.statusRow}>
                  <Text variant="bodyMedium">Data Cleanup:</Text>
                  <Chip
                    icon="information"
                    mode="outlined"
                    style={[styles.statusChip, styles.infoChip]}
                  >
                    {complianceStatus.data_retention.old_data_records} old
                    records
                  </Chip>
                </View>
              )}

              <Text variant="bodySmall" style={styles.complianceNote}>
                Last checked:{' '}
                {new Date(complianceStatus.check_date).toLocaleDateString()}
              </Text>
            </Card.Content>
          </Card>
        )}
      </ScrollView>

      {/* Delete Confirmation Dialog */}
      <Portal>
        <Dialog
          visible={showDeleteDialog}
          onDismiss={() => setShowDeleteDialog(false)}
        >
          <Dialog.Title>Delete Account</Dialog.Title>
          <Dialog.Content>
            <Text variant="bodyMedium" style={styles.deleteWarning}>
              ⚠️ This action cannot be undone. All your data will be permanently
              deleted.
            </Text>
            <Text variant="bodyMedium" style={styles.deleteInstructions}>
              Type "DELETE" to confirm:
            </Text>
            {/* Note: In a real implementation, you'd use TextInput here */}
            <Text variant="bodySmall" style={styles.deleteNote}>
              (TextInput component would go here in full implementation)
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowDeleteDialog(false)}>Cancel</Button>
            <Button
              mode="contained"
              buttonColor="#d32f2f"
              onPress={handleDataDeletion}
              disabled={deleteConfirmation !== 'DELETE'}
            >
              Delete Forever
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  title: {
    marginBottom: 8,
    fontWeight: 'bold',
  },
  subtitle: {
    marginBottom: 24,
    color: '#666',
  },
  card: {
    marginBottom: 16,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  switchLabel: {
    flex: 1,
    marginRight: 16,
  },
  switchDescription: {
    color: '#666',
    marginTop: 4,
  },
  divider: {
    marginVertical: 8,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusChip: {
    marginLeft: 8,
  },
  successChip: {
    backgroundColor: '#e8f5e8',
  },
  warningChip: {
    backgroundColor: '#fff3e0',
  },
  infoChip: {
    backgroundColor: '#e3f2fd',
  },
  complianceNote: {
    color: '#666',
    marginTop: 8,
  },
  deleteWarning: {
    color: '#d32f2f',
    marginBottom: 16,
  },
  deleteInstructions: {
    marginBottom: 8,
  },
  deleteNote: {
    color: '#666',
    fontStyle: 'italic',
  },
});

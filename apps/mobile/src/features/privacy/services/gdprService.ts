import { supabase } from '../../../lib/supabase';

export interface UserConsent {
  analytics_consent: boolean;
  marketing_consent: boolean;
  personalization_consent: boolean;
  data_processing_consent: boolean;
  consent_version: string;
  consent_date: string;
  updated_at: string;
}

export interface GDPRComplianceStatus {
  user_id: string;
  check_date: string;
  consent_status: 'recorded' | 'missing';
  consent_details: UserConsent;
  data_retention: {
    retention_period_days: number;
    old_data_records: number;
    cleanup_recommended: boolean;
  };
  rights_available: string[];
}

export interface GDPRAuditEntry {
  id: string;
  action_type: 'data_export' | 'data_deletion' | 'consent_update';
  action_details: Record<string, any>;
  created_at: string;
}

export interface DataExportResult {
  export_info: {
    user_id: string;
    export_date: string;
    export_version: string;
    format: string;
  };
  profile: Record<string, any>;
  questionnaires: {
    responses: any[];
    sessions: any[];
  };
  persona: {
    persona: Record<string, any>;
    insights: any[];
    updates: any[];
  };
  meals: {
    meal_plans: any[];
    meal_logs: any[];
  };
  workouts: {
    workout_plans: any[];
    workout_logs: any[];
  };
  progress: any[];
  feedback: any[];
  consent: UserConsent;
}

export interface DataDeletionResult {
  user_id: string;
  deletion_date: string;
  tables_affected: string[];
  total_rows_deleted: number;
  status: 'completed' | 'failed';
}

/**
 * GDPR Service for handling user privacy rights
 * Provides data export, deletion, and consent management
 */
export class GDPRService {
  /**
   * Export all user data in JSON format (GDPR Data Portability)
   */
  static async exportUserData(): Promise<{
    data: DataExportResult | null;
    error: string | null;
  }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return { data: null, error: 'User not authenticated' };
      }

      const { data, error } = await supabase.rpc('export_user_data', {
        p_user_id: user.id,
      });

      if (error) {
        console.error('Error exporting user data:', error);
        return { data: null, error: error.message };
      }

      return { data: data as DataExportResult, error: null };
    } catch (error) {
      console.error('Error in exportUserData:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Download user data as a JSON file
   */
  static async downloadUserData(): Promise<{
    success: boolean;
    error: string | null;
    filename?: string;
  }> {
    try {
      const { data, error } = await this.exportUserData();

      if (error || !data) {
        return { success: false, error: error || 'No data to export' };
      }

      // Create a blob with the JSON data
      const jsonString = JSON.stringify(data, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });

      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `platemotion-data-export-${timestamp}.json`;
      link.download = filename;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      return { success: true, error: null, filename };
    } catch (error) {
      console.error('Error downloading user data:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Download failed',
      };
    }
  }

  /**
   * Delete all user data (GDPR Right to be Forgotten)
   */
  static async deleteUserData(confirmationToken?: string): Promise<{
    data: DataDeletionResult | null;
    error: string | null;
  }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return { data: null, error: 'User not authenticated' };
      }

      const { data, error } = await supabase.rpc('delete_user_data', {
        p_user_id: user.id,
        p_confirmation_token: confirmationToken || null,
      });

      if (error) {
        console.error('Error deleting user data:', error);
        return { data: null, error: error.message };
      }

      return { data: data as DataDeletionResult, error: null };
    } catch (error) {
      console.error('Error in deleteUserData:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Update user consent preferences
   */
  static async updateConsent(
    consent: Partial<{
      analytics_consent: boolean;
      marketing_consent: boolean;
      personalization_consent: boolean;
      data_processing_consent: boolean;
    }>
  ): Promise<{
    data: UserConsent | null;
    error: string | null;
  }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return { data: null, error: 'User not authenticated' };
      }

      const { data, error } = await supabase.rpc('update_user_consent', {
        p_user_id: user.id,
        p_analytics_consent: consent.analytics_consent ?? null,
        p_marketing_consent: consent.marketing_consent ?? null,
        p_personalization_consent: consent.personalization_consent ?? null,
        p_data_processing_consent: consent.data_processing_consent ?? null,
      });

      if (error) {
        console.error('Error updating consent:', error);
        return { data: null, error: error.message };
      }

      return { data: data as UserConsent, error: null };
    } catch (error) {
      console.error('Error in updateConsent:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get current user consent preferences
   */
  static async getUserConsent(): Promise<{
    data: UserConsent | null;
    error: string | null;
  }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return { data: null, error: 'User not authenticated' };
      }

      const { data, error } = await supabase
        .from('user_consent')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        // PGRST116 is "not found" error, which is acceptable
        console.error('Error getting user consent:', error);
        return { data: null, error: error.message };
      }

      return { data: data as UserConsent | null, error: null };
    } catch (error) {
      console.error('Error in getUserConsent:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Check GDPR compliance status for user
   */
  static async checkComplianceStatus(): Promise<{
    data: GDPRComplianceStatus | null;
    error: string | null;
  }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return { data: null, error: 'User not authenticated' };
      }

      const { data, error } = await supabase.rpc('check_gdpr_compliance', {
        p_user_id: user.id,
      });

      if (error) {
        console.error('Error checking GDPR compliance:', error);
        return { data: null, error: error.message };
      }

      return { data: data as GDPRComplianceStatus, error: null };
    } catch (error) {
      console.error('Error in checkComplianceStatus:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get GDPR audit history for user
   */
  static async getAuditHistory(limit: number = 50): Promise<{
    data: {
      user_id: string;
      audit_history: GDPRAuditEntry[];
      total_records: number;
    } | null;
    error: string | null;
  }> {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        return { data: null, error: 'User not authenticated' };
      }

      const { data, error } = await supabase.rpc('get_gdpr_audit_history', {
        p_user_id: user.id,
        p_limit: limit,
      });

      if (error) {
        console.error('Error getting audit history:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error in getAuditHistory:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}

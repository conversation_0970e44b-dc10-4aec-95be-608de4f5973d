-- AI Learning Database Schema for PlateMotion
-- This schema supports universal AI learning while maintaining user privacy

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Table for storing anonymous food correction data
CREATE TABLE ai_learning_corrections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Original AI analysis
  original_food_name TEXT NOT NULL,
  original_category TEXT NOT NULL,
  original_portion TEXT,
  original_calories INTEGER,
  original_confidence FLOAT,
  
  -- User correction
  corrected_food_name TEXT, -- NULL if user removed the item
  corrected_category TEXT,
  corrected_portion TEXT,
  corrected_calories INTEGER,
  correction_type TEXT NOT NULL CHECK (correction_type IN ('name_change', 'category_change', 'portion_change', 'calorie_change', 'removal', 'addition')),
  
  -- Metadata
  analysis_id TEXT NOT NULL, -- Links to original photo analysis
  image_hash TEXT, -- Hash of image for deduplication (no actual image stored)
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes for performance
  INDEX idx_corrections_food_name ON ai_learning_corrections(original_food_name),
  INDEX idx_corrections_category ON ai_learning_corrections(original_category),
  INDEX idx_corrections_type ON ai_learning_corrections(correction_type),
  INDEX idx_corrections_created_at ON ai_learning_corrections(created_at)
);

-- Table for storing improved food recognition patterns
CREATE TABLE food_recognition_patterns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Food identification
  food_name TEXT NOT NULL UNIQUE,
  alternative_names TEXT[] DEFAULT '{}', -- Common alternative names
  category TEXT NOT NULL,
  
  -- Portion and nutrition data
  common_portions JSONB DEFAULT '{}', -- {"1 cup": 200, "1 medium": 150, etc.}
  avg_calories_per_100g FLOAT,
  macro_profile JSONB, -- {"protein": 25, "carbs": 0, "fat": 5} per 100g
  
  -- AI confidence boosting
  confidence_boost FLOAT DEFAULT 0.0, -- How much to boost confidence for this food
  recognition_accuracy FLOAT DEFAULT 0.0, -- Historical accuracy rate
  correction_count INTEGER DEFAULT 0, -- Number of corrections received
  
  -- Learning metadata
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_patterns_food_name ON food_recognition_patterns(food_name),
  INDEX idx_patterns_category ON food_recognition_patterns(category),
  INDEX idx_patterns_accuracy ON food_recognition_patterns(recognition_accuracy)
);

-- Table for storing food image embeddings (vector database)
CREATE TABLE food_image_embeddings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Food identification
  food_name TEXT NOT NULL,
  category TEXT NOT NULL,
  
  -- Vector embedding (1536 dimensions for OpenAI embeddings)
  embedding vector(1536),
  
  -- Image metadata (no actual image stored)
  image_hash TEXT NOT NULL UNIQUE,
  image_features JSONB, -- Color, texture, shape features
  
  -- Learning data
  confidence_score FLOAT NOT NULL,
  correction_count INTEGER DEFAULT 0,
  success_rate FLOAT DEFAULT 1.0,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_matched TIMESTAMP WITH TIME ZONE,
  
  -- Vector similarity index
  INDEX idx_embeddings_vector ON food_image_embeddings USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100),
  INDEX idx_embeddings_food_name ON food_image_embeddings(food_name),
  INDEX idx_embeddings_hash ON food_image_embeddings(image_hash)
);

-- Table for tracking AI learning statistics
CREATE TABLE ai_learning_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Statistics
  total_corrections INTEGER DEFAULT 0,
  total_analyses INTEGER DEFAULT 0,
  accuracy_improvement FLOAT DEFAULT 0.0,
  
  -- Category breakdown
  category_stats JSONB DEFAULT '{}', -- {"protein": {"corrections": 50, "accuracy": 0.85}, ...}
  
  -- Time period
  date DATE NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  INDEX idx_stats_date ON ai_learning_stats(date)
);

-- Table for user learning preferences (anonymous)
CREATE TABLE user_learning_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Anonymous user identifier (hashed)
  user_hash TEXT NOT NULL UNIQUE,
  
  -- Learning preferences
  share_corrections BOOLEAN DEFAULT true,
  share_patterns BOOLEAN DEFAULT true,
  
  -- Usage patterns (anonymous)
  preferred_foods TEXT[] DEFAULT '{}',
  common_portions JSONB DEFAULT '{}',
  dietary_patterns JSONB DEFAULT '{}', -- Inferred patterns, no personal data
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  INDEX idx_preferences_hash ON user_learning_preferences(user_hash)
);

-- Function to update food recognition patterns based on corrections
CREATE OR REPLACE FUNCTION update_food_patterns()
RETURNS TRIGGER AS $$
BEGIN
  -- Update or create food pattern based on correction
  INSERT INTO food_recognition_patterns (
    food_name,
    category,
    correction_count,
    last_updated
  )
  VALUES (
    COALESCE(NEW.corrected_food_name, NEW.original_food_name),
    COALESCE(NEW.corrected_category, NEW.original_category),
    1,
    NOW()
  )
  ON CONFLICT (food_name) DO UPDATE SET
    correction_count = food_recognition_patterns.correction_count + 1,
    last_updated = NOW(),
    -- Update accuracy based on correction type
    recognition_accuracy = CASE 
      WHEN NEW.correction_type = 'removal' THEN 
        GREATEST(food_recognition_patterns.recognition_accuracy - 0.1, 0.0)
      ELSE 
        LEAST(food_recognition_patterns.recognition_accuracy + 0.05, 1.0)
    END;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update patterns when corrections are added
CREATE TRIGGER trigger_update_food_patterns
  AFTER INSERT ON ai_learning_corrections
  FOR EACH ROW
  EXECUTE FUNCTION update_food_patterns();

-- Function to get improved food suggestions based on learning
CREATE OR REPLACE FUNCTION get_improved_food_suggestions(
  input_food_name TEXT,
  input_category TEXT DEFAULT NULL
)
RETURNS TABLE (
  suggested_name TEXT,
  category TEXT,
  confidence_boost FLOAT,
  common_portions JSONB,
  avg_calories FLOAT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    frp.food_name,
    frp.category,
    frp.confidence_boost,
    frp.common_portions,
    frp.avg_calories_per_100g
  FROM food_recognition_patterns frp
  WHERE 
    frp.food_name ILIKE '%' || input_food_name || '%'
    OR input_food_name = ANY(frp.alternative_names)
    OR (input_category IS NOT NULL AND frp.category = input_category)
  ORDER BY 
    frp.recognition_accuracy DESC,
    frp.confidence_boost DESC
  LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- Row Level Security (RLS) policies for privacy
ALTER TABLE ai_learning_corrections ENABLE ROW LEVEL SECURITY;
ALTER TABLE food_recognition_patterns ENABLE ROW LEVEL SECURITY;
ALTER TABLE food_image_embeddings ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_learning_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_learning_preferences ENABLE ROW LEVEL SECURITY;

-- Allow anonymous inserts for corrections (no user identification required)
CREATE POLICY "Allow anonymous corrections" ON ai_learning_corrections
  FOR INSERT WITH CHECK (true);

-- Allow read access to patterns for all users
CREATE POLICY "Allow read patterns" ON food_recognition_patterns
  FOR SELECT USING (true);

-- Allow read access to embeddings for similarity search
CREATE POLICY "Allow read embeddings" ON food_image_embeddings
  FOR SELECT USING (true);

-- Allow read access to stats
CREATE POLICY "Allow read stats" ON ai_learning_stats
  FOR SELECT USING (true);

-- User preferences are tied to anonymous hash
CREATE POLICY "User preferences access" ON user_learning_preferences
  FOR ALL USING (user_hash = current_setting('app.user_hash', true));

-- Create indexes for better performance
CREATE INDEX CONCURRENTLY idx_corrections_composite ON ai_learning_corrections(original_food_name, correction_type, created_at);
CREATE INDEX CONCURRENTLY idx_patterns_composite ON food_recognition_patterns(category, recognition_accuracy DESC);

-- Initial data seeding with common foods
INSERT INTO food_recognition_patterns (food_name, category, common_portions, avg_calories_per_100g, macro_profile) VALUES
('chicken breast', 'protein', '{"3 oz": 140, "1 medium piece": 185, "100g": 165}', 165, '{"protein": 31, "carbs": 0, "fat": 3.6}'),
('white rice', 'carbs', '{"1 cup cooked": 205, "1/2 cup": 103, "100g": 130}', 130, '{"protein": 2.7, "carbs": 28, "fat": 0.3}'),
('broccoli', 'vegetables', '{"1 cup": 25, "1 medium head": 150, "100g": 34}', 34, '{"protein": 2.8, "carbs": 7, "fat": 0.4}'),
('banana', 'fruits', '{"1 medium": 105, "1 large": 121, "100g": 89}', 89, '{"protein": 1.1, "carbs": 23, "fat": 0.3}'),
('salmon', 'protein', '{"3 oz": 175, "1 fillet": 350, "100g": 208}', 208, '{"protein": 25, "carbs": 0, "fat": 12}');

-- Function for vector similarity search
CREATE OR REPLACE FUNCTION match_food_embeddings(
  query_embedding vector(1536),
  match_threshold float DEFAULT 0.7,
  match_count int DEFAULT 5
)
RETURNS TABLE (
  food_name text,
  category text,
  similarity float,
  success_rate float,
  confidence_score float
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    fie.food_name,
    fie.category,
    1 - (fie.embedding <=> query_embedding) as similarity,
    fie.success_rate,
    fie.confidence_score
  FROM food_image_embeddings fie
  WHERE 1 - (fie.embedding <=> query_embedding) > match_threshold
  ORDER BY fie.embedding <=> query_embedding
  LIMIT match_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get learning insights and recommendations
CREATE OR REPLACE FUNCTION get_learning_insights()
RETURNS TABLE (
  insight_type text,
  insight_data jsonb,
  confidence float
) AS $$
BEGIN
  RETURN QUERY
  -- Most corrected foods (need improvement)
  SELECT
    'most_corrected_foods'::text,
    jsonb_build_object(
      'foods', jsonb_agg(
        jsonb_build_object(
          'food_name', original_food_name,
          'correction_count', correction_count,
          'accuracy_rate', 1.0 - (correction_count::float / GREATEST(total_analyses, 1))
        )
      )
    ),
    0.9::float
  FROM (
    SELECT
      original_food_name,
      COUNT(*) as correction_count,
      COUNT(*) as total_analyses -- Simplified for now
    FROM ai_learning_corrections
    WHERE created_at >= NOW() - INTERVAL '30 days'
    GROUP BY original_food_name
    HAVING COUNT(*) >= 3
    ORDER BY COUNT(*) DESC
    LIMIT 10
  ) most_corrected

  UNION ALL

  -- Best performing foods (high accuracy)
  SELECT
    'best_performing_foods'::text,
    jsonb_build_object(
      'foods', jsonb_agg(
        jsonb_build_object(
          'food_name', food_name,
          'recognition_accuracy', recognition_accuracy,
          'confidence_boost', confidence_boost
        )
      )
    ),
    0.8::float
  FROM food_recognition_patterns
  WHERE recognition_accuracy >= 0.8
  ORDER BY recognition_accuracy DESC
  LIMIT 10

  UNION ALL

  -- Category performance analysis
  SELECT
    'category_performance'::text,
    jsonb_build_object(
      'categories', jsonb_agg(
        jsonb_build_object(
          'category', category,
          'avg_accuracy', avg_accuracy,
          'total_corrections', total_corrections
        )
      )
    ),
    0.7::float
  FROM (
    SELECT
      frp.category,
      AVG(frp.recognition_accuracy) as avg_accuracy,
      COUNT(alc.id) as total_corrections
    FROM food_recognition_patterns frp
    LEFT JOIN ai_learning_corrections alc ON alc.original_category = frp.category
    GROUP BY frp.category
    ORDER BY avg_accuracy DESC
  ) category_stats;
END;
$$ LANGUAGE plpgsql;

-- Function to optimize AI model based on corrections
CREATE OR REPLACE FUNCTION optimize_ai_model()
RETURNS TABLE (
  optimization_type text,
  affected_foods text[],
  improvement_score float
) AS $$
BEGIN
  -- Update confidence boosts based on correction patterns
  UPDATE food_recognition_patterns
  SET
    confidence_boost = CASE
      WHEN recognition_accuracy >= 0.9 THEN 0.2
      WHEN recognition_accuracy >= 0.8 THEN 0.1
      WHEN recognition_accuracy >= 0.7 THEN 0.05
      ELSE 0.0
    END,
    last_updated = NOW()
  WHERE last_updated < NOW() - INTERVAL '1 day';

  -- Return optimization results
  RETURN QUERY
  SELECT
    'confidence_boost_update'::text,
    array_agg(food_name),
    AVG(confidence_boost)
  FROM food_recognition_patterns
  WHERE last_updated >= NOW() - INTERVAL '1 minute'
  GROUP BY 'confidence_boost_update';
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically optimize model when corrections reach threshold
CREATE OR REPLACE FUNCTION trigger_model_optimization()
RETURNS TRIGGER AS $$
DECLARE
  correction_count int;
BEGIN
  -- Check if we have enough new corrections to trigger optimization
  SELECT COUNT(*) INTO correction_count
  FROM ai_learning_corrections
  WHERE created_at >= NOW() - INTERVAL '1 hour';

  -- Trigger optimization if we have 10+ corrections in the last hour
  IF correction_count >= 10 THEN
    PERFORM optimize_ai_model();
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic optimization
CREATE TRIGGER trigger_auto_optimization
  AFTER INSERT ON ai_learning_corrections
  FOR EACH STATEMENT
  EXECUTE FUNCTION trigger_model_optimization();

-- Comments for documentation
COMMENT ON TABLE ai_learning_corrections IS 'Stores anonymous user corrections to improve AI food recognition';
COMMENT ON TABLE food_recognition_patterns IS 'Learned patterns for better food identification and nutrition estimation';
COMMENT ON TABLE food_image_embeddings IS 'Vector embeddings for visual food recognition similarity matching';
COMMENT ON TABLE ai_learning_stats IS 'Daily statistics tracking AI learning progress and accuracy improvements';
COMMENT ON TABLE user_learning_preferences IS 'Anonymous user preferences for AI learning participation';

COMMENT ON FUNCTION match_food_embeddings IS 'Vector similarity search for finding similar foods based on image embeddings';
COMMENT ON FUNCTION get_learning_insights IS 'Provides insights about AI learning performance and areas for improvement';
COMMENT ON FUNCTION optimize_ai_model IS 'Automatically optimizes AI model parameters based on correction patterns';

name: CI

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: pnpm/action-setup@v4

      - name: Set up Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile

      - name: Lint
        run: pnpm --filter ./apps/admin run lint:strict && pnpm --filter ./apps/mobile run lint && pnpm lint

      - name: Format Check
        run: pnpm format:check

      - name: Build
        run: pnpm build

      - name: Test
        run: pnpm test --if-present

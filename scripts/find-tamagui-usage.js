#!/usr/bin/env node

/**
 * Script to identify all files that still use Tamagui components
 * This helps track migration progress
 */

const fs = require("fs");
const path = require("path");

// Directories to search
const SEARCH_DIRECTORIES = ["apps/mobile/src"];

// Tamagui import patterns to look for
const TAMAGUI_PATTERNS = [
  'from "tamagui"',
  'from "@tamagui/lucide-icons"',
  "import.*tamagui",
  "import.*@tamagui/lucide-icons",
];

// File extensions to check
const FILE_EXTENSIONS = [".js", ".jsx", ".ts", ".tsx"];

function searchFiles(dir, results = []) {
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      searchFiles(filePath, results);
    } else if (
      stat.isFile() &&
      FILE_EXTENSIONS.some((ext) => file.endsWith(ext))
    ) {
      try {
        const content = fs.readFileSync(filePath, "utf8");

        // Check if file contains any Tamagui patterns
        const hasTamagui = TAMAGUI_PATTERNS.some((pattern) =>
          new RegExp(pattern, "i").test(content),
        );

        if (hasTamagui) {
          results.push(filePath);
        }
      } catch (error) {
        console.error(`Error reading file ${filePath}:`, error.message);
      }
    }
  }

  return results;
}

function main() {
  console.log("Searching for Tamagui usage...\n");

  const results = [];

  for (const dir of SEARCH_DIRECTORIES) {
    if (fs.existsSync(dir)) {
      console.log(`Searching in ${dir}...`);
      searchFiles(dir, results);
    } else {
      console.log(`Directory ${dir} does not exist`);
    }
  }

  console.log("\nFiles still using Tamagui:");
  console.log("========================");

  if (results.length === 0) {
    console.log("No files found using Tamagui! Migration complete.");
  } else {
    results.forEach((file) => {
      console.log(`- ${file}`);
    });
    console.log(`\nTotal files: ${results.length}`);
  }
}

main();

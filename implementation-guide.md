# PlateMotion Recipe Database Implementation Guide

## 🎯 **Database Structure Overview**

The enhanced recipe database now includes **ALL** the attributes you requested plus critical additions for AI meal planning:

### ✅ **Your Original Requirements (Implemented)**

- ✅ **Image** → `image_url`, `video_url`, `animation_url`
- ✅ **Title** → `name`
- ✅ **Description** → `description`
- ✅ **Budget** → `estimated_cost_per_serving`, `cost_category`
- ✅ **Ease of cuisine** → `difficulty_level` (beginner/intermediate/advanced)
- ✅ **Instructions** → `instructions` (JSONB with detailed steps)
- ✅ **Macros** → `calories_per_serving`, `protein_grams`, `carbs_grams`, `fat_grams`, plus enhanced nutrition
- ✅ **Types of cuisine** → `cuisine_type`
- ✅ **Tags** → `dietary_tags`, `occasion_tags`, `flavor_profile`, `allergens`

### 🚀 **Critical Additions for AI & User Experience**

#### **Time Information (CRITICAL)**

```sql
prep_time_minutes INTEGER NOT NULL,
cook_time_minutes INTEGER NOT NULL,
total_time_minutes INTEGER GENERATED, -- Auto-calculated
active_time_minutes INTEGER, -- Hands-on vs passive time
```

#### **Detailed Ingredients (CRITICAL)**

```sql
ingredients JSONB NOT NULL,
-- Example: [{"name": "chicken breast", "amount": "1", "unit": "lb", "category": "meat", "optional": false, "substitutes": ["turkey breast"]}]

equipment_required TEXT[] DEFAULT '{}', -- ['oven', 'blender']
pantry_staples TEXT[] DEFAULT '{}', -- ['salt', 'pepper', 'olive oil']
```

#### **Enhanced Dietary & Allergy Info**

```sql
dietary_tags TEXT[] DEFAULT '{}', -- ['vegetarian', 'vegan', 'gluten-free', 'keto']
allergens TEXT[] DEFAULT '{}', -- ['nuts', 'dairy', 'eggs', 'shellfish']
meal_types TEXT[] DEFAULT '{}', -- ['breakfast', 'lunch', 'dinner', 'snack']
```

#### **Meal Planning Specific**

```sql
servings INTEGER NOT NULL DEFAULT 4,
serving_size VARCHAR(100), -- '1 cup', '1 piece'
meal_prep_friendly BOOLEAN DEFAULT FALSE,
storage_instructions TEXT,
reheating_instructions TEXT,
seasonality TEXT[] DEFAULT '{}', -- ['spring', 'summer', 'fall', 'winter']
```

#### **User Experience & Quality**

```sql
rating_average DECIMAL(3,2) DEFAULT 0.00,
rating_count INTEGER DEFAULT 0,
success_rate DECIMAL(3,2) DEFAULT 0.00, -- How often people complete it successfully
spice_level VARCHAR(20), -- 'none', 'mild', 'medium', 'hot'
energy_level VARCHAR(20), -- 'light', 'moderate', 'heavy'
```

## 🔧 **Implementation Steps**

### **Phase 1: Database Setup**

```bash
# 1. Apply the enhanced schema to Supabase
psql -h your-supabase-host -U postgres -d postgres -f database-schemas/recipes-schema.sql

# 2. Set up Row Level Security (RLS)
# Enable RLS on all tables and create policies for user data access
```

### **Phase 2: Sample Data Population**

```sql
-- Insert sample recipe data
INSERT INTO recipes (
    name, description, cuisine_type, prep_time_minutes, cook_time_minutes,
    difficulty_level, servings, ingredients, dietary_tags, meal_types,
    calories_per_serving, protein_grams, carbs_grams, fat_grams,
    estimated_cost_per_serving, image_url
) VALUES (
    'Quick Chicken Stir Fry',
    'A healthy and delicious stir fry ready in 20 minutes',
    'asian',
    10, 10, 'beginner', 4,
    '[
        {"name": "chicken breast", "amount": "1", "unit": "lb", "category": "meat"},
        {"name": "mixed vegetables", "amount": "2", "unit": "cups", "category": "produce"},
        {"name": "soy sauce", "amount": "2", "unit": "tbsp", "category": "pantry"}
    ]'::jsonb,
    ARRAY['high-protein', 'gluten-free'],
    ARRAY['lunch', 'dinner'],
    320, 35, 12, 8, 4.50,
    'https://example.com/chicken-stir-fry.jpg'
);
```

### **Phase 3: AI Tools Integration**

```typescript
// Example AI tool implementation
class MealPlannerTool {
  async generateMealPlan(params: MealPlanParams): Promise<MealPlan> {
    // 1. Get user preferences
    const userPrefs = await this.getUserPreferences(params.user_id);

    // 2. Build optimized database query
    const query = `
            SELECT r.*, 
                   calculate_recipe_compatibility(r.id, $1) as compatibility_score
            FROM recipes r
            WHERE r.is_active = TRUE
              AND r.dietary_tags @> $2
              AND NOT (r.allergens && $3)
              AND r.total_time_minutes <= $4
              AND r.equipment_required <@ $5
            ORDER BY compatibility_score DESC, r.rating_average DESC
            LIMIT 50
        `;

    // 3. Execute fast database query (< 100ms)
    const candidateRecipes = await supabase.rpc("execute_query", {
      query,
      params: [
        params.user_id,
        params.dietary_preferences,
        userPrefs.allergens_to_avoid,
        params.max_prep_time_minutes,
        userPrefs.available_equipment,
      ],
    });

    // 4. AI selects optimal combination for 7-day plan
    return this.optimizeMealPlan(candidateRecipes, params);
  }
}
```

## 📊 **Performance Optimization**

### **Database Indexes (Already Created)**

- **GIN indexes** on all array fields (`dietary_tags`, `allergens`, `equipment_required`)
- **B-tree indexes** on common filters (`total_time_minutes`, `difficulty_level`, `rating_average`)
- **Composite indexes** for common AI query patterns
- **Full-text search** index for recipe names and descriptions

### **Query Performance Targets**

- Recipe search: **< 50ms**
- Meal plan generation: **< 2 seconds**
- User compatibility scoring: **< 100ms**

### **Expected Database Size**

- **500 recipes** = ~50MB storage
- **10,000 users** with preferences = ~10MB
- **100,000 reviews** = ~100MB
- **Total**: ~200MB (tiny for modern databases)

## 🎯 **Content Strategy**

### **Recipe Collection Plan**

**Phase 1: Foundation (100 recipes)**

- 25 breakfast recipes (quick, balanced, various diets)
- 35 lunch recipes (meal-prep friendly, portable)
- 40 dinner recipes (family-friendly, various cuisines)

**Phase 2: Expansion (200 more recipes)**

- Specialty diets (keto: 30, paleo: 20, Mediterranean: 25)
- International cuisines (Italian: 25, Mexican: 20, Indian: 15)
- Seasonal recipes (25 per season)
- Advanced techniques (20 recipes)

**Phase 3: User-Driven (200+ recipes)**

- User submissions (moderated)
- Trending requests
- Seasonal updates

### **Quality Standards**

- ✅ Professional food photography
- ✅ Tested recipes with accurate timing
- ✅ Complete nutritional analysis
- ✅ Clear, step-by-step instructions
- ✅ Equipment alternatives provided
- ✅ Dietary modifications suggested

## 🚀 **Next Steps**

1. **Apply database schema** to Supabase
2. **Create sample data** (20-30 recipes for testing)
3. **Implement AI tools** with database integration
4. **Test performance** with realistic queries
5. **Build content pipeline** for recipe addition
6. **Launch with core collection** and expand based on usage

This enhanced database structure gives PlateMotion everything needed for intelligent, personalized meal planning while maintaining excellent performance and user experience!

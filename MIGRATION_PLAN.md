# Tamagui to React Native Paper Migration Plan

## Migration Order and Status

1. **Nutrition Feature** (In Progress)
   - [x] `NutritionScreen.tsx` - Migrated to React Native Paper
   - [x] `GroceryListScreen.tsx` - Migrated to React Native Paper
   - [ ] `RecipeDetailScreen.tsx` - Pending
2. **Exercise Feature** (Pending)
   - [ ] `ExerciseScreen.tsx` - Pending
   - [ ] `ExercisePlayerScreen.tsx` - Pending
   - [ ] `WorkoutDetailScreen.tsx` - Pending
3. **Profile Feature** (Pending)
   - [ ] `ProfileScreen.tsx` - Pending
4. **Progress Feature** (Pending)
   - [ ] `ProgressScreen.tsx` - Pending

## Migration Steps for Each File

For each file, follow these steps:

1. **Backup**: Create a backup of the original file
2. **Analyze**: Review the file to understand Tamagui component usage
3. **Replace Imports**: Replace Tamagui imports with React Native Paper equivalents
4. **Update Components**: Replace Tamagui components with React Native Paper components
5. **Adjust Styling**: Update styling to use React Native Paper conventions
6. **Fix Icons**: Replace Tamagui icons with React Native Paper IconButton or react-native-vector-icons
7. **Test**: Run TypeScript compiler to check for errors
8. **Verify**: Test the component in the app

## Component Mapping Reference

Use the mapping from `TAMAGUI_MIGRATION_INVENTORY.md` as a reference:

- Tamagui YStack → React Native Paper View
- Tamagui XStack → React Native Paper View with flexDirection: 'row'
- Tamagui Text → React Native Paper Text
- Tamagui Button → React Native Paper Button
- Tamagui Card → React Native Paper Card
- Tamagui Input → React Native Paper TextInput
- Tamagui Icons → React Native Paper IconButton or react-native-vector-icons

## Time Estimates

- Simple screens (1-2 components): 30-60 minutes
- Complex screens (5+ components): 1-2 hours
- Screens with custom components: 2-3 hours

## Quality Assurance

After migrating each file:

1. Run TypeScript compilation: `npx tsc --noEmit`
2. Check for any remaining Tamagui references
3. Verify component functionality
4. Check styling consistency
5. Update the migration inventory and progress tracking files

## Completion Criteria

The migration is complete when:

1. All 7 remaining files have been migrated
2. No Tamagui imports remain in the codebase
3. TypeScript compilation passes with no errors
4. All existing functionality is preserved
5. Visual appearance is consistent with the original design
6. All tests pass
7. Documentation is updated

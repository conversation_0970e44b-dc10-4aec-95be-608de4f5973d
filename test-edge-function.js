// Test script to debug the edge function
const fetch = require('node-fetch');

async function testEdgeFunction() {
  const url = 'https://oykpnhzgxpzrgibaplid.supabase.co/functions/v1/gemini-chat';
  
  const payload = {
    message: 'Hello',
    conversationHistory: []
  };

  try {
    console.log('Testing edge function...');
    console.log('URL:', url);
    console.log('Payload:', JSON.stringify(payload, null, 2));

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im95a3BuaHpneHB6cmdpYmFwbGlkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM2NTIzMzcsImV4cCI6MjA2OTIyODMzN30.0VIpb8nodQdv5tobWXln0JNbWJnDK1nUrA5LzSTVaHc'
      },
      body: JSON.stringify(payload)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('Response body:', responseText);

    if (response.ok) {
      try {
        const jsonData = JSON.parse(responseText);
        console.log('Parsed JSON:', JSON.stringify(jsonData, null, 2));
      } catch (e) {
        console.log('Response is not valid JSON');
      }
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

testEdgeFunction();
